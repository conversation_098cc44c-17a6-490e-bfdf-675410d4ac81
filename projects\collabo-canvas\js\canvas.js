/**
 * CollaboCanvas Canvas Module
 * Handles canvas drawing operations and rendering
 */

class CanvasManager {
  constructor() {
    this.canvas = null;
    this.ctx = null;
    this.isDrawing = false;
    this.currentTool = 'pen';
    this.currentColor = '#000000';
    this.currentSize = 5;
    this.zoom = 1;
    this.panX = 0;
    this.panY = 0;
    
    // Drawing state
    this.lastX = 0;
    this.lastY = 0;
    this.startX = 0;
    this.startY = 0;
    
    // History for undo/redo
    this.history = [];
    this.historyStep = -1;
    this.maxHistorySteps = 50;
    
    // Temporary canvas for shape preview
    this.tempCanvas = null;
    this.tempCtx = null;
    
    // Event callbacks
    this.onDrawStart = null;
    this.onDrawing = null;
    this.onDrawEnd = null;
    this.onCursorMove = null;
  }

  init(canvasId) {
    this.canvas = document.getElementById(canvasId);
    if (!this.canvas) {
      throw new Error(`Canvas element with id '${canvasId}' not found`);
    }

    this.ctx = this.canvas.getContext('2d');
    this.setupCanvas();
    this.setupEventListeners();
    this.saveState(); // Save initial blank state
    
    console.log('Canvas initialized successfully');
  }

  setupCanvas() {
    // Set canvas size
    this.resizeCanvas();
    
    // Create temporary canvas for shape preview
    this.tempCanvas = document.createElement('canvas');
    this.tempCanvas.width = this.canvas.width;
    this.tempCanvas.height = this.canvas.height;
    this.tempCtx = this.tempCanvas.getContext('2d');
    
    // Set default drawing properties
    this.ctx.lineCap = 'round';
    this.ctx.lineJoin = 'round';
    this.ctx.imageSmoothingEnabled = true;
    
    // Handle window resize
    window.addEventListener('resize', () => {
      this.resizeCanvas();
    });
  }

  resizeCanvas() {
    const container = this.canvas.parentElement;
    const rect = container.getBoundingClientRect();
    
    // Store current canvas content
    const imageData = this.ctx ? this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height) : null;
    
    // Resize canvas
    this.canvas.width = Math.max(1200, rect.width);
    this.canvas.height = Math.max(800, rect.height - 60); // Account for toolbar
    
    // Restore canvas content if it existed
    if (imageData) {
      this.ctx.putImageData(imageData, 0, 0);
    }
    
    // Update temporary canvas size
    if (this.tempCanvas) {
      this.tempCanvas.width = this.canvas.width;
      this.tempCanvas.height = this.canvas.height;
    }
  }

  setupEventListeners() {
    // Mouse events
    this.canvas.addEventListener('mousedown', (e) => this.handleMouseDown(e));
    this.canvas.addEventListener('mousemove', (e) => this.handleMouseMove(e));
    this.canvas.addEventListener('mouseup', (e) => this.handleMouseUp(e));
    this.canvas.addEventListener('mouseout', (e) => this.handleMouseUp(e));
    
    // Touch events for mobile
    this.canvas.addEventListener('touchstart', (e) => this.handleTouchStart(e));
    this.canvas.addEventListener('touchmove', (e) => this.handleTouchMove(e));
    this.canvas.addEventListener('touchend', (e) => this.handleTouchEnd(e));
    
    // Prevent scrolling on touch
    this.canvas.addEventListener('touchstart', (e) => e.preventDefault());
    this.canvas.addEventListener('touchmove', (e) => e.preventDefault());
  }

  getMousePos(e) {
    const rect = this.canvas.getBoundingClientRect();
    const scaleX = this.canvas.width / rect.width;
    const scaleY = this.canvas.height / rect.height;
    
    return {
      x: (e.clientX - rect.left) * scaleX,
      y: (e.clientY - rect.top) * scaleY
    };
  }

  getTouchPos(e) {
    const rect = this.canvas.getBoundingClientRect();
    const scaleX = this.canvas.width / rect.width;
    const scaleY = this.canvas.height / rect.height;
    
    const touch = e.touches[0] || e.changedTouches[0];
    return {
      x: (touch.clientX - rect.left) * scaleX,
      y: (touch.clientY - rect.top) * scaleY
    };
  }

  handleMouseDown(e) {
    const pos = this.getMousePos(e);
    this.startDrawing(pos.x, pos.y);
  }

  handleMouseMove(e) {
    const pos = this.getMousePos(e);
    
    // Always broadcast cursor position
    if (this.onCursorMove) {
      this.onCursorMove(pos.x, pos.y);
    }
    
    if (this.isDrawing) {
      this.continuDrawing(pos.x, pos.y);
    }
  }

  handleMouseUp(e) {
    if (this.isDrawing) {
      const pos = this.getMousePos(e);
      this.stopDrawing(pos.x, pos.y);
    }
  }

  handleTouchStart(e) {
    const pos = this.getTouchPos(e);
    this.startDrawing(pos.x, pos.y);
  }

  handleTouchMove(e) {
    const pos = this.getTouchPos(e);
    
    if (this.onCursorMove) {
      this.onCursorMove(pos.x, pos.y);
    }
    
    if (this.isDrawing) {
      this.continuDrawing(pos.x, pos.y);
    }
  }

  handleTouchEnd(e) {
    if (this.isDrawing) {
      const pos = this.getTouchPos(e);
      this.stopDrawing(pos.x, pos.y);
    }
  }

  startDrawing(x, y) {
    this.isDrawing = true;
    this.lastX = x;
    this.lastY = y;
    this.startX = x;
    this.startY = y;
    
    // Set drawing properties
    this.ctx.strokeStyle = this.currentColor;
    this.ctx.fillStyle = this.currentColor;
    this.ctx.lineWidth = this.currentSize;
    
    if (this.currentTool === 'pen') {
      this.ctx.beginPath();
      this.ctx.moveTo(x, y);
    }
    
    if (this.onDrawStart) {
      this.onDrawStart({
        tool: this.currentTool,
        color: this.currentColor,
        size: this.currentSize,
        x: x,
        y: y
      });
    }
  }

  continuDrawing(x, y) {
    if (!this.isDrawing) return;
    
    switch (this.currentTool) {
      case 'pen':
        this.drawLine(this.lastX, this.lastY, x, y);
        break;
      case 'eraser':
        this.erase(x, y);
        break;
      case 'line':
      case 'rectangle':
      case 'circle':
        this.drawShapePreview(x, y);
        break;
    }
    
    if (this.onDrawing) {
      this.onDrawing({
        tool: this.currentTool,
        color: this.currentColor,
        size: this.currentSize,
        x: x,
        y: y,
        lastX: this.lastX,
        lastY: this.lastY
      });
    }
    
    this.lastX = x;
    this.lastY = y;
  }

  stopDrawing(x, y) {
    if (!this.isDrawing) return;
    
    this.isDrawing = false;
    
    // Finalize shape drawing
    if (['line', 'rectangle', 'circle'].includes(this.currentTool)) {
      this.drawShapeFinal(x, y);
    }
    
    // Save state for undo/redo
    this.saveState();
    
    if (this.onDrawEnd) {
      this.onDrawEnd({
        tool: this.currentTool,
        color: this.currentColor,
        size: this.currentSize,
        startX: this.startX,
        startY: this.startY,
        endX: x,
        endY: y
      });
    }
  }

  drawLine(x1, y1, x2, y2) {
    this.ctx.beginPath();
    this.ctx.moveTo(x1, y1);
    this.ctx.lineTo(x2, y2);
    this.ctx.stroke();
  }

  erase(x, y) {
    this.ctx.save();
    this.ctx.globalCompositeOperation = 'destination-out';
    this.ctx.beginPath();
    this.ctx.arc(x, y, this.currentSize, 0, 2 * Math.PI);
    this.ctx.fill();
    this.ctx.restore();
  }

  drawShapePreview(x, y) {
    // Clear temporary canvas
    this.tempCtx.clearRect(0, 0, this.tempCanvas.width, this.tempCanvas.height);
    
    // Copy main canvas to temp canvas
    this.tempCtx.drawImage(this.canvas, 0, 0);
    
    // Draw shape preview on temp canvas
    this.tempCtx.strokeStyle = this.currentColor;
    this.tempCtx.lineWidth = this.currentSize;
    
    switch (this.currentTool) {
      case 'line':
        this.tempCtx.beginPath();
        this.tempCtx.moveTo(this.startX, this.startY);
        this.tempCtx.lineTo(x, y);
        this.tempCtx.stroke();
        break;
      case 'rectangle':
        const width = x - this.startX;
        const height = y - this.startY;
        this.tempCtx.strokeRect(this.startX, this.startY, width, height);
        break;
      case 'circle':
        const radius = Math.sqrt(Math.pow(x - this.startX, 2) + Math.pow(y - this.startY, 2));
        this.tempCtx.beginPath();
        this.tempCtx.arc(this.startX, this.startY, radius, 0, 2 * Math.PI);
        this.tempCtx.stroke();
        break;
    }
    
    // Copy temp canvas back to main canvas
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    this.ctx.drawImage(this.tempCanvas, 0, 0);
  }

  drawShapeFinal(x, y) {
    // Restore canvas without preview
    if (this.history.length > 0) {
      const img = new Image();
      img.onload = () => {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        this.ctx.drawImage(img, 0, 0);
        
        // Draw final shape
        this.ctx.strokeStyle = this.currentColor;
        this.ctx.lineWidth = this.currentSize;
        
        switch (this.currentTool) {
          case 'line':
            this.ctx.beginPath();
            this.ctx.moveTo(this.startX, this.startY);
            this.ctx.lineTo(x, y);
            this.ctx.stroke();
            break;
          case 'rectangle':
            const width = x - this.startX;
            const height = y - this.startY;
            this.ctx.strokeRect(this.startX, this.startY, width, height);
            break;
          case 'circle':
            const radius = Math.sqrt(Math.pow(x - this.startX, 2) + Math.pow(y - this.startY, 2));
            this.ctx.beginPath();
            this.ctx.arc(this.startX, this.startY, radius, 0, 2 * Math.PI);
            this.ctx.stroke();
            break;
        }
      };
      img.src = this.history[this.historyStep];
    }
  }

  // Drawing methods for remote operations
  drawRemoteLine(data) {
    this.ctx.save();
    this.ctx.strokeStyle = data.color;
    this.ctx.lineWidth = data.size;
    this.ctx.lineCap = 'round';
    this.ctx.lineJoin = 'round';
    
    this.ctx.beginPath();
    this.ctx.moveTo(data.lastX, data.lastY);
    this.ctx.lineTo(data.x, data.y);
    this.ctx.stroke();
    
    this.ctx.restore();
  }

  drawRemoteShape(data) {
    this.ctx.save();
    this.ctx.strokeStyle = data.color;
    this.ctx.lineWidth = data.size;
    
    switch (data.tool) {
      case 'line':
        this.ctx.beginPath();
        this.ctx.moveTo(data.startX, data.startY);
        this.ctx.lineTo(data.endX, data.endY);
        this.ctx.stroke();
        break;
      case 'rectangle':
        const width = data.endX - data.startX;
        const height = data.endY - data.startY;
        this.ctx.strokeRect(data.startX, data.startY, width, height);
        break;
      case 'circle':
        const radius = Math.sqrt(Math.pow(data.endX - data.startX, 2) + Math.pow(data.endY - data.startY, 2));
        this.ctx.beginPath();
        this.ctx.arc(data.startX, data.startY, radius, 0, 2 * Math.PI);
        this.ctx.stroke();
        break;
    }
    
    this.ctx.restore();
  }

  addText(text, x, y, fontSize = 24, bold = false, italic = false) {
    this.ctx.save();
    this.ctx.fillStyle = this.currentColor;
    
    let font = '';
    if (italic) font += 'italic ';
    if (bold) font += 'bold ';
    font += `${fontSize}px Arial`;
    
    this.ctx.font = font;
    this.ctx.fillText(text, x, y);
    this.ctx.restore();
    
    this.saveState();
  }

  // History management
  saveState() {
    this.historyStep++;
    
    // Remove any future history if we're not at the end
    if (this.historyStep < this.history.length) {
      this.history.length = this.historyStep;
    }
    
    // Add current state
    this.history.push(this.canvas.toDataURL());
    
    // Limit history size
    if (this.history.length > this.maxHistorySteps) {
      this.history.shift();
      this.historyStep--;
    }
  }

  undo() {
    if (this.historyStep > 0) {
      this.historyStep--;
      this.restoreState();
      return true;
    }
    return false;
  }

  redo() {
    if (this.historyStep < this.history.length - 1) {
      this.historyStep++;
      this.restoreState();
      return true;
    }
    return false;
  }

  restoreState() {
    const img = new Image();
    img.onload = () => {
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
      this.ctx.drawImage(img, 0, 0);
    };
    img.src = this.history[this.historyStep];
  }

  clear() {
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    this.saveState();
  }

  // Tool and property setters
  setTool(tool) {
    this.currentTool = tool;
  }

  setColor(color) {
    this.currentColor = color;
  }

  setSize(size) {
    this.currentSize = size;
  }

  // Export functionality
  exportAsImage() {
    const link = document.createElement('a');
    link.download = `canvas-${Date.now()}.png`;
    link.href = this.canvas.toDataURL();
    link.click();
  }

  getCanvasData() {
    return this.canvas.toDataURL();
  }

  loadCanvasData(dataURL) {
    const img = new Image();
    img.onload = () => {
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
      this.ctx.drawImage(img, 0, 0);
      this.saveState();
    };
    img.src = dataURL;
  }
}

export default CanvasManager;
