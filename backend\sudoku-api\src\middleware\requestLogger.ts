/**
 * Request logging middleware
 * Comprehensive request/response logging with performance metrics
 */

import { Request, Response, NextFunction } from 'express';
import { log } from '@/utils/logger';

/**
 * Request logging middleware
 */
export const requestLogger = (req: Request, res: Response, next: NextFunction): void => {
  const startTime = Date.now();
  const startHrTime = process.hrtime();

  // Log incoming request
  log.http('Incoming request', {
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    contentType: req.get('Content-Type'),
    contentLength: req.get('Content-Length'),
    requestId: req.id,
    timestamp: new Date().toISOString(),
  });

  // Override res.end to capture response details
  const originalEnd = res.end;
  const originalWrite = res.write;
  const originalSend = res.send;

  let responseBody = '';
  let responseSize = 0;

  // Capture response body for logging (only for small responses)
  res.write = function (chunk: any, ...args: any[]) {
    if (chunk) {
      responseSize += Buffer.byteLength(chunk);
      if (responseSize < 1024 && typeof chunk === 'string') {
        responseBody += chunk;
      }
    }
    return originalWrite.call(this, chunk, ...args);
  };

  res.send = function (body: any) {
    if (body && responseSize < 1024) {
      responseBody = typeof body === 'string' ? body : JSON.stringify(body);
      responseSize = Buffer.byteLength(responseBody);
    }
    return originalSend.call(this, body);
  };

  res.end = function (chunk?: any, ...args: any[]) {
    const endTime = Date.now();
    const diff = process.hrtime(startHrTime);
    const responseTime = endTime - startTime;
    const responseTimeHr = diff[0] * 1000 + diff[1] * 1e-6; // Convert to milliseconds

    // Capture final chunk
    if (chunk) {
      responseSize += Buffer.byteLength(chunk);
      if (responseSize < 1024 && typeof chunk === 'string') {
        responseBody += chunk;
      }
    }

    // Determine log level based on status code
    const statusCode = res.statusCode;
    let logLevel: 'info' | 'warn' | 'error' = 'info';

    if (statusCode >= 400 && statusCode < 500) {
      logLevel = 'warn';
    } else if (statusCode >= 500) {
      logLevel = 'error';
    }

    // Log response
    const logData = {
      method: req.method,
      url: req.url,
      statusCode,
      responseTime,
      responseTimeHr: Math.round(responseTimeHr * 100) / 100,
      responseSize,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      requestId: req.id,
      timestamp: new Date().toISOString(),
    };

    // Add response body for errors or in development
    if (process.env.NODE_ENV === 'development' || statusCode >= 400) {
      if (responseBody && responseSize < 1024) {
        try {
          (logData as any).responseBody = JSON.parse(responseBody);
        } catch {
          (logData as any).responseBody = responseBody;
        }
      }
    }

    // Add request body for POST/PUT requests in development
    if (process.env.NODE_ENV === 'development' && ['POST', 'PUT', 'PATCH'].includes(req.method)) {
      if (req.body && Object.keys(req.body).length > 0) {
        (logData as any).requestBody = req.body;
      }
    }

    // Log based on level
    switch (logLevel) {
      case 'error':
        log.error('Request completed with error', undefined, logData);
        break;
      case 'warn':
        log.warn('Request completed with warning', logData);
        break;
      default:
        log.http('Request completed', logData);
    }

    // Log slow requests
    if (responseTime > 1000) {
      log.warn('Slow request detected', {
        ...logData,
        slowRequest: true,
      });
    }

    // Call original end
    return originalEnd.call(this, chunk, ...args);
  };

  next();
};

/**
 * Skip logging for certain paths
 */
export const skipLogging = (paths: string[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (paths.some(path => req.path.startsWith(path))) {
      return next();
    }
    return requestLogger(req, res, next);
  };
};

/**
 * Enhanced request logger with custom options
 */
export interface RequestLoggerOptions {
  skipPaths?: string[];
  logRequestBody?: boolean;
  logResponseBody?: boolean;
  maxBodySize?: number;
  logSlowRequests?: boolean;
  slowRequestThreshold?: number;
}

export const createRequestLogger = (options: RequestLoggerOptions = {}) => {
  const {
    skipPaths = [],
    logRequestBody = false,
    logResponseBody = false,
    maxBodySize = 1024,
    logSlowRequests = true,
    slowRequestThreshold = 1000,
  } = options;

  return (req: Request, res: Response, next: NextFunction): void => {
    // Skip logging for specified paths
    if (skipPaths.some(path => req.path.startsWith(path))) {
      return next();
    }

    const startTime = Date.now();
    const startHrTime = process.hrtime();

    // Log incoming request
    const requestLogData: any = {
      method: req.method,
      url: req.url,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      contentType: req.get('Content-Type'),
      contentLength: req.get('Content-Length'),
      requestId: req.id,
      timestamp: new Date().toISOString(),
    };

    // Add request body if enabled
    if (logRequestBody && req.body && Object.keys(req.body).length > 0) {
      const bodyStr = JSON.stringify(req.body);
      if (bodyStr.length <= maxBodySize) {
        requestLogData.requestBody = req.body;
      } else {
        requestLogData.requestBodyTruncated = true;
        requestLogData.requestBodySize = bodyStr.length;
      }
    }

    log.http('Incoming request', requestLogData);

    // Override response methods to capture response data
    const originalEnd = res.end;
    let responseBody = '';
    let responseSize = 0;

    if (logResponseBody) {
      const originalSend = res.send;
      res.send = function (body: any) {
        if (body) {
          const bodyStr = typeof body === 'string' ? body : JSON.stringify(body);
          responseSize = Buffer.byteLength(bodyStr);
          if (responseSize <= maxBodySize) {
            responseBody = bodyStr;
          }
        }
        return originalSend.call(this, body);
      };
    }

    res.end = function (chunk?: any, ...args: any[]) {
      const endTime = Date.now();
      const diff = process.hrtime(startHrTime);
      const responseTime = endTime - startTime;
      const responseTimeHr = diff[0] * 1000 + diff[1] * 1e-6;

      // Determine log level
      const statusCode = res.statusCode;
      let logLevel: 'info' | 'warn' | 'error' = 'info';

      if (statusCode >= 400 && statusCode < 500) {
        logLevel = 'warn';
      } else if (statusCode >= 500) {
        logLevel = 'error';
      }

      // Prepare response log data
      const responseLogData: any = {
        method: req.method,
        url: req.url,
        statusCode,
        responseTime,
        responseTimeHr: Math.round(responseTimeHr * 100) / 100,
        ip: req.ip,
        requestId: req.id,
        timestamp: new Date().toISOString(),
      };

      // Add response body if enabled and available
      if (logResponseBody && responseBody) {
        try {
          responseLogData.responseBody = JSON.parse(responseBody);
        } catch {
          responseLogData.responseBody = responseBody;
        }
        responseLogData.responseSize = responseSize;
      }

      // Log response
      switch (logLevel) {
        case 'error':
          log.error('Request completed with error', undefined, responseLogData);
          break;
        case 'warn':
          log.warn('Request completed with warning', responseLogData);
          break;
        default:
          log.http('Request completed', responseLogData);
      }

      // Log slow requests if enabled
      if (logSlowRequests && responseTime > slowRequestThreshold) {
        log.warn('Slow request detected', {
          ...responseLogData,
          slowRequest: true,
          threshold: slowRequestThreshold,
        });
      }

      return originalEnd.call(this, chunk, ...args);
    };

    next();
  };
};
