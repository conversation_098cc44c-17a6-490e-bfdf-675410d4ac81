/**
 * Enterprise Collaborative Workspace API Server
 * Production-ready server with real-time collaboration, whiteboard, task management, and advanced team features
 */

import express, { Application, Request, Response } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import { config } from 'dotenv';
import multer from 'multer';
import path from 'path';

// Import middleware
import { requestLogger } from '@/middleware/requestLogger';
import { errorHandler } from '@/middleware/errorHandler';
import { MetricsCollector } from '@/middleware/metrics';
import { authMiddleware } from '@/middleware/auth';

// Import services
import { DatabaseService } from '@/services/DatabaseService';
import { CacheService } from '@/services/CacheService';
import { WorkspaceService } from '@/services/WorkspaceService';
import { RoomService } from '@/services/RoomService';
import { ChatService } from '@/services/ChatService';
import { WhiteboardService } from '@/services/WhiteboardService';
import { TaskService } from '@/services/TaskService';
import { FileService } from '@/services/FileService';
import { UserService } from '@/services/UserService';
import { RealTimeService } from '@/services/RealTimeService';
import { NotificationService } from '@/services/NotificationService';
import { AnalyticsService } from '@/services/AnalyticsService';

// Import routes
import authRoutes from '@/routes/auth';
import workspaceRoutes from '@/routes/workspace';
import roomRoutes from '@/routes/room';
import chatRoutes from '@/routes/chat';
import whiteboardRoutes from '@/routes/whiteboard';
import taskRoutes from '@/routes/task';
import fileRoutes from '@/routes/file';
import userRoutes from '@/routes/user';
import analyticsRoutes from '@/routes/analytics';

// Import utilities
import { logger } from '@/utils/logger';

// Load environment variables
config();

/**
 * Enterprise Collaborative Workspace Server
 */
export class CollaborativeWorkspaceServer {
  private app: Application;
  private server: any;
  private io: SocketIOServer;
  private port: number;
  private isShuttingDown: boolean = false;

  // Services
  private databaseService: DatabaseService;
  private cacheService: CacheService;
  private workspaceService: WorkspaceService;
  private roomService: RoomService;
  private chatService: ChatService;
  private whiteboardService: WhiteboardService;
  private taskService: TaskService;
  private fileService: FileService;
  private userService: UserService;
  private realTimeService: RealTimeService;
  private notificationService: NotificationService;
  private analyticsService: AnalyticsService;

  constructor() {
    this.app = express();
    this.port = parseInt(process.env.PORT || '3006', 10);
    this.server = createServer(this.app);
    this.io = new SocketIOServer(this.server, {
      cors: {
        origin: process.env.CORS_ORIGIN || "*",
        methods: ["GET", "POST", "PUT", "DELETE"],
        credentials: true
      },
      maxHttpBufferSize: 1e8, // 100MB for file uploads
      pingTimeout: 60000,
      pingInterval: 25000
    });

    // Initialize services
    this.initializeServices();
  }

  /**
   * Initialize all services
   */
  private async initializeServices(): Promise<void> {
    try {
      // Initialize database service
      this.databaseService = new DatabaseService({
        type: 'sqlite',
        database: process.env.DB_PATH || './data/workspace.db'
      });
      await this.databaseService.initialize();

      // Initialize cache service
      this.cacheService = new CacheService({
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379', 10),
        password: process.env.REDIS_PASSWORD
      });
      await this.cacheService.initialize();

      // Initialize core services
      this.userService = new UserService(this.databaseService, this.cacheService);
      this.workspaceService = new WorkspaceService(this.databaseService, this.cacheService);
      this.roomService = new RoomService(this.databaseService, this.cacheService, this.workspaceService);
      this.chatService = new ChatService(this.databaseService, this.cacheService);
      this.whiteboardService = new WhiteboardService(this.databaseService, this.cacheService);
      this.taskService = new TaskService(this.databaseService, this.cacheService);
      this.fileService = new FileService(this.databaseService, this.cacheService);
      this.notificationService = new NotificationService(this.databaseService, this.cacheService);
      this.analyticsService = new AnalyticsService(this.databaseService, this.cacheService);

      // Initialize real-time service
      this.realTimeService = new RealTimeService(
        this.io,
        this.roomService,
        this.chatService,
        this.whiteboardService,
        this.taskService,
        this.userService
      );
      this.realTimeService.initialize();

      logger.info('All services initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize services:', error);
      throw error;
    }
  }

  /**
   * Configure middleware
   */
  private configureMiddleware(): void {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'", "https:"],
          scriptSrc: ["'self'", "'unsafe-inline'"],
          imgSrc: ["'self'", "data:", "https:", "blob:"],
          connectSrc: ["'self'", "ws:", "wss:", "https:"],
          fontSrc: ["'self'", "https:", "data:"],
          mediaSrc: ["'self'", "blob:", "https:"],
          frameSrc: ["'self'", "https:"],
        },
      },
      crossOriginEmbedderPolicy: false
    }));

    // CORS configuration
    this.app.use(cors({
      origin: process.env.CORS_ORIGIN || "*",
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-File-Name']
    }));

    // Compression
    this.app.use(compression());

    // Rate limiting
    const limiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: process.env.NODE_ENV === 'production' ? 200 : 1000, // requests per window
      message: {
        error: 'Too many requests from this IP, please try again later.',
        retryAfter: '15 minutes'
      },
      standardHeaders: true,
      legacyHeaders: false,
    });
    this.app.use(limiter);

    // Body parsing
    this.app.use(express.json({ limit: '50mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '50mb' }));

    // Request logging
    this.app.use(requestLogger);

    // Metrics collection
    this.app.use(MetricsCollector.middleware);

    // File upload configuration
    const upload = multer({
      dest: process.env.UPLOAD_DIR || './uploads',
      limits: {
        fileSize: 100 * 1024 * 1024, // 100MB
        files: 10
      },
      fileFilter: (req, file, cb) => {
        // Allow all file types for now, but validate in service layer
        cb(null, true);
      }
    });
    this.app.use('/api/files', upload.array('files', 10));

    // Static files
    this.app.use('/uploads', express.static(process.env.UPLOAD_DIR || './uploads'));
    this.app.use(express.static('public'));
  }

  /**
   * Configure API routes
   */
  private configureRoutes(): void {
    // Public routes (no authentication required)
    this.app.use('/api/auth', authRoutes);

    // Protected routes (authentication required)
    this.app.use('/api/workspace', authMiddleware, workspaceRoutes);
    this.app.use('/api/room', authMiddleware, roomRoutes);
    this.app.use('/api/chat', authMiddleware, chatRoutes);
    this.app.use('/api/whiteboard', authMiddleware, whiteboardRoutes);
    this.app.use('/api/task', authMiddleware, taskRoutes);
    this.app.use('/api/file', authMiddleware, fileRoutes);
    this.app.use('/api/user', authMiddleware, userRoutes);
    this.app.use('/api/analytics', authMiddleware, analyticsRoutes);

    // Health check endpoint
    this.app.get('/health', (req: Request, res: Response) => {
      const healthStatus = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.npm_package_version || '2.0.0',
        environment: process.env.NODE_ENV || 'development',
        services: {
          database: this.databaseService.isHealthy(),
          cache: this.cacheService.isHealthy(),
          realtime: this.realTimeService.getActiveConnectionsCount(),
          storage: this.fileService.getStorageInfo()
        },
        memory: process.memoryUsage(),
        cpu: process.cpuUsage()
      };

      res.json(healthStatus);
    });

    // Metrics endpoint
    this.app.get('/metrics', MetricsCollector.metricsEndpoint);

    // API documentation
    if (process.env.NODE_ENV !== 'production') {
      const swaggerOptions = {
        definition: {
          openapi: '3.0.0',
          info: {
            title: 'Collaborative Workspace API',
            version: '2.0.0',
            description: 'Enterprise-grade collaborative workspace with real-time features',
          },
          servers: [
            {
              url: `http://localhost:${this.port}`,
              description: 'Development server',
            },
          ],
          components: {
            securitySchemes: {
              bearerAuth: {
                type: 'http',
                scheme: 'bearer',
                bearerFormat: 'JWT',
              },
            },
          },
          security: [
            {
              bearerAuth: [],
            },
          ],
        },
        apis: ['./src/routes/*.ts'],
      };

      const specs = swaggerJsdoc(swaggerOptions);
      this.app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs));
    }

    // 404 handler
    this.app.use('*', (req: Request, res: Response) => {
      res.status(404).json({
        success: false,
        error: 'Endpoint not found',
        path: req.originalUrl,
        method: req.method,
        timestamp: new Date().toISOString()
      });
    });

    // Global error handler
    this.app.use(errorHandler);
  }

  /**
   * Start the server
   */
  public async start(): Promise<void> {
    try {
      // Configure middleware and routes
      this.configureMiddleware();
      this.configureRoutes();

      // Start server
      this.server.listen(this.port, () => {
        logger.info(`🚀 Collaborative Workspace API server started on port ${this.port}`);
        logger.info(`📚 API Documentation: http://localhost:${this.port}/api-docs`);
        logger.info(`🏥 Health Check: http://localhost:${this.port}/health`);
        logger.info(`📊 Metrics: http://localhost:${this.port}/metrics`);
        logger.info(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
        logger.info(`💾 Database: ${process.env.DB_PATH || './data/workspace.db'}`);
        logger.info(`📁 Upload Directory: ${process.env.UPLOAD_DIR || './uploads'}`);
      });

      // Setup graceful shutdown
      this.setupGracefulShutdown();

    } catch (error) {
      logger.error('Failed to start server:', error);
      process.exit(1);
    }
  }

  /**
   * Setup graceful shutdown
   */
  private setupGracefulShutdown(): void {
    const shutdown = async (signal: string) => {
      if (this.isShuttingDown) return;
      this.isShuttingDown = true;

      logger.info(`Received ${signal}. Starting graceful shutdown...`);

      // Stop accepting new connections
      this.server.close(async () => {
        logger.info('HTTP server closed');

        try {
          // Close real-time connections
          this.io.close();
          logger.info('WebSocket server closed');

          // Close database connections
          await this.databaseService.close();
          logger.info('Database connections closed');

          // Close cache connections
          await this.cacheService.close();
          logger.info('Cache connections closed');

          logger.info('Graceful shutdown completed');
          process.exit(0);
        } catch (error) {
          logger.error('Error during shutdown:', error);
          process.exit(1);
        }
      });

      // Force shutdown after 30 seconds
      setTimeout(() => {
        logger.error('Forced shutdown after timeout');
        process.exit(1);
      }, 30000);
    };

    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGUSR2', () => shutdown('SIGUSR2')); // nodemon restart

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception:', error);
      shutdown('uncaughtException');
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
      shutdown('unhandledRejection');
    });
  }

  /**
   * Get server instance
   */
  public getApp(): Application {
    return this.app;
  }

  /**
   * Get Socket.IO instance
   */
  public getIO(): SocketIOServer {
    return this.io;
  }
}

// Start server if this file is run directly
if (require.main === module) {
  const server = new CollaborativeWorkspaceServer();
  server.start().catch((error) => {
    logger.error('Failed to start server:', error);
    process.exit(1);
  });
}

export default CollaborativeWorkspaceServer;
