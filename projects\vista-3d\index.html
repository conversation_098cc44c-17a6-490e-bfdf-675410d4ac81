<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Vista3D - 3D Product Configurator</title>
  <meta name="description" content="Interactive 3D product configurator with three.js - part of Portfolio Eight">
  <meta name="keywords" content="3d, three.js, product configurator, webgl, gltf">
  <meta name="author" content="Portfolio Eight">
  
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://yourusername.github.io/portfolio-eight/projects/vista-3d/">
  <meta property="og:title" content="Vista3D - 3D Product Configurator">
  <meta property="og:description" content="Interactive 3D product configurator with three.js">
  <meta property="og:image" content="https://yourusername.github.io/portfolio-eight/projects/vista-3d/assets/og-image.png">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:url" content="https://yourusername.github.io/portfolio-eight/projects/vista-3d/">
  <meta property="twitter:title" content="Vista3D - 3D Product Configurator">
  <meta property="twitter:description" content="Interactive 3D product configurator with three.js">
  <meta property="twitter:image" content="https://yourusername.github.io/portfolio-eight/projects/vista-3d/assets/og-image.png">

  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="./assets/favicon.ico">
  <link rel="apple-touch-icon" sizes="180x180" href="./assets/apple-touch-icon.png">
  <link rel="icon" type="image/png" sizes="32x32" href="./assets/favicon-32x32.png">
  <link rel="icon" type="image/png" sizes="16x16" href="./assets/favicon-16x16.png">

  <!-- Styles -->
  <link rel="stylesheet" href="styles.css">
  
  <!-- Three.js -->
  <script src="https://unpkg.com/three@0.158.0/build/three.min.js"></script>
  <script src="https://unpkg.com/three@0.158.0/examples/js/controls/OrbitControls.js"></script>
  <script src="https://unpkg.com/three@0.158.0/examples/js/loaders/GLTFLoader.js"></script>
  <script src="https://unpkg.com/three@0.158.0/examples/js/loaders/DRACOLoader.js"></script>
  
  <!-- Preconnect for performance -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
  <!-- Loading Screen -->
  <div id="loadingScreen" class="loading-screen">
    <div class="loading-content">
      <div class="loading-logo">
        <span class="logo-icon">🎯</span>
        <span class="logo-text">Vista3D</span>
      </div>
      <div class="loading-spinner">
        <div class="spinner"></div>
      </div>
      <div class="loading-text" id="loadingText">Initializing 3D Engine...</div>
      <div class="loading-progress">
        <div class="progress-bar">
          <div class="progress-fill" id="progressFill"></div>
        </div>
        <div class="progress-text" id="progressText">0%</div>
      </div>
    </div>
  </div>

  <!-- Header -->
  <header class="header">
    <nav class="navbar">
      <div class="nav-container">
        <!-- Logo -->
        <div class="nav-brand">
          <a href="#" class="brand-link">
            <span class="brand-icon">🎯</span>
            <span class="brand-text">Vista3D</span>
          </a>
        </div>

        <!-- Navigation Menu -->
        <ul class="nav-menu" id="navMenu">
          <li class="nav-item">
            <a href="#configurator" class="nav-link active">Configurator</a>
          </li>
          <li class="nav-item">
            <a href="#gallery" class="nav-link">Gallery</a>
          </li>
          <li class="nav-item">
            <a href="#about" class="nav-link">About</a>
          </li>
          <li class="nav-item">
            <a href="#help" class="nav-link">Help</a>
          </li>
        </ul>

        <!-- Actions -->
        <div class="nav-actions">
          <button class="theme-toggle" id="themeToggle" aria-label="Toggle theme">
            <span class="theme-icon">🌙</span>
          </button>
          
          <button class="fullscreen-btn" id="fullscreenBtn" aria-label="Toggle fullscreen">
            <span class="fullscreen-icon">⛶</span>
          </button>
          
          <button class="nav-toggle" id="navToggle" aria-label="Toggle navigation">
            <span class="hamburger"></span>
          </button>
        </div>
      </div>
    </nav>
  </header>

  <!-- Main Content -->
  <main class="main">
    <!-- 3D Configurator Section -->
    <section id="configurator" class="configurator">
      <div class="configurator-container">
        <!-- 3D Viewport -->
        <div class="viewport-container">
          <canvas id="viewport" class="viewport"></canvas>
          
          <!-- Viewport Controls -->
          <div class="viewport-controls">
            <button class="control-btn" id="resetCameraBtn" title="Reset Camera">
              <span>🎯</span>
            </button>
            <button class="control-btn" id="wireframeBtn" title="Toggle Wireframe">
              <span>🔲</span>
            </button>
            <button class="control-btn" id="screenshotBtn" title="Take Screenshot">
              <span>📷</span>
            </button>
            <button class="control-btn" id="exportBtn" title="Export PNG">
              <span>💾</span>
            </button>
          </div>

          <!-- Performance Stats -->
          <div class="performance-stats" id="performanceStats">
            <div class="stat-item">
              <span class="stat-label">FPS:</span>
              <span class="stat-value" id="fpsCounter">60</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Triangles:</span>
              <span class="stat-value" id="triangleCount">0</span>
            </div>
          </div>

          <!-- Loading Progress for Models -->
          <div class="model-loading" id="modelLoading">
            <div class="loading-spinner">
              <div class="spinner"></div>
            </div>
            <div class="loading-text">Loading 3D Model...</div>
          </div>
        </div>

        <!-- Configuration Panel -->
        <div class="config-panel">
          <div class="panel-header">
            <h2 class="panel-title">Product Configurator</h2>
            <button class="panel-toggle" id="panelToggle" aria-label="Toggle panel">
              <span>⚙️</span>
            </button>
          </div>

          <div class="panel-content" id="panelContent">
            <!-- Model Selection -->
            <div class="config-section">
              <h3 class="section-title">Select Product</h3>
              <div class="model-selector">
                <button class="model-btn active" data-model="chair">
                  <div class="model-thumb">🪑</div>
                  <span>Office Chair</span>
                </button>
                <button class="model-btn" data-model="lamp">
                  <div class="model-thumb">💡</div>
                  <span>Desk Lamp</span>
                </button>
                <button class="model-btn" data-model="table">
                  <div class="model-thumb">🪑</div>
                  <span>Coffee Table</span>
                </button>
              </div>
            </div>

            <!-- Material Configuration -->
            <div class="config-section">
              <h3 class="section-title">Materials</h3>
              <div class="material-groups" id="materialGroups">
                <!-- Material groups will be populated dynamically -->
              </div>
            </div>

            <!-- Color Configuration -->
            <div class="config-section">
              <h3 class="section-title">Colors</h3>
              <div class="color-palette" id="colorPalette">
                <button class="color-btn active" data-color="#8B4513" style="background-color: #8B4513;" title="Brown"></button>
                <button class="color-btn" data-color="#2F4F4F" style="background-color: #2F4F4F;" title="Dark Slate Gray"></button>
                <button class="color-btn" data-color="#800080" style="background-color: #800080;" title="Purple"></button>
                <button class="color-btn" data-color="#FF6347" style="background-color: #FF6347;" title="Tomato"></button>
                <button class="color-btn" data-color="#4682B4" style="background-color: #4682B4;" title="Steel Blue"></button>
                <button class="color-btn" data-color="#32CD32" style="background-color: #32CD32;" title="Lime Green"></button>
                <button class="color-btn" data-color="#FFD700" style="background-color: #FFD700;" title="Gold"></button>
                <button class="color-btn" data-color="#DC143C" style="background-color: #DC143C;" title="Crimson"></button>
              </div>
            </div>

            <!-- Environment Settings -->
            <div class="config-section">
              <h3 class="section-title">Environment</h3>
              <div class="environment-controls">
                <div class="control-group">
                  <label for="environmentSelect">Background:</label>
                  <select id="environmentSelect" class="control-select">
                    <option value="studio">Studio</option>
                    <option value="outdoor">Outdoor</option>
                    <option value="indoor">Indoor</option>
                    <option value="neutral">Neutral</option>
                  </select>
                </div>
                
                <div class="control-group">
                  <label for="lightingIntensity">Lighting:</label>
                  <input type="range" id="lightingIntensity" class="control-slider" 
                         min="0.1" max="2" step="0.1" value="1">
                  <span class="slider-value" id="lightingValue">1.0</span>
                </div>
              </div>
            </div>

            <!-- Animation Controls -->
            <div class="config-section">
              <h3 class="section-title">Animation</h3>
              <div class="animation-controls">
                <button class="control-btn" id="playAnimationBtn">
                  <span>▶️</span> Play Animation
                </button>
                <button class="control-btn" id="autoRotateBtn">
                  <span>🔄</span> Auto Rotate
                </button>
              </div>
            </div>

            <!-- Export Options -->
            <div class="config-section">
              <h3 class="section-title">Export</h3>
              <div class="export-controls">
                <div class="control-group">
                  <label for="exportResolution">Resolution:</label>
                  <select id="exportResolution" class="control-select">
                    <option value="1920x1080">1920x1080 (HD)</option>
                    <option value="2560x1440">2560x1440 (2K)</option>
                    <option value="3840x2160">3840x2160 (4K)</option>
                    <option value="custom">Custom</option>
                  </select>
                </div>
                
                <div class="control-group" id="customResolution" style="display: none;">
                  <label>Custom Size:</label>
                  <div class="resolution-inputs">
                    <input type="number" id="customWidth" placeholder="Width" min="100" max="7680">
                    <span>×</span>
                    <input type="number" id="customHeight" placeholder="Height" min="100" max="4320">
                  </div>
                </div>
                
                <button class="btn btn-primary" id="exportPngBtn">
                  <span>📸</span> Export PNG
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Gallery Section -->
    <section id="gallery" class="gallery">
      <div class="gallery-container">
        <h2 class="section-title">3D Model Gallery</h2>
        <p class="section-description">
          Explore our collection of high-quality 3D models optimized for web viewing.
        </p>
        
        <div class="gallery-grid" id="galleryGrid">
          <!-- Gallery items will be populated dynamically -->
        </div>
      </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about">
      <div class="about-container">
        <div class="about-content">
          <h2 class="section-title">About Vista3D</h2>
          <p class="about-text">
            Vista3D is a cutting-edge 3D product configurator built with three.js and WebGL. 
            It enables real-time visualization and customization of 3D models directly in the browser, 
            providing an immersive experience for product exploration and configuration.
          </p>
          
          <div class="features-grid">
            <div class="feature-item">
              <div class="feature-icon">🎨</div>
              <h3>Real-time Rendering</h3>
              <p>High-performance WebGL rendering with smooth 60fps animations</p>
            </div>
            
            <div class="feature-item">
              <div class="feature-icon">🔧</div>
              <h3>Material Editor</h3>
              <p>Advanced material system with PBR textures and real-time updates</p>
            </div>
            
            <div class="feature-item">
              <div class="feature-icon">📱</div>
              <h3>Responsive Design</h3>
              <p>Optimized for desktop, tablet, and mobile devices</p>
            </div>
            
            <div class="feature-item">
              <div class="feature-icon">💾</div>
              <h3>Export Functionality</h3>
              <p>High-resolution PNG export with customizable dimensions</p>
            </div>
          </div>
        </div>
        
        <div class="tech-stack">
          <h3>Technology Stack</h3>
          <div class="tech-items">
            <span class="tech-item">Three.js</span>
            <span class="tech-item">WebGL</span>
            <span class="tech-item">GLTF 2.0</span>
            <span class="tech-item">Draco Compression</span>
            <span class="tech-item">PBR Materials</span>
            <span class="tech-item">ES2022</span>
          </div>
        </div>
      </div>
    </section>

    <!-- Help Section -->
    <section id="help" class="help">
      <div class="help-container">
        <h2 class="section-title">How to Use</h2>
        
        <div class="help-content">
          <div class="help-section">
            <h3>Navigation Controls</h3>
            <ul>
              <li><strong>Mouse:</strong> Left click + drag to rotate, right click + drag to pan, scroll to zoom</li>
              <li><strong>Touch:</strong> Single finger to rotate, two fingers to zoom and pan</li>
              <li><strong>Keyboard:</strong> Arrow keys to rotate, +/- to zoom</li>
            </ul>
          </div>
          
          <div class="help-section">
            <h3>Configuration</h3>
            <ul>
              <li>Select different 3D models from the product selector</li>
              <li>Change materials and colors using the configuration panel</li>
              <li>Adjust lighting and environment settings for optimal viewing</li>
              <li>Enable auto-rotation for automatic model presentation</li>
            </ul>
          </div>
          
          <div class="help-section">
            <h3>Export Options</h3>
            <ul>
              <li>Take screenshots directly from the viewport</li>
              <li>Export high-resolution PNG images up to 4K</li>
              <li>Choose from preset resolutions or set custom dimensions</li>
              <li>All exports maintain the current configuration and camera angle</li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  </main>

  <!-- Footer -->
  <footer class="footer">
    <div class="footer-container">
      <div class="footer-content">
        <div class="footer-section">
          <h4>Vista3D</h4>
          <p>Advanced 3D product configurator for the modern web.</p>
        </div>
        
        <div class="footer-section">
          <h4>Features</h4>
          <ul>
            <li>Real-time 3D Rendering</li>
            <li>Material Customization</li>
            <li>High-res Export</li>
            <li>Mobile Optimized</li>
          </ul>
        </div>
        
        <div class="footer-section">
          <h4>Technology</h4>
          <ul>
            <li>Three.js & WebGL</li>
            <li>GLTF 2.0 Models</li>
            <li>PBR Materials</li>
            <li>Draco Compression</li>
          </ul>
        </div>
      </div>
      
      <div class="footer-bottom">
        <p>&copy; 2025 Vista3D Demo. Part of Portfolio Eight.</p>
        <p>
          <a href="../../">← Back to Portfolio</a> |
          <a href="https://github.com/yourusername/portfolio-eight/tree/main/projects/vista-3d">View Source</a>
        </p>
      </div>
    </div>
  </footer>

  <!-- Notification Container -->
  <div id="notificationContainer" class="notification-container"></div>

  <!-- Main JavaScript -->
  <script src="main.js" type="module"></script>
</body>
</html>
