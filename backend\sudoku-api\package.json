{"name": "sudoku-solver-api", "version": "2.0.0", "description": "Enterprise-grade Sudoku API with advanced algorithms, TypeScript, comprehensive testing, and production monitoring", "main": "dist/server.js", "types": "dist/server.d.ts", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --testPathPattern=integration", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "type-check": "tsc --noEmit", "docker:build": "docker build -t sudoku-api .", "docker:run": "docker run -p 3002:3002 sudoku-api", "docker:dev": "docker-compose up --build", "migrate": "node dist/database/migrate.js", "seed": "node dist/database/seed.js", "docs": "typedoc src --out docs", "health-check": "curl -f http://localhost:3002/health || exit 1", "precommit": "npm run lint && npm run type-check && npm run test", "postinstall": "echo 'Skipping husky install for now'"}, "keywords": ["sudoku", "solver", "algorithm", "puzzle", "api", "nodejs", "typescript", "backtracking", "enterprise", "production-ready"], "author": "<PERSON><PERSON><PERSON> Sharp <<EMAIL>>", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "prom-client": "^15.1.0", "swagger-ui-express": "^5.0.0", "swagger-jsdoc": "^6.2.8", "redis": "^4.6.10", "sqlite3": "^5.1.6", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2", "uuid": "^9.0.1", "dotenv": "^16.3.1", "joi": "^17.11.0"}, "devDependencies": {"@types/node": "^20.8.0", "@types/express": "^4.17.20", "@types/cors": "^2.8.15", "@types/compression": "^1.7.4", "@types/bcrypt": "^5.0.1", "@types/jsonwebtoken": "^9.0.4", "@types/uuid": "^9.0.6", "@types/swagger-ui-express": "^4.1.4", "@types/swagger-jsdoc": "^6.0.2", "@types/jest": "^29.5.6", "@types/supertest": "^2.0.15", "typescript": "^5.2.2", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "@typescript-eslint/eslint-plugin": "^6.8.0", "@typescript-eslint/parser": "^6.8.0", "eslint": "^8.51.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "prettier": "^3.0.3", "husky": "^8.0.3", "lint-staged": "^15.0.2", "typedoc": "^0.25.2", "nodemon": "^3.0.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "lint-staged": {"src/**/*.ts": ["eslint --fix", "prettier --write", "git add"]}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src", "<rootDir>/tests"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "moduleNameMapper": {"^@/(.*)$": "<rootDir>/src/$1"}, "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/server.ts"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"], "coverageThreshold": {"global": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}}}