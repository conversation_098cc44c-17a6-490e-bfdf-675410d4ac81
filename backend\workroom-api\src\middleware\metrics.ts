/**
 * Prometheus metrics collection middleware for collaborative workspace API
 * Comprehensive metrics for HTTP requests, WebSocket connections, business operations, and system performance
 */

import { Request, Response, NextFunction } from 'express';
import client from 'prom-client';
import { logger } from '@/utils/logger';

// Create a Registry to register the metrics
const register = new client.Registry();

// Add default metrics
client.collectDefaultMetrics({
  register,
  prefix: 'workspace_',
});

// HTTP Request metrics
const httpRequestDuration = new client.Histogram({
  name: 'workspace_http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code', 'user_id', 'workspace_id'],
  buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10]
});

const httpRequestTotal = new client.Counter({
  name: 'workspace_http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code', 'user_id', 'workspace_id']
});

const httpRequestSize = new client.Histogram({
  name: 'workspace_http_request_size_bytes',
  help: 'Size of HTTP requests in bytes',
  labelNames: ['method', 'route'],
  buckets: [100, 1000, 10000, 100000, 1000000, 10000000]
});

const httpResponseSize = new client.Histogram({
  name: 'workspace_http_response_size_bytes',
  help: 'Size of HTTP responses in bytes',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [100, 1000, 10000, 100000, 1000000, 10000000]
});

// WebSocket metrics
const websocketConnections = new client.Gauge({
  name: 'workspace_websocket_connections_active',
  help: 'Number of active WebSocket connections',
  labelNames: ['room_id', 'workspace_id']
});

const websocketMessages = new client.Counter({
  name: 'workspace_websocket_messages_total',
  help: 'Total number of WebSocket messages',
  labelNames: ['type', 'room_id', 'workspace_id', 'user_id']
});

const websocketConnectionDuration = new client.Histogram({
  name: 'workspace_websocket_connection_duration_seconds',
  help: 'Duration of WebSocket connections in seconds',
  labelNames: ['room_id', 'workspace_id', 'user_id'],
  buckets: [60, 300, 900, 1800, 3600, 7200, 14400, 28800]
});

// Business metrics
const workspaceOperations = new client.Counter({
  name: 'workspace_operations_total',
  help: 'Total number of workspace operations',
  labelNames: ['operation', 'workspace_id', 'user_id', 'status']
});

const roomOperations = new client.Counter({
  name: 'workspace_room_operations_total',
  help: 'Total number of room operations',
  labelNames: ['operation', 'room_id', 'workspace_id', 'user_id', 'status']
});

const chatMessages = new client.Counter({
  name: 'workspace_chat_messages_total',
  help: 'Total number of chat messages',
  labelNames: ['room_id', 'workspace_id', 'user_id', 'message_type']
});

const whiteboardActions = new client.Counter({
  name: 'workspace_whiteboard_actions_total',
  help: 'Total number of whiteboard actions',
  labelNames: ['action_type', 'room_id', 'workspace_id', 'user_id']
});

const taskOperations = new client.Counter({
  name: 'workspace_task_operations_total',
  help: 'Total number of task operations',
  labelNames: ['operation', 'task_id', 'room_id', 'workspace_id', 'user_id']
});

const fileOperations = new client.Counter({
  name: 'workspace_file_operations_total',
  help: 'Total number of file operations',
  labelNames: ['operation', 'file_type', 'room_id', 'workspace_id', 'user_id']
});

const meetingOperations = new client.Counter({
  name: 'workspace_meeting_operations_total',
  help: 'Total number of meeting operations',
  labelNames: ['operation', 'meeting_id', 'room_id', 'workspace_id', 'user_id']
});

// Database metrics
const databaseOperations = new client.Counter({
  name: 'workspace_database_operations_total',
  help: 'Total number of database operations',
  labelNames: ['operation', 'table', 'status']
});

const databaseQueryDuration = new client.Histogram({
  name: 'workspace_database_query_duration_seconds',
  help: 'Duration of database queries in seconds',
  labelNames: ['operation', 'table'],
  buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 5]
});

// Cache metrics
const cacheOperations = new client.Counter({
  name: 'workspace_cache_operations_total',
  help: 'Total number of cache operations',
  labelNames: ['operation', 'status']
});

const cacheHitRatio = new client.Gauge({
  name: 'workspace_cache_hit_ratio',
  help: 'Cache hit ratio',
  labelNames: ['cache_type']
});

// System metrics
const activeUsers = new client.Gauge({
  name: 'workspace_active_users',
  help: 'Number of active users',
  labelNames: ['workspace_id']
});

const storageUsage = new client.Gauge({
  name: 'workspace_storage_usage_bytes',
  help: 'Storage usage in bytes',
  labelNames: ['workspace_id', 'type']
});

const errorRate = new client.Counter({
  name: 'workspace_errors_total',
  help: 'Total number of errors',
  labelNames: ['type', 'severity', 'component']
});

// Register all metrics
register.registerMetric(httpRequestDuration);
register.registerMetric(httpRequestTotal);
register.registerMetric(httpRequestSize);
register.registerMetric(httpResponseSize);
register.registerMetric(websocketConnections);
register.registerMetric(websocketMessages);
register.registerMetric(websocketConnectionDuration);
register.registerMetric(workspaceOperations);
register.registerMetric(roomOperations);
register.registerMetric(chatMessages);
register.registerMetric(whiteboardActions);
register.registerMetric(taskOperations);
register.registerMetric(fileOperations);
register.registerMetric(meetingOperations);
register.registerMetric(databaseOperations);
register.registerMetric(databaseQueryDuration);
register.registerMetric(cacheOperations);
register.registerMetric(cacheHitRatio);
register.registerMetric(activeUsers);
register.registerMetric(storageUsage);
register.registerMetric(errorRate);

/**
 * Metrics collection class
 */
export class MetricsCollector {
  /**
   * HTTP request metrics middleware
   */
  static middleware = (req: Request, res: Response, next: NextFunction): void => {
    const startTime = Date.now();
    const route = req.route?.path || req.path;

    // Track request size
    const requestSize = parseInt(req.get('Content-Length') || '0', 10);
    if (requestSize > 0) {
      httpRequestSize.observe(
        { method: req.method, route },
        requestSize
      );
    }

    // Override res.end to capture metrics
    const originalEnd = res.end;
    res.end = function(chunk?: any, encoding?: any) {
      const duration = (Date.now() - startTime) / 1000;
      const responseSize = parseInt(res.get('Content-Length') || '0', 10);

      // Record metrics
      httpRequestDuration.observe(
        {
          method: req.method,
          route,
          status_code: res.statusCode.toString(),
          user_id: req.userId || 'anonymous',
          workspace_id: req.workspaceId || 'none'
        },
        duration
      );

      httpRequestTotal.inc({
        method: req.method,
        route,
        status_code: res.statusCode.toString(),
        user_id: req.userId || 'anonymous',
        workspace_id: req.workspaceId || 'none'
      });

      if (responseSize > 0) {
        httpResponseSize.observe(
          {
            method: req.method,
            route,
            status_code: res.statusCode.toString()
          },
          responseSize
        );
      }

      // Track errors
      if (res.statusCode >= 400) {
        errorRate.inc({
          type: res.statusCode >= 500 ? 'server_error' : 'client_error',
          severity: res.statusCode >= 500 ? 'high' : 'medium',
          component: 'http'
        });
      }

      return originalEnd.call(this, chunk, encoding);
    };

    next();
  };

  /**
   * WebSocket connection metrics
   */
  static trackWebSocketConnection(roomId: string, workspaceId: string, userId: string): void {
    websocketConnections.inc({ room_id: roomId, workspace_id: workspaceId });
    logger.debug('WebSocket connection tracked', { roomId, workspaceId, userId });
  }

  static trackWebSocketDisconnection(roomId: string, workspaceId: string, userId: string, duration: number): void {
    websocketConnections.dec({ room_id: roomId, workspace_id: workspaceId });
    websocketConnectionDuration.observe(
      { room_id: roomId, workspace_id: workspaceId, user_id: userId },
      duration
    );
    logger.debug('WebSocket disconnection tracked', { roomId, workspaceId, userId, duration });
  }

  static trackWebSocketMessage(type: string, roomId: string, workspaceId: string, userId: string): void {
    websocketMessages.inc({ type, room_id: roomId, workspace_id: workspaceId, user_id: userId });
  }

  /**
   * Business operation metrics
   */
  static trackWorkspaceOperation(operation: string, workspaceId: string, userId: string, status: string): void {
    workspaceOperations.inc({ operation, workspace_id: workspaceId, user_id: userId, status });
  }

  static trackRoomOperation(operation: string, roomId: string, workspaceId: string, userId: string, status: string): void {
    roomOperations.inc({ operation, room_id: roomId, workspace_id: workspaceId, user_id: userId, status });
  }

  static trackChatMessage(roomId: string, workspaceId: string, userId: string, messageType: string): void {
    chatMessages.inc({ room_id: roomId, workspace_id: workspaceId, user_id: userId, message_type: messageType });
  }

  static trackWhiteboardAction(actionType: string, roomId: string, workspaceId: string, userId: string): void {
    whiteboardActions.inc({ action_type: actionType, room_id: roomId, workspace_id: workspaceId, user_id: userId });
  }

  static trackTaskOperation(operation: string, taskId: string, roomId: string, workspaceId: string, userId: string): void {
    taskOperations.inc({ operation, task_id: taskId, room_id: roomId, workspace_id: workspaceId, user_id: userId });
  }

  static trackFileOperation(operation: string, fileType: string, roomId: string, workspaceId: string, userId: string): void {
    fileOperations.inc({ operation, file_type: fileType, room_id: roomId, workspace_id: workspaceId, user_id: userId });
  }

  static trackMeetingOperation(operation: string, meetingId: string, roomId: string, workspaceId: string, userId: string): void {
    meetingOperations.inc({ operation, meeting_id: meetingId, room_id: roomId, workspace_id: workspaceId, user_id: userId });
  }

  /**
   * Database metrics
   */
  static trackDatabaseOperation(operation: string, table: string, status: string, duration?: number): void {
    databaseOperations.inc({ operation, table, status });
    if (duration !== undefined) {
      databaseQueryDuration.observe({ operation, table }, duration / 1000);
    }
  }

  /**
   * Cache metrics
   */
  static trackCacheOperation(operation: string, status: string): void {
    cacheOperations.inc({ operation, status });
  }

  static updateCacheHitRatio(cacheType: string, ratio: number): void {
    cacheHitRatio.set({ cache_type: cacheType }, ratio);
  }

  /**
   * System metrics
   */
  static updateActiveUsers(workspaceId: string, count: number): void {
    activeUsers.set({ workspace_id: workspaceId }, count);
  }

  static updateStorageUsage(workspaceId: string, type: string, bytes: number): void {
    storageUsage.set({ workspace_id: workspaceId, type }, bytes);
  }

  static trackError(type: string, severity: string, component: string): void {
    errorRate.inc({ type, severity, component });
  }

  /**
   * Metrics endpoint handler
   */
  static metricsEndpoint = async (req: Request, res: Response): Promise<void> => {
    try {
      res.set('Content-Type', register.contentType);
      const metrics = await register.metrics();
      res.end(metrics);
    } catch (error) {
      logger.error('Failed to generate metrics', error);
      res.status(500).json({ error: 'Failed to generate metrics' });
    }
  };

  /**
   * Get registry for external use
   */
  static getRegistry(): client.Registry {
    return register;
  }
}

export default MetricsCollector;
