/**
 * Gallery Data - Project Screenshots and Information
 * Comprehensive data for all portfolio projects
 */

const GALLERY_DATA = {
  'hair-salon': {
    title: 'A Elements Hair - Premium Salon',
    description: 'Professional hair salon website with booking system, portfolio gallery, and sustainable practices',
    liveUrl: '../hair-salon/',
    codeUrl: 'https://github.com/yourusername/portfolio-eight/tree/main/projects/hair-salon',
    category: ['business', 'design'],
    screenshots: [
      {
        url: '../../Website Resume/Photo Previews/Enlighten .png',
        title: 'Hair Salon Website Preview',
        description: 'Professional hair salon website featuring elegant design, service showcase, and modern branding with booking capabilities.'
      }
    ]
  },
  
  'plumbing-website': {
    title: 'Professional Plumbing Services',
    description: 'Complete plumbing services website with service listings and contact information',
    liveUrl: '../plumbing-website/',
    codeUrl: 'https://github.com/yourusername/portfolio-eight/tree/main/projects/plumbing-website',
    category: ['business', 'design'],
    screenshots: [
      {
        url: '../../Website Resume/Photo Previews/Allstarsplumbing.png',
        title: 'Plumbing Services Website Preview',
        description: 'Professional plumbing services website featuring service offerings, contact information, and business details.'
      }
    ]
  },
  
  'painting-website': {
    title: 'Professional Painting Services',
    description: 'Complete painting contractor website with portfolio gallery and service details',
    liveUrl: '../painting-website/',
    codeUrl: 'https://github.com/yourusername/portfolio-eight/tree/main/projects/painting-website',
    category: ['business', 'design'],
    screenshots: [
      {
        url: '../../Website Resume/Photo Previews/Cocky\'s painting.png',
        title: 'Painting Services Website Preview',
        description: 'Professional painting services website featuring company introduction, service highlights, and portfolio showcase.'
      }
    ]
  },
  
  'qara-website': {
    title: 'Qara - Modern Business Website',
    description: 'Sleek single-page business website with modern design and professional layout',
    liveUrl: '../qara-website/',
    codeUrl: 'https://github.com/yourusername/portfolio-eight/tree/main/projects/qara-website',
    category: ['business', 'design'],
    screenshots: [
      {
        url: '../../Website Resume/Photo Previews/QARA.png',
        title: 'Qara Business Website Preview',
        description: 'Modern single-page business website featuring clean design, professional layout, and comprehensive business information.'
      }
    ]
  },
  
  'chinese-shop': {
    title: 'Chinese Shop - E-commerce Website',
    description: 'Complete e-commerce website for Chinese products with modern design',
    liveUrl: '../chinese-shop/',
    codeUrl: 'https://github.com/yourusername/portfolio-eight/tree/main/projects/chinese-shop',
    category: ['business', 'ecommerce'],
    screenshots: [
      {
        url: '../../Website Resume/Photo Previews/MR SO.png',
        title: 'Chinese Shop Website Preview',
        description: 'Modern e-commerce website featuring Chinese products, clean layout, and professional design with product showcases.'
      }
    ]
  },
  
  'barber-website': {
    title: 'Professional Barber Shop',
    description: 'Modern barber shop website with services, booking, and professional styling',
    liveUrl: '../barber-website/',
    codeUrl: 'https://github.com/yourusername/portfolio-eight/tree/main/projects/barber-website',
    category: ['business', 'design'],
    screenshots: [
      {
        url: '../../Website Resume/Photo Previews/4 elements.png',
        title: 'Barber Shop Website Preview',
        description: 'Professional barber shop website featuring services, styling, modern design, and booking capabilities.'
      }
    ]
  }
};

// Enhanced placeholder image generator that recreates the actual hair salon screenshots
function generateHairSalonScreenshot(screenshotTitle, width = 800, height = 600) {
  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  const ctx = canvas.getContext('2d');

  // Different layouts based on screenshot title
  switch (screenshotTitle) {
    case 'Hero & Stylist Introduction':
      // Recreate the hero section with pink gradient and decorative circles
      const heroGradient = ctx.createLinearGradient(0, 0, width, height);
      heroGradient.addColorStop(0, '#fdf2f8');
      heroGradient.addColorStop(0.3, '#f9a8d4');
      heroGradient.addColorStop(1, '#ec4899');
      ctx.fillStyle = heroGradient;
      ctx.fillRect(0, 0, width, height);

      // Add large decorative circles like in the original
      ctx.fillStyle = 'rgba(255, 255, 255, 0.15)';
      ctx.beginPath();
      ctx.arc(width * 0.8, height * 0.7, 120, 0, Math.PI * 2);
      ctx.fill();

      ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
      ctx.beginPath();
      ctx.arc(width * 0.6, height * 0.8, 80, 0, Math.PI * 2);
      ctx.fill();

      // Main title
      ctx.fillStyle = '#1f2937';
      ctx.font = 'bold 36px serif';
      ctx.textAlign = 'center';
      ctx.fillText('A ELEMENTS HAIR', width / 2, height / 2 - 20);

      // Subtitle
      ctx.font = '18px sans-serif';
      ctx.fillStyle = '#374151';
      ctx.fillText('Hero & Stylist Introduction', width / 2, height / 2 + 20);

      // Description
      ctx.font = '14px sans-serif';
      ctx.fillStyle = '#6b7280';
      ctx.fillText('Premium Hair Salon Website', width / 2, height / 2 + 50);
      break;

    case 'Contact & Location':
      // White/cream background for contact section
      ctx.fillStyle = '#fef7f0';
      ctx.fillRect(0, 0, width, height);

      // Header section with pink accent
      ctx.fillStyle = '#f4c2c2';
      ctx.fillRect(0, 0, width, 80);

      // Contact title
      ctx.fillStyle = '#1f2937';
      ctx.font = 'bold 24px sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText('CONTACT & LOCATION', width / 2, 50);

      // Contact details mockup
      ctx.font = '16px sans-serif';
      ctx.textAlign = 'left';
      ctx.fillText('📍 123 Style Street, Hair City', 50, 150);
      ctx.fillText('📞 (555) 123-4567', 50, 180);
      ctx.fillText('✉️ <EMAIL>', 50, 210);
      ctx.fillText('🕒 Mon-Sat: 9AM-7PM', 50, 240);

      // Booking button mockup
      ctx.fillStyle = '#ec4899';
      ctx.fillRect(50, 280, 150, 40);
      ctx.fillStyle = 'white';
      ctx.font = 'bold 14px sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText('BOOK NOW', 125, 305);
      break;

    case 'Premium Services':
      // Services page layout
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 0, width, height);

      // Pink header
      ctx.fillStyle = '#f4c2c2';
      ctx.fillRect(0, 0, width, 100);

      ctx.fillStyle = '#1f2937';
      ctx.font = 'bold 28px serif';
      ctx.textAlign = 'center';
      ctx.fillText('PREMIUM SERVICES', width / 2, 60);

      // Service cards mockup
      const services = [
        { name: 'Blonde Specialists', price: '$180-$250', y: 140 },
        { name: 'Color Correction', price: '$150-$300', y: 220 },
        { name: 'Hair Coloring', price: '$80-$160', y: 300 }
      ];

      services.forEach(service => {
        // Service card background
        ctx.fillStyle = '#fdf2f8';
        ctx.fillRect(50, service.y, width - 100, 60);

        // Service name
        ctx.fillStyle = '#1f2937';
        ctx.font = 'bold 16px sans-serif';
        ctx.textAlign = 'left';
        ctx.fillText(service.name, 70, service.y + 25);

        // Price
        ctx.fillStyle = '#ec4899';
        ctx.font = 'bold 14px sans-serif';
        ctx.textAlign = 'right';
        ctx.fillText(service.price, width - 70, service.y + 25);
      });
      break;

    case 'About & Sustainability':
      // About section with sustainability focus
      ctx.fillStyle = '#fef7f0';
      ctx.fillRect(0, 0, width, height);

      // Split layout - about on left, sustainability on right
      ctx.fillStyle = '#f4c2c2';
      ctx.fillRect(0, 0, width / 2, height);

      // About section
      ctx.fillStyle = '#1f2937';
      ctx.font = 'bold 20px serif';
      ctx.textAlign = 'center';
      ctx.fillText('ABOUT', width / 4, 80);

      ctx.font = '14px sans-serif';
      ctx.textAlign = 'left';
      ctx.fillText('Expert stylist with', 20, 120);
      ctx.fillText('10+ years experience', 20, 140);
      ctx.fillText('Specializing in blonde', 20, 160);
      ctx.fillText('transformations', 20, 180);

      // Sustainability section
      ctx.fillStyle = '#1f2937';
      ctx.font = 'bold 20px serif';
      ctx.textAlign = 'center';
      ctx.fillText('SUSTAINABILITY', width * 0.75, 80);

      ctx.font = '14px sans-serif';
      ctx.textAlign = 'left';
      ctx.fillText('🌱 Eco-friendly products', width / 2 + 20, 120);
      ctx.fillText('♻️ Sustainable practices', width / 2 + 20, 140);
      ctx.fillText('🌍 Environmental focus', width / 2 + 20, 160);
      ctx.fillText('💚 Green beauty', width / 2 + 20, 180);
      break;

    case 'Sustainability Focus':
      // Dedicated sustainability page
      const sustainGradient = ctx.createLinearGradient(0, 0, width, height);
      sustainGradient.addColorStop(0, '#f0fdf4');
      sustainGradient.addColorStop(0.5, '#dcfce7');
      sustainGradient.addColorStop(1, '#bbf7d0');
      ctx.fillStyle = sustainGradient;
      ctx.fillRect(0, 0, width, height);

      // Four elements approach
      ctx.fillStyle = '#1f2937';
      ctx.font = 'bold 24px serif';
      ctx.textAlign = 'center';
      ctx.fillText('FOUR ELEMENTS APPROACH', width / 2, 80);

      const elements = [
        { name: '🌍 EARTH', desc: 'Natural ingredients', x: width * 0.25, y: 150 },
        { name: '💧 WATER', desc: 'Conservation focus', x: width * 0.75, y: 150 },
        { name: '🔥 FIRE', desc: 'Energy efficiency', x: width * 0.25, y: 300 },
        { name: '💨 AIR', desc: 'Clean environment', x: width * 0.75, y: 300 }
      ];

      elements.forEach(element => {
        // Element circle
        ctx.fillStyle = 'rgba(34, 197, 94, 0.2)';
        ctx.beginPath();
        ctx.arc(element.x, element.y, 60, 0, Math.PI * 2);
        ctx.fill();

        // Element name
        ctx.fillStyle = '#1f2937';
        ctx.font = 'bold 14px sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText(element.name, element.x, element.y - 10);

        // Description
        ctx.font = '12px sans-serif';
        ctx.fillText(element.desc, element.x, element.y + 10);
      });
      break;

    default:
      // Fallback design
      const defaultGradient = ctx.createLinearGradient(0, 0, width, height);
      defaultGradient.addColorStop(0, '#fdf2f8');
      defaultGradient.addColorStop(1, '#ec4899');
      ctx.fillStyle = defaultGradient;
      ctx.fillRect(0, 0, width, height);

      ctx.fillStyle = '#1f2937';
      ctx.font = 'bold 24px serif';
      ctx.textAlign = 'center';
      ctx.fillText('A ELEMENTS HAIR', width / 2, height / 2);
  }

  return canvas.toDataURL();
}

// Placeholder image generator for missing screenshots
function generatePlaceholderImage(projectName, screenshotTitle, width = 800, height = 600) {
  // Special handling for hair salon project
  if (projectName.toLowerCase().includes('hair') || projectName.toLowerCase().includes('salon')) {
    return generateHairSalonScreenshot(screenshotTitle, width, height);
  }

  // Default styling for other projects
  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  const ctx = canvas.getContext('2d');

  const gradient = ctx.createLinearGradient(0, 0, width, height);
  gradient.addColorStop(0, '#667eea');
  gradient.addColorStop(1, '#764ba2');
  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, width, height);

  // Add project name
  ctx.fillStyle = 'white';
  ctx.font = 'bold 32px Arial';
  ctx.textAlign = 'center';
  ctx.fillText(projectName.toUpperCase(), width / 2, height / 2 - 20);

  // Add screenshot title
  ctx.font = '20px Arial';
  ctx.fillText(screenshotTitle, width / 2, height / 2 + 20);

  // Add decorative elements
  ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
  ctx.lineWidth = 2;
  for (let i = 0; i < 5; i++) {
    ctx.beginPath();
    ctx.arc(Math.random() * width, Math.random() * height, Math.random() * 50 + 10, 0, Math.PI * 2);
    ctx.stroke();
  }

  return canvas.toDataURL();
}

// Function to get project data
function getProjectData(projectId) {
  return GALLERY_DATA[projectId] || null;
}

// Function to get all projects
function getAllProjects() {
  return Object.keys(GALLERY_DATA).map(id => ({
    id,
    ...GALLERY_DATA[id]
  }));
}

// Function to filter projects by category
function getProjectsByCategory(category) {
  return Object.keys(GALLERY_DATA)
    .filter(id => GALLERY_DATA[id].category.includes(category))
    .map(id => ({
      id,
      ...GALLERY_DATA[id]
    }));
}

// Function to create placeholder screenshots if images don't exist (except real website projects which have actual images)
function ensureScreenshots() {
  // List of projects that have real images and should not use placeholders
  const realWebsiteProjects = [
    'hair-salon',
    'plumbing-website',
    'painting-website',
    'qara-website',
    'chinese-shop',
    'barber-website'
  ];

  Object.keys(GALLERY_DATA).forEach(projectId => {
    const project = GALLERY_DATA[projectId];
    project.screenshots.forEach((screenshot, index) => {
      // For real website projects, use the actual screenshot files we created
      if (realWebsiteProjects.includes(projectId)) {
        // Keep the original URLs pointing to our actual screenshot files
        // Don't override with placeholders since we have real images now
        return;
      } else {
        // Create a fallback placeholder if image fails to load for other projects
        const img = new Image();
        img.onerror = () => {
          screenshot.url = generatePlaceholderImage(
            project.title,
            screenshot.title
          );
        };
        img.src = screenshot.url;
      }
    });
  });
}

// Initialize placeholder system
document.addEventListener('DOMContentLoaded', () => {
  ensureScreenshots();
});

// Export for use in main.js
window.GALLERY_DATA = GALLERY_DATA;
window.getProjectData = getProjectData;
window.getAllProjects = getAllProjects;
window.getProjectsByCategory = getProjectsByCategory;
window.generatePlaceholderImage = generatePlaceholderImage;
