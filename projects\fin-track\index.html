<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="FinTrack - Personal finance tracker PWA with offline support and data visualization">
  <meta name="keywords" content="finance, budget, expense tracker, PWA, offline, personal finance">
  <meta name="theme-color" content="#10b981">
  
  <!-- PWA Manifest -->
  <link rel="manifest" href="manifest.json">
  
  <!-- Icons -->
  <link rel="icon" type="image/svg+xml" href="../../assets/favicon.svg">
  <link rel="apple-touch-icon" href="assets/icon-192.svg">
  
  <title>FinTrack - Personal Finance Tracker</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <header class="header">
    <nav class="nav">
      <div class="nav-container">
        <a href="../../" class="logo">
          <span class="logo-icon">💰</span>
          <span class="logo-text">FinTrack</span>
        </a>
        <div class="nav-controls">
          <button class="theme-toggle" aria-label="Toggle theme">
            <span class="theme-icon">🌙</span>
          </button>
          <button class="sync-btn" aria-label="Sync data" title="Sync data">
            <span class="sync-icon">🔄</span>
          </button>
          <a href="../../" class="back-link" aria-label="Back to portfolio">
            <span class="back-icon">←</span>
          </a>
        </div>
      </div>
    </nav>
  </header>

  <main class="main">
    <!-- Dashboard Overview -->
    <section class="dashboard">
      <div class="dashboard-container">
        <h1 class="dashboard-title">Financial Dashboard</h1>
        
        <div class="stats-grid">
          <div class="stat-card balance">
            <div class="stat-icon">💳</div>
            <div class="stat-content">
              <h3>Current Balance</h3>
              <p class="stat-value" id="currentBalance">$0.00</p>
              <span class="stat-change" id="balanceChange">+$0.00 this month</span>
            </div>
          </div>
          
          <div class="stat-card income">
            <div class="stat-icon">📈</div>
            <div class="stat-content">
              <h3>Monthly Income</h3>
              <p class="stat-value" id="monthlyIncome">$0.00</p>
              <span class="stat-change" id="incomeChange">+0% vs last month</span>
            </div>
          </div>
          
          <div class="stat-card expenses">
            <div class="stat-icon">📉</div>
            <div class="stat-content">
              <h3>Monthly Expenses</h3>
              <p class="stat-value" id="monthlyExpenses">$0.00</p>
              <span class="stat-change" id="expensesChange">+0% vs last month</span>
            </div>
          </div>
          
          <div class="stat-card savings">
            <div class="stat-icon">🎯</div>
            <div class="stat-content">
              <h3>Savings Rate</h3>
              <p class="stat-value" id="savingsRate">0%</p>
              <span class="stat-change" id="savingsGoal">Goal: 20%</span>
            </div>
          </div>
        </div>

        <!-- Charts Section -->
        <div class="charts-section">
          <div class="chart-card">
            <h3>Spending by Category</h3>
            <canvas id="categoryChart" width="400" height="200"></canvas>
            <noscript>
              <div class="chart-fallback">
                <p>Charts require JavaScript to display properly.</p>
              </div>
            </noscript>
          </div>

          <div class="chart-card">
            <h3>Income vs Expenses</h3>
            <canvas id="trendChart" width="400" height="200"></canvas>
            <noscript>
              <div class="chart-fallback">
                <p>Charts require JavaScript to display properly.</p>
              </div>
            </noscript>
          </div>
        </div>
      </div>
    </section>

    <!-- Quick Actions -->
    <section class="quick-actions">
      <div class="actions-container">
        <h2>Quick Actions</h2>
        <div class="actions-grid">
          <button class="action-btn income-btn" id="addIncomeBtn">
            <span class="action-icon">💵</span>
            <span class="action-text">Add Income</span>
          </button>
          
          <button class="action-btn expense-btn" id="addExpenseBtn">
            <span class="action-icon">💸</span>
            <span class="action-text">Add Expense</span>
          </button>
          
          <button class="action-btn transfer-btn" id="addTransferBtn">
            <span class="action-icon">🔄</span>
            <span class="action-text">Transfer</span>
          </button>
          
          <button class="action-btn goal-btn" id="addGoalBtn">
            <span class="action-icon">🎯</span>
            <span class="action-text">Set Goal</span>
          </button>
        </div>
      </div>
    </section>

    <!-- Transactions List -->
    <section class="transactions">
      <div class="transactions-container">
        <div class="transactions-header">
          <h2>Recent Transactions</h2>
          <div class="transactions-controls">
            <select id="filterCategory" class="filter-select">
              <option value="all">All Categories</option>
              <option value="food">Food & Dining</option>
              <option value="transport">Transportation</option>
              <option value="shopping">Shopping</option>
              <option value="bills">Bills & Utilities</option>
              <option value="entertainment">Entertainment</option>
              <option value="health">Health & Fitness</option>
              <option value="income">Income</option>
              <option value="other">Other</option>
            </select>
            
            <select id="filterPeriod" class="filter-select">
              <option value="all">All Time</option>
              <option value="today">Today</option>
              <option value="week">This Week</option>
              <option value="month">This Month</option>
              <option value="year">This Year</option>
            </select>
            
            <button class="btn btn-outline" id="exportBtn">Export CSV</button>
            <button class="btn btn-outline" id="importBtn">Import CSV</button>
          </div>
        </div>
        
        <div class="transactions-list" id="transactionsList">
          <!-- Transactions will be dynamically inserted here -->
        </div>
        
        <div class="transactions-pagination">
          <button class="btn btn-outline" id="loadMoreBtn">Load More</button>
        </div>
      </div>
    </section>

    <!-- Goals Section -->
    <section class="goals">
      <div class="goals-container">
        <h2>Financial Goals</h2>
        <div class="goals-grid" id="goalsGrid">
          <!-- Goals will be dynamically inserted here -->
        </div>
      </div>
    </section>
  </main>

  <!-- Transaction Modal -->
  <div class="modal" id="transactionModal" style="display: none;">
    <div class="modal-content">
      <div class="modal-header">
        <h3 id="modalTitle">Add Transaction</h3>
        <button class="modal-close" id="closeModal">&times;</button>
      </div>
      <form class="modal-form" id="transactionForm">
        <div class="form-row">
          <div class="form-group">
            <label for="transactionType">Type:</label>
            <select id="transactionType" required>
              <option value="">Select type</option>
              <option value="income">Income</option>
              <option value="expense">Expense</option>
              <option value="transfer">Transfer</option>
            </select>
          </div>
          
          <div class="form-group">
            <label for="transactionAmount">Amount:</label>
            <input type="number" id="transactionAmount" step="0.01" min="0" required placeholder="0.00">
          </div>
        </div>
        
        <div class="form-group">
          <label for="transactionCategory">Category:</label>
          <select id="transactionCategory" required>
            <option value="">Select category</option>
            <!-- Options will be populated dynamically -->
          </select>
        </div>
        
        <div class="form-group">
          <label for="transactionDescription">Description:</label>
          <input type="text" id="transactionDescription" required placeholder="Enter description">
        </div>
        
        <div class="form-row">
          <div class="form-group">
            <label for="transactionDate">Date:</label>
            <input type="date" id="transactionDate" required>
          </div>
          
          <div class="form-group">
            <label for="transactionAccount">Account:</label>
            <select id="transactionAccount" required>
              <option value="checking">Checking</option>
              <option value="savings">Savings</option>
              <option value="credit">Credit Card</option>
              <option value="cash">Cash</option>
            </select>
          </div>
        </div>
        
        <div class="form-group">
          <label for="transactionNotes">Notes (Optional):</label>
          <textarea id="transactionNotes" placeholder="Additional notes..."></textarea>
        </div>
        
        <div class="form-actions">
          <button type="button" class="btn btn-secondary" id="cancelTransaction">Cancel</button>
          <button type="submit" class="btn btn-primary">Save Transaction</button>
        </div>
      </form>
    </div>
  </div>

  <!-- Goal Modal -->
  <div class="modal" id="goalModal" style="display: none;">
    <div class="modal-content">
      <div class="modal-header">
        <h3>Set Financial Goal</h3>
        <button class="modal-close" id="closeGoalModal">&times;</button>
      </div>
      <form class="modal-form" id="goalForm">
        <div class="form-group">
          <label for="goalName">Goal Name:</label>
          <input type="text" id="goalName" required placeholder="e.g., Emergency Fund">
        </div>
        
        <div class="form-row">
          <div class="form-group">
            <label for="goalTarget">Target Amount:</label>
            <input type="number" id="goalTarget" step="0.01" min="0" required placeholder="0.00">
          </div>
          
          <div class="form-group">
            <label for="goalCurrent">Current Amount:</label>
            <input type="number" id="goalCurrent" step="0.01" min="0" placeholder="0.00">
          </div>
        </div>
        
        <div class="form-group">
          <label for="goalDeadline">Target Date:</label>
          <input type="date" id="goalDeadline">
        </div>
        
        <div class="form-group">
          <label for="goalCategory">Category:</label>
          <select id="goalCategory" required>
            <option value="">Select category</option>
            <option value="emergency">Emergency Fund</option>
            <option value="vacation">Vacation</option>
            <option value="house">House Down Payment</option>
            <option value="car">Car Purchase</option>
            <option value="education">Education</option>
            <option value="retirement">Retirement</option>
            <option value="other">Other</option>
          </select>
        </div>
        
        <div class="form-actions">
          <button type="button" class="btn btn-secondary" id="cancelGoal">Cancel</button>
          <button type="submit" class="btn btn-primary">Save Goal</button>
        </div>
      </form>
    </div>
  </div>

  <!-- File input for CSV import -->
  <input type="file" id="csvFileInput" accept=".csv" style="display: none;">

  <!-- Offline indicator -->
  <div class="offline-indicator" id="offlineIndicator" style="display: none;">
    <span class="offline-icon">📡</span>
    <span class="offline-text">You're offline. Changes will sync when connected.</span>
  </div>

  <!-- Loading overlay -->
  <div class="loading-overlay" id="loadingOverlay" style="display: none;">
    <div class="loading-spinner"></div>
    <p>Processing...</p>
  </div>

  <footer class="footer">
    <div class="footer-container">
      <p>&copy; 2025 FinTrack. Part of Portfolio Eight.</p>
      <p>
        <a href="../../">← Back to Portfolio</a> |
        <a href="https://github.com/yourusername/portfolio-eight/tree/main/projects/fin-track">View Source</a>
      </p>
    </div>
  </footer>

  <!-- Chart.js removed - using custom HTML charts instead -->
  
  <!-- Main JavaScript modules -->
  <script src="js/database.js" type="module"></script>
  <script src="js/charts.js" type="module"></script>
  <script src="js/transactions.js" type="module"></script>
  <script src="js/goals.js" type="module"></script>
  <script src="js/export.js" type="module"></script>
  <script src="main.js" type="module"></script>
</body>
</html>
