/**
 * Integration Tests for Sudoku API
 * Comprehensive end-to-end API testing
 */

import request from 'supertest';
import { SudokuApiServer } from '@/server';
import { SudokuGrid } from '@/types';

describe('Sudoku API Integration Tests', () => {
  let server: SudokuApiServer;
  let app: any;

  // Test puzzle data
  const validPuzzle: SudokuGrid = [
    [5, 3, 0, 0, 7, 0, 0, 0, 0],
    [6, 0, 0, 1, 9, 5, 0, 0, 0],
    [0, 9, 8, 0, 0, 0, 0, 6, 0],
    [8, 0, 0, 0, 6, 0, 0, 0, 3],
    [4, 0, 0, 8, 0, 3, 0, 0, 1],
    [7, 0, 0, 0, 2, 0, 0, 0, 6],
    [0, 6, 0, 0, 0, 0, 2, 8, 0],
    [0, 0, 0, 4, 1, 9, 0, 0, 5],
    [0, 0, 0, 0, 8, 0, 0, 7, 9],
  ];

  const invalidPuzzle: SudokuGrid = [
    [5, 5, 0, 0, 7, 0, 0, 0, 0], // Duplicate 5 in first row
    [6, 0, 0, 1, 9, 5, 0, 0, 0],
    [0, 9, 8, 0, 0, 0, 0, 6, 0],
    [8, 0, 0, 0, 6, 0, 0, 0, 3],
    [4, 0, 0, 8, 0, 3, 0, 0, 1],
    [7, 0, 0, 0, 2, 0, 0, 0, 6],
    [0, 6, 0, 0, 0, 0, 2, 8, 0],
    [0, 0, 0, 4, 1, 9, 0, 0, 5],
    [0, 0, 0, 0, 8, 0, 0, 7, 9],
  ];

  beforeAll(async () => {
    server = new SudokuApiServer();
    app = server.getApp();
  });

  afterAll(async () => {
    // Clean up if needed
  });

  describe('Health Endpoints', () => {
    test('GET /health should return healthy status', async () => {
      const response = await request(app).get('/health').expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          status: 'healthy',
          uptime: expect.any(Number),
          version: '2.0.0',
        },
      });
    });

    test('GET /health/detailed should return detailed health info', async () => {
      const response = await request(app).get('/health/detailed').expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          status: 'healthy',
          checks: expect.any(Object),
          system: expect.any(Object),
        },
      });
    });

    test('GET /health/ready should return readiness status', async () => {
      const response = await request(app).get('/health/ready').expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          ready: true,
        },
      });
    });

    test('GET /health/live should return liveness status', async () => {
      const response = await request(app).get('/health/live').expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          alive: true,
        },
      });
    });
  });

  describe('Metrics Endpoints', () => {
    test('GET /metrics should return Prometheus metrics', async () => {
      const response = await request(app).get('/metrics').expect(200);

      expect(response.headers['content-type']).toMatch(/text\/plain/);
      expect(response.text).toContain('# HELP');
      expect(response.text).toContain('# TYPE');
    });

    test('GET /metrics/summary should return metrics summary', async () => {
      const response = await request(app).get('/metrics/summary').expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: expect.any(Object),
      });
    });

    test('GET /metrics/business should return business metrics', async () => {
      const response = await request(app).get('/metrics/business').expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          puzzlesSolved: expect.any(Number),
          puzzlesGenerated: expect.any(Number),
          hintsProvided: expect.any(Number),
          validationRequests: expect.any(Number),
        },
      });
    });

    test('GET /metrics/system should return system metrics', async () => {
      const response = await request(app).get('/metrics/system').expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          memory: expect.any(Object),
          cpu: expect.any(Object),
          process: expect.any(Object),
        },
      });
    });
  });

  describe('Sudoku Solve Endpoint', () => {
    test('POST /api/v2/sudoku/solve should solve valid puzzle', async () => {
      const response = await request(app)
        .post('/api/v2/sudoku/solve')
        .send({ puzzle: validPuzzle })
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          solved: true,
          solution: expect.any(Array),
          difficulty: expect.any(String),
          techniques: expect.any(Array),
          statistics: expect.any(Object),
        },
      });

      // Verify solution is complete
      const solution = response.body.data.solution;
      expect(solution).toHaveLength(9);
      solution.forEach((row: number[]) => {
        expect(row).toHaveLength(9);
        row.forEach((cell: number) => {
          expect(cell).toBeGreaterThanOrEqual(1);
          expect(cell).toBeLessThanOrEqual(9);
        });
      });
    });

    test('POST /api/v2/sudoku/solve with step-by-step option', async () => {
      const response = await request(app)
        .post('/api/v2/sudoku/solve')
        .send({
          puzzle: validPuzzle,
          options: { stepByStep: true },
        })
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          solved: true,
          solution: expect.any(Array),
          steps: expect.any(Array),
        },
      });
    });

    test('POST /api/v2/sudoku/solve should handle invalid puzzle', async () => {
      const response = await request(app)
        .post('/api/v2/sudoku/solve')
        .send({ puzzle: invalidPuzzle })
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          solved: false,
          reason: expect.any(String),
        },
      });
    });

    test('POST /api/v2/sudoku/solve should validate input', async () => {
      const response = await request(app)
        .post('/api/v2/sudoku/solve')
        .send({ puzzle: 'invalid' })
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        error: 'Validation failed',
        data: {
          validationErrors: expect.any(Array),
        },
      });
    });
  });

  describe('Sudoku Generate Endpoint', () => {
    test('POST /api/v2/sudoku/generate should generate puzzle', async () => {
      const response = await request(app)
        .post('/api/v2/sudoku/generate')
        .send({ difficulty: 'medium' })
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          puzzle: expect.any(Array),
          solution: expect.any(Array),
          difficulty: 'medium',
          metadata: expect.any(Object),
        },
      });

      // Verify puzzle structure
      const puzzle = response.body.data.puzzle;
      expect(puzzle).toHaveLength(9);
      puzzle.forEach((row: number[]) => {
        expect(row).toHaveLength(9);
        row.forEach((cell: number) => {
          expect(cell).toBeGreaterThanOrEqual(0);
          expect(cell).toBeLessThanOrEqual(9);
        });
      });
    });

    test('POST /api/v2/sudoku/generate with different difficulties', async () => {
      const difficulties = ['easy', 'medium', 'hard', 'expert', 'evil'];

      for (const difficulty of difficulties) {
        const response = await request(app)
          .post('/api/v2/sudoku/generate')
          .send({ difficulty })
          .expect(200);

        expect(response.body.data.difficulty).toBe(difficulty);
        expect(response.body.data.metadata.clues).toBeGreaterThan(0);
      }
    });

    test('POST /api/v2/sudoku/generate should handle invalid difficulty', async () => {
      const response = await request(app)
        .post('/api/v2/sudoku/generate')
        .send({ difficulty: 'invalid' })
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        error: 'Validation failed',
      });
    });
  });

  describe('Sudoku Validate Endpoint', () => {
    test('POST /api/v2/sudoku/validate should validate valid puzzle', async () => {
      const response = await request(app)
        .post('/api/v2/sudoku/validate')
        .send({ puzzle: validPuzzle })
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          isValid: true,
          completeness: expect.any(Number),
          conflicts: [],
          errors: [],
        },
      });
    });

    test('POST /api/v2/sudoku/validate should detect conflicts', async () => {
      const response = await request(app)
        .post('/api/v2/sudoku/validate')
        .send({ puzzle: invalidPuzzle })
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          isValid: false,
          conflicts: expect.arrayContaining([
            expect.objectContaining({
              type: 'row',
              value: 5,
            }),
          ]),
        },
      });
    });
  });

  describe('Sudoku Hint Endpoint', () => {
    test('POST /api/v2/sudoku/hint should provide hint', async () => {
      const response = await request(app)
        .post('/api/v2/sudoku/hint')
        .send({ puzzle: validPuzzle })
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          available: expect.any(Boolean),
        },
      });

      if (response.body.data.available) {
        expect(response.body.data.hint).toMatchObject({
          row: expect.any(Number),
          col: expect.any(Number),
          value: expect.any(Number),
          technique: expect.any(String),
          explanation: expect.any(String),
        });
      }
    });
  });

  describe('Sudoku Analyze Endpoint', () => {
    test('POST /api/v2/sudoku/analyze should analyze puzzle', async () => {
      const response = await request(app)
        .post('/api/v2/sudoku/analyze')
        .send({ puzzle: validPuzzle })
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          difficulty: expect.any(String),
          solvable: expect.any(Boolean),
          clueCount: expect.any(Number),
          techniques: expect.any(Array),
        },
      });
    });
  });

  describe('Information Endpoints', () => {
    test('GET /api/v2/sudoku/difficulties should return difficulty levels', async () => {
      const response = await request(app).get('/api/v2/sudoku/difficulties').expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          difficulties: expect.arrayContaining([
            expect.objectContaining({
              level: expect.any(String),
              description: expect.any(String),
              estimatedTime: expect.any(String),
              clueRange: expect.any(String),
            }),
          ]),
        },
      });
    });

    test('GET /api/v2/sudoku/techniques should return solving techniques', async () => {
      const response = await request(app).get('/api/v2/sudoku/techniques').expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          techniques: expect.arrayContaining([
            expect.objectContaining({
              name: expect.any(String),
              description: expect.any(String),
              difficulty: expect.any(String),
            }),
          ]),
        },
      });
    });

    test('GET /api/v2/sudoku/stats should return API statistics', async () => {
      const response = await request(app).get('/api/v2/sudoku/stats').expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          puzzlesSolved: expect.any(Number),
          puzzlesGenerated: expect.any(Number),
          hintsProvided: expect.any(Number),
          validationRequests: expect.any(Number),
          uptime: expect.any(Number),
          memory: expect.any(Object),
        },
      });
    });
  });

  describe('Documentation Endpoint', () => {
    test('GET /api/v2/docs should serve Swagger UI', async () => {
      const response = await request(app).get('/api/v2/docs').expect(200);

      expect(response.headers['content-type']).toMatch(/text\/html/);
      expect(response.text).toContain('swagger-ui');
    });

    test('GET /api/v2/docs/spec should return OpenAPI spec', async () => {
      const response = await request(app).get('/api/v2/docs/spec').expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          openapi: '3.0.0',
          info: expect.any(Object),
          paths: expect.any(Object),
        },
      });
    });
  });

  describe('Root Endpoint', () => {
    test('GET / should return API information', async () => {
      const response = await request(app).get('/').expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          name: 'Sudoku Solver API',
          version: '2.0.0',
          description: expect.any(String),
          endpoints: expect.any(Object),
        },
      });
    });
  });

  describe('Error Handling', () => {
    test('GET /nonexistent should return 404', async () => {
      const response = await request(app).get('/nonexistent').expect(404);

      expect(response.body).toMatchObject({
        success: false,
        error: expect.stringContaining('Endpoint not found'),
      });
    });

    test('POST /api/v2/sudoku/solve with malformed JSON should return 400', async () => {
      const response = await request(app)
        .post('/api/v2/sudoku/solve')
        .set('Content-Type', 'application/json')
        .send('{"invalid": json}')
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        error: expect.any(String),
      });
    });
  });

  describe('Request ID Handling', () => {
    test('Should include request ID in response', async () => {
      const response = await request(app).get('/health').expect(200);

      expect(response.body.requestId).toBeDefined();
      expect(typeof response.body.requestId).toBe('string');
    });

    test('Should use provided X-Request-ID header', async () => {
      const customRequestId = 'test-request-id-123';

      const response = await request(app)
        .get('/health')
        .set('X-Request-ID', customRequestId)
        .expect(200);

      expect(response.body.requestId).toBe(customRequestId);
      expect(response.headers['x-request-id']).toBe(customRequestId);
    });
  });
});
