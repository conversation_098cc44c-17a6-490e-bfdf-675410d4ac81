# Project Screenshots

This directory contains screenshots for the Interactive Project Gallery.

## Screenshot Naming Convention

Screenshots are named using the following pattern:
`{project-name}-{number}.jpg`

For example:
- `weather-dashboard-1.jpg`
- `weather-dashboard-2.jpg`
- `task-manager-1.jpg`
- etc.

## Projects Included

### Weather Dashboard (4 screenshots)
- weather-dashboard-1.jpg - Main Dashboard
- weather-dashboard-2.jpg - Search Functionality  
- weather-dashboard-3.jpg - 5-Day Forecast
- weather-dashboard-4.jpg - Responsive Design

### Task Manager (5 screenshots)
- task-manager-1.jpg - Dashboard Overview
- task-manager-2.jpg - Drag & Drop Interface
- task-manager-3.jpg - Task Creation Modal
- task-manager-4.jpg - Filter & Search
- task-manager-5.jpg - Dark Mode

### Expense Tracker (4 screenshots)
- expense-tracker-1.jpg - Financial Overview
- expense-tracker-2.jpg - Expense Categories
- expense-tracker-3.jpg - Transaction History
- expense-tracker-4.jpg - Budget Planning

### Wordle Game (4 screenshots)
- wordle-game-1.jpg - Game Interface
- wordle-game-2.jpg - Gameplay in Action
- wordle-game-3.jpg - Statistics Modal
- wordle-game-4.jpg - Victory Screen

### Sudoku Solver (5 screenshots)
- sudoku-solver-1.jpg - Game Board
- sudoku-solver-2.jpg - Difficulty Selection
- sudoku-solver-3.jpg - Hint System
- sudoku-solver-4.jpg - Auto Solver
- sudoku-solver-5.jpg - Victory Modal

### Typing Speed Test (4 screenshots)
- typing-test-1.jpg - Test Interface
- typing-test-2.jpg - Live Statistics
- typing-test-3.jpg - Results Analysis
- typing-test-4.jpg - Settings Panel

## Note

The gallery includes a fallback system that generates placeholder images if screenshot files are missing. This ensures the gallery remains functional even without actual screenshot files.

The placeholder generator creates gradient backgrounds with project names and screenshot titles, providing a professional appearance while maintaining full functionality.
