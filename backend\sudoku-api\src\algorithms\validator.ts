/**
 * Advanced Sudoku Validator
 * Comprehensive validation with detailed conflict detection
 */

import {
  SudokuGrid,
  SudokuCell,
  SudokuPosition,
  ValidationResult,
  Conflict,
  ValidationIssue,
} from '@/types';
import { log } from '@/utils/logger';

export interface ValidateOptions {
  checkCompleteness?: boolean;
  checkConflicts?: boolean;
  detailed?: boolean;
}

export class SudokuValidator {
  /**
   * Validate a Sudoku puzzle
   */
  public validate(puzzle: SudokuGrid, options: ValidateOptions = {}): ValidationResult {
    const { checkCompleteness = true, checkConflicts = true, detailed = false } = options;

    const startTime = Date.now();
    const conflicts: Conflict[] = [];
    const errors: ValidationIssue[] = [];

    try {
      // Basic structure validation
      this.validateStructure(puzzle, errors);

      // Check for conflicts if requested
      if (checkConflicts && errors.length === 0) {
        this.findConflicts(puzzle, conflicts);
      }

      // Calculate completeness
      const completeness = checkCompleteness ? this.calculateCompleteness(puzzle) : 0;

      // Add detailed analysis if requested
      if (detailed) {
        this.addDetailedAnalysis(puzzle, errors);
      }

      const isValid = errors.length === 0 && conflicts.length === 0;
      const duration = Date.now() - startTime;

      log.info('Puzzle validation completed', {
        isValid,
        conflicts: conflicts.length,
        errors: errors.length,
        completeness,
        duration,
      });

      return {
        isValid,
        conflicts,
        completeness,
        errors,
      };
    } catch (error) {
      log.error('Validation failed', error);

      errors.push({
        code: 'VALIDATION_FAILED',
        message: 'Internal validation error',
        severity: 'error',
      });

      return {
        isValid: false,
        conflicts: [],
        completeness: 0,
        errors,
      };
    }
  }

  /**
   * Validate basic puzzle structure
   */
  private validateStructure(puzzle: SudokuGrid, errors: ValidationIssue[]): void {
    // Check if puzzle is an array
    if (!Array.isArray(puzzle)) {
      errors.push({
        code: 'INVALID_TYPE',
        message: 'Puzzle must be an array',
        severity: 'error',
      });
      return;
    }

    // Check dimensions
    if (puzzle.length !== 9) {
      errors.push({
        code: 'INVALID_DIMENSIONS',
        message: 'Puzzle must have exactly 9 rows',
        severity: 'error',
      });
      return;
    }

    // Check each row
    for (let row = 0; row < 9; row++) {
      if (!Array.isArray(puzzle[row])) {
        errors.push({
          code: 'INVALID_ROW_TYPE',
          message: `Row ${row} must be an array`,
          position: { row, col: 0 },
          severity: 'error',
        });
        continue;
      }

      if (puzzle[row].length !== 9) {
        errors.push({
          code: 'INVALID_ROW_LENGTH',
          message: `Row ${row} must have exactly 9 columns`,
          position: { row, col: 0 },
          severity: 'error',
        });
        continue;
      }

      // Check each cell
      for (let col = 0; col < 9; col++) {
        const cell = puzzle[row][col];

        if (!Number.isInteger(cell)) {
          errors.push({
            code: 'INVALID_CELL_TYPE',
            message: `Cell at (${row}, ${col}) must be an integer`,
            position: { row, col },
            severity: 'error',
          });
        } else if (cell < 0 || cell > 9) {
          errors.push({
            code: 'INVALID_CELL_VALUE',
            message: `Cell at (${row}, ${col}) must be between 0 and 9, got ${cell}`,
            position: { row, col },
            severity: 'error',
          });
        }
      }
    }
  }

  /**
   * Find all conflicts in the puzzle
   */
  private findConflicts(puzzle: SudokuGrid, conflicts: Conflict[]): void {
    // Check row conflicts
    this.findRowConflicts(puzzle, conflicts);

    // Check column conflicts
    this.findColumnConflicts(puzzle, conflicts);

    // Check box conflicts
    this.findBoxConflicts(puzzle, conflicts);
  }

  /**
   * Find conflicts in rows
   */
  private findRowConflicts(puzzle: SudokuGrid, conflicts: Conflict[]): void {
    for (let row = 0; row < 9; row++) {
      const seen = new Map<SudokuCell, number[]>();

      for (let col = 0; col < 9; col++) {
        const value = puzzle[row][col];
        if (value !== 0) {
          if (!seen.has(value)) {
            seen.set(value, []);
          }
          seen.get(value)!.push(col);
        }
      }

      // Find duplicates
      for (const [value, positions] of seen.entries()) {
        if (positions.length > 1) {
          for (const col of positions) {
            conflicts.push({
              type: 'row',
              position: { row, col },
              value,
              conflictingPositions: positions.filter(c => c !== col).map(c => ({ row, col: c })),
            });
          }
        }
      }
    }
  }

  /**
   * Find conflicts in columns
   */
  private findColumnConflicts(puzzle: SudokuGrid, conflicts: Conflict[]): void {
    for (let col = 0; col < 9; col++) {
      const seen = new Map<SudokuCell, number[]>();

      for (let row = 0; row < 9; row++) {
        const value = puzzle[row][col];
        if (value !== 0) {
          if (!seen.has(value)) {
            seen.set(value, []);
          }
          seen.get(value)!.push(row);
        }
      }

      // Find duplicates
      for (const [value, positions] of seen.entries()) {
        if (positions.length > 1) {
          for (const row of positions) {
            conflicts.push({
              type: 'column',
              position: { row, col },
              value,
              conflictingPositions: positions.filter(r => r !== row).map(r => ({ row: r, col })),
            });
          }
        }
      }
    }
  }

  /**
   * Find conflicts in 3x3 boxes
   */
  private findBoxConflicts(puzzle: SudokuGrid, conflicts: Conflict[]): void {
    for (let boxRow = 0; boxRow < 3; boxRow++) {
      for (let boxCol = 0; boxCol < 3; boxCol++) {
        const seen = new Map<SudokuCell, SudokuPosition[]>();

        // Check each cell in the box
        for (let row = boxRow * 3; row < boxRow * 3 + 3; row++) {
          for (let col = boxCol * 3; col < boxCol * 3 + 3; col++) {
            const value = puzzle[row][col];
            if (value !== 0) {
              if (!seen.has(value)) {
                seen.set(value, []);
              }
              seen.get(value)!.push({ row, col });
            }
          }
        }

        // Find duplicates
        for (const [value, positions] of seen.entries()) {
          if (positions.length > 1) {
            for (const position of positions) {
              conflicts.push({
                type: 'box',
                position,
                value,
                conflictingPositions: positions.filter(
                  p => p.row !== position.row || p.col !== position.col
                ),
              });
            }
          }
        }
      }
    }
  }

  /**
   * Calculate puzzle completeness percentage
   */
  private calculateCompleteness(puzzle: SudokuGrid): number {
    const filledCells = puzzle.flat().filter(cell => cell !== 0).length;
    return Math.round((filledCells / 81) * 100 * 100) / 100; // Round to 2 decimal places
  }

  /**
   * Add detailed analysis to errors
   */
  private addDetailedAnalysis(puzzle: SudokuGrid, errors: ValidationIssue[]): void {
    // Check for empty puzzle
    const filledCells = puzzle.flat().filter(cell => cell !== 0).length;
    if (filledCells === 0) {
      errors.push({
        code: 'EMPTY_PUZZLE',
        message: 'Puzzle is completely empty',
        severity: 'warning',
      });
    }

    // Check for minimal clues
    if (filledCells < 17) {
      errors.push({
        code: 'INSUFFICIENT_CLUES',
        message: `Puzzle has only ${filledCells} clues. Minimum 17 required for unique solution`,
        severity: 'warning',
      });
    }

    // Check for too many clues
    if (filledCells > 60) {
      errors.push({
        code: 'TOO_MANY_CLUES',
        message: `Puzzle has ${filledCells} clues. This might be too easy`,
        severity: 'warning',
      });
    }

    // Check for distribution issues
    this.checkClueDistribution(puzzle, errors);
  }

  /**
   * Check clue distribution across the grid
   */
  private checkClueDistribution(puzzle: SudokuGrid, errors: ValidationIssue[]): void {
    // Check each 3x3 box for clue distribution
    for (let boxRow = 0; boxRow < 3; boxRow++) {
      for (let boxCol = 0; boxCol < 3; boxCol++) {
        let cluesInBox = 0;

        for (let row = boxRow * 3; row < boxRow * 3 + 3; row++) {
          for (let col = boxCol * 3; col < boxCol * 3 + 3; col++) {
            if (puzzle[row][col] !== 0) {
              cluesInBox++;
            }
          }
        }

        if (cluesInBox === 0) {
          errors.push({
            code: 'EMPTY_BOX',
            message: `Box ${boxRow * 3 + boxCol + 1} has no clues`,
            severity: 'warning',
          });
        } else if (cluesInBox === 9) {
          errors.push({
            code: 'FULL_BOX',
            message: `Box ${boxRow * 3 + boxCol + 1} is completely filled`,
            severity: 'warning',
          });
        }
      }
    }

    // Check rows and columns for empty/full cases
    for (let i = 0; i < 9; i++) {
      // Check row
      const rowClues = puzzle[i].filter(cell => cell !== 0).length;
      if (rowClues === 0) {
        errors.push({
          code: 'EMPTY_ROW',
          message: `Row ${i + 1} has no clues`,
          severity: 'warning',
        });
      } else if (rowClues === 9) {
        errors.push({
          code: 'FULL_ROW',
          message: `Row ${i + 1} is completely filled`,
          severity: 'warning',
        });
      }

      // Check column
      const colClues = puzzle.map(row => row[i]).filter(cell => cell !== 0).length;
      if (colClues === 0) {
        errors.push({
          code: 'EMPTY_COLUMN',
          message: `Column ${i + 1} has no clues`,
          severity: 'warning',
        });
      } else if (colClues === 9) {
        errors.push({
          code: 'FULL_COLUMN',
          message: `Column ${i + 1} is completely filled`,
          severity: 'warning',
        });
      }
    }
  }

  /**
   * Quick validation for basic checks
   */
  public quickValidate(puzzle: SudokuGrid): boolean {
    try {
      // Basic structure check
      if (!Array.isArray(puzzle) || puzzle.length !== 9) {
        return false;
      }

      for (let row = 0; row < 9; row++) {
        if (!Array.isArray(puzzle[row]) || puzzle[row].length !== 9) {
          return false;
        }

        for (let col = 0; col < 9; col++) {
          const cell = puzzle[row][col];
          if (!Number.isInteger(cell) || cell < 0 || cell > 9) {
            return false;
          }
        }
      }

      // Quick conflict check
      return !this.hasConflicts(puzzle);
    } catch {
      return false;
    }
  }

  /**
   * Check if puzzle has any conflicts
   */
  private hasConflicts(puzzle: SudokuGrid): boolean {
    // Check rows
    for (let row = 0; row < 9; row++) {
      const seen = new Set<number>();
      for (let col = 0; col < 9; col++) {
        const value = puzzle[row][col];
        if (value !== 0) {
          if (seen.has(value)) return true;
          seen.add(value);
        }
      }
    }

    // Check columns
    for (let col = 0; col < 9; col++) {
      const seen = new Set<number>();
      for (let row = 0; row < 9; row++) {
        const value = puzzle[row][col];
        if (value !== 0) {
          if (seen.has(value)) return true;
          seen.add(value);
        }
      }
    }

    // Check boxes
    for (let boxRow = 0; boxRow < 3; boxRow++) {
      for (let boxCol = 0; boxCol < 3; boxCol++) {
        const seen = new Set<number>();
        for (let row = boxRow * 3; row < boxRow * 3 + 3; row++) {
          for (let col = boxCol * 3; col < boxCol * 3 + 3; col++) {
            const value = puzzle[row][col];
            if (value !== 0) {
              if (seen.has(value)) return true;
              seen.add(value);
            }
          }
        }
      }
    }

    return false;
  }

  /**
   * Get validation summary
   */
  public getValidationSummary(result: ValidationResult): string {
    if (result.isValid) {
      return `Valid puzzle (${result.completeness}% complete)`;
    }

    const issues: string[] = [];

    if (result.errors.length > 0) {
      const errorCount = result.errors.filter(e => e.severity === 'error').length;
      const warningCount = result.errors.filter(e => e.severity === 'warning').length;

      if (errorCount > 0) issues.push(`${errorCount} error(s)`);
      if (warningCount > 0) issues.push(`${warningCount} warning(s)`);
    }

    if (result.conflicts.length > 0) {
      issues.push(`${result.conflicts.length} conflict(s)`);
    }

    return `Invalid puzzle: ${issues.join(', ')}`;
  }
}
