/**
 * Comprehensive error handling middleware
 * Handles all types of errors with proper logging and response formatting
 */

import { Request, Response, NextFunction } from 'express';
import { SudokuError, ValidationError, SolvingError, GenerationError, ApiResponse } from '@/types';
import { log } from '@/utils/logger';

/**
 * Main error handling middleware
 */
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Log the error
  log.error('Request error', error, {
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    requestId: req.id,
  });

  // Handle different error types
  if (error instanceof SudokuError) {
    handleSudokuError(error, req, res);
  } else if (error.name === 'ValidationError') {
    handleValidationError(error, req, res);
  } else if (error.name === 'SyntaxError' && 'body' in error) {
    handleJsonSyntaxError(error, req, res);
  } else if (error.name === 'UnauthorizedError') {
    handleUnauthorizedError(error, req, res);
  } else if (error.name === 'MulterError') {
    handleMulterError(error, req, res);
  } else {
    handleGenericError(error, req, res);
  }
};

/**
 * Handle Sudoku-specific errors
 */
function handleSudokuError(error: SudokuError, req: Request, res: Response): void {
  const response: ApiResponse = {
    success: false,
    error: error.message,
    timestamp: new Date().toISOString(),
    requestId: req.id,
  };

  // Add details for development
  if (process.env.NODE_ENV === 'development' && error.details) {
    response.data = { details: error.details };
  }

  res.status(error.statusCode).json(response);
}

/**
 * Handle validation errors
 */
function handleValidationError(error: Error, req: Request, res: Response): void {
  const response: ApiResponse = {
    success: false,
    error: 'Validation failed',
    timestamp: new Date().toISOString(),
    requestId: req.id,
  };

  // Extract validation details if available
  if ('details' in error && Array.isArray((error as any).details)) {
    response.data = {
      validationErrors: (error as any).details.map((detail: any) => ({
        field: detail.path?.join('.') || 'unknown',
        message: detail.message,
        value: detail.context?.value,
      })),
    };
  }

  res.status(400).json(response);
}

/**
 * Handle JSON syntax errors
 */
function handleJsonSyntaxError(error: Error, req: Request, res: Response): void {
  const response: ApiResponse = {
    success: false,
    error: 'Invalid JSON in request body',
    timestamp: new Date().toISOString(),
    requestId: req.id,
  };

  res.status(400).json(response);
}

/**
 * Handle unauthorized errors
 */
function handleUnauthorizedError(error: Error, req: Request, res: Response): void {
  const response: ApiResponse = {
    success: false,
    error: 'Unauthorized access',
    timestamp: new Date().toISOString(),
    requestId: req.id,
  };

  res.status(401).json(response);
}

/**
 * Handle file upload errors
 */
function handleMulterError(error: any, req: Request, res: Response): void {
  let message = 'File upload error';
  let statusCode = 400;

  switch (error.code) {
    case 'LIMIT_FILE_SIZE':
      message = 'File too large';
      statusCode = 413;
      break;
    case 'LIMIT_FILE_COUNT':
      message = 'Too many files';
      break;
    case 'LIMIT_UNEXPECTED_FILE':
      message = 'Unexpected file field';
      break;
    default:
      message = error.message || 'File upload error';
  }

  const response: ApiResponse = {
    success: false,
    error: message,
    timestamp: new Date().toISOString(),
    requestId: req.id,
  };

  res.status(statusCode).json(response);
}

/**
 * Handle generic errors
 */
function handleGenericError(error: Error, req: Request, res: Response): void {
  // Don't expose internal error details in production
  const message = process.env.NODE_ENV === 'production' 
    ? 'Internal server error' 
    : error.message;

  const response: ApiResponse = {
    success: false,
    error: message,
    timestamp: new Date().toISOString(),
    requestId: req.id,
  };

  // Add stack trace in development
  if (process.env.NODE_ENV === 'development') {
    response.data = { stack: error.stack };
  }

  res.status(500).json(response);
}

/**
 * Async error wrapper for route handlers
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Not found handler
 */
export const notFoundHandler = (req: Request, res: Response): void => {
  const response: ApiResponse = {
    success: false,
    error: `Route not found: ${req.method} ${req.originalUrl}`,
    timestamp: new Date().toISOString(),
    requestId: req.id,
  };

  res.status(404).json(response);
};

/**
 * Custom error classes for specific scenarios
 */
export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode: number = 500,
    public code?: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

export class BadRequestError extends ApiError {
  constructor(message: string = 'Bad Request') {
    super(message, 400, 'BAD_REQUEST');
  }
}

export class UnauthorizedError extends ApiError {
  constructor(message: string = 'Unauthorized') {
    super(message, 401, 'UNAUTHORIZED');
  }
}

export class ForbiddenError extends ApiError {
  constructor(message: string = 'Forbidden') {
    super(message, 403, 'FORBIDDEN');
  }
}

export class NotFoundError extends ApiError {
  constructor(message: string = 'Not Found') {
    super(message, 404, 'NOT_FOUND');
  }
}

export class ConflictError extends ApiError {
  constructor(message: string = 'Conflict') {
    super(message, 409, 'CONFLICT');
  }
}

export class TooManyRequestsError extends ApiError {
  constructor(message: string = 'Too Many Requests') {
    super(message, 429, 'TOO_MANY_REQUESTS');
  }
}

export class InternalServerError extends ApiError {
  constructor(message: string = 'Internal Server Error') {
    super(message, 500, 'INTERNAL_SERVER_ERROR');
  }
}

export class ServiceUnavailableError extends ApiError {
  constructor(message: string = 'Service Unavailable') {
    super(message, 503, 'SERVICE_UNAVAILABLE');
  }
}
