/**
 * CollaboCanvas WebRTC Module
 * Handles real-time peer-to-peer communication for collaborative drawing
 */

class WebRTCManager {
  constructor() {
    this.localPeerId = this.generatePeerId();
    this.roomId = null;
    this.peers = new Map();
    this.dataChannels = new Map();
    this.isHost = false;
    this.connectionState = 'disconnected';
    
    // WebRTC Configuration
    this.rtcConfig = {
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' },
        { urls: 'stun:stun2.l.google.com:19302' }
      ]
    };
    
    // Mock signaling server (in real implementation, this would be WebSocket)
    this.signalingServer = new MockSignalingServer();
    
    // Event callbacks
    this.onConnectionStateChange = null;
    this.onPeerJoined = null;
    this.onPeerLeft = null;
    this.onDataReceived = null;
    this.onCursorMove = null;
  }

  generatePeerId() {
    return 'peer_' + Math.random().toString(36).substr(2, 9);
  }

  async createRoom() {
    this.roomId = this.generateRoomId();
    this.isHost = true;
    
    try {
      this.updateConnectionState('connecting');
      
      // Register with signaling server
      await this.signalingServer.createRoom(this.roomId, this.localPeerId);
      
      // Setup signaling listeners
      this.setupSignalingListeners();
      
      this.updateConnectionState('connected');
      
      return this.roomId;
    } catch (error) {
      console.error('Failed to create room:', error);
      this.updateConnectionState('error');
      throw error;
    }
  }

  async joinRoom(roomId) {
    this.roomId = roomId;
    this.isHost = false;
    
    try {
      this.updateConnectionState('connecting');
      
      // Join room via signaling server
      const roomInfo = await this.signalingServer.joinRoom(roomId, this.localPeerId);
      
      if (!roomInfo.exists) {
        throw new Error('Room does not exist');
      }
      
      // Setup signaling listeners
      this.setupSignalingListeners();
      
      // Connect to existing peers
      for (const peerId of roomInfo.peers) {
        if (peerId !== this.localPeerId) {
          await this.connectToPeer(peerId, true);
        }
      }
      
      this.updateConnectionState('connected');
      
      return roomInfo;
    } catch (error) {
      console.error('Failed to join room:', error);
      this.updateConnectionState('error');
      throw error;
    }
  }

  async connectToPeer(peerId, initiator = false) {
    if (this.peers.has(peerId)) {
      console.log('Already connected to peer:', peerId);
      return;
    }

    try {
      const peerConnection = new RTCPeerConnection(this.rtcConfig);
      this.peers.set(peerId, peerConnection);

      // Setup peer connection event handlers
      this.setupPeerConnectionHandlers(peerId, peerConnection);

      // Create data channel for the initiator
      if (initiator) {
        const dataChannel = peerConnection.createDataChannel('canvas', {
          ordered: true
        });
        this.setupDataChannelHandlers(peerId, dataChannel);
        this.dataChannels.set(peerId, dataChannel);

        // Create and send offer
        const offer = await peerConnection.createOffer();
        await peerConnection.setLocalDescription(offer);
        
        this.signalingServer.sendSignal(this.roomId, peerId, {
          type: 'offer',
          offer: offer,
          from: this.localPeerId
        });
      }

    } catch (error) {
      console.error('Failed to connect to peer:', peerId, error);
      this.peers.delete(peerId);
    }
  }

  setupPeerConnectionHandlers(peerId, peerConnection) {
    peerConnection.onicecandidate = (event) => {
      if (event.candidate) {
        this.signalingServer.sendSignal(this.roomId, peerId, {
          type: 'ice-candidate',
          candidate: event.candidate,
          from: this.localPeerId
        });
      }
    };

    peerConnection.onconnectionstatechange = () => {
      console.log(`Peer ${peerId} connection state:`, peerConnection.connectionState);
      
      if (peerConnection.connectionState === 'disconnected' || 
          peerConnection.connectionState === 'failed') {
        this.handlePeerDisconnection(peerId);
      }
    };

    peerConnection.ondatachannel = (event) => {
      const dataChannel = event.channel;
      this.setupDataChannelHandlers(peerId, dataChannel);
      this.dataChannels.set(peerId, dataChannel);
    };
  }

  setupDataChannelHandlers(peerId, dataChannel) {
    dataChannel.onopen = () => {
      console.log(`Data channel opened with peer: ${peerId}`);
      if (this.onPeerJoined) {
        this.onPeerJoined(peerId);
      }
    };

    dataChannel.onclose = () => {
      console.log(`Data channel closed with peer: ${peerId}`);
    };

    dataChannel.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        this.handleIncomingData(peerId, data);
      } catch (error) {
        console.error('Failed to parse incoming data:', error);
      }
    };

    dataChannel.onerror = (error) => {
      console.error(`Data channel error with peer ${peerId}:`, error);
    };
  }

  setupSignalingListeners() {
    this.signalingServer.onSignal = async (signal) => {
      try {
        await this.handleSignalingMessage(signal);
      } catch (error) {
        console.error('Failed to handle signaling message:', error);
      }
    };

    this.signalingServer.onPeerJoined = (peerId) => {
      if (peerId !== this.localPeerId) {
        console.log('New peer joined:', peerId);
        // If we're the host, initiate connection
        if (this.isHost) {
          this.connectToPeer(peerId, true);
        }
      }
    };

    this.signalingServer.onPeerLeft = (peerId) => {
      console.log('Peer left:', peerId);
      this.handlePeerDisconnection(peerId);
    };
  }

  async handleSignalingMessage(signal) {
    const { type, from } = signal;
    const peerConnection = this.peers.get(from);

    switch (type) {
      case 'offer':
        await this.handleOffer(signal);
        break;
      case 'answer':
        if (peerConnection) {
          await peerConnection.setRemoteDescription(signal.answer);
        }
        break;
      case 'ice-candidate':
        if (peerConnection) {
          await peerConnection.addIceCandidate(signal.candidate);
        }
        break;
    }
  }

  async handleOffer(signal) {
    const { from, offer } = signal;
    
    // Create peer connection if it doesn't exist
    if (!this.peers.has(from)) {
      await this.connectToPeer(from, false);
    }

    const peerConnection = this.peers.get(from);
    if (peerConnection) {
      await peerConnection.setRemoteDescription(offer);
      
      const answer = await peerConnection.createAnswer();
      await peerConnection.setLocalDescription(answer);
      
      this.signalingServer.sendSignal(this.roomId, from, {
        type: 'answer',
        answer: answer,
        from: this.localPeerId
      });
    }
  }

  handleIncomingData(peerId, data) {
    switch (data.type) {
      case 'draw':
        if (this.onDataReceived) {
          this.onDataReceived(peerId, data);
        }
        break;
      case 'cursor':
        if (this.onCursorMove) {
          this.onCursorMove(peerId, data.x, data.y);
        }
        break;
      case 'chat':
        if (this.onDataReceived) {
          this.onDataReceived(peerId, data);
        }
        break;
      case 'clear':
        if (this.onDataReceived) {
          this.onDataReceived(peerId, data);
        }
        break;
    }
  }

  handlePeerDisconnection(peerId) {
    const peerConnection = this.peers.get(peerId);
    if (peerConnection) {
      peerConnection.close();
      this.peers.delete(peerId);
    }

    const dataChannel = this.dataChannels.get(peerId);
    if (dataChannel) {
      dataChannel.close();
      this.dataChannels.delete(peerId);
    }

    if (this.onPeerLeft) {
      this.onPeerLeft(peerId);
    }
  }

  // Public methods for sending data
  broadcastDrawData(drawData) {
    const message = JSON.stringify({
      type: 'draw',
      ...drawData,
      timestamp: Date.now()
    });

    this.dataChannels.forEach((channel, peerId) => {
      if (channel.readyState === 'open') {
        try {
          channel.send(message);
        } catch (error) {
          console.error(`Failed to send draw data to peer ${peerId}:`, error);
        }
      }
    });
  }

  broadcastCursorPosition(x, y) {
    const message = JSON.stringify({
      type: 'cursor',
      x: x,
      y: y,
      timestamp: Date.now()
    });

    this.dataChannels.forEach((channel, peerId) => {
      if (channel.readyState === 'open') {
        try {
          channel.send(message);
        } catch (error) {
          console.error(`Failed to send cursor data to peer ${peerId}:`, error);
        }
      }
    });
  }

  broadcastChatMessage(message) {
    const chatData = JSON.stringify({
      type: 'chat',
      message: message,
      author: this.localPeerId,
      timestamp: Date.now()
    });

    this.dataChannels.forEach((channel, peerId) => {
      if (channel.readyState === 'open') {
        try {
          channel.send(chatData);
        } catch (error) {
          console.error(`Failed to send chat message to peer ${peerId}:`, error);
        }
      }
    });
  }

  broadcastClearCanvas() {
    const message = JSON.stringify({
      type: 'clear',
      timestamp: Date.now()
    });

    this.dataChannels.forEach((channel, peerId) => {
      if (channel.readyState === 'open') {
        try {
          channel.send(message);
        } catch (error) {
          console.error(`Failed to send clear command to peer ${peerId}:`, error);
        }
      }
    });
  }

  updateConnectionState(state) {
    this.connectionState = state;
    if (this.onConnectionStateChange) {
      this.onConnectionStateChange(state);
    }
  }

  generateRoomId() {
    return Math.random().toString(36).substr(2, 8).toUpperCase();
  }

  leaveRoom() {
    // Close all peer connections
    this.peers.forEach((peerConnection, peerId) => {
      peerConnection.close();
    });
    this.peers.clear();

    // Close all data channels
    this.dataChannels.forEach((channel, peerId) => {
      channel.close();
    });
    this.dataChannels.clear();

    // Leave room on signaling server
    if (this.roomId) {
      this.signalingServer.leaveRoom(this.roomId, this.localPeerId);
    }

    this.roomId = null;
    this.isHost = false;
    this.updateConnectionState('disconnected');
  }

  getConnectedPeers() {
    return Array.from(this.peers.keys());
  }

  isConnected() {
    return this.connectionState === 'connected';
  }
}

// Mock Signaling Server (in real implementation, this would be WebSocket-based)
class MockSignalingServer {
  constructor() {
    this.rooms = new Map();
    this.onSignal = null;
    this.onPeerJoined = null;
    this.onPeerLeft = null;
  }

  async createRoom(roomId, peerId) {
    this.rooms.set(roomId, new Set([peerId]));
    console.log(`Room ${roomId} created by ${peerId}`);
    return { success: true };
  }

  async joinRoom(roomId, peerId) {
    if (!this.rooms.has(roomId)) {
      return { exists: false };
    }

    const room = this.rooms.get(roomId);
    const existingPeers = Array.from(room);
    room.add(peerId);

    console.log(`Peer ${peerId} joined room ${roomId}`);

    // Notify existing peers
    setTimeout(() => {
      if (this.onPeerJoined) {
        this.onPeerJoined(peerId);
      }
    }, 100);

    return {
      exists: true,
      peers: existingPeers
    };
  }

  sendSignal(roomId, targetPeerId, signal) {
    // In a real implementation, this would send via WebSocket
    setTimeout(() => {
      if (this.onSignal) {
        this.onSignal(signal);
      }
    }, 50);
  }

  leaveRoom(roomId, peerId) {
    const room = this.rooms.get(roomId);
    if (room) {
      room.delete(peerId);
      if (room.size === 0) {
        this.rooms.delete(roomId);
      }

      setTimeout(() => {
        if (this.onPeerLeft) {
          this.onPeerLeft(peerId);
        }
      }, 100);
    }
  }
}

export default WebRTCManager;
