/**
 * <PERSON><PERSON><PERSON> Sharp Portfolio - Main JavaScript Module
 * Handles theme switching, animations, and interactive features
 */

class PortfolioApp {
  constructor() {
    this.themeToggle = document.querySelector('.theme-toggle');
    this.themeIcon = document.querySelector('.theme-icon');
    this.projectCards = document.querySelectorAll('.project-card');
    
    this.init();
  }

  init() {
    this.setupTheme();
    this.setupAnimations();
    this.setupKeyboardNavigation();
    this.setupIntersectionObserver();
    this.setupSmoothScrolling();
    this.setupContactModal();
  }

  /**
   * Theme Management
   */
  setupTheme() {
    // Get saved theme or default to system preference
    const savedTheme = localStorage.getItem('portfolio-theme');
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    const initialTheme = savedTheme || (systemPrefersDark ? 'dark' : 'light');
    
    this.setTheme(initialTheme);
    
    // Theme toggle event listener
    this.themeToggle?.addEventListener('click', () => {
      const currentTheme = document.documentElement.getAttribute('data-theme');
      const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
      this.setTheme(newTheme);
    });

    // Listen for system theme changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
      if (!localStorage.getItem('portfolio-theme')) {
        this.setTheme(e.matches ? 'dark' : 'light');
      }
    });
  }

  setTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('portfolio-theme', theme);
    
    // Update theme toggle icon
    if (this.themeIcon) {
      this.themeIcon.textContent = theme === 'dark' ? '☀️' : '🌙';
      this.themeToggle.setAttribute('aria-label', 
        `Switch to ${theme === 'dark' ? 'light' : 'dark'} theme`
      );
    }
  }

  /**
   * Animation Setup
   */
  setupAnimations() {
    // Add animation classes to elements that should animate
    const animatedElements = document.querySelectorAll('.hero-title, .hero-description, .hero-stats');
    animatedElements.forEach((el, index) => {
      el.style.animationDelay = `${index * 0.2}s`;
    });

    // Stagger project card animations
    this.projectCards.forEach((card, index) => {
      card.style.animationDelay = `${index * 0.1}s`;
    });
  }

  /**
   * Intersection Observer for scroll animations
   */
  setupIntersectionObserver() {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');
          observer.unobserve(entry.target);
        }
      });
    }, observerOptions);

    // Observe project cards and other elements
    const elementsToObserve = document.querySelectorAll('.project-card, .section-title, .contact-links');
    elementsToObserve.forEach(el => observer.observe(el));
  }

  /**
   * Keyboard Navigation
   */
  setupKeyboardNavigation() {
    // Enhanced keyboard navigation for project cards
    this.projectCards.forEach((card, index) => {
      card.setAttribute('tabindex', '0');
      
      card.addEventListener('keydown', (e) => {
        switch (e.key) {
          case 'Enter':
          case ' ':
            e.preventDefault();
            const primaryLink = card.querySelector('.btn-primary');
            if (primaryLink) {
              primaryLink.click();
            }
            break;
          case 'ArrowRight':
          case 'ArrowDown':
            e.preventDefault();
            this.focusNextCard(index);
            break;
          case 'ArrowLeft':
          case 'ArrowUp':
            e.preventDefault();
            this.focusPreviousCard(index);
            break;
        }
      });
    });

    // Escape key to close any modals or return focus
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        document.activeElement?.blur();
      }
    });
  }

  focusNextCard(currentIndex) {
    const nextIndex = (currentIndex + 1) % this.projectCards.length;
    this.projectCards[nextIndex].focus();
  }

  focusPreviousCard(currentIndex) {
    const prevIndex = currentIndex === 0 ? this.projectCards.length - 1 : currentIndex - 1;
    this.projectCards[prevIndex].focus();
  }

  /**
   * Smooth Scrolling Enhancement
   */
  setupSmoothScrolling() {
    // Enhanced smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', (e) => {
        e.preventDefault();
        const target = document.querySelector(anchor.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });
  }

  /**
   * Contact Modal Management
   */
  setupContactModal() {
    const emailBtn = document.getElementById('emailContactBtn');
    const modal = document.getElementById('contactModal');
    const closeBtn = document.getElementById('closeContactModal');
    const copyBtn = document.getElementById('copyEmailBtn');

    // Open modal
    emailBtn?.addEventListener('click', () => {
      modal.style.display = 'flex';
      document.body.style.overflow = 'hidden';

      // Focus management for accessibility
      closeBtn?.focus();
    });

    // Close modal
    const closeModal = () => {
      modal.style.display = 'none';
      document.body.style.overflow = '';
      emailBtn?.focus(); // Return focus to trigger
    };

    closeBtn?.addEventListener('click', closeModal);

    // Close on backdrop click
    modal?.addEventListener('click', (e) => {
      if (e.target === modal) {
        closeModal();
      }
    });

    // Close on Escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && modal.style.display === 'flex') {
        closeModal();
      }
    });

    // Copy email functionality
    copyBtn?.addEventListener('click', async () => {
      const email = '<EMAIL>';
      try {
        await PortfolioUtils.copyToClipboard(email);
        copyBtn.textContent = '✅';
        copyBtn.title = 'Copied!';

        setTimeout(() => {
          copyBtn.textContent = '📋';
          copyBtn.title = 'Copy email';
        }, 2000);
      } catch (err) {
        console.error('Failed to copy email:', err);
        copyBtn.textContent = '❌';
        setTimeout(() => {
          copyBtn.textContent = '📋';
        }, 2000);
      }
    });
  }

  /**
   * Performance Monitoring
   */
  static measurePerformance() {
    if ('performance' in window) {
      window.addEventListener('load', () => {
        const perfData = performance.getEntriesByType('navigation')[0];
        const loadTime = perfData.loadEventEnd - perfData.loadEventStart;
        
        // Log performance metrics (can be sent to analytics)
        console.log('Portfolio Performance Metrics:', {
          loadTime: `${loadTime}ms`,
          domContentLoaded: `${perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart}ms`,
          firstPaint: performance.getEntriesByType('paint')[0]?.startTime || 'N/A'
        });
      });
    }
  }

  /**
   * Error Handling & User Feedback
   */
  static setupErrorHandling() {
    // Global error handler
    window.addEventListener('error', (e) => {
      console.error('Portfolio Error:', e.error);
      PortfolioApp.showErrorNotification('An unexpected error occurred. Please refresh the page.');

      // In production, send to error tracking service
      if (typeof gtag !== 'undefined') {
        gtag('event', 'exception', {
          description: e.error?.message || 'Unknown error',
          fatal: false
        });
      }
    });

    // Promise rejection handler
    window.addEventListener('unhandledrejection', (e) => {
      console.error('Unhandled Promise Rejection:', e.reason);
      PortfolioApp.showErrorNotification('A network or loading error occurred.');

      // Prevent default browser behavior
      e.preventDefault();
    });

    // Network error detection
    window.addEventListener('offline', () => {
      PortfolioApp.showErrorNotification('You are currently offline. Some features may not work.', 'warning');
    });

    window.addEventListener('online', () => {
      PortfolioApp.showSuccessNotification('Connection restored!');
    });
  }

  /**
   * Show error notification to user
   */
  static showErrorNotification(message, type = 'error') {
    const notification = PortfolioApp.createNotification(message, type);
    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove();
      }
    }, 5000);
  }

  /**
   * Show success notification to user
   */
  static showSuccessNotification(message) {
    PortfolioApp.showErrorNotification(message, 'success');
  }

  /**
   * Create notification element
   */
  static createNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
      <div class="notification-content">
        <span class="notification-icon">${type === 'error' ? '⚠️' : type === 'warning' ? '⚠️' : '✅'}</span>
        <span class="notification-message">${message}</span>
        <button class="notification-close" aria-label="Close notification">&times;</button>
      </div>
    `;

    // Add close functionality
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => notification.remove());

    return notification;
  }
}

/**
 * Utility Functions
 */
class PortfolioUtils {
  /**
   * Debounce function for performance optimization
   */
  static debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  /**
   * Check if user prefers reduced motion
   */
  static prefersReducedMotion() {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  }

  /**
   * Get system theme preference
   */
  static getSystemTheme() {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  }

  /**
   * Copy text to clipboard
   */
  static async copyToClipboard(text) {
    try {
      await navigator.clipboard.writeText(text);
      return true;
    } catch (err) {
      console.error('Failed to copy text: ', err);
      return false;
    }
  }

  /**
   * Sanitize user input to prevent XSS attacks
   */
  static sanitizeInput(input) {
    if (typeof input !== 'string') return '';

    return input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;')
      .trim();
  }

  /**
   * Validate email format
   */
  static isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate input length and content
   */
  static validateInput(input, options = {}) {
    const {
      minLength = 0,
      maxLength = 1000,
      allowEmpty = false,
      pattern = null
    } = options;

    if (!input && !allowEmpty) {
      return { valid: false, error: 'This field is required' };
    }

    if (input && input.length < minLength) {
      return { valid: false, error: `Minimum ${minLength} characters required` };
    }

    if (input && input.length > maxLength) {
      return { valid: false, error: `Maximum ${maxLength} characters allowed` };
    }

    if (pattern && input && !pattern.test(input)) {
      return { valid: false, error: 'Invalid format' };
    }

    return { valid: true, error: null };
  }

  /**
   * Secure form data processing
   */
  static processFormData(formElement) {
    const formData = new FormData(formElement);
    const processedData = {};

    for (const [key, value] of formData.entries()) {
      // Sanitize all string inputs
      if (typeof value === 'string') {
        processedData[key] = PortfolioUtils.sanitizeInput(value);
      } else {
        processedData[key] = value;
      }
    }

    return processedData;
  }

  /**
   * Rate limiting for API calls or form submissions
   */
  static createRateLimiter(maxCalls = 5, timeWindow = 60000) {
    const calls = [];

    return function() {
      const now = Date.now();

      // Remove calls outside the time window
      while (calls.length > 0 && calls[0] < now - timeWindow) {
        calls.shift();
      }

      // Check if we've exceeded the limit
      if (calls.length >= maxCalls) {
        return false;
      }

      calls.push(now);
      return true;
    };
  }
}

/**
 * Initialize the application
 */
document.addEventListener('DOMContentLoaded', () => {
  // Initialize main app
  new PortfolioApp();
  
  // Setup performance monitoring
  PortfolioApp.measurePerformance();
  
  // Setup error handling
  PortfolioApp.setupErrorHandling();
  
  // Add loaded class for CSS animations
  document.body.classList.add('loaded');
});

// Export for potential use in other modules
export { PortfolioApp, PortfolioUtils };
