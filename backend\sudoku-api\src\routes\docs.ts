/**
 * Documentation Routes
 * Swagger/OpenAPI documentation endpoints
 */

import { Router } from 'express';
import swaggerUi from 'swagger-ui-express';
import { asyncHandler } from '@/middleware/errorHandler';
import { ApiResponse } from '@/types';

const router = Router();

/**
 * Swagger/OpenAPI specification
 */
const swaggerSpec = {
  openapi: '3.0.0',
  info: {
    title: 'Sudoku Solver API',
    version: '2.0.0',
    description: 'Enterprise-grade Sudoku API with advanced algorithms and monitoring',
    contact: {
      name: 'API Support',
      email: '<EMAIL>',
    },
    license: {
      name: 'MIT',
      url: 'https://opensource.org/licenses/MIT',
    },
  },
  servers: [
    {
      url: '/api/v2',
      description: 'Version 2 API',
    },
    {
      url: '/api',
      description: 'Legacy API (v1 compatibility)',
    },
  ],
  paths: {
    '/sudoku/solve': {
      post: {
        summary: 'Solve a Sudoku puzzle',
        description: 'Solve a given Sudoku puzzle using advanced algorithms',
        tags: ['Sudoku Operations'],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                required: ['puzzle'],
                properties: {
                  puzzle: {
                    type: 'array',
                    items: {
                      type: 'array',
                      items: {
                        type: 'integer',
                        minimum: 0,
                        maximum: 9,
                      },
                      minItems: 9,
                      maxItems: 9,
                    },
                    minItems: 9,
                    maxItems: 9,
                    description: '9x9 Sudoku grid with 0 for empty cells',
                  },
                  options: {
                    type: 'object',
                    properties: {
                      method: {
                        type: 'string',
                        enum: ['logical', 'backtrack', 'advanced'],
                        default: 'advanced',
                        description: 'Solving method to use',
                      },
                      stepByStep: {
                        type: 'boolean',
                        default: false,
                        description: 'Return step-by-step solution',
                      },
                      maxIterations: {
                        type: 'integer',
                        minimum: 1,
                        maximum: 1000000,
                        default: 100000,
                        description: 'Maximum iterations before timeout',
                      },
                      timeout: {
                        type: 'integer',
                        minimum: 1000,
                        maximum: 300000,
                        default: 30000,
                        description: 'Timeout in milliseconds',
                      },
                    },
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: 'Puzzle solved successfully',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/SolveResponse',
                },
              },
            },
          },
          400: {
            description: 'Invalid puzzle format',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ErrorResponse',
                },
              },
            },
          },
        },
      },
    },
    '/sudoku/generate': {
      post: {
        summary: 'Generate a new Sudoku puzzle',
        description: 'Generate a new Sudoku puzzle with specified difficulty',
        tags: ['Sudoku Operations'],
        requestBody: {
          required: false,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  difficulty: {
                    type: 'string',
                    enum: ['easy', 'medium', 'hard', 'expert', 'evil'],
                    default: 'medium',
                    description: 'Difficulty level of the puzzle',
                  },
                  options: {
                    type: 'object',
                    properties: {
                      seed: {
                        type: 'integer',
                        minimum: 0,
                        description: 'Random seed for reproducible generation',
                      },
                      uniqueSolution: {
                        type: 'boolean',
                        default: true,
                        description: 'Ensure puzzle has unique solution',
                      },
                      symmetry: {
                        type: 'string',
                        enum: ['none', 'rotational', 'horizontal', 'vertical', 'diagonal'],
                        default: 'none',
                        description: 'Symmetry pattern for clue placement',
                      },
                    },
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: 'Puzzle generated successfully',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/GenerateResponse',
                },
              },
            },
          },
        },
      },
    },
    '/sudoku/validate': {
      post: {
        summary: 'Validate a Sudoku puzzle',
        description: 'Check if a Sudoku puzzle is valid and find conflicts',
        tags: ['Sudoku Operations'],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                required: ['puzzle'],
                properties: {
                  puzzle: {
                    $ref: '#/components/schemas/SudokuGrid',
                  },
                  options: {
                    type: 'object',
                    properties: {
                      checkCompleteness: {
                        type: 'boolean',
                        default: true,
                      },
                      checkConflicts: {
                        type: 'boolean',
                        default: true,
                      },
                      detailed: {
                        type: 'boolean',
                        default: false,
                      },
                    },
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: 'Validation completed',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/ValidateResponse',
                },
              },
            },
          },
        },
      },
    },
    '/sudoku/hint': {
      post: {
        summary: 'Get a hint for the next move',
        description: 'Get a hint for the next logical move in the puzzle',
        tags: ['Sudoku Operations'],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                required: ['puzzle'],
                properties: {
                  puzzle: {
                    $ref: '#/components/schemas/SudokuGrid',
                  },
                  options: {
                    type: 'object',
                    properties: {
                      technique: {
                        type: 'string',
                        enum: ['any', 'naked_single', 'hidden_single', 'intersection_removal'],
                        default: 'any',
                      },
                      maxHints: {
                        type: 'integer',
                        minimum: 1,
                        maximum: 10,
                        default: 1,
                      },
                    },
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: 'Hint provided',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/HintResponse',
                },
              },
            },
          },
        },
      },
    },
    '/sudoku/analyze': {
      post: {
        summary: 'Analyze a Sudoku puzzle',
        description: 'Analyze puzzle difficulty and required techniques',
        tags: ['Sudoku Operations'],
        requestBody: {
          required: true,
          content: {
            'application/json': {
              schema: {
                type: 'object',
                required: ['puzzle'],
                properties: {
                  puzzle: {
                    $ref: '#/components/schemas/SudokuGrid',
                  },
                  options: {
                    type: 'object',
                    properties: {
                      detailed: {
                        type: 'boolean',
                        default: false,
                      },
                      includeTechniques: {
                        type: 'boolean',
                        default: true,
                      },
                      estimateTime: {
                        type: 'boolean',
                        default: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
        responses: {
          200: {
            description: 'Analysis completed',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/AnalyzeResponse',
                },
              },
            },
          },
        },
      },
    },
    '/sudoku/difficulties': {
      get: {
        summary: 'Get available difficulty levels',
        description: 'List all available difficulty levels with descriptions',
        tags: ['Information'],
        responses: {
          200: {
            description: 'Difficulty levels retrieved',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/DifficultiesResponse',
                },
              },
            },
          },
        },
      },
    },
    '/sudoku/techniques': {
      get: {
        summary: 'Get available solving techniques',
        description: 'List all available solving techniques with descriptions',
        tags: ['Information'],
        responses: {
          200: {
            description: 'Techniques retrieved',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/TechniquesResponse',
                },
              },
            },
          },
        },
      },
    },
    '/sudoku/stats': {
      get: {
        summary: 'Get API usage statistics',
        description: 'Get current API usage and performance statistics',
        tags: ['Monitoring'],
        responses: {
          200: {
            description: 'Statistics retrieved',
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/StatsResponse',
                },
              },
            },
          },
        },
      },
    },
  },
  components: {
    schemas: {
      SudokuGrid: {
        type: 'array',
        items: {
          type: 'array',
          items: {
            type: 'integer',
            minimum: 0,
            maximum: 9,
          },
          minItems: 9,
          maxItems: 9,
        },
        minItems: 9,
        maxItems: 9,
        description: '9x9 Sudoku grid with 0 for empty cells',
      },
      ApiResponse: {
        type: 'object',
        properties: {
          success: {
            type: 'boolean',
            description: 'Whether the request was successful',
          },
          data: {
            type: 'object',
            description: 'Response data',
          },
          error: {
            type: 'string',
            description: 'Error message if success is false',
          },
          timestamp: {
            type: 'string',
            format: 'date-time',
            description: 'Response timestamp',
          },
          requestId: {
            type: 'string',
            description: 'Unique request identifier',
          },
        },
        required: ['success', 'timestamp', 'requestId'],
      },
      SolveResponse: {
        allOf: [
          { $ref: '#/components/schemas/ApiResponse' },
          {
            type: 'object',
            properties: {
              data: {
                type: 'object',
                properties: {
                  solved: { type: 'boolean' },
                  solution: { $ref: '#/components/schemas/SudokuGrid' },
                  difficulty: {
                    type: 'string',
                    enum: ['easy', 'medium', 'hard', 'expert', 'evil'],
                  },
                  techniques: {
                    type: 'array',
                    items: { type: 'string' },
                  },
                  statistics: {
                    type: 'object',
                    properties: {
                      iterations: { type: 'integer' },
                      backtrackCount: { type: 'integer' },
                      logicalSteps: { type: 'integer' },
                      solvingTime: { type: 'number' },
                      complexity: { type: 'number' },
                    },
                  },
                },
              },
            },
          },
        ],
      },
      GenerateResponse: {
        allOf: [
          { $ref: '#/components/schemas/ApiResponse' },
          {
            type: 'object',
            properties: {
              data: {
                type: 'object',
                properties: {
                  puzzle: { $ref: '#/components/schemas/SudokuGrid' },
                  solution: { $ref: '#/components/schemas/SudokuGrid' },
                  difficulty: {
                    type: 'string',
                    enum: ['easy', 'medium', 'hard', 'expert', 'evil'],
                  },
                  metadata: {
                    type: 'object',
                    properties: {
                      clues: { type: 'integer' },
                      uniqueSolution: { type: 'boolean' },
                      generationTime: { type: 'number' },
                      seed: { type: 'integer' },
                      requiredTechniques: {
                        type: 'array',
                        items: { type: 'string' },
                      },
                      symmetry: { type: 'string' },
                      complexity: { type: 'number' },
                    },
                  },
                },
              },
            },
          },
        ],
      },
      ValidateResponse: {
        allOf: [
          { $ref: '#/components/schemas/ApiResponse' },
          {
            type: 'object',
            properties: {
              data: {
                type: 'object',
                properties: {
                  isValid: { type: 'boolean' },
                  completeness: { type: 'number' },
                  conflicts: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        type: {
                          type: 'string',
                          enum: ['row', 'column', 'box'],
                        },
                        position: {
                          type: 'object',
                          properties: {
                            row: { type: 'integer' },
                            col: { type: 'integer' },
                          },
                        },
                        value: { type: 'integer' },
                        conflictingPositions: {
                          type: 'array',
                          items: {
                            type: 'object',
                            properties: {
                              row: { type: 'integer' },
                              col: { type: 'integer' },
                            },
                          },
                        },
                      },
                    },
                  },
                  errors: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        code: { type: 'string' },
                        message: { type: 'string' },
                        severity: {
                          type: 'string',
                          enum: ['error', 'warning'],
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        ],
      },
      HintResponse: {
        allOf: [
          { $ref: '#/components/schemas/ApiResponse' },
          {
            type: 'object',
            properties: {
              data: {
                type: 'object',
                properties: {
                  hint: {
                    type: 'object',
                    nullable: true,
                    properties: {
                      row: { type: 'integer' },
                      col: { type: 'integer' },
                      value: { type: 'integer' },
                      technique: { type: 'string' },
                      explanation: { type: 'string' },
                    },
                  },
                  available: { type: 'boolean' },
                },
              },
            },
          },
        ],
      },
      AnalyzeResponse: {
        allOf: [
          { $ref: '#/components/schemas/ApiResponse' },
          {
            type: 'object',
            properties: {
              data: {
                type: 'object',
                properties: {
                  difficulty: {
                    type: 'string',
                    enum: ['easy', 'medium', 'hard', 'expert', 'evil'],
                  },
                  solvable: { type: 'boolean' },
                  uniqueSolution: { type: 'boolean' },
                  clueCount: { type: 'integer' },
                  techniques: {
                    type: 'array',
                    items: { type: 'string' },
                  },
                  estimatedTime: { type: 'string' },
                  complexity: { type: 'number' },
                },
              },
            },
          },
        ],
      },
      DifficultiesResponse: {
        allOf: [
          { $ref: '#/components/schemas/ApiResponse' },
          {
            type: 'object',
            properties: {
              data: {
                type: 'object',
                properties: {
                  difficulties: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        level: { type: 'string' },
                        description: { type: 'string' },
                        estimatedTime: { type: 'string' },
                        clueRange: { type: 'string' },
                      },
                    },
                  },
                },
              },
            },
          },
        ],
      },
      TechniquesResponse: {
        allOf: [
          { $ref: '#/components/schemas/ApiResponse' },
          {
            type: 'object',
            properties: {
              data: {
                type: 'object',
                properties: {
                  techniques: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        name: { type: 'string' },
                        description: { type: 'string' },
                        difficulty: { type: 'string' },
                      },
                    },
                  },
                },
              },
            },
          },
        ],
      },
      StatsResponse: {
        allOf: [
          { $ref: '#/components/schemas/ApiResponse' },
          {
            type: 'object',
            properties: {
              data: {
                type: 'object',
                properties: {
                  puzzlesSolved: { type: 'integer' },
                  puzzlesGenerated: { type: 'integer' },
                  hintsProvided: { type: 'integer' },
                  validationRequests: { type: 'integer' },
                  uptime: { type: 'number' },
                  memory: {
                    type: 'object',
                    properties: {
                      rss: { type: 'integer' },
                      heapTotal: { type: 'integer' },
                      heapUsed: { type: 'integer' },
                      external: { type: 'integer' },
                    },
                  },
                },
              },
            },
          },
        ],
      },
      ErrorResponse: {
        allOf: [
          { $ref: '#/components/schemas/ApiResponse' },
          {
            type: 'object',
            properties: {
              success: {
                type: 'boolean',
                enum: [false],
              },
              error: {
                type: 'string',
                description: 'Error message',
              },
              data: {
                type: 'object',
                properties: {
                  validationErrors: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        field: { type: 'string' },
                        message: { type: 'string' },
                        value: {},
                        type: { type: 'string' },
                      },
                    },
                  },
                },
              },
            },
          },
        ],
      },
    },
  },
  tags: [
    {
      name: 'Sudoku Operations',
      description: 'Core Sudoku puzzle operations',
    },
    {
      name: 'Information',
      description: 'API information and metadata',
    },
    {
      name: 'Monitoring',
      description: 'Health checks and metrics',
    },
  ],
};

/**
 * Serve Swagger UI
 */
router.use('/', swaggerUi.serve);
router.get('/', swaggerUi.setup(swaggerSpec, {
  customCss: '.swagger-ui .topbar { display: none }',
  customSiteTitle: 'Sudoku Solver API Documentation',
  customfavIcon: '/favicon.ico',
  swaggerOptions: {
    persistAuthorization: true,
    displayRequestDuration: true,
    docExpansion: 'list',
    filter: true,
    showExtensions: true,
    showCommonExtensions: true,
  },
}));

/**
 * GET /spec
 * Get OpenAPI specification as JSON
 */
router.get('/spec', asyncHandler(async (req, res) => {
  const response: ApiResponse = {
    success: true,
    data: swaggerSpec,
    timestamp: new Date().toISOString(),
    requestId: req.id,
  };

  res.json(response);
}));

export default router;
