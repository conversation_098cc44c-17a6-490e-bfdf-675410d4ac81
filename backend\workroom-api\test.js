const http = require('http');
const assert = require('assert');
const io = require('socket.io-client');

const API_BASE = 'http://localhost:3006';
const API_URL = `${API_BASE}/api`;

// Test configuration
const TEST_TIMEOUT = 10000;
let testResults = [];
let server = null;

// Helper function to make HTTP requests
function makeRequest(method, path, data = null) {
    return new Promise((resolve, reject) => {
        const url = new URL(path, API_URL);
        const options = {
            method,
            headers: {
                'Content-Type': 'application/json'
            }
        };

        const req = http.request(url, options, (res) => {
            let body = '';
            res.on('data', chunk => body += chunk);
            res.on('end', () => {
                try {
                    const parsedBody = body ? JSON.parse(body) : {};
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: parsedBody
                    });
                } catch (error) {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: body
                    });
                }
            });
        });

        req.on('error', reject);

        if (data) {
            req.write(JSON.stringify(data));
        }

        req.end();
    });
}

// Test helper functions
function runTest(name, testFn) {
    return new Promise(async (resolve) => {
        const startTime = Date.now();
        try {
            await testFn();
            const duration = Date.now() - startTime;
            testResults.push({ name, status: 'PASS', duration, error: null });
            console.log(`✅ ${name} (${duration}ms)`);
            resolve();
        } catch (error) {
            const duration = Date.now() - startTime;
            testResults.push({ name, status: 'FAIL', duration, error: error.message });
            console.log(`❌ ${name} (${duration}ms): ${error.message}`);
            resolve();
        }
    });
}

// Test Suite
async function runTests() {
    console.log('🧪 Starting WorkRoom API Test Suite\n');

    // Test 1: API Info Endpoint
    await runTest('API Info Endpoint', async () => {
        const response = await makeRequest('GET', '/info');
        assert.strictEqual(response.statusCode, 200);
        assert.strictEqual(response.body.name, 'WorkRoom API');
        assert.strictEqual(response.body.version, '1.0.0');
        assert(Array.isArray(response.body.features));
        assert(typeof response.body.statistics === 'object');
    });

    // Test 2: Get Rooms (Empty)
    await runTest('Get Rooms - Empty State', async () => {
        const response = await makeRequest('GET', '/rooms');
        assert.strictEqual(response.statusCode, 200);
        assert.strictEqual(response.body.success, true);
        assert(Array.isArray(response.body.data));
        assert.strictEqual(response.body.total, response.body.data.length);
    });

    // Test 3: Create Room - Valid Data
    let testRoom = null;
    let testUser = null;
    let testToken = null;

    await runTest('Create Room - Valid Data', async () => {
        const roomData = {
            name: 'Test Room',
            username: 'Test User',
            password: 'testpass123'
        };

        const response = await makeRequest('POST', '/rooms', roomData);
        assert.strictEqual(response.statusCode, 200);
        assert.strictEqual(response.body.success, true);
        assert(response.body.data.room);
        assert(response.body.data.user);
        assert(response.body.data.token);

        testRoom = response.body.data.room;
        testUser = response.body.data.user;
        testToken = response.body.data.token;

        assert.strictEqual(testRoom.name, 'Test Room');
        assert.strictEqual(testRoom.hasPassword, true);
        assert.strictEqual(testRoom.memberCount, 1);
        assert.strictEqual(testUser.username, 'Test User');
    });

    // Test 4: Create Room - Invalid Data
    await runTest('Create Room - Invalid Data', async () => {
        const roomData = {
            name: '', // Empty name should fail
            username: 'Test User'
        };

        const response = await makeRequest('POST', '/rooms', roomData);
        assert.strictEqual(response.statusCode, 400);
        assert.strictEqual(response.body.success, false);
        assert(response.body.errors);
    });

    // Test 5: Get Rooms (With Data)
    await runTest('Get Rooms - With Data', async () => {
        const response = await makeRequest('GET', '/rooms');
        assert.strictEqual(response.statusCode, 200);
        assert.strictEqual(response.body.success, true);
        assert(response.body.data.length >= 1);
        
        const room = response.body.data.find(r => r.id === testRoom.id);
        assert(room);
        assert.strictEqual(room.name, 'Test Room');
        assert.strictEqual(room.hasPassword, true);
    });

    // Test 6: Get Room Details
    await runTest('Get Room Details', async () => {
        const response = await makeRequest('GET', `/rooms/${testRoom.id}`);
        assert.strictEqual(response.statusCode, 200);
        assert.strictEqual(response.body.success, true);
        assert.strictEqual(response.body.data.id, testRoom.id);
        assert.strictEqual(response.body.data.name, 'Test Room');
    });

    // Test 7: Get Non-existent Room
    await runTest('Get Non-existent Room', async () => {
        const response = await makeRequest('GET', '/rooms/nonexistent-id');
        assert.strictEqual(response.statusCode, 404);
        assert.strictEqual(response.body.success, false);
        assert.strictEqual(response.body.message, 'Room not found');
    });

    // Test 8: Join Room - Correct Password
    await runTest('Join Room - Correct Password', async () => {
        const joinData = {
            username: 'Second User',
            password: 'testpass123'
        };

        const response = await makeRequest('POST', `/rooms/${testRoom.id}/join`, joinData);
        assert.strictEqual(response.statusCode, 200);
        assert.strictEqual(response.body.success, true);
        assert(response.body.data.room);
        assert(response.body.data.user);
        assert(response.body.data.token);
        assert.strictEqual(response.body.data.user.username, 'Second User');
    });

    // Test 9: Join Room - Wrong Password
    await runTest('Join Room - Wrong Password', async () => {
        const joinData = {
            username: 'Third User',
            password: 'wrongpassword'
        };

        const response = await makeRequest('POST', `/rooms/${testRoom.id}/join`, joinData);
        assert.strictEqual(response.statusCode, 401);
        assert.strictEqual(response.body.success, false);
        assert.strictEqual(response.body.message, 'Invalid password');
    });

    // Test 10: Join Room - Invalid Data
    await runTest('Join Room - Invalid Data', async () => {
        const joinData = {
            username: '', // Empty username should fail
            password: 'testpass123'
        };

        const response = await makeRequest('POST', `/rooms/${testRoom.id}/join`, joinData);
        assert.strictEqual(response.statusCode, 400);
        assert.strictEqual(response.body.success, false);
        assert(response.body.errors);
    });

    // Test 11: Create Public Room
    await runTest('Create Public Room', async () => {
        const roomData = {
            name: 'Public Test Room',
            username: 'Public User'
            // No password = public room
        };

        const response = await makeRequest('POST', '/rooms', roomData);
        assert.strictEqual(response.statusCode, 200);
        assert.strictEqual(response.body.success, true);
        assert.strictEqual(response.body.data.room.hasPassword, false);
    });

    // Test 12: Join Public Room
    await runTest('Join Public Room', async () => {
        // First get the public room
        const roomsResponse = await makeRequest('GET', '/rooms');
        const publicRoom = roomsResponse.body.data.find(r => !r.hasPassword);
        assert(publicRoom);

        const joinData = {
            username: 'Public Joiner'
            // No password needed for public room
        };

        const response = await makeRequest('POST', `/rooms/${publicRoom.id}/join`, joinData);
        assert.strictEqual(response.statusCode, 200);
        assert.strictEqual(response.body.success, true);
    });

    // Test 13: WebSocket Connection Test
    await runTest('WebSocket Connection', async () => {
        return new Promise((resolve, reject) => {
            const client = io(API_BASE, {
                transports: ['websocket']
            });

            const timeout = setTimeout(() => {
                client.disconnect();
                reject(new Error('WebSocket connection timeout'));
            }, 5000);

            client.on('connect', () => {
                clearTimeout(timeout);
                client.disconnect();
                resolve();
            });

            client.on('connect_error', (error) => {
                clearTimeout(timeout);
                reject(error);
            });
        });
    });

    // Test 14: Rate Limiting
    await runTest('Rate Limiting', async () => {
        // Make multiple rapid requests to test rate limiting
        const promises = [];
        for (let i = 0; i < 10; i++) {
            promises.push(makeRequest('GET', '/info'));
        }

        const responses = await Promise.all(promises);
        
        // All requests should succeed (within rate limit)
        responses.forEach(response => {
            assert(response.statusCode === 200 || response.statusCode === 429);
        });
    });

    // Test 15: Security Headers
    await runTest('Security Headers', async () => {
        const response = await makeRequest('GET', '/info');
        assert.strictEqual(response.statusCode, 200);
        
        // Check for security headers
        assert(response.headers['content-security-policy']);
        assert(response.headers['x-content-type-options']);
        assert(response.headers['x-frame-options']);
    });

    // Test 16: CORS Headers
    await runTest('CORS Headers', async () => {
        const response = await makeRequest('OPTIONS', '/info');
        // OPTIONS requests should be handled properly
        assert(response.statusCode === 200 || response.statusCode === 204);
    });

    // Test 17: Input Validation
    await runTest('Input Validation - XSS Prevention', async () => {
        const roomData = {
            name: '<script>alert("xss")</script>',
            username: '<img src=x onerror=alert(1)>'
        };

        const response = await makeRequest('POST', '/rooms', roomData);
        // Should either sanitize or reject malicious input
        if (response.statusCode === 200) {
            // If accepted, should be sanitized
            assert(!response.body.data.room.name.includes('<script>'));
        } else {
            // If rejected, should return error
            assert.strictEqual(response.body.success, false);
        }
    });

    // Test 18: Large Payload Handling
    await runTest('Large Payload Handling', async () => {
        const largeString = 'A'.repeat(1000000); // 1MB string
        const roomData = {
            name: largeString,
            username: 'Test User'
        };

        const response = await makeRequest('POST', '/rooms', roomData);
        // Should handle large payloads gracefully
        assert(response.statusCode === 400 || response.statusCode === 413);
    });

    // Print test summary
    console.log('\n📊 Test Summary:');
    console.log('================');
    
    const passed = testResults.filter(t => t.status === 'PASS').length;
    const failed = testResults.filter(t => t.status === 'FAIL').length;
    const totalTime = testResults.reduce((sum, t) => sum + t.duration, 0);

    console.log(`Total Tests: ${testResults.length}`);
    console.log(`Passed: ${passed} ✅`);
    console.log(`Failed: ${failed} ❌`);
    console.log(`Total Time: ${totalTime}ms`);
    console.log(`Success Rate: ${((passed / testResults.length) * 100).toFixed(1)}%`);

    if (failed > 0) {
        console.log('\n❌ Failed Tests:');
        testResults.filter(t => t.status === 'FAIL').forEach(test => {
            console.log(`  - ${test.name}: ${test.error}`);
        });
    }

    console.log('\n🎯 Test Coverage:');
    console.log('- ✅ REST API Endpoints');
    console.log('- ✅ Authentication & Authorization');
    console.log('- ✅ Input Validation & Sanitization');
    console.log('- ✅ Error Handling');
    console.log('- ✅ Security Headers & CORS');
    console.log('- ✅ Rate Limiting');
    console.log('- ✅ WebSocket Connectivity');
    console.log('- ✅ Edge Cases & Error Conditions');

    process.exit(failed > 0 ? 1 : 0);
}

// Check if server is running
async function checkServer() {
    try {
        const response = await makeRequest('GET', '/info');
        if (response.statusCode === 200) {
            console.log('🚀 Server is running, starting tests...\n');
            return true;
        }
    } catch (error) {
        console.error('❌ Server is not running on port 3004');
        console.error('Please start the server with: npm start');
        process.exit(1);
    }
}

// Main execution
async function main() {
    await checkServer();
    await runTests();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = { runTests, makeRequest };
