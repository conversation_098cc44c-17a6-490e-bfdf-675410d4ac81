/**
 * Application Configuration
 * Centralized configuration management with environment variable support
 */

import { config } from 'dotenv';
import { AppConfig } from '@/types';

// Load environment variables
config();

/**
 * Get environment variable with type safety and default values
 */
function getEnvVar(key: string, defaultValue?: string): string {
  const value = process.env[key];
  if (value === undefined) {
    if (defaultValue !== undefined) {
      return defaultValue;
    }
    throw new Error(`Environment variable ${key} is required but not set`);
  }
  return value;
}

/**
 * Get environment variable as number
 */
function getEnvNumber(key: string, defaultValue?: number): number {
  const value = process.env[key];
  if (value === undefined) {
    if (defaultValue !== undefined) {
      return defaultValue;
    }
    throw new Error(`Environment variable ${key} is required but not set`);
  }
  const parsed = parseInt(value, 10);
  if (isNaN(parsed)) {
    throw new Error(`Environment variable ${key} must be a valid number`);
  }
  return parsed;
}

/**
 * Get environment variable as boolean
 */
function getEnvBoolean(key: string, defaultValue?: boolean): boolean {
  const value = process.env[key];
  if (value === undefined) {
    if (defaultValue !== undefined) {
      return defaultValue;
    }
    throw new Error(`Environment variable ${key} is required but not set`);
  }
  return value.toLowerCase() === 'true';
}

/**
 * Application configuration object
 */
export const appConfig: AppConfig = {
  port: getEnvNumber('PORT', 3002),
  nodeEnv: (getEnvVar('NODE_ENV', 'development') as 'development' | 'production' | 'test'),
  
  database: {
    url: getEnvVar('DATABASE_URL', 'sqlite:///app/data/sudoku.db'),
    maxConnections: getEnvNumber('DB_MAX_CONNECTIONS', 10),
    timeout: getEnvNumber('DB_TIMEOUT', 30000),
  },
  
  redis: {
    url: getEnvVar('REDIS_URL', 'redis://localhost:6379'),
    ttl: getEnvNumber('REDIS_TTL', 3600), // 1 hour
    maxRetries: getEnvNumber('REDIS_MAX_RETRIES', 3),
  },
  
  jwt: {
    secret: getEnvVar('JWT_SECRET', 'your-super-secret-jwt-key-change-in-production'),
    expiresIn: getEnvVar('JWT_EXPIRES_IN', '24h'),
    issuer: getEnvVar('JWT_ISSUER', 'sudoku-api'),
  },
  
  rateLimit: {
    windowMs: getEnvNumber('RATE_LIMIT_WINDOW_MS', 15 * 60 * 1000), // 15 minutes
    maxRequests: getEnvNumber('RATE_LIMIT_MAX_REQUESTS', 100),
    skipSuccessfulRequests: getEnvBoolean('RATE_LIMIT_SKIP_SUCCESS', false),
  },
  
  cors: {
    origin: getEnvVar('CORS_ORIGIN', 'http://localhost:3000').split(','),
    credentials: getEnvBoolean('CORS_CREDENTIALS', true),
  },
  
  logging: {
    level: (getEnvVar('LOG_LEVEL', 'info') as 'error' | 'warn' | 'info' | 'debug'),
    format: (getEnvVar('LOG_FORMAT', 'json') as 'json' | 'simple'),
    maxFiles: getEnvNumber('LOG_MAX_FILES', 14),
    maxSize: getEnvVar('LOG_MAX_SIZE', '20m'),
  },
};

/**
 * Validate configuration
 */
export function validateConfig(): void {
  const errors: string[] = [];
  
  // Validate port
  if (appConfig.port < 1 || appConfig.port > 65535) {
    errors.push('Port must be between 1 and 65535');
  }
  
  // Validate node environment
  if (!['development', 'production', 'test'].includes(appConfig.nodeEnv)) {
    errors.push('NODE_ENV must be development, production, or test');
  }
  
  // Validate JWT secret in production
  if (appConfig.nodeEnv === 'production' && appConfig.jwt.secret === 'your-super-secret-jwt-key-change-in-production') {
    errors.push('JWT_SECRET must be changed in production');
  }
  
  // Validate log level
  if (!['error', 'warn', 'info', 'debug'].includes(appConfig.logging.level)) {
    errors.push('LOG_LEVEL must be error, warn, info, or debug');
  }
  
  if (errors.length > 0) {
    throw new Error(`Configuration validation failed:\n${errors.join('\n')}`);
  }
}

/**
 * Get configuration for specific environment
 */
export function getConfig(): AppConfig {
  validateConfig();
  return appConfig;
}

/**
 * Check if running in production
 */
export const isProduction = (): boolean => appConfig.nodeEnv === 'production';

/**
 * Check if running in development
 */
export const isDevelopment = (): boolean => appConfig.nodeEnv === 'development';

/**
 * Check if running in test
 */
export const isTest = (): boolean => appConfig.nodeEnv === 'test';

/**
 * Database configuration helpers
 */
export const database = {
  isPostgres: (): boolean => appConfig.database.url.startsWith('postgres'),
  isSQLite: (): boolean => appConfig.database.url.startsWith('sqlite'),
  isMySQL: (): boolean => appConfig.database.url.startsWith('mysql'),
};

/**
 * Feature flags based on environment
 */
export const features = {
  enableMetrics: isProduction(),
  enableSwagger: !isProduction(),
  enableDebugLogs: isDevelopment(),
  enableRateLimit: isProduction(),
  enableCaching: true,
  enableAuth: false, // Can be enabled later
};

/**
 * API versioning
 */
export const api = {
  version: '2.0.0',
  prefix: '/api/v2',
  deprecatedVersions: ['1.0.0'],
  supportedVersions: ['2.0.0'],
};

/**
 * Default values for various components
 */
export const defaults = {
  pagination: {
    limit: 20,
    maxLimit: 100,
  },
  sudoku: {
    maxIterations: 100000,
    timeout: 30000, // 30 seconds
    cacheTimeout: 3600, // 1 hour
  },
  session: {
    timeout: 24 * 60 * 60 * 1000, // 24 hours
    cleanupInterval: 60 * 60 * 1000, // 1 hour
  },
};

export default appConfig;
