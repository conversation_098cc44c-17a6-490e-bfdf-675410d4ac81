/**
 * Health Check Routes
 * Comprehensive health monitoring endpoints
 */

import { Router } from 'express';
import { async<PERSON>and<PERSON> } from '@/middleware/errorHandler';
import { ApiResponse } from '@/types';
import { log } from '@/utils/logger';

const router = Router();

/**
 * GET /health
 * Basic health check endpoint
 */
router.get(
  '/',
  asyncHandler(async (req, res) => {
    const healthCheck = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: '2.0.0',
      environment: process.env.NODE_ENV || 'development',
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
    };

    const response: ApiResponse = {
      success: true,
      data: healthCheck,
      timestamp: new Date().toISOString(),
      requestId: req.id,
    };

    res.json(response);
  })
);

/**
 * GET /health/detailed
 * Detailed health check with dependency status
 */
router.get(
  '/detailed',
  asyncHandler(async (req, res) => {
    const startTime = Date.now();

    // Check various system components
    const checks = await Promise.allSettled([
      checkMemory(),
      checkDisk(),
      checkDatabase(),
      checkRedis(),
      checkExternalServices(),
    ]);

    const healthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: '2.0.0',
      environment: process.env.NODE_ENV || 'development',
      responseTime: Date.now() - startTime,
      checks: {
        memory: getCheckResult(checks[0]),
        disk: getCheckResult(checks[1]),
        database: getCheckResult(checks[2]),
        redis: getCheckResult(checks[3]),
        external: getCheckResult(checks[4]),
      },
      system: {
        memory: process.memoryUsage(),
        cpu: process.cpuUsage(),
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version,
        pid: process.pid,
      },
    };

    // Determine overall status
    const hasFailures = Object.values(healthStatus.checks).some(
      check => check.status === 'unhealthy'
    );

    if (hasFailures) {
      healthStatus.status = 'unhealthy';
    }

    const statusCode = healthStatus.status === 'healthy' ? 200 : 503;

    const response: ApiResponse = {
      success: healthStatus.status === 'healthy',
      data: healthStatus,
      timestamp: new Date().toISOString(),
      requestId: req.id,
    };

    res.status(statusCode).json(response);
  })
);

/**
 * GET /health/ready
 * Readiness probe for Kubernetes
 */
router.get(
  '/ready',
  asyncHandler(async (req, res) => {
    // Check if the application is ready to serve requests
    const ready = await checkReadiness();

    const response: ApiResponse = {
      success: ready,
      data: {
        ready,
        timestamp: new Date().toISOString(),
        message: ready ? 'Application is ready' : 'Application is not ready',
      },
      timestamp: new Date().toISOString(),
      requestId: req.id,
    };

    res.status(ready ? 200 : 503).json(response);
  })
);

/**
 * GET /health/live
 * Liveness probe for Kubernetes
 */
router.get(
  '/live',
  asyncHandler(async (req, res) => {
    // Simple liveness check
    const response: ApiResponse = {
      success: true,
      data: {
        alive: true,
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
      },
      timestamp: new Date().toISOString(),
      requestId: req.id,
    };

    res.json(response);
  })
);

/**
 * Health check helper functions
 */

async function checkMemory(): Promise<{ status: string; details: any }> {
  const memUsage = process.memoryUsage();
  const totalMemory = memUsage.heapTotal;
  const usedMemory = memUsage.heapUsed;
  const memoryUsagePercent = (usedMemory / totalMemory) * 100;

  return {
    status: memoryUsagePercent > 90 ? 'unhealthy' : 'healthy',
    details: {
      usage: memUsage,
      usagePercent: Math.round(memoryUsagePercent * 100) / 100,
      threshold: 90,
    },
  };
}

async function checkDisk(): Promise<{ status: string; details: any }> {
  try {
    const fs = require('fs');
    const stats = fs.statSync('.');

    return {
      status: 'healthy',
      details: {
        accessible: true,
        path: process.cwd(),
      },
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      details: {
        accessible: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      },
    };
  }
}

async function checkDatabase(): Promise<{ status: string; details: any }> {
  // Placeholder for database health check
  // In a real implementation, you would check database connectivity
  return {
    status: 'healthy',
    details: {
      connected: true,
      type: 'sqlite',
      responseTime: 5,
    },
  };
}

async function checkRedis(): Promise<{ status: string; details: any }> {
  // Placeholder for Redis health check
  // In a real implementation, you would check Redis connectivity
  return {
    status: 'healthy',
    details: {
      connected: true,
      responseTime: 2,
    },
  };
}

async function checkExternalServices(): Promise<{ status: string; details: any }> {
  // Placeholder for external service checks
  return {
    status: 'healthy',
    details: {
      services: [],
      allHealthy: true,
    },
  };
}

async function checkReadiness(): Promise<boolean> {
  try {
    // Check if all critical components are ready
    const checks = await Promise.all([checkMemory(), checkDatabase()]);

    return checks.every(check => check.status === 'healthy');
  } catch (error) {
    log.error('Readiness check failed', error);
    return false;
  }
}

function getCheckResult(settledResult: PromiseSettledResult<any>): any {
  if (settledResult.status === 'fulfilled') {
    return settledResult.value;
  } else {
    return {
      status: 'unhealthy',
      details: {
        error: settledResult.reason?.message || 'Unknown error',
      },
    };
  }
}

export default router;
