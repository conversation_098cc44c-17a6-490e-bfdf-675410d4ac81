/* ===== CSS CUSTOM PROPERTIES ===== */
:root {
  /* Colors - Finance Theme */
  --color-primary: #10b981;
  --color-primary-hover: #059669;
  --color-secondary: #64748b;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-danger: #ef4444;
  --color-info: #3b82f6;
  --color-income: #10b981;
  --color-expense: #ef4444;
  --color-transfer: #8b5cf6;
  
  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-card: #ffffff;
  --bg-overlay: rgba(0, 0, 0, 0.5);
  --bg-success: rgba(16, 185, 129, 0.1);
  --bg-danger: rgba(239, 68, 68, 0.1);
  --bg-warning: rgba(245, 158, 11, 0.1);
  
  /* Text Colors */
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-tertiary: #64748b;
  --text-inverse: #ffffff;
  --text-success: #065f46;
  --text-danger: #991b1b;
  --text-warning: #92400e;
  
  /* Border Colors */
  --border-primary: #e2e8f0;
  --border-secondary: #cbd5e1;
  --border-focus: #10b981;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  
  /* Typography */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-mono: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', Consolas, monospace;
  
  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  
  /* Transitions */
  --transition: 0.2s ease-in-out;
}

/* Dark Theme */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-card: #1e293b;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-tertiary: #94a3b8;
    --border-primary: #334155;
    --border-secondary: #475569;
    --bg-success: rgba(16, 185, 129, 0.2);
    --bg-danger: rgba(239, 68, 68, 0.2);
    --bg-warning: rgba(245, 158, 11, 0.2);
  }
}

[data-theme="dark"] {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-card: #1e293b;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-tertiary: #94a3b8;
  --border-primary: #334155;
  --border-secondary: #475569;
  --bg-success: rgba(16, 185, 129, 0.2);
  --bg-danger: rgba(239, 68, 68, 0.2);
  --bg-warning: rgba(245, 158, 11, 0.2);
}

/* ===== RESET & BASE STYLES ===== */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  transition: background-color var(--transition), color var(--transition);
}

/* ===== HEADER ===== */
.header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-primary);
  backdrop-filter: blur(8px);
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-4);
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  text-decoration: none;
  color: var(--text-primary);
  font-weight: 700;
  font-size: 1.25rem;
}

.logo-icon {
  font-size: 1.5rem;
}

.nav-controls {
  display: flex;
  gap: var(--space-3);
}

.theme-toggle,
.sync-btn,
.back-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  background-color: var(--bg-card);
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition);
  text-decoration: none;
}

.theme-toggle:hover,
.sync-btn:hover,
.back-link:hover {
  background-color: var(--bg-secondary);
  transform: translateY(-1px);
}

.sync-btn.syncing .sync-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* ===== BUTTONS ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-3) var(--space-6);
  border: 1px solid transparent;
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition);
  text-decoration: none;
  white-space: nowrap;
  font-family: inherit;
}

.btn-primary {
  background-color: var(--color-primary);
  color: var(--text-inverse);
  border-color: var(--color-primary);
}

.btn-primary:hover {
  background-color: var(--color-primary-hover);
}

.btn-secondary {
  background-color: transparent;
  color: var(--text-primary);
  border-color: var(--border-primary);
}

.btn-secondary:hover {
  background-color: var(--bg-secondary);
}

.btn-outline {
  background-color: transparent;
  color: var(--color-primary);
  border-color: var(--color-primary);
}

.btn-outline:hover {
  background-color: var(--color-primary);
  color: var(--text-inverse);
}

.btn-success {
  background-color: var(--color-success);
  color: var(--text-inverse);
}

.btn-danger {
  background-color: var(--color-danger);
  color: var(--text-inverse);
}

.btn-small {
  padding: var(--space-2) var(--space-4);
  font-size: 0.75rem;
}

/* ===== DASHBOARD ===== */
.dashboard {
  padding: var(--space-8) 0;
  background-color: var(--bg-secondary);
}

.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.dashboard-title {
  font-size: 2.5rem;
  font-weight: 800;
  text-align: center;
  margin-bottom: var(--space-8);
  background: linear-gradient(135deg, var(--color-primary), var(--color-info));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-12);
}

.stat-card {
  background-color: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  display: flex;
  align-items: center;
  gap: var(--space-4);
  transition: all var(--transition);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--color-primary);
}

.stat-card.balance::before { background: var(--color-info); }
.stat-card.income::before { background: var(--color-income); }
.stat-card.expenses::before { background: var(--color-expense); }
.stat-card.savings::before { background: var(--color-warning); }

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.stat-icon {
  font-size: 2.5rem;
  opacity: 0.8;
}

.stat-content h3 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: var(--space-1);
}

.stat-value {
  font-size: 1.875rem;
  font-weight: 800;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
  font-family: var(--font-mono);
}

.stat-change {
  font-size: 0.75rem;
  font-weight: 500;
}

.stat-change.positive { color: var(--color-success); }
.stat-change.negative { color: var(--color-danger); }
.stat-change.neutral { color: var(--text-tertiary); }

/* ===== CHARTS SECTION ===== */
.charts-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--space-6);
}

.chart-card {
  background-color: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
}

.chart-card h3 {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: var(--space-4);
  color: var(--text-primary);
}

.chart-card canvas {
  max-width: 100%;
  height: auto;
}

.chart-error {
  text-align: center;
  padding: var(--space-8);
  color: var(--text-secondary);
}

.chart-error .error-icon {
  font-size: 3rem;
  margin-bottom: var(--space-4);
  opacity: 0.5;
}

.chart-error h3 {
  font-size: 1.25rem;
  margin-bottom: var(--space-2);
  color: var(--text-primary);
}

.chart-error p {
  margin-bottom: var(--space-4);
  font-size: 0.875rem;
}

.empty-state {
  text-align: center;
  padding: var(--space-12);
  color: var(--text-secondary);
}

.empty-state .empty-icon {
  font-size: 4rem;
  margin-bottom: var(--space-4);
  opacity: 0.5;
}

.empty-state h3 {
  font-size: 1.5rem;
  margin-bottom: var(--space-2);
  color: var(--text-primary);
}

.empty-state p {
  margin-bottom: var(--space-6);
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* ===== SIMPLE CHARTS ===== */
.simple-chart {
  padding: var(--space-4);
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: var(--space-6);
  margin-bottom: var(--space-6);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: 0.875rem;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
}

.legend-color.income {
  background-color: var(--color-income);
}

.legend-color.expense {
  background-color: var(--color-expense);
}

.chart-bars {
  display: flex;
  justify-content: space-between;
  align-items: end;
  height: 200px;
  gap: var(--space-2);
}

.bar-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.bar-container {
  display: flex;
  align-items: end;
  gap: 2px;
  height: 180px;
  width: 100%;
  justify-content: center;
}

.bar {
  width: 20px;
  min-height: 4px;
  border-radius: 2px 2px 0 0;
  position: relative;
  display: flex;
  align-items: end;
  justify-content: center;
}

.income-bar {
  background-color: var(--color-income);
}

.expense-bar {
  background-color: var(--color-expense);
}

.bar-value {
  position: absolute;
  bottom: 100%;
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--text-secondary);
  white-space: nowrap;
  margin-bottom: 2px;
}

.bar-label {
  margin-top: var(--space-2);
  font-size: 0.75rem;
  color: var(--text-secondary);
  text-align: center;
}

/* Simple Pie Chart */
.simple-pie-chart {
  padding: var(--space-4);
}

.pie-segments {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.pie-segment {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-2);
  border-radius: var(--radius-md);
  transition: background-color var(--transition);
}

.pie-segment:hover {
  background-color: var(--bg-secondary);
}

.segment-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  flex-shrink: 0;
}

.segment-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.segment-label {
  font-weight: 500;
  color: var(--text-primary);
}

.segment-value {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-family: var(--font-mono);
}

@media (max-width: 768px) {
  .chart-bars {
    height: 150px;
  }

  .bar-container {
    height: 130px;
  }

  .bar {
    width: 16px;
  }

  .bar-value {
    font-size: 0.625rem;
  }

  .bar-label {
    font-size: 0.625rem;
  }

  .segment-info {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-1);
  }
}

/* ===== QUICK ACTIONS ===== */
.quick-actions {
  padding: var(--space-12) 0;
  background-color: var(--bg-primary);
}

.actions-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.actions-container h2 {
  font-size: 2rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: var(--space-8);
  color: var(--text-primary);
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-6);
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-8);
  background-color: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  cursor: pointer;
  transition: all var(--transition);
  text-decoration: none;
  color: var(--text-primary);
}

.action-btn:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--border-secondary);
}

.action-icon {
  font-size: 3rem;
}

.action-text {
  font-size: 1.125rem;
  font-weight: 600;
}

.income-btn:hover { border-color: var(--color-income); }
.expense-btn:hover { border-color: var(--color-expense); }
.transfer-btn:hover { border-color: var(--color-transfer); }
.goal-btn:hover { border-color: var(--color-warning); }

@media (max-width: 768px) {
  .dashboard-title {
    font-size: 2rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .charts-section {
    grid-template-columns: 1fr;
  }
  
  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .stat-card {
    padding: var(--space-4);
  }
  
  .stat-icon {
    font-size: 2rem;
  }
  
  .stat-value {
    font-size: 1.5rem;
  }
}

/* ===== TRANSACTIONS SECTION ===== */
.transactions {
  padding: var(--space-12) 0;
  background-color: var(--bg-secondary);
}

.transactions-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.transactions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-8);
  flex-wrap: wrap;
  gap: var(--space-4);
}

.transactions-header h2 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
}

.transactions-controls {
  display: flex;
  gap: var(--space-3);
  align-items: center;
  flex-wrap: wrap;
}

.filter-select {
  padding: var(--space-2) var(--space-4);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background-color: var(--bg-card);
  color: var(--text-primary);
  font-size: 0.875rem;
  font-family: inherit;
}

.transactions-list {
  background-color: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  overflow: hidden;
  margin-bottom: var(--space-6);
}

.transaction-item {
  display: flex;
  align-items: center;
  padding: var(--space-4);
  border-bottom: 1px solid var(--border-primary);
  transition: background-color var(--transition);
  cursor: pointer;
}

.transaction-item:last-child {
  border-bottom: none;
}

.transaction-item:hover {
  background-color: var(--bg-secondary);
}

.transaction-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-right: var(--space-4);
}

.transaction-icon.income {
  background-color: var(--bg-success);
  color: var(--color-income);
}

.transaction-icon.expense {
  background-color: var(--bg-danger);
  color: var(--color-expense);
}

.transaction-icon.transfer {
  background-color: var(--bg-warning);
  color: var(--color-transfer);
}

.transaction-details {
  flex: 1;
  min-width: 0;
}

.transaction-description {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.transaction-meta {
  display: flex;
  gap: var(--space-4);
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.transaction-amount {
  font-family: var(--font-mono);
  font-weight: 700;
  font-size: 1.125rem;
  text-align: right;
}

.transaction-amount.income {
  color: var(--color-income);
}

.transaction-amount.expense {
  color: var(--color-expense);
}

.transaction-amount.transfer {
  color: var(--color-transfer);
}

.transactions-pagination {
  text-align: center;
}

/* ===== GOALS SECTION ===== */
.goals {
  padding: var(--space-12) 0;
  background-color: var(--bg-primary);
}

.goals-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.goals-container h2 {
  font-size: 2rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: var(--space-8);
  color: var(--text-primary);
}

.goals-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-6);
}

.goal-card {
  background-color: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  transition: all var(--transition);
}

.goal-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.goal-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-4);
}

.goal-name {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
}

.goal-category {
  background-color: var(--color-primary);
  color: var(--text-inverse);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
}

.goal-progress {
  margin-bottom: var(--space-4);
}

.goal-amounts {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--space-2);
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.goal-progress-bar {
  width: 100%;
  height: 8px;
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.goal-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary), var(--color-success));
  transition: width 0.3s ease;
}

.goal-meta {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
  color: var(--text-tertiary);
}

/* ===== MODAL STYLES ===== */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--bg-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: var(--space-4);
}

.modal-content {
  background-color: var(--bg-card);
  border-radius: var(--radius-xl);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-xl);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-primary);
}

.modal-header h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-secondary);
  padding: var(--space-2);
  border-radius: var(--radius-md);
  transition: all var(--transition);
}

.modal-close:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.modal-form {
  padding: var(--space-6);
}

.form-group {
  margin-bottom: var(--space-4);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4);
}

.form-group label {
  display: block;
  margin-bottom: var(--space-2);
  font-weight: 600;
  color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: var(--space-3);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-family: inherit;
  font-size: 0.875rem;
  transition: border-color var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.form-actions {
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
  margin-top: var(--space-6);
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }

  .transactions-header {
    flex-direction: column;
    align-items: stretch;
  }

  .transactions-controls {
    justify-content: stretch;
  }

  .filter-select {
    flex: 1;
  }

  .transaction-item {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-3);
  }

  .transaction-meta {
    justify-content: space-between;
  }
}

/* ===== OFFLINE INDICATOR ===== */
.offline-indicator {
  position: fixed;
  bottom: var(--space-4);
  left: var(--space-4);
  right: var(--space-4);
  background-color: var(--color-warning);
  color: var(--text-inverse);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-weight: 500;
  z-index: 3000;
  animation: slideUp 0.3s ease-out;
}

.offline-icon {
  font-size: 1.25rem;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* ===== LOADING OVERLAY ===== */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--bg-overlay);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 4000;
  color: var(--text-inverse);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4);
}

.loading-overlay p {
  font-size: 1.125rem;
  font-weight: 500;
}

/* ===== FOOTER ===== */
.footer {
  padding: var(--space-8) 0;
  background-color: var(--bg-tertiary);
  text-align: center;
  border-top: 1px solid var(--border-primary);
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.footer p {
  color: var(--text-secondary);
  margin-bottom: var(--space-2);
}

.footer a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition);
}

.footer a:hover {
  color: var(--color-primary-hover);
  text-decoration: underline;
}

/* ===== UTILITY CLASSES ===== */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.text-success { color: var(--color-success); }
.text-danger { color: var(--color-danger); }
.text-warning { color: var(--color-warning); }
.text-info { color: var(--color-info); }

.bg-success { background-color: var(--bg-success); }
.bg-danger { background-color: var(--bg-danger); }
.bg-warning { background-color: var(--bg-warning); }

.hidden { display: none !important; }
.visible { display: block !important; }

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in-up {
  animation: slideInUp 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

.btn:focus,
.action-btn:focus,
.transaction-item:focus,
.modal-close:focus,
input:focus,
select:focus,
textarea:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* ===== PRINT STYLES ===== */
@media print {
  .header,
  .nav-controls,
  .quick-actions,
  .modal,
  .offline-indicator,
  .loading-overlay,
  .footer {
    display: none;
  }

  .dashboard,
  .transactions,
  .goals {
    padding: var(--space-4) 0;
  }

  .stat-card,
  .chart-card,
  .transaction-item,
  .goal-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #000;
  }

  .dashboard-title {
    color: #000;
    -webkit-text-fill-color: #000;
  }
}

/* ===== RESPONSIVE DESIGN ENHANCEMENTS ===== */
@media (max-width: 480px) {
  .nav-container {
    padding: var(--space-3);
  }

  .dashboard-container,
  .actions-container,
  .transactions-container,
  .goals-container {
    padding: 0 var(--space-3);
  }

  .modal {
    padding: var(--space-2);
  }

  .modal-content {
    max-height: 95vh;
  }

  .modal-header,
  .modal-form {
    padding: var(--space-4);
  }

  .stats-grid {
    gap: var(--space-3);
  }

  .stat-card {
    padding: var(--space-4);
    flex-direction: column;
    text-align: center;
  }

  .actions-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .action-btn {
    padding: var(--space-6);
  }
}
