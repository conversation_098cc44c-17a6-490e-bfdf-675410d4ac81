/* ===== CSS CUSTOM PROPERTIES ===== */
:root {
  /* Colors */
  --color-primary: #3b82f6;
  --color-primary-hover: #2563eb;
  --color-secondary: #64748b;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-danger: #ef4444;
  --color-accent: #8b5cf6;
  
  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-card: #ffffff;
  --bg-overlay: rgba(0, 0, 0, 0.5);
  
  /* Text Colors */
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-tertiary: #64748b;
  --text-inverse: #ffffff;
  
  /* Border Colors */
  --border-primary: #e2e8f0;
  --border-secondary: #cbd5e1;
  --border-focus: #3b82f6;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  
  /* Typography */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  
  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  
  /* Transitions */
  --transition: 0.2s ease-in-out;
}

/* Dark Theme */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-card: #1e293b;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-tertiary: #94a3b8;
    --border-primary: #334155;
    --border-secondary: #475569;
  }
}

[data-theme="dark"] {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-card: #1e293b;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-tertiary: #94a3b8;
  --border-primary: #334155;
  --border-secondary: #475569;
}

/* ===== RESET & BASE STYLES ===== */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  transition: background-color var(--transition), color var(--transition);
}

/* ===== HEADER ===== */
.header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-primary);
  backdrop-filter: blur(8px);
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-4);
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  text-decoration: none;
  color: var(--text-primary);
  font-weight: 700;
  font-size: 1.25rem;
}

.logo-icon {
  font-size: 1.5rem;
}

.nav-controls {
  display: flex;
  gap: var(--space-3);
}

.theme-toggle,
.back-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  background-color: var(--bg-card);
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition);
  text-decoration: none;
}

.theme-toggle:hover,
.back-link:hover {
  background-color: var(--bg-secondary);
  transform: translateY(-1px);
}

/* ===== BUTTONS ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-3) var(--space-6);
  border: 1px solid transparent;
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition);
  text-decoration: none;
  white-space: nowrap;
}

.btn-primary {
  background-color: var(--color-primary);
  color: var(--text-inverse);
  border-color: var(--color-primary);
}

.btn-primary:hover {
  background-color: var(--color-primary-hover);
}

.btn-secondary {
  background-color: transparent;
  color: var(--text-primary);
  border-color: var(--border-primary);
}

.btn-secondary:hover {
  background-color: var(--bg-secondary);
}

.btn-outline {
  background-color: transparent;
  color: var(--color-primary);
  border-color: var(--color-primary);
}

.btn-outline:hover {
  background-color: var(--color-primary);
  color: var(--text-inverse);
}

.btn-success {
  background-color: var(--color-success);
  color: var(--text-inverse);
}

.btn-warning {
  background-color: var(--color-warning);
  color: var(--text-inverse);
}

.btn-danger {
  background-color: var(--color-danger);
  color: var(--text-inverse);
}

.btn-small {
  padding: var(--space-2) var(--space-4);
  font-size: 0.75rem;
}

/* ===== HERO SECTION ===== */
.hero {
  padding: var(--space-20) 0;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  text-align: center;
}

.hero-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.hero-title {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: var(--space-4);
  background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-description {
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin-bottom: var(--space-8);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-actions {
  display: flex;
  gap: var(--space-4);
  justify-content: center;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-description {
    font-size: 1rem;
  }
  
  .hero-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .hero-actions .btn {
    width: 100%;
    max-width: 300px;
  }
}

/* ===== PRACTICE SESSION ===== */
.practice-session {
  padding: var(--space-16) 0;
  background-color: var(--bg-secondary);
}

.practice-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.practice-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-8);
}

.practice-header h2 {
  font-size: 2rem;
  color: var(--text-primary);
}

.practice-controls {
  display: flex;
  gap: var(--space-3);
}

.question-card {
  background-color: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  margin-bottom: var(--space-6);
  box-shadow: var(--shadow-lg);
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
}

.question-number {
  font-weight: 600;
  color: var(--text-secondary);
}

.question-category {
  background-color: var(--color-primary);
  color: var(--text-inverse);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-md);
  font-size: 0.75rem;
  font-weight: 500;
}

.question-content h3 {
  font-size: 1.5rem;
  line-height: 1.4;
  margin-bottom: var(--space-6);
  color: var(--text-primary);
}

.question-actions {
  display: flex;
  gap: var(--space-4);
  justify-content: center;
}

.answer-section {
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  margin-top: var(--space-6);
}

.answer-content h4 {
  margin-bottom: var(--space-4);
  color: var(--text-primary);
}

.answer-text {
  background-color: var(--bg-card);
  padding: var(--space-4);
  border-radius: var(--radius-md);
  border-left: 4px solid var(--color-primary);
  margin-bottom: var(--space-6);
  line-height: 1.6;
}

.answer-actions {
  display: flex;
  gap: var(--space-3);
  justify-content: center;
  flex-wrap: wrap;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-md);
  overflow: hidden;
  margin-top: var(--space-6);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
  width: 0%;
  transition: width 0.3s ease;
}

@media (max-width: 768px) {
  .practice-header {
    flex-direction: column;
    gap: var(--space-4);
    text-align: center;
  }
  
  .question-header {
    flex-direction: column;
    gap: var(--space-2);
    text-align: center;
  }
  
  .question-actions,
  .answer-actions {
    flex-direction: column;
  }
}

/* ===== FLASH CARDS SECTION ===== */
.flash-cards {
  padding: var(--space-16) 0;
  background-color: var(--bg-primary);
}

.cards-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.cards-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-8);
}

.cards-header h2 {
  font-size: 2rem;
  color: var(--text-primary);
}

.cards-controls {
  display: flex;
  gap: var(--space-4);
  align-items: center;
}

.category-filter {
  padding: var(--space-2) var(--space-4);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background-color: var(--bg-card);
  color: var(--text-primary);
  font-size: 0.875rem;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-12);
}

.flash-card {
  background-color: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  cursor: pointer;
  transition: all var(--transition);
  position: relative;
  min-height: 200px;
  display: flex;
  flex-direction: column;
}

.flash-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.flash-card.flipped .card-front {
  display: none;
}

.flash-card.flipped .card-back {
  display: block;
}

.card-front,
.card-back {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card-back {
  display: none;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-4);
}

.card-category {
  background-color: var(--color-primary);
  color: var(--text-inverse);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
}

.card-difficulty {
  font-size: 0.75rem;
  font-weight: 500;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
}

.card-difficulty.easy {
  background-color: var(--color-success);
  color: var(--text-inverse);
}

.card-difficulty.medium {
  background-color: var(--color-warning);
  color: var(--text-inverse);
}

.card-difficulty.hard {
  background-color: var(--color-danger);
  color: var(--text-inverse);
}

.card-content {
  flex: 1;
  margin-bottom: var(--space-4);
}

.card-question,
.card-answer {
  font-size: 1rem;
  line-height: 1.5;
  color: var(--text-primary);
}

.card-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-stats {
  font-size: 0.75rem;
  color: var(--text-tertiary);
}

.card-buttons {
  display: flex;
  gap: var(--space-2);
}

.card-btn {
  padding: var(--space-1) var(--space-2);
  border: none;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  cursor: pointer;
  transition: all var(--transition);
}

.card-btn.edit {
  background-color: var(--color-warning);
  color: var(--text-inverse);
}

.card-btn.delete {
  background-color: var(--color-danger);
  color: var(--text-inverse);
}

.cards-stats {
  display: flex;
  justify-content: center;
  gap: var(--space-8);
  padding: var(--space-8);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-lg);
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 800;
  color: var(--color-primary);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* ===== AI INTEGRATION SECTION ===== */
.ai-integration {
  padding: var(--space-16) 0;
  background-color: var(--bg-secondary);
}

.ai-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
  text-align: center;
}

.ai-container h2 {
  font-size: 2rem;
  margin-bottom: var(--space-8);
  color: var(--text-primary);
}

.ai-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-6);
}

.feature-card {
  background-color: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-8);
  text-align: center;
  transition: all var(--transition);
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: var(--space-4);
}

.feature-card h3 {
  font-size: 1.25rem;
  margin-bottom: var(--space-4);
  color: var(--text-primary);
}

.feature-card p {
  color: var(--text-secondary);
  margin-bottom: var(--space-6);
  line-height: 1.6;
}

/* ===== MODAL ===== */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--bg-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.modal-content {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-xl);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-primary);
}

.modal-header h3 {
  color: var(--text-primary);
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-secondary);
  padding: var(--space-2);
}

.modal-form {
  padding: var(--space-6);
}

.form-group {
  margin-bottom: var(--space-4);
}

.form-group label {
  display: block;
  margin-bottom: var(--space-2);
  font-weight: 500;
  color: var(--text-primary);
}

.form-group textarea,
.form-group select {
  width: 100%;
  padding: var(--space-3);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-family: inherit;
  resize: vertical;
}

.form-group textarea {
  min-height: 100px;
}

.form-actions {
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
  margin-top: var(--space-6);
}

/* ===== LOADING OVERLAY ===== */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--bg-overlay);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 3000;
  color: var(--text-inverse);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== FOOTER ===== */
.footer {
  padding: var(--space-8) 0;
  background-color: var(--bg-tertiary);
  text-align: center;
  border-top: 1px solid var(--border-primary);
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.footer p {
  color: var(--text-secondary);
  margin-bottom: var(--space-2);
}

.footer a {
  color: var(--color-primary);
  text-decoration: none;
}

.footer a:hover {
  text-decoration: underline;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .cards-header {
    flex-direction: column;
    gap: var(--space-4);
    text-align: center;
  }

  .cards-controls {
    flex-direction: column;
    width: 100%;
  }

  .category-filter {
    width: 100%;
  }

  .cards-grid {
    grid-template-columns: 1fr;
  }

  .cards-stats {
    flex-direction: column;
    gap: var(--space-4);
  }

  .ai-features {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }

  .form-actions .btn {
    width: 100%;
  }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

.btn:focus,
.flash-card:focus,
.modal-close:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}
