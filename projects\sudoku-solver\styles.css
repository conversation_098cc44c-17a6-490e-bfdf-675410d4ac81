/**
 * <PERSON><PERSON><PERSON> Solver & Game - Comprehensive Styling
 * Modern, responsive design with animations and accessibility
 */

/* CSS Variables for theming */
:root {
  /* Light theme colors */
  --bg-color: #ffffff;
  --text-color: #1a1a1b;
  --border-color: #d3d6da;
  --cell-bg: #ffffff;
  --cell-border: #000000;
  --cell-border-thick: #000000;
  --cell-given: #f8f9fa;
  --cell-user: #ffffff;
  --cell-selected: #bbdefb;
  --cell-highlighted: #e3f2fd;
  --cell-error: #ffebee;
  --cell-hint: #e8f5e8;
  
  /* Button colors */
  --btn-primary: #1976d2;
  --btn-primary-hover: #1565c0;
  --btn-secondary: #757575;
  --btn-secondary-hover: #616161;
  --btn-success: #388e3c;
  --btn-success-hover: #2e7d32;
  --btn-danger: #d32f2f;
  --btn-danger-hover: #c62828;
  
  /* Status colors */
  --success-color: #4caf50;
  --error-color: #f44336;
  --warning-color: #ff9800;
  --info-color: #2196f3;
  
  /* UI elements */
  --modal-bg: rgba(255, 255, 255, 0.95);
  --modal-shadow: rgba(0, 0, 0, 0.3);
  --toast-bg: #323232;
  --toast-text: #ffffff;
  
  /* Spacing */
  --gap-xs: 4px;
  --gap-sm: 8px;
  --gap-md: 12px;
  --gap-lg: 16px;
  --gap-xl: 24px;
  
  /* Border radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  
  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* Dark theme */
[data-theme="dark"] {
  --bg-color: #121212;
  --text-color: #ffffff;
  --border-color: #333333;
  --cell-bg: #1e1e1e;
  --cell-border: #555555;
  --cell-border-thick: #777777;
  --cell-given: #2a2a2a;
  --cell-user: #1e1e1e;
  --cell-selected: #1565c0;
  --cell-highlighted: #0d47a1;
  --cell-error: #d32f2f;
  --cell-hint: #2e7d32;
  --modal-bg: rgba(18, 18, 18, 0.95);
  --toast-bg: #f5f5f5;
  --toast-text: #000000;
}

/* Base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--bg-color);
  color: var(--text-color);
  line-height: 1.4;
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

.container {
  max-width: 600px;
  margin: 0 auto;
  padding: var(--gap-lg);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Back Navigation */
.back-nav {
  margin-bottom: var(--gap-xl);
}

.back-button {
  display: inline-flex;
  align-items: center;
  gap: var(--gap-sm);
  color: var(--text-color);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  padding: var(--gap-sm) var(--gap-md);
  border-radius: var(--radius-sm);
  background: var(--cell-bg);
  border: 1px solid var(--border-color);
  transition: all var(--transition-fast);
}

.back-button:hover {
  background: var(--btn-primary);
  color: white;
  transform: translateY(-1px);
}

/* Header */
.header {
  text-align: center;
  margin-bottom: var(--gap-xl);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--gap-lg);
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  letter-spacing: 0.1em;
  color: var(--btn-primary);
  flex: 1;
}

.header-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--gap-sm);
}

.timer {
  font-size: 1.5rem;
  font-weight: 600;
  font-family: 'Courier New', monospace;
  color: var(--text-color);
}

.difficulty-badge {
  background-color: var(--btn-primary);
  color: white;
  padding: var(--gap-xs) var(--gap-md);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
}

/* Controls */
.controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--gap-lg);
  flex-wrap: wrap;
  gap: var(--gap-md);
}

.control-group {
  display: flex;
  align-items: center;
  gap: var(--gap-sm);
}

.control-group label {
  font-weight: 600;
  color: var(--text-color);
}

.difficulty-select {
  background-color: var(--cell-bg);
  color: var(--text-color);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--gap-sm) var(--gap-md);
  font-size: 1rem;
  cursor: pointer;
  transition: border-color var(--transition-fast);
}

.difficulty-select:focus {
  outline: none;
  border-color: var(--btn-primary);
}

.control-buttons {
  display: flex;
  gap: var(--gap-sm);
  flex-wrap: wrap;
}

/* Buttons */
.btn {
  background-color: var(--btn-secondary);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  padding: var(--gap-sm) var(--gap-lg);
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn:hover {
  background-color: var(--btn-secondary-hover);
  transform: translateY(-1px);
}

.btn:active {
  transform: translateY(0);
}

.btn-primary {
  background-color: var(--btn-primary);
}

.btn-primary:hover {
  background-color: var(--btn-primary-hover);
}

.btn-success {
  background-color: var(--btn-success);
}

.btn-success:hover {
  background-color: var(--btn-success-hover);
}

.btn-danger {
  background-color: var(--btn-danger);
}

.btn-danger:hover {
  background-color: var(--btn-danger-hover);
}

.btn:disabled {
  background-color: var(--border-color);
  cursor: not-allowed;
  transform: none;
}

/* Game Status */
.game-status {
  display: flex;
  justify-content: space-around;
  margin-bottom: var(--gap-xl);
  padding: var(--gap-lg);
  background-color: var(--cell-given);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
}

.status-item {
  text-align: center;
}

.status-label {
  display: block;
  font-size: 0.875rem;
  color: var(--text-color);
  opacity: 0.8;
  margin-bottom: var(--gap-xs);
}

.status-value {
  display: block;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--btn-primary);
}

/* Game Container */
.game-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--gap-xl);
  flex: 1;
}

/* Sudoku Grid */
.sudoku-grid {
  display: grid;
  grid-template-columns: repeat(9, 1fr);
  grid-template-rows: repeat(9, 1fr);
  gap: 1px;
  background-color: var(--cell-border-thick);
  border: 3px solid var(--cell-border-thick);
  border-radius: var(--radius-md);
  padding: 1px;
  max-width: 450px;
  aspect-ratio: 1;
  width: 100%;
}

.sudoku-cell {
  background-color: var(--cell-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  user-select: none;
  position: relative;
}

.sudoku-cell:nth-child(3n):not(:nth-child(9n)) {
  border-right: 2px solid var(--cell-border-thick);
}

.sudoku-cell:nth-child(n+19):nth-child(-n+27),
.sudoku-cell:nth-child(n+46):nth-child(-n+54) {
  border-bottom: 2px solid var(--cell-border-thick);
}

.sudoku-cell.given {
  background-color: var(--cell-given);
  font-weight: 700;
  color: var(--text-color);
}

.sudoku-cell.user {
  background-color: var(--cell-user);
  color: var(--btn-primary);
}

.sudoku-cell.selected {
  background-color: var(--cell-selected);
  box-shadow: inset 0 0 0 2px var(--btn-primary);
}

.sudoku-cell.highlighted {
  background-color: var(--cell-highlighted);
}

.sudoku-cell.error {
  background-color: var(--cell-error);
  color: var(--error-color);
  animation: shake 0.5s ease-in-out;
}

.sudoku-cell.hint {
  background-color: var(--cell-hint);
  animation: pulse 1s ease-in-out;
}

.sudoku-cell:hover:not(.given) {
  background-color: var(--cell-highlighted);
}

/* Number Pad */
.number-pad {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: var(--gap-sm);
  max-width: 300px;
  width: 100%;
}

.number-btn {
  background-color: var(--btn-secondary);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  padding: var(--gap-lg);
  font-size: 1.25rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.number-btn:hover {
  background-color: var(--btn-secondary-hover);
  transform: translateY(-2px);
}

.number-btn:active {
  transform: translateY(0);
}

.number-btn.selected {
  background-color: var(--btn-primary);
}

.erase-btn {
  background-color: var(--btn-danger);
}

.erase-btn:hover {
  background-color: var(--btn-danger-hover);
}

/* Message Container */
.message-container {
  text-align: center;
  margin: var(--gap-lg) 0;
  min-height: 30px;
}

.message {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--info-color);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.message.show {
  opacity: 1;
}

.message.success {
  color: var(--success-color);
}

.message.error {
  color: var(--error-color);
}

.message.warning {
  color: var(--warning-color);
}

/* Modals */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--modal-shadow);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--gap-lg);
}

.modal.show {
  display: flex;
  animation: fadeIn var(--transition-normal);
}

.modal-content {
  background-color: var(--modal-bg);
  border-radius: var(--radius-lg);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px var(--modal-shadow);
  animation: slideUp var(--transition-normal);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--gap-xl);
  border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-color);
}

.close-btn {
  background: none;
  border: none;
  font-size: 2rem;
  color: var(--text-color);
  cursor: pointer;
  padding: var(--gap-sm);
  border-radius: var(--radius-md);
  transition: background-color var(--transition-fast);
}

.close-btn:hover {
  background-color: var(--border-color);
}

.modal-body {
  padding: var(--gap-xl);
}

.modal-body p {
  margin-bottom: var(--gap-md);
  line-height: 1.6;
}

.modal-body ul {
  margin: var(--gap-md) 0;
  padding-left: var(--gap-xl);
}

.modal-body li {
  margin-bottom: var(--gap-sm);
  line-height: 1.6;
}

/* Victory Modal */
.victory-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--gap-lg);
  margin-bottom: var(--gap-xl);
}

.victory-stat {
  text-align: center;
  padding: var(--gap-lg);
  background-color: var(--cell-given);
  border-radius: var(--radius-md);
}

.victory-label {
  font-size: 0.875rem;
  color: var(--text-color);
  opacity: 0.8;
  margin-bottom: var(--gap-xs);
}

.victory-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--btn-primary);
}

.victory-actions,
.game-over-actions {
  display: flex;
  gap: var(--gap-md);
  justify-content: center;
}

/* Help Modal */
.help-section {
  margin-bottom: var(--gap-xl);
}

.help-section h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: var(--gap-md);
  color: var(--btn-primary);
}

/* Settings Modal */
.setting {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--gap-lg) 0;
  border-bottom: 1px solid var(--border-color);
}

.setting:last-child {
  border-bottom: none;
}

.setting-text {
  flex: 1;
}

.setting-title {
  font-weight: 600;
  margin-bottom: var(--gap-xs);
}

.setting-description {
  font-size: 0.875rem;
  opacity: 0.8;
}

.setting-switch {
  position: relative;
}

.switch-input {
  opacity: 0;
  width: 0;
  height: 0;
}

.switch-label {
  display: block;
  width: 50px;
  height: 26px;
  background-color: var(--border-color);
  border-radius: 13px;
  cursor: pointer;
  transition: background-color var(--transition-fast);
  position: relative;
}

.switch-label::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 22px;
  height: 22px;
  background-color: white;
  border-radius: 50%;
  transition: transform var(--transition-fast);
}

.switch-input:checked + .switch-label {
  background-color: var(--btn-primary);
}

.switch-input:checked + .switch-label::after {
  transform: translateX(24px);
}

/* Action Buttons */
.action-buttons {
  position: fixed;
  bottom: var(--gap-xl);
  right: var(--gap-xl);
  display: flex;
  flex-direction: column;
  gap: var(--gap-md);
}

.icon-btn {
  background-color: var(--btn-primary);
  color: white;
  border: none;
  border-radius: 50%;
  width: 56px;
  height: 56px;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.icon-btn:hover {
  background-color: var(--btn-primary-hover);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

/* Toast Notifications */
.toast {
  position: fixed;
  bottom: 100px;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--toast-bg);
  color: var(--toast-text);
  padding: var(--gap-md) var(--gap-lg);
  border-radius: var(--radius-md);
  font-weight: 600;
  z-index: 2000;
  opacity: 0;
  pointer-events: none;
  transition: opacity var(--transition-normal);
}

.toast.show {
  opacity: 1;
  animation: toastSlide var(--transition-normal);
}

/* Animations */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }
  20%, 40%, 60%, 80% { transform: translateX(3px); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(30px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes toastSlide {
  from { 
    opacity: 0;
    transform: translateX(-50%) translateY(10px);
  }
  to { 
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: var(--gap-md);
  }
  
  .title {
    font-size: 2rem;
  }
  
  .header-content {
    flex-direction: column;
    gap: var(--gap-md);
  }
  
  .header-info {
    flex-direction: row;
    align-items: center;
    gap: var(--gap-lg);
  }
  
  .controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .control-buttons {
    justify-content: center;
  }
  
  .game-status {
    flex-direction: column;
    gap: var(--gap-md);
  }
  
  .status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .status-label,
  .status-value {
    display: inline;
    margin: 0;
  }
  
  .sudoku-grid {
    max-width: 350px;
  }
  
  .sudoku-cell {
    font-size: 1.25rem;
  }
  
  .number-pad {
    max-width: 250px;
  }
  
  .number-btn {
    padding: var(--gap-md);
    font-size: 1rem;
  }
  
  .victory-stats {
    grid-template-columns: 1fr;
  }
  
  .victory-actions,
  .game-over-actions {
    flex-direction: column;
  }
  
  .action-buttons {
    bottom: var(--gap-md);
    right: var(--gap-md);
  }
  
  .icon-btn {
    width: 48px;
    height: 48px;
  }
}

@media (max-width: 480px) {
  .sudoku-grid {
    max-width: 300px;
  }
  
  .sudoku-cell {
    font-size: 1rem;
  }
  
  .number-pad {
    max-width: 200px;
  }
  
  .number-btn {
    padding: var(--gap-sm);
    font-size: 0.875rem;
  }
  
  .modal-content {
    margin: var(--gap-md);
  }
  
  .modal-header,
  .modal-body {
    padding: var(--gap-lg);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus styles for keyboard navigation */
.sudoku-cell:focus,
.number-btn:focus,
.btn:focus,
.icon-btn:focus,
.close-btn:focus,
.difficulty-select:focus {
  outline: 2px solid var(--btn-primary);
  outline-offset: 2px;
}

/* High contrast mode adjustments */
@media (prefers-contrast: high) {
  :root {
    --border-color: #000000;
    --cell-border: #000000;
    --cell-border-thick: #000000;
  }
  
  [data-theme="dark"] {
    --border-color: #ffffff;
    --cell-border: #ffffff;
    --cell-border-thick: #ffffff;
  }
}
