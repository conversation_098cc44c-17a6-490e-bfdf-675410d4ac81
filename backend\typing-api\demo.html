<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Typing Analytics API - Professional Backend Showcase</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
            color: #f8fafc;
            min-height: 100vh;
            padding: 24px;
            font-weight: 400;
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(16px);
            border-radius: 16px;
            padding: 48px;
            box-shadow: 
                0 4px 6px -1px rgba(0, 0, 0, 0.1),
                0 2px 4px -1px rgba(0, 0, 0, 0.06),
                0 0 0 1px rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
        }

        .header h1 {
            font-size: 2.75rem;
            font-weight: 700;
            margin-bottom: 16px;
            color: #f8fafc;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            letter-spacing: -0.025em;
        }

        .header p {
            font-size: 1.125rem;
            color: #cbd5e1;
            font-weight: 400;
            line-height: 1.7;
            max-width: 600px;
            margin: 0 auto;
        }

        .backend-showcase {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .showcase-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .showcase-header h3 {
            font-size: 1.875rem;
            font-weight: 600;
            margin-bottom: 12px;
            color: #f8fafc;
            letter-spacing: -0.025em;
        }

        .architecture-overview {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }

        .arch-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 25px;
        }

        .arch-section h4 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: #f8fafc;
            display: flex;
            align-items: center;
            gap: 12px;
            letter-spacing: -0.025em;
        }

        .arch-diagram {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .arch-layer {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 8px;
            padding: 18px;
            border-left: 3px solid #3b82f6;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .arch-layer h5 {
            font-size: 0.95rem;
            font-weight: 600;
            margin-bottom: 8px;
            color: #f8fafc;
            letter-spacing: -0.025em;
        }

        .arch-layer p {
            font-size: 0.875rem;
            color: #cbd5e1;
            line-height: 1.6;
            margin: 0;
            font-weight: 400;
        }

        .api-showcase {
            grid-column: span 2;
        }

        .endpoint-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .endpoint-card {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 12px;
            padding: 24px;
            border: 1px solid rgba(255, 255, 255, 0.12);
            backdrop-filter: blur(8px);
            box-shadow: 
                0 4px 6px -1px rgba(0, 0, 0, 0.1),
                0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.2s ease-in-out;
        }

        .endpoint-card:hover {
            background: rgba(255, 255, 255, 0.12);
            border-color: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 
                0 10px 15px -3px rgba(0, 0, 0, 0.1),
                0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        .endpoint-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 15px;
        }

        .method-badge {
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 600;
            color: #ffffff;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            border: 1px solid rgba(255, 255, 255, 0.2);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', monospace;
        }

        .method-post { 
            background: #10b981;
            color: #ffffff;
        }
        .method-get { 
            background: #3b82f6;
            color: #ffffff;
        }

        .endpoint-path {
            font-family: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', Consolas, 'Courier New', monospace;
            font-size: 0.9rem;
            color: #a78bfa;
            font-weight: 500;
            background: rgba(0, 0, 0, 0.3);
            padding: 8px 12px;
            border-radius: 6px;
            display: inline-block;
            border: 1px solid rgba(167, 139, 250, 0.3);
            letter-spacing: -0.025em;
        }

        .endpoint-description {
            color: #cbd5e1;
            font-size: 0.9rem;
            margin-bottom: 15px;
            line-height: 1.6;
        }

        .endpoint-features {
            list-style: none;
            padding: 0;
        }

        .endpoint-features li {
            color: #94a3b8;
            font-size: 0.85rem;
            margin-bottom: 6px;
            padding-left: 20px;
            position: relative;
        }

        .endpoint-features li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #10b981;
            font-weight: bold;
        }

        .demo-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
        }

        .demo-section h3 {
            font-size: 1.75rem;
            font-weight: 600;
            margin-bottom: 24px;
            color: #f8fafc;
            letter-spacing: -0.025em;
        }

        .demo-instructions {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }

        .demo-instructions h4 {
            color: #f8fafc;
            font-weight: 600;
            font-size: 1.125rem;
            margin-bottom: 16px;
            letter-spacing: -0.025em;
        }

        .demo-instructions ol {
            padding-left: 20px;
            color: #cbd5e1;
        }

        .demo-instructions li {
            margin-bottom: 8px;
            line-height: 1.6;
        }

        .demo-instructions code {
            background: rgba(0, 0, 0, 0.4);
            padding: 4px 8px;
            border-radius: 4px;
            font-family: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', Consolas, 'Courier New', monospace;
            color: #a78bfa;
            border: 1px solid rgba(167, 139, 250, 0.3);
            font-size: 0.875rem;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.875rem;
            transition: all 0.2s ease-in-out;
            margin: 8px;
            border: 1px solid transparent;
            cursor: pointer;
            letter-spacing: -0.025em;
        }

        .btn-primary {
            background: #3b82f6;
            color: #ffffff;
            border-color: #3b82f6;
        }

        .btn-primary:hover {
            background: #2563eb;
            border-color: #2563eb;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.08);
            color: #f8fafc;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.12);
            border-color: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        .server-status {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        @media (max-width: 768px) {
            .container {
                padding: 24px;
                margin: 12px;
            }
            
            .architecture-overview {
                grid-template-columns: 1fr;
            }
            
            .endpoint-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚡ Typing Analytics API</h1>
            <p>Advanced algorithmic backend with performance tracking, leaderboards, and comprehensive analytics</p>
        </div>

        <div class="backend-showcase">
            <div class="showcase-header">
                <h3>🏗️ Professional Backend Architecture</h3>
                <p>Complete Node.js API showcasing advanced algorithms and mathematical analysis</p>
            </div>

            <div class="architecture-overview">
                <div class="arch-section">
                    <h4>📊 System Architecture</h4>
                    <div class="arch-diagram">
                        <div class="arch-layer">
                            <h5>API Layer</h5>
                            <p>RESTful endpoints with comprehensive error handling and validation</p>
                        </div>
                        <div class="arch-layer">
                            <h5>Analytics Engine</h5>
                            <p>Real-time performance calculations: WPM, accuracy, error analysis</p>
                        </div>
                        <div class="arch-layer">
                            <h5>Leaderboard System</h5>
                            <p>Dynamic ranking with difficulty-based categorization</p>
                        </div>
                        <div class="arch-layer">
                            <h5>Data Layer</h5>
                            <p>In-memory storage with statistical aggregation</p>
                        </div>
                    </div>
                </div>

                <div class="arch-section">
                    <h4>🔧 Technical Features</h4>
                    <div class="arch-diagram">
                        <div class="arch-layer">
                            <h5>Performance Metrics</h5>
                            <p>WPM calculation, accuracy analysis, error tracking</p>
                        </div>
                        <div class="arch-layer">
                            <h5>Text Management</h5>
                            <p>Categorized samples with difficulty levels</p>
                        </div>
                        <div class="arch-layer">
                            <h5>User Analytics</h5>
                            <p>Personal statistics and progress tracking</p>
                        </div>
                        <div class="arch-layer">
                            <h5>Global Statistics</h5>
                            <p>Aggregated analytics and trend analysis</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="api-showcase">
                <h4>🚀 API Endpoints</h4>
                <div class="endpoint-grid">
                    <div class="endpoint-card">
                        <div class="endpoint-header">
                            <span class="method-badge method-get">GET</span>
                            <span class="endpoint-path">/api/texts</span>
                        </div>
                        <p class="endpoint-description">Retrieve available text samples with filtering options</p>
                        <ul class="endpoint-features">
                            <li>Filter by difficulty (easy, medium, hard)</li>
                            <li>Filter by category (programming, technical, etc.)</li>
                            <li>Metadata with available options</li>
                        </ul>
                    </div>

                    <div class="endpoint-card">
                        <div class="endpoint-header">
                            <span class="method-badge method-get">GET</span>
                            <span class="endpoint-path">/api/texts/:id</span>
                        </div>
                        <p class="endpoint-description">Get specific text sample by ID</p>
                        <ul class="endpoint-features">
                            <li>Individual text retrieval</li>
                            <li>Difficulty and category information</li>
                            <li>Character count and metadata</li>
                        </ul>
                    </div>

                    <div class="endpoint-card">
                        <div class="endpoint-header">
                            <span class="method-badge method-post">POST</span>
                            <span class="endpoint-path">/api/submit</span>
                        </div>
                        <p class="endpoint-description">Submit typing test results for analysis</p>
                        <ul class="endpoint-features">
                            <li>WPM and accuracy calculation</li>
                            <li>Error analysis and tracking</li>
                            <li>Automatic leaderboard updates</li>
                        </ul>
                    </div>

                    <div class="endpoint-card">
                        <div class="endpoint-header">
                            <span class="method-badge method-get">GET</span>
                            <span class="endpoint-path">/api/leaderboard</span>
                        </div>
                        <p class="endpoint-description">Get top performers with ranking system</p>
                        <ul class="endpoint-features">
                            <li>Sorted by WPM and accuracy</li>
                            <li>Difficulty-based filtering</li>
                            <li>Configurable result limits</li>
                        </ul>
                    </div>

                    <div class="endpoint-card">
                        <div class="endpoint-header">
                            <span class="method-badge method-get">GET</span>
                            <span class="endpoint-path">/api/analytics</span>
                        </div>
                        <p class="endpoint-description">Comprehensive global analytics and statistics</p>
                        <ul class="endpoint-features">
                            <li>Global performance metrics</li>
                            <li>Difficulty-based breakdowns</li>
                            <li>Recent test history</li>
                        </ul>
                    </div>

                    <div class="endpoint-card">
                        <div class="endpoint-header">
                            <span class="method-badge method-get">GET</span>
                            <span class="endpoint-path">/api/user/:username/stats</span>
                        </div>
                        <p class="endpoint-description">Individual user performance statistics</p>
                        <ul class="endpoint-features">
                            <li>Personal best records</li>
                            <li>Average performance metrics</li>
                            <li>Test history and progress</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>🧪 Live Demo Instructions</h3>
            <p>Experience the complete typing analytics system with interactive test interface</p>

            <div class="server-status">
                <span class="status-indicator"></span>
                <strong>Server Status:</strong> <span id="serverStatus">Checking...</span>
            </div>

            <div class="demo-instructions">
                <h4>How to Test the Full Application:</h4>
                <ol>
                    <li>Navigate to project: <code>cd backend/typing-api</code></li>
                    <li>Install dependencies: <code>npm install</code></li>
                    <li>Start the server: <code>npm start</code></li>
                    <li>API will be available at: <code>http://localhost:3003</code></li>
                    <li>Click "Launch Interactive Demo" below to test the full typing application!</li>
                </ol>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <a href="./public/index.html" target="_blank" class="btn btn-primary">🎮 Launch Interactive Demo</a>
                <button class="btn btn-secondary" onclick="testAPI()">🚀 Test API Connection</button>
                <button class="btn btn-secondary" onclick="loadTexts()">📝 Load Text Samples</button>
                <button class="btn btn-secondary" onclick="viewLeaderboard()">🏆 View Leaderboard</button>
                <button class="btn btn-secondary" onclick="getAnalytics()">📊 Get Analytics</button>
            </div>

            <div id="apiResults" style="background: rgba(0,0,0,0.3); border-radius: 8px; padding: 20px; margin-top: 20px; display: none;">
                <h4 style="color: #f8fafc; margin-bottom: 15px;">API Response:</h4>
                <pre id="responseData" style="color: #a78bfa; font-family: monospace; white-space: pre-wrap; font-size: 0.9rem;"></pre>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px; padding-top: 30px; border-top: 1px solid rgba(255,255,255,0.1);">
            <p style="color: #94a3b8; font-size: 0.9rem;">
                Built with Node.js, Express, and advanced algorithmic analysis •
                <a href="https://github.com/yourusername/portfolio-eight/tree/main/backend/typing-api" style="color: #3b82f6;">View Source Code</a>
            </p>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3003/api';

        // Check server status on page load
        window.addEventListener('load', checkServerStatus);

        async function checkServerStatus() {
            try {
                const response = await fetch(`${API_BASE}/info`);
                if (response.ok) {
                    document.getElementById('serverStatus').textContent = 'Online ✅';
                    document.getElementById('serverStatus').style.color = '#10b981';
                } else {
                    throw new Error('Server not responding');
                }
            } catch (error) {
                document.getElementById('serverStatus').textContent = 'Offline ❌ (Start server with: npm start)';
                document.getElementById('serverStatus').style.color = '#ef4444';
            }
        }

        async function testAPI() {
            showResults('Testing API connection...');
            try {
                const response = await fetch(`${API_BASE}/info`);
                const data = await response.json();
                showResults(JSON.stringify(data, null, 2));
            } catch (error) {
                showResults(`Error: ${error.message}\n\nMake sure the server is running on port 3003`);
            }
        }

        async function loadTexts() {
            showResults('Loading text samples...');
            try {
                const response = await fetch(`${API_BASE}/texts`);
                const data = await response.json();
                showResults(JSON.stringify(data, null, 2));
            } catch (error) {
                showResults(`Error: ${error.message}`);
            }
        }

        async function viewLeaderboard() {
            showResults('Loading leaderboard...');
            try {
                const response = await fetch(`${API_BASE}/leaderboard?limit=5`);
                const data = await response.json();
                showResults(JSON.stringify(data, null, 2));
            } catch (error) {
                showResults(`Error: ${error.message}`);
            }
        }

        async function getAnalytics() {
            showResults('Loading analytics...');
            try {
                const response = await fetch(`${API_BASE}/analytics`);
                const data = await response.json();
                showResults(JSON.stringify(data, null, 2));
            } catch (error) {
                showResults(`Error: ${error.message}`);
            }
        }

        function showResults(data) {
            const resultsDiv = document.getElementById('apiResults');
            const responseData = document.getElementById('responseData');
            responseData.textContent = data;
            resultsDiv.style.display = 'block';
            resultsDiv.scrollIntoView({ behavior: 'smooth' });
        }
    </script>
</body>
</html>
