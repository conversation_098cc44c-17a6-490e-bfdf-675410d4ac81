<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WorkRoom API - Enterprise Collaboration Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
            color: #f8fafc;
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 16px;
            color: #f8fafc;
            letter-spacing: -0.025em;
        }

        .header .subtitle {
            font-size: 1.25rem;
            color: #cbd5e1;
            font-weight: 400;
            margin-bottom: 8px;
        }

        .header .description {
            font-size: 1.125rem;
            color: #94a3b8;
            max-width: 600px;
            margin: 0 auto;
        }

        .hero-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(16px);
            border-radius: 12px;
            padding: 24px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #3b82f6;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 0.875rem;
            color: #cbd5e1;
            font-weight: 500;
        }

        .section {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(16px);
            border-radius: 16px;
            padding: 40px;
            margin-bottom: 40px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 
                0 4px 6px -1px rgba(0, 0, 0, 0.1),
                0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .section h2 {
            font-size: 1.875rem;
            font-weight: 600;
            margin-bottom: 24px;
            color: #f8fafc;
            letter-spacing: -0.025em;
        }

        .section h3 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 16px;
            color: #f8fafc;
        }

        .section h4 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 12px;
            color: #f8fafc;
        }

        .section p {
            color: #cbd5e1;
            margin-bottom: 16px;
            font-size: 1rem;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-top: 32px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 24px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .feature-icon {
            font-size: 2rem;
            margin-bottom: 12px;
        }

        .feature-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #f8fafc;
            margin-bottom: 8px;
        }

        .feature-description {
            color: #94a3b8;
            font-size: 0.875rem;
            line-height: 1.5;
        }

        .arch-section {
            margin: 32px 0;
        }

        .arch-diagram {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 24px;
        }

        .arch-layer {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid #3b82f6;
        }

        .arch-layer h5 {
            color: #3b82f6;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .arch-layer p {
            color: #cbd5e1;
            font-size: 0.875rem;
            margin: 0;
        }

        .api-showcase {
            margin: 32px 0;
        }

        .endpoint-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 24px;
        }

        .endpoint-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .endpoint-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
        }

        .method-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .method-post {
            background: #10b981;
            color: #ffffff;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .method-get {
            background: #3b82f6;
            color: #ffffff;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .method-websocket {
            background: #8b5cf6;
            color: #ffffff;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .endpoint-path {
            font-family: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', Consolas, 'Courier New', monospace;
            background: rgba(0, 0, 0, 0.3);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.875rem;
            color: #f8fafc;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .endpoint-description {
            color: #cbd5e1;
            margin-bottom: 12px;
            font-size: 0.875rem;
        }

        .endpoint-features {
            list-style: none;
            padding: 0;
        }

        .endpoint-features li {
            color: #94a3b8;
            font-size: 0.8125rem;
            margin-bottom: 4px;
            padding-left: 16px;
            position: relative;
        }

        .endpoint-features li::before {
            content: '•';
            color: #3b82f6;
            position: absolute;
            left: 0;
        }

        .demo-section {
            text-align: center;
        }

        .server-status {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: rgba(255, 255, 255, 0.08);
            padding: 12px 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #ef4444;
            animation: pulse 2s infinite;
        }

        .status-indicator.online {
            background: #10b981;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .demo-instructions {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 24px;
            margin: 24px 0;
            text-align: left;
        }

        .demo-instructions h4 {
            color: #f8fafc;
            margin-bottom: 16px;
        }

        .demo-instructions ol {
            color: #cbd5e1;
            padding-left: 20px;
        }

        .demo-instructions li {
            margin-bottom: 8px;
        }

        .demo-instructions code {
            background: rgba(0, 0, 0, 0.3);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', Consolas, 'Courier New', monospace;
            color: #f8fafc;
            font-size: 0.875rem;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.875rem;
            transition: all 0.2s ease-in-out;
            margin: 8px;
            border: 1px solid transparent;
            cursor: pointer;
            letter-spacing: -0.025em;
        }

        .btn-primary {
            background: #3b82f6;
            color: #ffffff;
            border-color: #3b82f6;
        }

        .btn-primary:hover {
            background: #2563eb;
            border-color: #2563eb;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.08);
            color: #f8fafc;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.12);
            border-color: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        .api-response {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
            font-family: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', Consolas, 'Courier New', monospace;
            font-size: 0.875rem;
            color: #f8fafc;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px 15px;
            }
            
            .header h1 {
                font-size: 2.5rem;
            }
            
            .hero-stats {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .features-grid,
            .endpoint-grid {
                grid-template-columns: 1fr;
            }
            
            .section {
                padding: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏢 WorkRoom API</h1>
            <div class="subtitle">Enterprise Collaboration Platform</div>
            <div class="description">
                Real-time collaborative workspace with advanced team productivity features, 
                secure room management, and professional-grade WebSocket architecture.
            </div>
            
            <div class="hero-stats">
                <div class="stat-card">
                    <div class="stat-value" id="totalRooms">0</div>
                    <div class="stat-label">Active Rooms</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="activeUsers">0</div>
                    <div class="stat-label">Online Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="totalConnections">0</div>
                    <div class="stat-label">Live Connections</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">WebSocket</div>
                    <div class="stat-label">Real-Time Protocol</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🚀 Enterprise Features</h2>
            <p>Professional-grade collaboration tools designed for modern teams and remote work environments.</p>

            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">💬</div>
                    <div class="feature-title">Real-Time Chat</div>
                    <div class="feature-description">
                        Instant messaging with typing indicators, message history, and emoji support.
                        WebSocket-powered for zero-latency communication.
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">🔒</div>
                    <div class="feature-title">Secure Room Management</div>
                    <div class="feature-description">
                        Password-protected rooms with JWT authentication, rate limiting, and
                        enterprise-grade security middleware.
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">✅</div>
                    <div class="feature-title">Collaborative Task Management</div>
                    <div class="feature-description">
                        Shared to-do lists with priority levels, assignments, and real-time updates.
                        Perfect for agile teams and project coordination.
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">🎨</div>
                    <div class="feature-title">Interactive Whiteboard</div>
                    <div class="feature-description">
                        Collaborative drawing and brainstorming with real-time synchronization.
                        Ideal for design sessions and visual planning.
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">⏱️</div>
                    <div class="feature-title">Focus Timer (Pomodoro)</div>
                    <div class="feature-description">
                        Synchronized team focus sessions with customizable durations.
                        Boost productivity with shared accountability.
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">👥</div>
                    <div class="feature-title">User Presence & Status</div>
                    <div class="feature-description">
                        Real-time user status tracking (online, away, busy) with connection management
                        and automatic cleanup.
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🏗️ Technical Architecture</h2>
            <p>Built with modern technologies and enterprise-grade patterns for scalability and performance.</p>

            <div class="arch-section">
                <h4>⚡ Core Technologies</h4>
                <div class="arch-diagram">
                    <div class="arch-layer">
                        <h5>WebSocket Communication</h5>
                        <p>Socket.IO for real-time bidirectional communication with automatic fallbacks</p>
                    </div>
                    <div class="arch-layer">
                        <h5>JWT Authentication</h5>
                        <p>Secure token-based authentication with bcrypt password hashing</p>
                    </div>
                    <div class="arch-layer">
                        <h5>Express.js Backend</h5>
                        <p>RESTful API with comprehensive middleware stack and validation</p>
                    </div>
                    <div class="arch-layer">
                        <h5>In-Memory Storage</h5>
                        <p>Optimized data structures with automatic cleanup and persistence ready</p>
                    </div>
                </div>
            </div>

            <div class="arch-section">
                <h4>🔧 Advanced Features</h4>
                <div class="arch-diagram">
                    <div class="arch-layer">
                        <h5>Connection Management</h5>
                        <p>Automatic user cleanup, room persistence, and connection tracking</p>
                    </div>
                    <div class="arch-layer">
                        <h5>Security Middleware</h5>
                        <p>Helmet.js, CORS, rate limiting, and input validation</p>
                    </div>
                    <div class="arch-layer">
                        <h5>Real-Time Synchronization</h5>
                        <p>Event-driven architecture with conflict resolution and state management</p>
                    </div>
                    <div class="arch-layer">
                        <h5>Scalable Design</h5>
                        <p>Room-based isolation, efficient broadcasting, and memory optimization</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📡 API Endpoints & WebSocket Events</h2>
            <p>Comprehensive API surface with RESTful endpoints and real-time WebSocket communication.</p>

            <div class="api-showcase">
                <h4>🌐 REST API Endpoints</h4>
                <div class="endpoint-grid">
                    <div class="endpoint-card">
                        <div class="endpoint-header">
                            <span class="method-badge method-get">GET</span>
                            <span class="endpoint-path">/api/rooms</span>
                        </div>
                        <div class="endpoint-description">
                            List all public rooms with member counts and metadata
                        </div>
                        <ul class="endpoint-features">
                            <li>Real-time room statistics</li>
                            <li>Sorting by activity and member count</li>
                            <li>Public room discovery</li>
                        </ul>
                    </div>

                    <div class="endpoint-card">
                        <div class="endpoint-header">
                            <span class="method-badge method-post">POST</span>
                            <span class="endpoint-path">/api/rooms</span>
                        </div>
                        <div class="endpoint-description">
                            Create new collaborative workspace with security options
                        </div>
                        <ul class="endpoint-features">
                            <li>Password protection with bcrypt</li>
                            <li>Custom room settings and limits</li>
                            <li>Automatic user authentication</li>
                            <li>JWT token generation</li>
                        </ul>
                    </div>

                    <div class="endpoint-card">
                        <div class="endpoint-header">
                            <span class="method-badge method-post">POST</span>
                            <span class="endpoint-path">/api/rooms/:id/join</span>
                        </div>
                        <div class="endpoint-description">
                            Join existing room with authentication and validation
                        </div>
                        <ul class="endpoint-features">
                            <li>Password verification</li>
                            <li>Room capacity checking</li>
                            <li>User creation and token issuance</li>
                            <li>Full room data retrieval</li>
                        </ul>
                    </div>

                    <div class="endpoint-card">
                        <div class="endpoint-header">
                            <span class="method-badge method-get">GET</span>
                            <span class="endpoint-path">/api/rooms/:id</span>
                        </div>
                        <div class="endpoint-description">
                            Get detailed room information and public metadata
                        </div>
                        <ul class="endpoint-features">
                            <li>Room statistics and settings</li>
                            <li>Member count and activity</li>
                            <li>Public room information</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="api-showcase">
                <h4>⚡ WebSocket Events</h4>
                <div class="endpoint-grid">
                    <div class="endpoint-card">
                        <div class="endpoint-header">
                            <span class="method-badge method-websocket">WS</span>
                            <span class="endpoint-path">send_message</span>
                        </div>
                        <div class="endpoint-description">
                            Real-time chat messaging with instant delivery
                        </div>
                        <ul class="endpoint-features">
                            <li>Message broadcasting to all room members</li>
                            <li>Message history and persistence</li>
                            <li>User avatar and timestamp inclusion</li>
                        </ul>
                    </div>

                    <div class="endpoint-card">
                        <div class="endpoint-header">
                            <span class="method-badge method-websocket">WS</span>
                            <span class="endpoint-path">task_operations</span>
                        </div>
                        <div class="endpoint-description">
                            Collaborative task management with real-time updates
                        </div>
                        <ul class="endpoint-features">
                            <li>Create, update, delete tasks</li>
                            <li>Priority levels and assignments</li>
                            <li>Real-time synchronization</li>
                        </ul>
                    </div>

                    <div class="endpoint-card">
                        <div class="endpoint-header">
                            <span class="method-badge method-websocket">WS</span>
                            <span class="endpoint-path">whiteboard_update</span>
                        </div>
                        <div class="endpoint-description">
                            Interactive whiteboard with collaborative drawing
                        </div>
                        <ul class="endpoint-features">
                            <li>Real-time drawing synchronization</li>
                            <li>Element-based updates</li>
                            <li>Conflict resolution</li>
                        </ul>
                    </div>

                    <div class="endpoint-card">
                        <div class="endpoint-header">
                            <span class="method-badge method-websocket">WS</span>
                            <span class="endpoint-path">focus_timer</span>
                        </div>
                        <div class="endpoint-description">
                            Synchronized team focus sessions (Pomodoro technique)
                        </div>
                        <ul class="endpoint-features">
                            <li>Customizable timer durations</li>
                            <li>Real-time countdown synchronization</li>
                            <li>Team-wide notifications</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="section demo-section">
            <h3>🧪 Live Demo Instructions</h3>
            <p>Experience the complete collaborative workspace with real-time team features</p>

            <div class="server-status">
                <span class="status-indicator" id="statusIndicator"></span>
                <strong>Server Status:</strong> <span id="serverStatus">Checking...</span>
            </div>

            <div class="demo-instructions">
                <h4>How to Test the Full Application:</h4>
                <ol>
                    <li>Navigate to project: <code>cd backend/workroom-api</code></li>
                    <li>Install dependencies: <code>npm install</code></li>
                    <li>Start the server: <code>npm start</code></li>
                    <li>API will be available at: <code>http://localhost:3004</code></li>
                    <li>Click "Launch Collaborative Workspace" below to test the full application!</li>
                </ol>
            </div>

            <div style="margin: 30px 0;">
                <a href="./public/index.html" target="_blank" class="btn btn-primary">🏢 Launch Collaborative Workspace</a>
                <button class="btn btn-secondary" onclick="testAPI()">🚀 Test API Connection</button>
                <button class="btn btn-secondary" onclick="loadRooms()">🏠 Load Active Rooms</button>
                <button class="btn btn-secondary" onclick="createTestRoom()">➕ Create Test Room</button>
            </div>

            <div class="api-response" id="apiResponse" style="display: none;"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3006/api';

        // Check server status on load
        window.addEventListener('load', () => {
            checkServerStatus();
            loadStats();
        });

        async function checkServerStatus() {
            const statusIndicator = document.getElementById('statusIndicator');
            const statusText = document.getElementById('serverStatus');

            try {
                const response = await fetch(`${API_BASE}/info`);
                if (response.ok) {
                    statusIndicator.classList.add('online');
                    statusText.textContent = 'Online and Ready';
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                statusIndicator.classList.remove('online');
                statusText.textContent = 'Offline - Start server to test';
            }
        }

        async function loadStats() {
            try {
                const response = await fetch(`${API_BASE}/info`);
                const data = await response.json();

                if (data.statistics) {
                    document.getElementById('totalRooms').textContent = data.statistics.totalRooms;
                    document.getElementById('activeUsers').textContent = data.statistics.activeUsers;
                    document.getElementById('totalConnections').textContent = data.statistics.totalConnections;
                }
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }

        async function testAPI() {
            const responseDiv = document.getElementById('apiResponse');
            responseDiv.style.display = 'block';
            responseDiv.textContent = 'Testing API connection...';

            try {
                const response = await fetch(`${API_BASE}/info`);
                const data = await response.json();
                responseDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                responseDiv.textContent = `Error: ${error.message}\n\nMake sure the server is running on port 3004`;
            }
        }

        async function loadRooms() {
            const responseDiv = document.getElementById('apiResponse');
            responseDiv.style.display = 'block';
            responseDiv.textContent = 'Loading active rooms...';

            try {
                const response = await fetch(`${API_BASE}/rooms`);
                const data = await response.json();
                responseDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                responseDiv.textContent = `Error: ${error.message}`;
            }
        }

        async function createTestRoom() {
            const responseDiv = document.getElementById('apiResponse');
            responseDiv.style.display = 'block';
            responseDiv.textContent = 'Creating test room...';

            try {
                const response = await fetch(`${API_BASE}/rooms`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: `Demo Room ${Date.now()}`,
                        username: 'Demo User',
                        password: '' // No password for demo
                    })
                });

                const data = await response.json();
                responseDiv.textContent = JSON.stringify(data, null, 2);

                // Refresh stats
                setTimeout(loadStats, 1000);
            } catch (error) {
                responseDiv.textContent = `Error: ${error.message}`;
            }
        }

        // Auto-refresh stats every 10 seconds
        setInterval(() => {
            loadStats();
            checkServerStatus();
        }, 10000);
    </script>
</body>
</html>
