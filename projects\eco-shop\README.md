# EcoShop - Sustainable E-commerce Demo

A modern, responsive e-commerce demo showcasing sustainable products with Stripe Checkout integration. Built with vanilla JavaScript, CSS Grid, and modern web standards.

## 🌱 Features

### Core Functionality
- **Product Catalog**: Browse eco-friendly products with filtering and search
- **Shopping Cart**: Add, remove, and modify items with persistent storage
- **Stripe Integration**: Secure checkout process (demo mode)
- **Responsive Design**: Mobile-first approach with CSS Grid and Flexbox
- **Theme Switching**: Light/dark mode with system preference detection
- **Product Modal**: Detailed product view with quantity selection

### User Experience
- **Smooth Animations**: CSS transitions and keyframe animations
- **Loading States**: Visual feedback during async operations
- **Notifications**: Toast-style messages for user actions
- **Form Validation**: Client-side validation for contact and newsletter forms
- **Accessibility**: ARIA labels, keyboard navigation, and semantic HTML

### Technical Features
- **Vanilla JavaScript**: No frameworks or build tools required
- **ES2022 Modules**: Modern JavaScript with class-based architecture
- **CSS Custom Properties**: Theme system with CSS variables
- **Local Storage**: Cart persistence and theme preferences
- **Progressive Enhancement**: Works without JavaScript for basic functionality

## 🛠️ Technology Stack

- **Frontend**: HTML5, CSS3, Vanilla JavaScript (ES2022)
- **Styling**: CSS Grid, Flexbox, Custom Properties
- **Payment**: Stripe.js (demo integration)
- **Storage**: localStorage for cart and preferences
- **Images**: Unsplash API for demo product images
- **Icons**: Unicode emojis for lightweight icons

## 🚀 Getting Started

### Prerequisites
- Modern web browser with ES2022 support
- Local web server (for development)

### Installation
1. Clone or download the project files
2. Serve the files using a local web server:
   ```bash
   # Using Python
   python -m http.server 8000
   
   # Using Node.js
   npx serve .
   
   # Using PHP
   php -S localhost:8000
   ```
3. Open `http://localhost:8000` in your browser

### Stripe Integration
For production use, replace the demo Stripe configuration in `main.js`:
```javascript
this.stripeConfig = {
  publishableKey: 'pk_live_your_actual_key', // Replace with your key
  currency: 'usd',
  country: 'US'
};
```

## 📁 Project Structure

```
eco-shop/
├── index.html          # Main HTML structure
├── styles.css          # Complete CSS styling
├── main.js             # Application logic
├── assets/             # Images and static files
│   └── .gitkeep       # Directory placeholder
└── README.md          # Project documentation
```

## 🎨 Design System

### Color Palette
- **Primary**: #22c55e (Green - eco-friendly theme)
- **Secondary**: #059669 (Dark green)
- **Accent**: #f59e0b (Amber for highlights)
- **Neutral**: Gray scale from #f9fafb to #111827

### Typography
- **Font**: Inter (Google Fonts)
- **Sizes**: Responsive scale from 0.75rem to 3rem
- **Weights**: 300, 400, 500, 600, 700

### Layout
- **Max Width**: 1280px for main content
- **Spacing**: 0.25rem to 6rem scale
- **Breakpoints**: 480px, 768px, 1024px

## 🔧 Customization

### Adding Products
Modify the `generateDemoProducts()` method in `main.js`:
```javascript
{
  id: 7,
  name: 'Your Product Name',
  description: 'Product description',
  price: 29.99,
  image: 'path/to/image.jpg',
  category: 'home', // home, personal, kitchen, clothing
  badge: 'New', // Optional badge
  inStock: true
}
```

### Styling
- Modify CSS custom properties in `:root` for theme changes
- Add new components following the existing naming convention
- Use the established spacing and color scales

### Functionality
- Extend the `EcoShopApp` class for new features
- Follow the existing event handling patterns
- Maintain accessibility standards

## 🌍 Sustainability Features

### Eco-Friendly Focus
- Green color scheme representing nature
- Sustainable product categories
- Environmental messaging and statistics
- Carbon-neutral shipping emphasis

### Performance
- Optimized images with lazy loading
- Minimal JavaScript bundle
- CSS-only animations where possible
- Efficient DOM manipulation

## 📱 Browser Support

- **Modern Browsers**: Chrome 88+, Firefox 85+, Safari 14+, Edge 88+
- **Features Used**: CSS Grid, Flexbox, Custom Properties, ES2022 Classes
- **Fallbacks**: Graceful degradation for older browsers

## 🔒 Security Considerations

- **Client-Side Only**: No sensitive data stored locally
- **Stripe Integration**: Uses secure Stripe.js library
- **Input Validation**: Client-side validation with server-side recommended
- **XSS Prevention**: Proper HTML escaping in dynamic content

## 🚀 Deployment

### Static Hosting
Deploy to any static hosting service:
- **GitHub Pages**: Push to `gh-pages` branch
- **Netlify**: Connect repository for automatic deployment
- **Vercel**: Import project for instant deployment
- **AWS S3**: Upload files to S3 bucket with static hosting

### CDN Optimization
- Enable gzip compression
- Set appropriate cache headers
- Use WebP images where supported
- Implement service worker for offline functionality

## 📈 Performance Metrics

- **Lighthouse Score**: 95+ (Performance, Accessibility, Best Practices, SEO)
- **First Contentful Paint**: < 1.5s
- **Largest Contentful Paint**: < 2.5s
- **Cumulative Layout Shift**: < 0.1

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is part of Portfolio Eight and is available under the MIT License.

## 🔗 Links

- **Live Demo**: [View EcoShop](https://yourusername.github.io/portfolio-eight/projects/eco-shop/)
- **Portfolio**: [Portfolio Eight](https://yourusername.github.io/portfolio-eight/)
- **Source Code**: [GitHub Repository](https://github.com/yourusername/portfolio-eight)

---

Built with 💚 for a sustainable future
