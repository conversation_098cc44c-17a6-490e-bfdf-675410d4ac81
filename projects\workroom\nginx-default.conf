# CollabSpace Frontend Server Configuration
server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    # Security headers specific to this server
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;

    # Serve static files with caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
        
        # CORS headers for fonts and assets
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, OPTIONS";
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept";
    }

    # Handle service worker
    location /sw.js {
        expires 0;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
    }

    # Handle manifest.json
    location /manifest.json {
        expires 1d;
        add_header Cache-Control "public";
    }

    # API proxy (if backend is available)
    location /api/ {
        # Rate limiting for API endpoints
        limit_req zone=api burst=20 nodelay;
        
        # Proxy to backend (uncomment when backend is available)
        # proxy_pass http://backend:3001;
        # proxy_http_version 1.1;
        # proxy_set_header Upgrade $http_upgrade;
        # proxy_set_header Connection 'upgrade';
        # proxy_set_header Host $host;
        # proxy_set_header X-Real-IP $remote_addr;
        # proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        # proxy_set_header X-Forwarded-Proto $scheme;
        # proxy_cache_bypass $http_upgrade;
        
        # For now, return 503 Service Unavailable
        return 503 '{"error": "Backend service not available", "message": "API endpoints will be available when backend is deployed"}';
        add_header Content-Type application/json;
    }

    # WebSocket proxy (if backend is available)
    location /socket.io/ {
        # Proxy to backend WebSocket (uncomment when backend is available)
        # proxy_pass http://backend:3001;
        # proxy_http_version 1.1;
        # proxy_set_header Upgrade $http_upgrade;
        # proxy_set_header Connection "upgrade";
        # proxy_set_header Host $host;
        # proxy_set_header X-Real-IP $remote_addr;
        # proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        # proxy_set_header X-Forwarded-Proto $scheme;
        
        # For now, return 503 Service Unavailable
        return 503 '{"error": "WebSocket service not available", "message": "Real-time features will be available when backend is deployed"}';
        add_header Content-Type application/json;
    }

    # Handle React Router (SPA routing)
    location / {
        try_files $uri $uri/ /index.html;
        
        # Cache HTML files for a short time
        expires 1h;
        add_header Cache-Control "public, must-revalidate";
    }

    # Security: Deny access to hidden files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Security: Deny access to backup files
    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Health check endpoint
    location /health {
        access_log off;
        return 200 '{"status": "healthy", "service": "collabspace-frontend", "timestamp": "$time_iso8601"}';
        add_header Content-Type application/json;
    }

    # Metrics endpoint (for monitoring)
    location /metrics {
        access_log off;
        return 200 '{"service": "collabspace-frontend", "status": "running", "uptime": "$upstream_response_time"}';
        add_header Content-Type application/json;
    }

    # Error pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /404.html {
        internal;
    }
    
    location = /50x.html {
        internal;
    }

    # Logging
    access_log /var/log/nginx/collabspace.access.log;
    error_log /var/log/nginx/collabspace.error.log;
}
