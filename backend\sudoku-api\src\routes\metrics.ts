/**
 * Metrics Routes
 * Prometheus metrics and monitoring endpoints
 */

import { Router } from 'express';
import { asyncHand<PERSON> } from '@/middleware/errorHandler';
import { getMetrics, getMetricsSummary, BusinessMetrics } from '@/middleware/metrics';
import { ApiResponse } from '@/types';

const router = Router();

/**
 * GET /metrics
 * Prometheus metrics endpoint
 */
router.get('/', asyncHandler(async (req, res) => {
  const metrics = await getMetrics();
  
  res.set('Content-Type', 'text/plain; version=0.0.4; charset=utf-8');
  res.send(metrics);
}));

/**
 * GET /metrics/summary
 * Human-readable metrics summary
 */
router.get('/summary', asyncHandler(async (req, res) => {
  const summary = await getMetricsSummary();
  
  const response: ApiResponse = {
    success: true,
    data: summary,
    timestamp: new Date().toISOString(),
    requestId: req.id,
  };

  res.json(response);
}));

/**
 * GET /metrics/business
 * Business metrics specific to Sudoku operations
 */
router.get('/business', asyncHandler(async (req, res) => {
  const businessStats = BusinessMetrics.getStats();
  
  const response: ApiResponse = {
    success: true,
    data: {
      ...businessStats,
      rates: {
        puzzlesSolvedPerHour: calculateHourlyRate(businessStats.puzzlesSolved),
        puzzlesGeneratedPerHour: calculateHourlyRate(businessStats.puzzlesGenerated),
        hintsProvidedPerHour: calculateHourlyRate(businessStats.hintsProvided),
        validationRequestsPerHour: calculateHourlyRate(businessStats.validationRequests),
      },
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    },
    timestamp: new Date().toISOString(),
    requestId: req.id,
  };

  res.json(response);
}));

/**
 * GET /metrics/system
 * System performance metrics
 */
router.get('/system', asyncHandler(async (req, res) => {
  const memUsage = process.memoryUsage();
  const cpuUsage = process.cpuUsage();
  
  const response: ApiResponse = {
    success: true,
    data: {
      memory: {
        rss: memUsage.rss,
        heapTotal: memUsage.heapTotal,
        heapUsed: memUsage.heapUsed,
        external: memUsage.external,
        arrayBuffers: memUsage.arrayBuffers,
        heapUsedPercent: (memUsage.heapUsed / memUsage.heapTotal) * 100,
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system,
        total: cpuUsage.user + cpuUsage.system,
      },
      process: {
        pid: process.pid,
        uptime: process.uptime(),
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version,
        title: process.title,
      },
      timestamp: new Date().toISOString(),
    },
    timestamp: new Date().toISOString(),
    requestId: req.id,
  };

  res.json(response);
}));

/**
 * Helper function to calculate hourly rates
 */
function calculateHourlyRate(total: number): number {
  const uptimeHours = process.uptime() / 3600;
  return uptimeHours > 0 ? Math.round((total / uptimeHours) * 100) / 100 : 0;
}

export default router;
