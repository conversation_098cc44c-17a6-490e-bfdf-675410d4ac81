/**
 * EcoShop - Sustainable E-commerce Demo Styles
 * Modern, responsive design with eco-friendly theme
 */

/* CSS Custom Properties */
:root {
  /* Colors - Eco Theme */
  --color-primary: #22c55e;
  --color-primary-dark: #16a34a;
  --color-primary-light: #86efac;
  --color-secondary: #059669;
  --color-accent: #f59e0b;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  
  /* Neutral Colors */
  --color-white: #ffffff;
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  
  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  /* Spacing */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  --spacing-24: 6rem;
  
  /* Layout */
  --max-width-sm: 640px;
  --max-width-md: 768px;
  --max-width-lg: 1024px;
  --max-width-xl: 1280px;
  --max-width-2xl: 1536px;
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  
  /* Z-index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* Dark Theme */
[data-theme="dark"] {
  --color-white: #111827;
  --color-gray-50: #1f2937;
  --color-gray-100: #374151;
  --color-gray-200: #4b5563;
  --color-gray-300: #6b7280;
  --color-gray-400: #9ca3af;
  --color-gray-500: #d1d5db;
  --color-gray-600: #e5e7eb;
  --color-gray-700: #f3f4f6;
  --color-gray-800: #f9fafb;
  --color-gray-900: #ffffff;
}

/* Reset and Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--color-gray-900);
  background-color: var(--color-white);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-gray-900);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
  margin-bottom: var(--spacing-4);
  color: var(--color-gray-700);
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--color-primary-dark);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-6);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  border: 1px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  color: var(--color-white);
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
  color: var(--color-white);
}

.btn-secondary {
  color: var(--color-gray-700);
  background-color: var(--color-white);
  border-color: var(--color-gray-300);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--color-gray-50);
  border-color: var(--color-gray-400);
  color: var(--color-gray-800);
}

.btn-outline {
  color: var(--color-primary);
  background-color: transparent;
  border-color: var(--color-primary);
}

.btn-outline:hover:not(:disabled) {
  color: var(--color-white);
  background-color: var(--color-primary);
}

.btn-full {
  width: 100%;
}

.btn-lg {
  padding: var(--spacing-4) var(--spacing-8);
  font-size: var(--font-size-base);
}

.btn-sm {
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-xs);
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  backdrop-filter: blur(4px);
}

.loading-spinner {
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--color-gray-200);
  border-top: 4px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--spacing-4);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Header */
.header {
  position: sticky;
  top: 0;
  background-color: var(--color-white);
  border-bottom: 1px solid var(--color-gray-200);
  z-index: var(--z-sticky);
  backdrop-filter: blur(8px);
}

.navbar {
  padding: var(--spacing-4) 0;
}

.nav-container {
  max-width: var(--max-width-xl);
  margin: 0 auto;
  padding: 0 var(--spacing-4);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nav-brand {
  display: flex;
  align-items: center;
}

.brand-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  text-decoration: none;
}

.brand-icon {
  font-size: var(--font-size-2xl);
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-8);
  list-style: none;
}

.nav-link {
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
  padding: var(--spacing-2) 0;
  position: relative;
  transition: color var(--transition-fast);
}

.nav-link:hover,
.nav-link.active {
  color: var(--color-primary);
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--color-primary);
  border-radius: var(--radius-full);
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.theme-toggle,
.cart-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: var(--radius-lg);
  background-color: var(--color-gray-100);
  color: var(--color-gray-700);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.theme-toggle:hover,
.cart-button:hover {
  background-color: var(--color-gray-200);
  color: var(--color-gray-900);
}

.cart-button {
  position: relative;
}

.cart-count {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: var(--color-error);
  color: var(--color-white);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  padding: 2px 6px;
  border-radius: var(--radius-full);
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-toggle {
  display: none;
  flex-direction: column;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  background: none;
  cursor: pointer;
}

.hamburger {
  width: 20px;
  height: 2px;
  background-color: var(--color-gray-700);
  transition: all var(--transition-fast);
  position: relative;
}

.hamburger::before,
.hamburger::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 2px;
  background-color: var(--color-gray-700);
  transition: all var(--transition-fast);
}

.hamburger::before {
  top: -6px;
}

.hamburger::after {
  bottom: -6px;
}

/* Hero Section */
.hero {
  padding: var(--spacing-20) 0;
  background: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-primary-light) 100%);
}

.hero-container {
  max-width: var(--max-width-xl);
  margin: 0 auto;
  padding: 0 var(--spacing-4);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-16);
  align-items: center;
}

.hero-title {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-6);
}

.highlight {
  color: var(--color-primary);
}

.hero-description {
  font-size: var(--font-size-lg);
  color: var(--color-gray-600);
  margin-bottom: var(--spacing-8);
  line-height: var(--line-height-relaxed);
}

.hero-actions {
  display: flex;
  gap: var(--spacing-4);
}

.hero-image img {
  width: 100%;
  height: auto;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
}

/* Features Section */
.features {
  padding: var(--spacing-20) 0;
  background-color: var(--color-white);
}

.features-container {
  max-width: var(--max-width-xl);
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.section-title {
  text-align: center;
  margin-bottom: var(--spacing-16);
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-8);
}

.feature-card {
  text-align: center;
  padding: var(--spacing-8);
  border-radius: var(--radius-xl);
  background-color: var(--color-gray-50);
  transition: transform var(--transition-normal);
}

.feature-card:hover {
  transform: translateY(-4px);
}

.feature-icon {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-4);
}

.feature-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-3);
}

.feature-description {
  color: var(--color-gray-600);
  line-height: var(--line-height-relaxed);
}

/* Products Section */
.products {
  padding: var(--spacing-20) 0;
  background-color: var(--color-gray-50);
}

.products-container {
  max-width: var(--max-width-xl);
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.products-header {
  text-align: center;
  margin-bottom: var(--spacing-12);
}

.products-filters {
  display: flex;
  justify-content: center;
  gap: var(--spacing-2);
  margin-top: var(--spacing-8);
  flex-wrap: wrap;
}

.filter-btn {
  padding: var(--spacing-2) var(--spacing-4);
  border: 1px solid var(--color-gray-300);
  background-color: var(--color-white);
  color: var(--color-gray-700);
  border-radius: var(--radius-full);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--font-size-sm);
}

.filter-btn:hover,
.filter-btn.active {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  color: var(--color-white);
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-12);
}

.products-actions {
  text-align: center;
}

/* Product Cards */
.product-card {
  background-color: var(--color-white);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
  cursor: pointer;
}

.product-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.product-image {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-slow);
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-badge {
  position: absolute;
  top: var(--spacing-3);
  left: var(--spacing-3);
  background-color: var(--color-primary);
  color: var(--color-white);
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.product-info {
  padding: var(--spacing-4);
}

.product-name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-2);
  color: var(--color-gray-900);
}

.product-description {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  margin-bottom: var(--spacing-3);
  line-height: var(--line-height-relaxed);
}

.product-price {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  margin-bottom: var(--spacing-4);
}

.product-actions {
  display: flex;
  gap: var(--spacing-2);
}

.product-actions .btn {
  flex: 1;
  font-size: var(--font-size-sm);
  padding: var(--spacing-2) var(--spacing-4);
}

/* About Section */
.about {
  padding: var(--spacing-20) 0;
  background-color: var(--color-white);
}

.about-container {
  max-width: var(--max-width-xl);
  margin: 0 auto;
  padding: 0 var(--spacing-4);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-16);
  align-items: center;
}

.about-text {
  font-size: var(--font-size-lg);
  color: var(--color-gray-600);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-8);
}

.about-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-6);
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  margin-bottom: var(--spacing-1);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
}

.about-image img {
  width: 100%;
  height: auto;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
}

/* Contact Section */
.contact {
  padding: var(--spacing-20) 0;
  background-color: var(--color-gray-50);
}

.contact-container {
  max-width: var(--max-width-lg);
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-12);
  margin-top: var(--spacing-12);
}

.contact-info h3 {
  margin-bottom: var(--spacing-6);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-4);
}

.contact-icon {
  font-size: var(--font-size-lg);
}

.contact-form {
  background-color: var(--color-white);
  padding: var(--spacing-8);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
}

.form-group {
  margin-bottom: var(--spacing-6);
}

.form-group label {
  display: block;
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
  margin-bottom: var(--spacing-2);
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: var(--spacing-3);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  transition: border-color var(--transition-fast);
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

/* Shopping Cart Sidebar */
.cart-sidebar {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  height: 100vh;
  background-color: var(--color-white);
  box-shadow: var(--shadow-xl);
  z-index: var(--z-modal);
  transition: right var(--transition-normal);
  display: flex;
  flex-direction: column;
}

.cart-sidebar.open {
  right: 0;
}

.cart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--color-gray-200);
}

.cart-header h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

.cart-close {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  font-size: var(--font-size-xl);
  cursor: pointer;
  color: var(--color-gray-500);
  transition: color var(--transition-fast);
}

.cart-close:hover {
  color: var(--color-gray-700);
}

.cart-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-6);
}

.cart-empty {
  text-align: center;
  padding: var(--spacing-12) var(--spacing-6);
}

.cart-empty p {
  color: var(--color-gray-500);
  margin-bottom: var(--spacing-6);
}

.cart-items {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.cart-item {
  display: flex;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
}

.cart-item-image {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-md);
  overflow: hidden;
  flex-shrink: 0;
}

.cart-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cart-item-details {
  flex: 1;
}

.cart-item-name {
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-1);
}

.cart-item-price {
  color: var(--color-primary);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-2);
}

.cart-item-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.quantity-btn {
  width: 24px;
  height: 24px;
  border: 1px solid var(--color-gray-300);
  background-color: var(--color-white);
  border-radius: var(--radius-sm);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
}

.quantity-btn:hover {
  background-color: var(--color-gray-50);
  border-color: var(--color-gray-400);
}

.quantity-input {
  width: 40px;
  text-align: center;
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-sm);
  padding: var(--spacing-1);
  font-size: var(--font-size-sm);
}

.remove-item {
  background: none;
  border: none;
  color: var(--color-error);
  cursor: pointer;
  font-size: var(--font-size-sm);
  padding: var(--spacing-1);
  transition: opacity var(--transition-fast);
}

.remove-item:hover {
  opacity: 0.7;
}

.cart-footer {
  border-top: 1px solid var(--color-gray-200);
  padding: var(--spacing-6);
}

.cart-total {
  margin-bottom: var(--spacing-6);
}

.total-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-2);
}

.total-final {
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-lg);
  border-top: 1px solid var(--color-gray-200);
  padding-top: var(--spacing-2);
  margin-top: var(--spacing-2);
}

.cart-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: var(--z-modal-backdrop);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.cart-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* Product Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: var(--z-modal);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.modal.active {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: var(--color-white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  transform: scale(0.9);
  transition: transform var(--transition-normal);
}

.modal.active .modal-content {
  transform: scale(1);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--color-gray-200);
}

.modal-header h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
}

.modal-close {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  font-size: var(--font-size-xl);
  cursor: pointer;
  color: var(--color-gray-500);
  transition: color var(--transition-fast);
}

.modal-close:hover {
  color: var(--color-gray-700);
}

.modal-body {
  padding: var(--spacing-6);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-6);
}

.modal-image {
  width: 100%;
  height: 250px;
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.modal-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.modal-details {
  display: flex;
  flex-direction: column;
}

.modal-price {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  margin-bottom: var(--spacing-4);
}

.modal-description {
  color: var(--color-gray-600);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-6);
}

.modal-options {
  margin-bottom: var(--spacing-6);
}

.quantity-selector {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.quantity-selector label {
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
}

.modal-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
  margin-top: auto;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: var(--z-modal-backdrop);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.modal-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* Footer */
.footer {
  background-color: var(--color-gray-900);
  color: var(--color-gray-300);
  padding: var(--spacing-16) 0 var(--spacing-8);
}

.footer-container {
  max-width: var(--max-width-xl);
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-8);
  margin-bottom: var(--spacing-12);
}

.footer-section h4 {
  color: var(--color-white);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-4);
}

.footer-section p {
  color: var(--color-gray-400);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-4);
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: var(--spacing-2);
}

.footer-section ul li a {
  color: var(--color-gray-400);
  transition: color var(--transition-fast);
}

.footer-section ul li a:hover {
  color: var(--color-white);
}

.social-links {
  display: flex;
  gap: var(--spacing-3);
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: var(--color-gray-800);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-lg);
  transition: all var(--transition-fast);
}

.social-links a:hover {
  background-color: var(--color-primary);
  transform: translateY(-2px);
}

.newsletter-form {
  display: flex;
  gap: var(--spacing-2);
  margin-top: var(--spacing-4);
}

.newsletter-form input {
  flex: 1;
  padding: var(--spacing-3);
  border: 1px solid var(--color-gray-600);
  border-radius: var(--radius-lg);
  background-color: var(--color-gray-800);
  color: var(--color-white);
  font-size: var(--font-size-sm);
}

.newsletter-form input::placeholder {
  color: var(--color-gray-500);
}

.newsletter-form button {
  padding: var(--spacing-3) var(--spacing-4);
  background-color: var(--color-primary);
  color: var(--color-white);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.newsletter-form button:hover {
  background-color: var(--color-primary-dark);
}

.footer-bottom {
  border-top: 1px solid var(--color-gray-800);
  padding-top: var(--spacing-8);
  text-align: center;
}

.footer-bottom p {
  color: var(--color-gray-500);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-2);
}

.footer-bottom a {
  color: var(--color-gray-400);
  transition: color var(--transition-fast);
}

.footer-bottom a:hover {
  color: var(--color-white);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.4s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

/* Utility Classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.hidden {
  display: none !important;
}

.visible {
  display: block !important;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-container {
    gap: var(--spacing-12);
  }

  .about-container {
    gap: var(--spacing-12);
  }

  .footer-content {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }

  .nav-toggle {
    display: flex;
  }

  .hero-container {
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
    text-align: center;
  }

  .hero-title {
    font-size: var(--font-size-3xl);
  }

  .hero-actions {
    justify-content: center;
    flex-direction: column;
    align-items: center;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }

  .about-container {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .about-stats {
    justify-content: center;
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
  }

  .cart-sidebar {
    width: 100vw;
    right: -100vw;
  }

  .modal-body {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }

  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .newsletter-form {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .hero {
    padding: var(--spacing-12) 0;
  }

  .hero-title {
    font-size: var(--font-size-2xl);
  }

  .section-title {
    font-size: var(--font-size-2xl);
  }

  .products-grid {
    grid-template-columns: 1fr;
  }

  .products-filters {
    flex-direction: column;
    align-items: center;
  }

  .hero-actions .btn {
    width: 100%;
    max-width: 200px;
  }
}
