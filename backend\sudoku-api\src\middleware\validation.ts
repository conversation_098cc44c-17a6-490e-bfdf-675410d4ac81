/**
 * Request validation middleware
 * Comprehensive input validation using Joi schemas
 */

import { Request, Response, NextFunction } from 'express';
import Jo<PERSON> from 'joi';
import { ValidationIssue, ApiResponse } from '@/types';
import { log } from '@/utils/logger';

/**
 * Sudoku grid validation schema
 */
const sudokuGridSchema = Joi.array()
  .items(Joi.array().items(Joi.number().integer().min(0).max(9)).length(9).required())
  .length(9)
  .required();

/**
 * Validation schemas for different endpoints
 */
export const schemas = {
  // Solve endpoint
  solve: Joi.object({
    puzzle: sudokuGridSchema.required(),
    options: Joi.object({
      method: Joi.string().valid('logical', 'backtrack', 'advanced').default('advanced'),
      stepByStep: Joi.boolean().default(false),
      maxIterations: Joi.number().integer().min(1).max(1000000).default(100000),
      timeout: Joi.number().integer().min(1000).max(300000).default(30000), // 1s to 5min
    }).default({}),
  }),

  // Generate endpoint
  generate: Joi.object({
    difficulty: Joi.string().valid('easy', 'medium', 'hard', 'expert', 'evil').default('medium'),
    options: Joi.object({
      seed: Joi.number().integer().min(0),
      uniqueSolution: Joi.boolean().default(true),
      symmetry: Joi.string()
        .valid('none', 'rotational', 'horizontal', 'vertical', 'diagonal')
        .default('none'),
    }).default({}),
  }),

  // Validate endpoint
  validate: Joi.object({
    puzzle: sudokuGridSchema.required(),
    options: Joi.object({
      checkCompleteness: Joi.boolean().default(true),
      checkConflicts: Joi.boolean().default(true),
      detailed: Joi.boolean().default(false),
    }).default({}),
  }),

  // Hint endpoint
  hint: Joi.object({
    puzzle: sudokuGridSchema.required(),
    options: Joi.object({
      technique: Joi.string()
        .valid('any', 'naked_single', 'hidden_single', 'intersection_removal')
        .default('any'),
      maxHints: Joi.number().integer().min(1).max(10).default(1),
      difficulty: Joi.string().valid('easy', 'medium', 'hard', 'expert', 'evil'),
    }).default({}),
  }),

  // Analyze endpoint
  analyze: Joi.object({
    puzzle: sudokuGridSchema.required(),
    options: Joi.object({
      detailed: Joi.boolean().default(false),
      includeTechniques: Joi.boolean().default(true),
      estimateTime: Joi.boolean().default(true),
    }).default({}),
  }),

  // Batch operations
  batch: Joi.object({
    operations: Joi.array()
      .items(
        Joi.object({
          id: Joi.string().required(),
          type: Joi.string().valid('solve', 'generate', 'validate', 'hint', 'analyze').required(),
          data: Joi.object().required(),
        })
      )
      .min(1)
      .max(10)
      .required(),
    options: Joi.object({
      parallel: Joi.boolean().default(false),
      stopOnError: Joi.boolean().default(false),
    }).default({}),
  }),
};

/**
 * Generic validation middleware factory
 */
export const validateRequest = (
  schema: Joi.ObjectSchema,
  property: 'body' | 'query' | 'params' = 'body'
) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error, value } = schema.validate(req[property], {
      abortEarly: false,
      allowUnknown: false,
      stripUnknown: true,
    });

    if (error) {
      log.warn('Request validation failed', {
        method: req.method,
        url: req.url,
        property,
        errors: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message,
          value: detail.context?.value,
        })),
        requestId: req.id,
      });

      const response: ApiResponse = {
        success: false,
        error: 'Validation failed',
        data: {
          validationErrors: error.details.map(detail => ({
            field: detail.path.join('.') || 'root',
            message: detail.message,
            value: detail.context?.value,
            type: detail.type,
          })),
        },
        timestamp: new Date().toISOString(),
        requestId: req.id,
      };

      res.status(400).json(response);
      return;
    }

    // Replace request property with validated and sanitized value
    req[property] = value;
    next();
  };
};

/**
 * Specific validation middleware for each endpoint
 */
export const validateSolve = validateRequest(schemas.solve);
export const validateGenerate = validateRequest(schemas.generate);
export const validateValidate = validateRequest(schemas.validate);
export const validateHint = validateRequest(schemas.hint);
export const validateAnalyze = validateRequest(schemas.analyze);
export const validateBatch = validateRequest(schemas.batch);

/**
 * Query parameter validation
 */
export const validateQuery = (schema: Joi.ObjectSchema) => {
  return validateRequest(schema, 'query');
};

/**
 * URL parameter validation
 */
export const validateParams = (schema: Joi.ObjectSchema) => {
  return validateRequest(schema, 'params');
};

/**
 * Common query schemas
 */
export const querySchemas = {
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    sort: Joi.string().valid('created', 'updated', 'difficulty', 'time').default('created'),
    order: Joi.string().valid('asc', 'desc').default('desc'),
  }),

  filter: Joi.object({
    difficulty: Joi.string().valid('easy', 'medium', 'hard', 'expert', 'evil'),
    solved: Joi.boolean(),
    dateFrom: Joi.date().iso(),
    dateTo: Joi.date().iso().min(Joi.ref('dateFrom')),
  }),
};

/**
 * Custom validation functions
 */
export const customValidators = {
  /**
   * Validate Sudoku grid structure and basic rules
   */
  validateSudokuGrid: (grid: number[][]): { valid: boolean; errors: string[] } => {
    const errors: string[] = [];

    // Check dimensions
    if (!Array.isArray(grid) || grid.length !== 9) {
      errors.push('Grid must be a 9x9 array');
      return { valid: false, errors };
    }

    for (let i = 0; i < 9; i++) {
      if (!Array.isArray(grid[i]) || grid[i].length !== 9) {
        errors.push(`Row ${i} must have exactly 9 cells`);
        continue;
      }

      for (let j = 0; j < 9; j++) {
        const cell = grid[i][j];
        if (!Number.isInteger(cell) || cell < 0 || cell > 9) {
          errors.push(`Invalid value at position (${i}, ${j}): ${cell}`);
        }
      }
    }

    // Check for conflicts if no structural errors
    if (errors.length === 0) {
      const conflicts = findConflicts(grid);
      if (conflicts.length > 0) {
        errors.push(...conflicts);
      }
    }

    return { valid: errors.length === 0, errors };
  },

  /**
   * Validate difficulty level
   */
  validateDifficulty: (difficulty: string): boolean => {
    return ['easy', 'medium', 'hard', 'expert', 'evil'].includes(difficulty);
  },

  /**
   * Validate solving method
   */
  validateSolvingMethod: (method: string): boolean => {
    return ['logical', 'backtrack', 'advanced'].includes(method);
  },
};

/**
 * Find conflicts in Sudoku grid
 */
function findConflicts(grid: number[][]): string[] {
  const conflicts: string[] = [];

  // Check rows
  for (let row = 0; row < 9; row++) {
    const seen = new Set<number>();
    for (let col = 0; col < 9; col++) {
      const value = grid[row][col];
      if (value !== 0) {
        if (seen.has(value)) {
          conflicts.push(`Duplicate ${value} in row ${row + 1}`);
        }
        seen.add(value);
      }
    }
  }

  // Check columns
  for (let col = 0; col < 9; col++) {
    const seen = new Set<number>();
    for (let row = 0; row < 9; row++) {
      const value = grid[row][col];
      if (value !== 0) {
        if (seen.has(value)) {
          conflicts.push(`Duplicate ${value} in column ${col + 1}`);
        }
        seen.add(value);
      }
    }
  }

  // Check boxes
  for (let boxRow = 0; boxRow < 3; boxRow++) {
    for (let boxCol = 0; boxCol < 3; boxCol++) {
      const seen = new Set<number>();
      for (let row = boxRow * 3; row < boxRow * 3 + 3; row++) {
        for (let col = boxCol * 3; col < boxCol * 3 + 3; col++) {
          const value = grid[row][col];
          if (value !== 0) {
            if (seen.has(value)) {
              conflicts.push(`Duplicate ${value} in box ${boxRow * 3 + boxCol + 1}`);
            }
            seen.add(value);
          }
        }
      }
    }
  }

  return conflicts;
}

/**
 * Sanitization functions
 */
export const sanitizers = {
  /**
   * Sanitize Sudoku grid
   */
  sanitizeGrid: (grid: any): number[][] | null => {
    if (!Array.isArray(grid) || grid.length !== 9) {
      return null;
    }

    const sanitized: number[][] = [];
    for (let i = 0; i < 9; i++) {
      if (!Array.isArray(grid[i]) || grid[i].length !== 9) {
        return null;
      }

      const row: number[] = [];
      for (let j = 0; j < 9; j++) {
        const cell = parseInt(grid[i][j], 10);
        if (isNaN(cell) || cell < 0 || cell > 9) {
          return null;
        }
        row.push(cell);
      }
      sanitized.push(row);
    }

    return sanitized;
  },

  /**
   * Sanitize string input
   */
  sanitizeString: (input: any, maxLength: number = 255): string => {
    if (typeof input !== 'string') {
      return '';
    }
    return input.trim().substring(0, maxLength);
  },
};

/**
 * Main validation middleware that applies to all requests
 */
export const validationMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  // Add validation helpers to request
  req.validate = {
    sudokuGrid: customValidators.validateSudokuGrid,
    difficulty: customValidators.validateDifficulty,
    solvingMethod: customValidators.validateSolvingMethod,
  };

  // Add sanitization helpers to request
  req.sanitize = {
    grid: sanitizers.sanitizeGrid,
    string: sanitizers.sanitizeString,
  };

  next();
};

// Extend Express Request interface
declare global {
  namespace Express {
    interface Request {
      validate: {
        sudokuGrid: (grid: number[][]) => { valid: boolean; errors: string[] };
        difficulty: (difficulty: string) => boolean;
        solvingMethod: (method: string) => boolean;
      };
      sanitize: {
        grid: (grid: any) => number[][] | null;
        string: (input: any, maxLength?: number) => string;
      };
    }
  }
}

export default {
  validateRequest,
  validateSolve,
  validateGenerate,
  validateValidate,
  validateHint,
  validateAnalyze,
  validateBatch,
  validateQuery,
  validateParams,
  schemas,
  querySchemas,
  customValidators,
  sanitizers,
  validationMiddleware,
};
