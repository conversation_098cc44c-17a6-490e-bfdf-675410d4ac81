<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wordle API - Portfolio Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        h1 {
            color: #333;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #666;
            font-size: 1.2rem;
            margin-bottom: 20px;
        }

        .status-badge {
            display: inline-block;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            margin-bottom: 30px;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .demo-section {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }

        .demo-section h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }

        .api-endpoint {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin-bottom: 15px;
            overflow-x: auto;
        }

        .method {
            color: #48bb78;
            font-weight: bold;
        }

        .url {
            color: #63b3ed;
        }

        .response-example {
            background: #1a202c;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
            margin-top: 15px;
        }

        .json-key {
            color: #63b3ed;
        }

        .json-string {
            color: #68d391;
        }

        .json-number {
            color: #f6ad55;
        }

        .json-boolean {
            color: #fc8181;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .feature-card {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: transform 0.2s, border-color 0.2s;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            border-color: #667eea;
        }

        .feature-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .feature-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .feature-desc {
            color: #666;
            font-size: 0.9rem;
        }

        .tech-stack {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
        }

        .tech-stack h3 {
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .tech-tags {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
        }

        .tech-tag {
            background: rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            backdrop-filter: blur(10px);
        }

        .back-link {
            display: inline-block;
            background: #6c757d;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 8px;
            margin-top: 20px;
            transition: background 0.2s;
        }

        .back-link:hover {
            background: #5a6268;
        }

        .server-note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }

        .server-note h4 {
            margin-bottom: 10px;
            color: #856404;
        }

        .backend-showcase {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            padding: 40px;
            border-radius: 20px;
            margin: 20px 0;
            color: white;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .showcase-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .showcase-header h3 {
            font-size: 2.2rem;
            margin-bottom: 10px;
            color: #ecf0f1;
        }

        .showcase-header p {
            font-size: 1.2rem;
            opacity: 0.9;
            color: #bdc3c7;
        }

        .architecture-overview {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .arch-section h4, .api-showcase h4 {
            font-size: 1.4rem;
            margin-bottom: 25px;
            color: #3498db;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        .arch-diagram {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .arch-layer {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #95a5a6;
            backdrop-filter: blur(10px);
        }

        .arch-layer.highlight {
            border-left-color: #e74c3c;
            background: rgba(231, 76, 60, 0.1);
        }

        .layer-title {
            font-weight: bold;
            font-size: 1.1rem;
            margin-bottom: 5px;
            color: #ecf0f1;
        }

        .layer-content {
            font-size: 0.9rem;
            opacity: 0.8;
            color: #bdc3c7;
        }

        .arch-arrow {
            text-align: center;
            font-size: 1.2rem;
            opacity: 0.6;
        }

        .endpoint-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .endpoint-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .endpoint-card:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .endpoint-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }

        .method {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
        }

        .method.post {
            background: #e74c3c;
            color: white;
        }

        .method.get {
            background: #27ae60;
            color: white;
        }

        .path {
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            color: #3498db;
        }

        .endpoint-desc {
            font-size: 0.9rem;
            margin-bottom: 15px;
            color: #bdc3c7;
        }

        .endpoint-features {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }

        .feature {
            background: rgba(52, 152, 219, 0.2);
            color: #3498db;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            border: 1px solid rgba(52, 152, 219, 0.3);
        }

        .demo-section {
            background: rgba(255, 255, 255, 0.05);
            padding: 30px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .demo-section h4 {
            font-size: 1.4rem;
            margin-bottom: 25px;
            color: #f39c12;
            border-bottom: 2px solid #f39c12;
            padding-bottom: 10px;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .demo-steps {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .step-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border-left: 4px solid #f39c12;
        }

        .step-num {
            background: #f39c12;
            color: #2c3e50;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            flex-shrink: 0;
        }

        .step-text strong {
            color: #ecf0f1;
            margin-right: 10px;
        }

        .step-text code {
            background: rgba(0, 0, 0, 0.3);
            padding: 4px 8px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            color: #3498db;
        }

        .demo-highlights {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .highlight-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }

        .highlight-icon {
            font-size: 1.5rem;
            width: 40px;
            text-align: center;
            flex-shrink: 0;
        }

        .highlight-item strong {
            display: block;
            margin-bottom: 5px;
            color: #ecf0f1;
        }

        .highlight-item p {
            margin: 0;
            opacity: 0.8;
            font-size: 0.9rem;
            color: #bdc3c7;
        }

        .demo-actions {
            text-align: center;
            margin-top: 30px;
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .demo-btn {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            cursor: pointer;
            display: inline-block;
        }

        .demo-btn.primary {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
        }

        .demo-btn.primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
        }

        .demo-btn.secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .demo-btn.secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .server-status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            font-weight: bold;
        }

        .server-status.online {
            background: rgba(39, 174, 96, 0.2);
            border: 1px solid rgba(39, 174, 96, 0.5);
            color: #27ae60;
        }

        .server-status.offline {
            background: rgba(231, 76, 60, 0.2);
            border: 1px solid rgba(231, 76, 60, 0.5);
            color: #e74c3c;
        }

        @media (max-width: 768px) {
            .architecture-overview {
                grid-template-columns: 1fr;
                gap: 30px;
            }

            .endpoint-grid {
                grid-template-columns: 1fr;
            }

            .demo-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .demo-actions {
                flex-direction: column;
                align-items: center;
            }

            .backend-showcase {
                padding: 25px;
            }

            .showcase-header h3 {
                font-size: 1.8rem;
            }
        }

        @media (max-width: 768px) {
            .demo-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 20px;
            }
            
            h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Wordle API</h1>
            <p class="subtitle">Professional Node.js API for Wordle Game Logic</p>
            <div class="status-badge">✅ Production Ready</div>
        </div>

        <div class="backend-showcase">
            <div class="showcase-header">
                <h3>🔧 Professional Backend Architecture</h3>
                <p>Complete Node.js API powering a pixel-perfect Wordle recreation</p>
            </div>

            <div class="architecture-overview">
                <div class="arch-section">
                    <h4>🏗️ System Architecture</h4>
                    <div class="arch-diagram">
                        <div class="arch-layer">
                            <div class="layer-title">Frontend Layer</div>
                            <div class="layer-content">Vanilla JS • Responsive UI • Real-time Updates</div>
                        </div>
                        <div class="arch-arrow">↕️</div>
                        <div class="arch-layer highlight">
                            <div class="layer-title">RESTful API Layer</div>
                            <div class="layer-content">Express.js • CORS • Error Handling • Validation</div>
                        </div>
                        <div class="arch-arrow">↕️</div>
                        <div class="arch-layer">
                            <div class="layer-title">Business Logic Layer</div>
                            <div class="layer-content">Game Engine • Word Validation • State Management</div>
                        </div>
                        <div class="arch-arrow">↕️</div>
                        <div class="arch-layer">
                            <div class="layer-title">Data Layer</div>
                            <div class="layer-content">In-Memory Storage • 2000+ Word Dictionary</div>
                        </div>
                    </div>
                </div>

                <div class="api-showcase">
                    <h4>📡 API Endpoints & Implementation</h4>
                    <div class="endpoint-grid">
                        <div class="endpoint-card">
                            <div class="endpoint-header">
                                <span class="method post">POST</span>
                                <span class="path">/api/game/new</span>
                            </div>
                            <div class="endpoint-desc">Creates game session with unique ID and daily word</div>
                            <div class="endpoint-features">
                                <span class="feature">UUID Generation</span>
                                <span class="feature">Daily Word Logic</span>
                                <span class="feature">State Initialization</span>
                            </div>
                        </div>

                        <div class="endpoint-card">
                            <div class="endpoint-header">
                                <span class="method post">POST</span>
                                <span class="path">/api/game/:id/guess</span>
                            </div>
                            <div class="endpoint-desc">Processes guess with advanced feedback algorithm</div>
                            <div class="endpoint-features">
                                <span class="feature">Duplicate Letter Handling</span>
                                <span class="feature">Position Analysis</span>
                                <span class="feature">Win/Loss Detection</span>
                            </div>
                        </div>

                        <div class="endpoint-card">
                            <div class="endpoint-header">
                                <span class="method get">GET</span>
                                <span class="path">/api/game/:id</span>
                            </div>
                            <div class="endpoint-desc">Retrieves complete game state and history</div>
                            <div class="endpoint-features">
                                <span class="feature">Session Management</span>
                                <span class="feature">Progress Tracking</span>
                                <span class="feature">Statistics</span>
                            </div>
                        </div>

                        <div class="endpoint-card">
                            <div class="endpoint-header">
                                <span class="method post">POST</span>
                                <span class="path">/api/validate</span>
                            </div>
                            <div class="endpoint-desc">Validates words against comprehensive dictionary</div>
                            <div class="endpoint-features">
                                <span class="feature">Dictionary Lookup</span>
                                <span class="feature">Input Sanitization</span>
                                <span class="feature">Error Responses</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="demo-section">
                <h4>🎮 Live Demo Instructions</h4>
                <div class="demo-grid">
                    <div class="demo-steps">
                        <div class="step-item">
                            <span class="step-num">1</span>
                            <div class="step-text">
                                <strong>Navigate:</strong> <code>cd backend/wordle-api</code>
                            </div>
                        </div>
                        <div class="step-item">
                            <span class="step-num">2</span>
                            <div class="step-text">
                                <strong>Install:</strong> <code>npm install</code>
                            </div>
                        </div>
                        <div class="step-item">
                            <span class="step-num">3</span>
                            <div class="step-text">
                                <strong>Start:</strong> <code>npm start</code>
                            </div>
                        </div>
                        <div class="step-item">
                            <span class="step-num">4</span>
                            <div class="step-text">
                                <strong>Play:</strong> <code>localhost:3001</code>
                            </div>
                        </div>
                    </div>

                    <div class="demo-highlights">
                        <div class="highlight-item">
                            <span class="highlight-icon">🎯</span>
                            <div>
                                <strong>Perfect Wordle Recreation</strong>
                                <p>Identical gameplay to the original with professional UI</p>
                            </div>
                        </div>
                        <div class="highlight-item">
                            <span class="highlight-icon">⚡</span>
                            <div>
                                <strong>Real-time Backend Integration</strong>
                                <p>Every action triggers API calls with instant feedback</p>
                            </div>
                        </div>
                        <div class="highlight-item">
                            <span class="highlight-icon">🔧</span>
                            <div>
                                <strong>Production-Ready Code</strong>
                                <p>Error handling, validation, and scalable architecture</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="demo-actions">
                    <a href="http://localhost:3001" target="_blank" class="demo-btn primary" id="liveDemo">
                        🎮 Launch Full Game
                    </a>
                    <button onclick="checkServer()" class="demo-btn secondary" id="checkServer">
                        🔍 Check API Status
                    </button>
                </div>

                <div id="serverStatus" class="server-status" style="display: none;"></div>
            </div>
        </div>

        <div class="demo-grid">
            <div class="demo-section">
                <h3>🎮 Create New Game</h3>
                <div class="api-endpoint">
                    <span class="method">POST</span> <span class="url">/api/game/new</span>
                </div>
                <p>Initializes a new Wordle game with unique game ID and today's word.</p>
                <div class="response-example">
{
  <span class="json-key">"success"</span>: <span class="json-boolean">true</span>,
  <span class="json-key">"gameId"</span>: <span class="json-string">"1703123456789abc123def"</span>,
  <span class="json-key">"gameState"</span>: {
    <span class="json-key">"gameId"</span>: <span class="json-string">"1703123456789abc123def"</span>,
    <span class="json-key">"guesses"</span>: [],
    <span class="json-key">"gameState"</span>: <span class="json-string">"playing"</span>,
    <span class="json-key">"maxGuesses"</span>: <span class="json-number">6</span>,
    <span class="json-key">"targetWord"</span>: <span class="json-boolean">null</span>
  }
}
                </div>
            </div>

            <div class="demo-section">
                <h3>🔤 Make Guess</h3>
                <div class="api-endpoint">
                    <span class="method">POST</span> <span class="url">/api/game/:gameId/guess</span>
                </div>
                <p>Submit a 5-letter word guess and receive color-coded feedback.</p>
                <div class="response-example">
{
  <span class="json-key">"success"</span>: <span class="json-boolean">true</span>,
  <span class="json-key">"guess"</span>: {
    <span class="json-key">"word"</span>: <span class="json-string">"CRANE"</span>,
    <span class="json-key">"feedback"</span>: [
      <span class="json-string">"absent"</span>, <span class="json-string">"correct"</span>, 
      <span class="json-string">"absent"</span>, <span class="json-string">"present"</span>, 
      <span class="json-string">"absent"</span>
    ]
  }
}
                </div>
            </div>
        </div>

        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">🎯</div>
                <div class="feature-title">Complete Game Logic</div>
                <div class="feature-desc">Full Wordle implementation with 6-guess limit and win/lose conditions</div>
            </div>
            <div class="feature-card">
                <div class="feature-icon">📚</div>
                <div class="feature-title">Word Validation</div>
                <div class="feature-desc">315+ target words and 2000+ valid guesses with dictionary validation</div>
            </div>
            <div class="feature-card">
                <div class="feature-icon">🔄</div>
                <div class="feature-title">State Management</div>
                <div class="feature-desc">Track game progress, guesses, feedback, and timing data</div>
            </div>
            <div class="feature-card">
                <div class="feature-icon">🌐</div>
                <div class="feature-title">RESTful API</div>
                <div class="feature-desc">Clean, documented endpoints with proper HTTP status codes</div>
            </div>
            <div class="feature-card">
                <div class="feature-icon">📅</div>
                <div class="feature-title">Daily Words</div>
                <div class="feature-desc">Deterministic daily word selection, same for all players</div>
            </div>
            <div class="feature-card">
                <div class="feature-icon">🛡️</div>
                <div class="feature-title">Error Handling</div>
                <div class="feature-desc">Comprehensive validation and user-friendly error responses</div>
            </div>
        </div>

        <div class="tech-stack">
            <h3>🛠️ Technology Stack</h3>
            <div class="tech-tags">
                <span class="tech-tag">Node.js</span>
                <span class="tech-tag">Express.js</span>
                <span class="tech-tag">RESTful API</span>
                <span class="tech-tag">CORS</span>
                <span class="tech-tag">JSON</span>
                <span class="tech-tag">Game Logic</span>
                <span class="tech-tag">Algorithm Design</span>
                <span class="tech-tag">Error Handling</span>
            </div>
        </div>

        <a href="../../index.html" class="back-link">← Back to Portfolio</a>
    </div>

    <script>
        async function checkServer() {
            const statusEl = document.getElementById('serverStatus');
            const checkBtn = document.getElementById('checkServer');
            const liveBtn = document.getElementById('liveDemo');

            statusEl.style.display = 'block';
            statusEl.className = 'server-status';
            statusEl.innerHTML = '🔄 Checking server status...';
            checkBtn.disabled = true;

            try {
                const response = await fetch('http://localhost:3001/api/info', {
                    method: 'GET',
                    mode: 'cors'
                });

                if (response.ok) {
                    const data = await response.json();
                    statusEl.className = 'server-status online';
                    statusEl.innerHTML = `
                        ✅ Server is running!
                        <br>
                        <small>API Version: ${data.version} | Total Words: ${data.totalWords}</small>
                    `;
                    liveBtn.style.display = 'inline-block';
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                statusEl.className = 'server-status offline';
                statusEl.innerHTML = `
                    ❌ Server is not running
                    <br>
                    <small>Please follow the setup instructions above to start the server</small>
                `;
                liveBtn.style.display = 'none';
            }

            checkBtn.disabled = false;
        }

        // Auto-check server status on page load
        window.addEventListener('load', () => {
            setTimeout(checkServer, 1000);
        });
    </script>
</body>
</html>
