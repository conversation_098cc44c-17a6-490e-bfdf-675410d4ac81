<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Sudoku Solver & Game - Zayden Sharp</title>
  <meta name="description" content="Complete Sudoku game with solver, generator, and multiple difficulty levels">
  <meta name="keywords" content="sudoku, puzzle, solver, game, javascript, portfolio">
  <meta name="author" content="Zayden Sharp">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://zaydenjs.github.io/PortFolio-2025/projects/sudoku-solver/">
  <meta property="og:title" content="Sudoku Solver & Game - Logic Puzzle Challenge">
  <meta property="og:description" content="Complete Sudoku implementation with solver and puzzle generator">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary">
  <meta property="twitter:url" content="https://zaydenjs.github.io/PortFolio-2025/projects/sudoku-solver/">
  <meta property="twitter:title" content="Sudoku Solver & Game - Logic Puzzle Challenge">
  <meta property="twitter:description" content="Complete Sudoku implementation with solver and puzzle generator">

  <link rel="icon" type="image/svg+xml" href="../../assets/favicon.svg">
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="container">
    <!-- Back Navigation -->
    <nav class="back-nav">
      <a href="../../index.html" class="back-button">← Back to Portfolio</a>
    </nav>

    <!-- Header -->
    <header class="header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="title">SUDOKU</h1>
        </div>
        <div class="header-info">
          <div class="timer" id="timer">00:00</div>
          <div class="difficulty-badge" id="difficultyBadge">Easy</div>
        </div>
      </div>
    </header>

    <!-- Game Controls -->
    <div class="controls">
      <div class="control-group">
        <label for="difficultySelect">Difficulty:</label>
        <select id="difficultySelect" class="difficulty-select">
          <option value="easy">Easy</option>
          <option value="medium">Medium</option>
          <option value="hard">Hard</option>
          <option value="expert">Expert</option>
        </select>
      </div>
      
      <div class="control-buttons">
        <button class="btn btn-primary" id="newGameBtn">New Game</button>
        <button class="btn btn-secondary" id="solveBtn">Solve</button>
        <button class="btn btn-secondary" id="hintBtn">Hint</button>
        <button class="btn btn-secondary" id="checkBtn">Check</button>
        <button class="btn btn-secondary" id="clearBtn">Clear</button>
      </div>
    </div>

    <!-- Game Status -->
    <div class="game-status">
      <div class="status-item">
        <span class="status-label">Mistakes:</span>
        <span class="status-value" id="mistakeCount">0/3</span>
      </div>
      <div class="status-item">
        <span class="status-label">Hints:</span>
        <span class="status-value" id="hintCount">3</span>
      </div>
      <div class="status-item">
        <span class="status-label">Progress:</span>
        <span class="status-value" id="progressCount">0/81</span>
      </div>
    </div>

    <!-- Sudoku Grid -->
    <main class="game-container">
      <div class="sudoku-grid" id="sudokuGrid">
        <!-- Grid will be generated by JavaScript -->
      </div>
      
      <!-- Number Pad -->
      <div class="number-pad" id="numberPad">
        <button class="number-btn" data-number="1">1</button>
        <button class="number-btn" data-number="2">2</button>
        <button class="number-btn" data-number="3">3</button>
        <button class="number-btn" data-number="4">4</button>
        <button class="number-btn" data-number="5">5</button>
        <button class="number-btn" data-number="6">6</button>
        <button class="number-btn" data-number="7">7</button>
        <button class="number-btn" data-number="8">8</button>
        <button class="number-btn" data-number="9">9</button>
        <button class="number-btn erase-btn" data-number="0">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M22 3H7c-.69 0-1.23.35-1.59.88L0 12l5.41 8.11c.********** 1.59.89h15c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-3 12.59L17.59 17 14 13.41 10.41 17 9 15.59 12.59 12 9 8.41 10.41 7 14 10.59 17.59 7 19 8.41 15.41 12 19 15.59z"/>
          </svg>
        </button>
      </div>
    </main>

    <!-- Game Messages -->
    <div class="message-container">
      <div class="message" id="gameMessage" aria-live="polite"></div>
    </div>

    <!-- Modals -->
    <!-- Victory Modal -->
    <div class="modal" id="victoryModal">
      <div class="modal-content">
        <div class="modal-header">
          <h2>🎉 Congratulations!</h2>
          <button class="close-btn" data-modal="victoryModal">&times;</button>
        </div>
        <div class="modal-body">
          <div class="victory-stats">
            <div class="victory-stat">
              <div class="victory-label">Time</div>
              <div class="victory-value" id="finalTime">--:--</div>
            </div>
            <div class="victory-stat">
              <div class="victory-label">Difficulty</div>
              <div class="victory-value" id="finalDifficulty">Easy</div>
            </div>
            <div class="victory-stat">
              <div class="victory-label">Mistakes</div>
              <div class="victory-value" id="finalMistakes">0</div>
            </div>
            <div class="victory-stat">
              <div class="victory-label">Hints Used</div>
              <div class="victory-value" id="finalHints">0</div>
            </div>
          </div>
          <div class="victory-actions">
            <button class="btn btn-primary" id="playAgainBtn">Play Again</button>
            <button class="btn btn-secondary" id="shareScoreBtn">Share Score</button>
          </div>
        </div>
      </div>
    </div>

    <!-- Game Over Modal -->
    <div class="modal" id="gameOverModal">
      <div class="modal-content">
        <div class="modal-header">
          <h2>😞 Game Over</h2>
          <button class="close-btn" data-modal="gameOverModal">&times;</button>
        </div>
        <div class="modal-body">
          <p>You've made too many mistakes!</p>
          <p>Don't worry, practice makes perfect.</p>
          <div class="game-over-actions">
            <button class="btn btn-primary" id="tryAgainBtn">Try Again</button>
            <button class="btn btn-secondary" id="showSolutionBtn">Show Solution</button>
          </div>
        </div>
      </div>
    </div>

    <!-- Help Modal -->
    <div class="modal" id="helpModal">
      <div class="modal-content">
        <div class="modal-header">
          <h2>How to Play Sudoku</h2>
          <button class="close-btn" data-modal="helpModal">&times;</button>
        </div>
        <div class="modal-body">
          <div class="help-section">
            <h3>Objective</h3>
            <p>Fill the 9×9 grid so that each column, each row, and each of the nine 3×3 sub-grids contains all digits from 1 to 9.</p>
          </div>
          
          <div class="help-section">
            <h3>Rules</h3>
            <ul>
              <li>Each row must contain the numbers 1-9 with no repetition</li>
              <li>Each column must contain the numbers 1-9 with no repetition</li>
              <li>Each 3×3 box must contain the numbers 1-9 with no repetition</li>
            </ul>
          </div>
          
          <div class="help-section">
            <h3>How to Play</h3>
            <ul>
              <li>Click on an empty cell to select it</li>
              <li>Use the number pad or keyboard to enter numbers</li>
              <li>Use the eraser button or press 0/Delete to clear a cell</li>
              <li>Use hints when you're stuck (limited per game)</li>
              <li>Check your progress with the Check button</li>
            </ul>
          </div>
          
          <div class="help-section">
            <h3>Difficulty Levels</h3>
            <ul>
              <li><strong>Easy:</strong> 36-40 clues, basic techniques</li>
              <li><strong>Medium:</strong> 30-35 clues, intermediate techniques</li>
              <li><strong>Hard:</strong> 25-29 clues, advanced techniques</li>
              <li><strong>Expert:</strong> 20-24 clues, expert techniques</li>
            </ul>
          </div>
          
          <div class="help-section">
            <h3>Tips</h3>
            <ul>
              <li>Start with cells that have the fewest possibilities</li>
              <li>Look for naked singles (cells with only one possibility)</li>
              <li>Use elimination to narrow down possibilities</li>
              <li>Focus on one number at a time across the entire grid</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Settings Modal -->
    <div class="modal" id="settingsModal">
      <div class="modal-content">
        <div class="modal-header">
          <h2>Settings</h2>
          <button class="close-btn" data-modal="settingsModal">&times;</button>
        </div>
        <div class="modal-body">
          <div class="setting">
            <div class="setting-text">
              <div class="setting-title">Show Mistakes</div>
              <div class="setting-description">Highlight incorrect entries immediately</div>
            </div>
            <div class="setting-switch">
              <input type="checkbox" id="showMistakes" class="switch-input" checked>
              <label for="showMistakes" class="switch-label"></label>
            </div>
          </div>
          
          <div class="setting">
            <div class="setting-text">
              <div class="setting-title">Auto-Check</div>
              <div class="setting-description">Automatically validate entries</div>
            </div>
            <div class="setting-switch">
              <input type="checkbox" id="autoCheck" class="switch-input" checked>
              <label for="autoCheck" class="switch-label"></label>
            </div>
          </div>
          
          <div class="setting">
            <div class="setting-text">
              <div class="setting-title">Highlight Related</div>
              <div class="setting-description">Highlight row, column, and box of selected cell</div>
            </div>
            <div class="setting-switch">
              <input type="checkbox" id="highlightRelated" class="switch-input" checked>
              <label for="highlightRelated" class="switch-label"></label>
            </div>
          </div>
          
          <div class="setting">
            <div class="setting-text">
              <div class="setting-title">Dark Theme</div>
              <div class="setting-description">Switch to dark mode</div>
            </div>
            <div class="setting-switch">
              <input type="checkbox" id="darkTheme" class="switch-input">
              <label for="darkTheme" class="switch-label"></label>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
      <button class="icon-btn" id="helpBtn" aria-label="Help" title="How to play">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z"/>
        </svg>
      </button>
      <button class="icon-btn" id="settingsBtn" aria-label="Settings" title="Game settings">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
          <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.82,11.69,4.82,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
        </svg>
      </button>
    </div>

    <!-- Toast Notifications -->
    <div class="toast" id="toast"></div>
  </div>

  <script src="sudoku-solver.js"></script>
  <script src="main.js"></script>
</body>
</html>
