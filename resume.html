<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - Full-Stack Developer Resume</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
            padding: 20px;
        }
        
        .resume {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .name {
            font-size: 2.5em;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }
        
        .title {
            font-size: 1.3em;
            color: #666;
            margin-bottom: 15px;
        }
        
        .contact {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .contact a {
            color: #007bff;
            text-decoration: none;
        }
        
        .section {
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 1.4em;
            color: #007bff;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
            margin-bottom: 15px;
        }
        
        .experience-item, .project-item {
            margin-bottom: 20px;
        }
        
        .job-title {
            font-weight: bold;
            font-size: 1.1em;
            color: #333;
        }
        
        .company {
            color: #007bff;
            font-weight: 500;
        }
        
        .date {
            color: #666;
            font-style: italic;
        }
        
        .skills {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .skill-category {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        
        .skill-category h4 {
            color: #007bff;
            margin-bottom: 10px;
        }
        
        .skill-list {
            list-style: none;
        }
        
        .skill-list li {
            padding: 2px 0;
            color: #555;
        }
        
        @media print {
            body { background: white; padding: 0; }
            .resume { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="resume">
        <header class="header">
            <h1 class="name">Zayden Sharp</h1>
            <p class="title">Full-Stack Developer</p>
            <div class="contact">
                <span>📧 Contact via Portfolio</span>
                <span>💼 <a href="https://linkedin.com/in/zayden-sharp">LinkedIn</a></span>
                <span>🐙 <a href="https://github.com/ZaydenJS">GitHub</a></span>
                <span>🌐 <a href="https://zaydenjs.github.io/PortFolio-2025/">Portfolio</a></span>
            </div>
        </header>

        <section class="section">
            <h2 class="section-title">Professional Summary</h2>
            <p>Full-Stack Developer specializing in vanilla JavaScript and Node.js with <strong>6+ production-ready applications</strong> in portfolio. Demonstrated expertise in complex algorithm implementation (Sudoku solving, Wordle logic), real-time collaboration systems (WebRTC), and performance optimization (Lighthouse scores ≥90%). Strong foundation in modern web standards, accessibility compliance (WCAG 2.1), and enterprise-grade application architecture.</p>
        </section>

        <section class="section">
            <h2 class="section-title">Technical Skills</h2>
            <div class="skills">
                <div class="skill-category">
                    <h4>Frontend</h4>
                    <ul class="skill-list">
                        <li>JavaScript (ES6+)</li>
                        <li>HTML5 & CSS3</li>
                        <li>Canvas API</li>
                        <li>WebRTC</li>
                        <li>PWA Development</li>
                        <li>Responsive Design</li>
                    </ul>
                </div>
                <div class="skill-category">
                    <h4>Backend</h4>
                    <ul class="skill-list">
                        <li>Node.js</li>
                        <li>Express.js</li>
                        <li>RESTful APIs</li>
                        <li>WebSocket</li>
                        <li>Algorithm Design</li>
                        <li>Data Processing</li>
                    </ul>
                </div>
                <div class="skill-category">
                    <h4>Tools & Practices</h4>
                    <ul class="skill-list">
                        <li>Git & GitHub</li>
                        <li>VS Code</li>
                        <li>Performance Optimization</li>
                        <li>Accessibility (WCAG)</li>
                        <li>Cross-browser Testing</li>
                        <li>Code Documentation</li>
                    </ul>
                </div>
            </div>
        </section>

        <section class="section">
            <h2 class="section-title">Featured Projects</h2>
            
            <div class="project-item">
                <div class="job-title">CollabSpace - Enterprise Workspace Platform</div>
                <div class="company">Full-Stack TypeScript Application</div>
                <p>Enterprise-grade collaborative platform with real-time chat, interactive whiteboard, task management, and focus timer. <strong>Features:</strong> Room-based isolation, LocalStorage persistence, Canvas API integration.</p>
                <p><strong>Technologies:</strong> TypeScript, Canvas API, LocalStorage, Real-time Sync</p>
                <p><strong>Metrics:</strong> 90+ Lighthouse Performance Score, Mobile-responsive design</p>
            </div>

            <div class="project-item">
                <div class="job-title">CollaboCanvas - WebRTC Whiteboard</div>
                <div class="company">Real-time P2P Application</div>
                <p>Peer-to-peer collaborative whiteboard with zero server dependency. <strong>Features:</strong> WebRTC data channels, real-time drawing sync, room management, multi-user support.</p>
                <p><strong>Technologies:</strong> WebRTC, Canvas API, P2P Networking, simple-peer</p>
                <p><strong>Metrics:</strong> Sub-100ms latency, Cross-browser compatibility</p>
            </div>

            <div class="project-item">
                <div class="job-title">Backend API Microservices</div>
                <div class="company">Node.js Production APIs</div>
                <p><strong>Sudoku API:</strong> Advanced backtracking algorithms with constraint propagation. <strong>Wordle API:</strong> Word validation with 5000+ word dictionary. <strong>Typing API:</strong> Real-time WPM calculation and accuracy analytics.</p>
                <p><strong>Technologies:</strong> Node.js, Express.js, Algorithm Design, Performance Optimization</p>
                <p><strong>Metrics:</strong> Docker containerized, RESTful design, Comprehensive error handling</p>
            </div>
        </section>

        <section class="section">
            <h2 class="section-title">Education & Certifications</h2>
            <div class="experience-item">
                <div class="job-title">Self-Taught Developer</div>
                <div class="date">2020 - Present</div>
                <p>Continuous learning through practical projects, online courses, and open-source contributions. Focus on modern web technologies and best practices.</p>
            </div>
        </section>

        <section class="section">
            <h2 class="section-title">Key Achievements</h2>
            <ul>
                <li>Built 6+ production-ready web applications using vanilla JavaScript</li>
                <li>Developed 3 backend APIs with comprehensive documentation</li>
                <li>Implemented real-time features using WebRTC and WebSocket</li>
                <li>Created responsive, accessible applications following WCAG guidelines</li>
                <li>Maintained clean, well-documented codebases with 100% vanilla JS</li>
            </ul>
        </section>
    </div>
</body>
</html>
