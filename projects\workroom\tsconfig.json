{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable", "WebWorker"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "removeComments": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "useDefineForClassFields": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/hooks/*": ["./src/hooks/*"], "@/stores/*": ["./src/stores/*"], "@/services/*": ["./src/services/*"], "@/utils/*": ["./src/utils/*"], "@/types/*": ["./src/types/*"], "@/assets/*": ["./src/assets/*"], "@/styles/*": ["./src/styles/*"], "@/tests/*": ["./src/tests/*"]}, "types": ["vite/client", "vitest/globals", "@testing-library/jest-dom", "node"]}, "include": ["src/**/*", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "tests/**/*", "vite.config.ts", "vitest.config.ts", "playwright.config.ts"], "exclude": ["node_modules", "dist", "build", "coverage", "storybook-static", "**/*.stories.ts", "**/*.stories.tsx"], "references": [{"path": "./tsconfig.node.json"}]}