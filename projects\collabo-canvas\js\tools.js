/**
 * CollaboCanvas Tools Module
 * Handles drawing tools and UI interactions
 */

class ToolsManager {
  constructor() {
    this.currentTool = 'pen';
    this.currentColor = '#000000';
    this.currentSize = 5;
    this.tools = ['pen', 'eraser', 'line', 'rectangle', 'circle', 'text'];
    
    // Tool configurations
    this.toolConfigs = {
      pen: { cursor: 'crosshair', icon: '✏️' },
      eraser: { cursor: 'crosshair', icon: '🧹' },
      line: { cursor: 'crosshair', icon: '📏' },
      rectangle: { cursor: 'crosshair', icon: '⬜' },
      circle: { cursor: 'crosshair', icon: '⭕' },
      text: { cursor: 'text', icon: '📝' }
    };
    
    // Event callbacks
    this.onToolChange = null;
    this.onColorChange = null;
    this.onSizeChange = null;
    this.onTextAdd = null;
  }

  init() {
    this.setupToolButtons();
    this.setupColorPicker();
    this.setupBrushSize();
    this.setupActionButtons();
    this.setupTextModal();
    
    // Set initial tool
    this.setTool('pen');
    
    console.log('Tools initialized successfully');
  }

  setupToolButtons() {
    const toolButtons = document.querySelectorAll('.tool-btn[data-tool]');
    
    toolButtons.forEach(button => {
      button.addEventListener('click', () => {
        const tool = button.dataset.tool;
        this.setTool(tool);
      });
    });
  }

  setupColorPicker() {
    const colorPicker = document.getElementById('colorPicker');
    const colorPresets = document.querySelectorAll('.color-preset');
    
    if (colorPicker) {
      colorPicker.addEventListener('change', (e) => {
        this.setColor(e.target.value);
      });
    }
    
    colorPresets.forEach(preset => {
      preset.addEventListener('click', () => {
        const color = preset.dataset.color;
        this.setColor(color);
        if (colorPicker) {
          colorPicker.value = color;
        }
      });
    });
  }

  setupBrushSize() {
    const brushSize = document.getElementById('brushSize');
    const brushSizeValue = document.getElementById('brushSizeValue');
    
    if (brushSize && brushSizeValue) {
      brushSize.addEventListener('input', (e) => {
        const size = parseInt(e.target.value);
        this.setSize(size);
        brushSizeValue.textContent = size;
      });
    }
  }

  setupActionButtons() {
    // Undo button
    const undoBtn = document.getElementById('undoBtn');
    if (undoBtn) {
      undoBtn.addEventListener('click', () => {
        if (this.onUndo) {
          this.onUndo();
        }
      });
    }

    // Redo button
    const redoBtn = document.getElementById('redoBtn');
    if (redoBtn) {
      redoBtn.addEventListener('click', () => {
        if (this.onRedo) {
          this.onRedo();
        }
      });
    }

    // Clear button
    const clearBtn = document.getElementById('clearBtn');
    if (clearBtn) {
      clearBtn.addEventListener('click', () => {
        if (confirm('Are you sure you want to clear the canvas? This action cannot be undone.')) {
          if (this.onClear) {
            this.onClear();
          }
        }
      });
    }

    // Save button
    const saveBtn = document.getElementById('saveBtn');
    if (saveBtn) {
      saveBtn.addEventListener('click', () => {
        if (this.onSave) {
          this.onSave();
        }
      });
    }

    // Export button
    const exportBtn = document.getElementById('exportBtn');
    if (exportBtn) {
      exportBtn.addEventListener('click', () => {
        if (this.onExport) {
          this.onExport();
        }
      });
    }
  }

  setupTextModal() {
    const textModal = document.getElementById('textModal');
    const closeTextModal = document.getElementById('closeTextModal');
    const cancelText = document.getElementById('cancelText');
    const addText = document.getElementById('addText');
    const textInput = document.getElementById('textInput');
    const fontSize = document.getElementById('fontSize');
    const fontSizeValue = document.getElementById('fontSizeValue');
    const boldText = document.getElementById('boldText');
    const italicText = document.getElementById('italicText');

    // Font size slider
    if (fontSize && fontSizeValue) {
      fontSize.addEventListener('input', (e) => {
        fontSizeValue.textContent = e.target.value + 'px';
      });
    }

    // Close modal handlers
    [closeTextModal, cancelText].forEach(btn => {
      if (btn) {
        btn.addEventListener('click', () => {
          this.hideTextModal();
        });
      }
    });

    // Add text handler
    if (addText) {
      addText.addEventListener('click', () => {
        const text = textInput?.value.trim();
        if (text) {
          const textData = {
            text: text,
            fontSize: parseInt(fontSize?.value || 24),
            bold: boldText?.checked || false,
            italic: italicText?.checked || false
          };
          
          if (this.onTextAdd) {
            this.onTextAdd(textData);
          }
          
          this.hideTextModal();
          this.resetTextModal();
        }
      });
    }

    // Close modal on outside click
    if (textModal) {
      textModal.addEventListener('click', (e) => {
        if (e.target === textModal) {
          this.hideTextModal();
        }
      });
    }
  }

  setTool(tool) {
    if (!this.tools.includes(tool)) {
      console.warn(`Unknown tool: ${tool}`);
      return;
    }

    this.currentTool = tool;
    
    // Update UI
    this.updateToolButtons();
    this.updateCursor();
    
    // Handle text tool special case
    if (tool === 'text') {
      this.showTextModal();
    }
    
    if (this.onToolChange) {
      this.onToolChange(tool);
    }
  }

  setColor(color) {
    this.currentColor = color;
    
    if (this.onColorChange) {
      this.onColorChange(color);
    }
  }

  setSize(size) {
    this.currentSize = size;
    
    if (this.onSizeChange) {
      this.onSizeChange(size);
    }
  }

  updateToolButtons() {
    const toolButtons = document.querySelectorAll('.tool-btn[data-tool]');
    
    toolButtons.forEach(button => {
      const tool = button.dataset.tool;
      if (tool === this.currentTool) {
        button.classList.add('active');
      } else {
        button.classList.remove('active');
      }
    });
  }

  updateCursor() {
    const canvas = document.getElementById('whiteboard');
    if (canvas && this.toolConfigs[this.currentTool]) {
      canvas.style.cursor = this.toolConfigs[this.currentTool].cursor;
    }
  }

  showTextModal() {
    const textModal = document.getElementById('textModal');
    if (textModal) {
      textModal.style.display = 'flex';
      
      // Focus on text input
      const textInput = document.getElementById('textInput');
      if (textInput) {
        setTimeout(() => textInput.focus(), 100);
      }
    }
  }

  hideTextModal() {
    const textModal = document.getElementById('textModal');
    if (textModal) {
      textModal.style.display = 'none';
    }
  }

  resetTextModal() {
    const textInput = document.getElementById('textInput');
    const fontSize = document.getElementById('fontSize');
    const fontSizeValue = document.getElementById('fontSizeValue');
    const boldText = document.getElementById('boldText');
    const italicText = document.getElementById('italicText');

    if (textInput) textInput.value = '';
    if (fontSize) fontSize.value = '24';
    if (fontSizeValue) fontSizeValue.textContent = '24px';
    if (boldText) boldText.checked = false;
    if (italicText) italicText.checked = false;
  }

  // Keyboard shortcuts
  handleKeyboardShortcuts(e) {
    // Prevent shortcuts when typing in inputs
    if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
      return;
    }

    if (e.ctrlKey || e.metaKey) {
      switch (e.key.toLowerCase()) {
        case 'z':
          e.preventDefault();
          if (e.shiftKey) {
            // Redo
            if (this.onRedo) {
              this.onRedo();
            }
          } else {
            // Undo
            if (this.onUndo) {
              this.onUndo();
            }
          }
          break;
        case 'y':
          e.preventDefault();
          // Redo
          if (this.onRedo) {
            this.onRedo();
          }
          break;
        case 's':
          e.preventDefault();
          if (this.onSave) {
            this.onSave();
          }
          break;
        case 'e':
          e.preventDefault();
          if (this.onExport) {
            this.onExport();
          }
          break;
      }
    } else {
      // Tool shortcuts
      switch (e.key.toLowerCase()) {
        case 'p':
          this.setTool('pen');
          break;
        case 'e':
          this.setTool('eraser');
          break;
        case 'l':
          this.setTool('line');
          break;
        case 'r':
          this.setTool('rectangle');
          break;
        case 'c':
          this.setTool('circle');
          break;
        case 't':
          this.setTool('text');
          break;
        case 'escape':
          this.hideTextModal();
          break;
      }
    }
  }

  // Color utilities
  getRandomColor() {
    const colors = [
      '#ff0000', '#00ff00', '#0000ff', '#ffff00', 
      '#ff00ff', '#00ffff', '#ff8000', '#8000ff',
      '#ff0080', '#80ff00', '#0080ff', '#ff8080'
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  }

  hexToRgb(hex) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  }

  rgbToHex(r, g, b) {
    return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
  }

  // Tool validation
  isValidTool(tool) {
    return this.tools.includes(tool);
  }

  isValidColor(color) {
    const s = new Option().style;
    s.color = color;
    return s.color !== '';
  }

  isValidSize(size) {
    return typeof size === 'number' && size > 0 && size <= 100;
  }

  // Get current tool state
  getToolState() {
    return {
      tool: this.currentTool,
      color: this.currentColor,
      size: this.currentSize
    };
  }

  // Set tool state (for synchronization)
  setToolState(state) {
    if (state.tool && this.isValidTool(state.tool)) {
      this.setTool(state.tool);
    }
    if (state.color && this.isValidColor(state.color)) {
      this.setColor(state.color);
    }
    if (state.size && this.isValidSize(state.size)) {
      this.setSize(state.size);
    }
  }

  // Tool presets
  getToolPresets() {
    return {
      pen: { size: 5, color: '#000000' },
      marker: { size: 15, color: '#ff0000' },
      brush: { size: 20, color: '#0000ff' },
      pencil: { size: 2, color: '#666666' },
      highlighter: { size: 25, color: '#ffff00' }
    };
  }

  applyPreset(presetName) {
    const presets = this.getToolPresets();
    const preset = presets[presetName];
    
    if (preset) {
      this.setTool('pen');
      this.setColor(preset.color);
      this.setSize(preset.size);
      
      // Update UI
      const colorPicker = document.getElementById('colorPicker');
      const brushSize = document.getElementById('brushSize');
      const brushSizeValue = document.getElementById('brushSizeValue');
      
      if (colorPicker) colorPicker.value = preset.color;
      if (brushSize) brushSize.value = preset.size;
      if (brushSizeValue) brushSizeValue.textContent = preset.size;
    }
  }

  // Cleanup
  destroy() {
    // Remove event listeners if needed
    this.onToolChange = null;
    this.onColorChange = null;
    this.onSizeChange = null;
    this.onTextAdd = null;
    this.onUndo = null;
    this.onRedo = null;
    this.onClear = null;
    this.onSave = null;
    this.onExport = null;
  }
}

export default ToolsManager;
