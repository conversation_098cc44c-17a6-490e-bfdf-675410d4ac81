/**
 * EcoShop - Sustainable E-commerce Demo
 * Main application logic with Stripe integration
 */

class EcoShopApp {
  constructor() {
    this.products = [];
    this.cart = [];
    this.currentFilter = 'all';
    this.currentTheme = 'light';
    this.stripe = null;
    this.isLoading = false;
    
    // Stripe configuration (demo keys)
    this.stripeConfig = {
      publishableKey: 'pk_test_demo_key', // Demo key - replace with real key
      currency: 'usd',
      country: 'US'
    };
    
    // Load cart from localStorage
    this.loadCart();
  }

  async init() {
    try {
      console.log('Initializing EcoShop...');
      
      // Initialize Stripe
      await this.initializeStripe();
      
      // Setup theme
      this.setupTheme();
      
      // Setup event listeners
      this.setupEventListeners();
      
      // Load products
      await this.loadProducts();
      
      // Update cart UI
      this.updateCartUI();
      
      // Hide loading overlay
      this.hideLoading();
      
      console.log('EcoShop initialized successfully');
      
    } catch (error) {
      console.error('Failed to initialize EcoShop:', error);
      this.showNotification('Failed to initialize application', 'error');
      this.hideLoading();
    }
  }

  async initializeStripe() {
    try {
      // In a real application, you would use your actual Stripe publishable key
      // For demo purposes, we'll simulate Stripe initialization
      console.log('Stripe initialized (demo mode)');
      this.stripe = {
        redirectToCheckout: (options) => {
          console.log('Demo checkout:', options);
          this.simulateCheckout(options);
        }
      };
    } catch (error) {
      console.error('Failed to initialize Stripe:', error);
      throw error;
    }
  }

  setupTheme() {
    const savedTheme = localStorage.getItem('ecoshop-theme');
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    this.currentTheme = savedTheme || (systemPrefersDark ? 'dark' : 'light');
    
    this.setTheme(this.currentTheme);
  }

  setTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('ecoshop-theme', theme);
    this.currentTheme = theme;
    
    const themeIcon = document.querySelector('.theme-icon');
    if (themeIcon) {
      themeIcon.textContent = theme === 'dark' ? '☀️' : '🌙';
    }
  }

  setupEventListeners() {
    // Theme toggle
    const themeToggle = document.getElementById('themeToggle');
    themeToggle?.addEventListener('click', () => {
      const newTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
      this.setTheme(newTheme);
    });

    // Navigation
    this.setupNavigation();
    
    // Product filters
    this.setupProductFilters();
    
    // Cart functionality
    this.setupCart();
    
    // Product modal
    this.setupProductModal();
    
    // Contact form
    this.setupContactForm();
    
    // Newsletter form
    this.setupNewsletterForm();
    
    // Load more products
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    loadMoreBtn?.addEventListener('click', () => {
      this.loadMoreProducts();
    });
  }

  setupNavigation() {
    // Mobile menu toggle
    const navToggle = document.getElementById('navToggle');
    const navMenu = document.getElementById('navMenu');
    
    navToggle?.addEventListener('click', () => {
      navMenu?.classList.toggle('active');
    });

    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        const href = link.getAttribute('href');
        if (href.startsWith('#')) {
          e.preventDefault();
          const target = document.querySelector(href);
          if (target) {
            target.scrollIntoView({ behavior: 'smooth' });
            
            // Update active nav link
            navLinks.forEach(l => l.classList.remove('active'));
            link.classList.add('active');
            
            // Close mobile menu
            navMenu?.classList.remove('active');
          }
        }
      });
    });

    // Update active nav link on scroll
    window.addEventListener('scroll', () => {
      this.updateActiveNavLink();
    });
  }

  updateActiveNavLink() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');
    
    let current = '';
    sections.forEach(section => {
      const sectionTop = section.offsetTop - 100;
      if (window.scrollY >= sectionTop) {
        current = section.getAttribute('id');
      }
    });

    navLinks.forEach(link => {
      link.classList.remove('active');
      if (link.getAttribute('href') === `#${current}`) {
        link.classList.add('active');
      }
    });
  }

  setupProductFilters() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    
    filterButtons.forEach(button => {
      button.addEventListener('click', () => {
        const filter = button.dataset.filter;
        
        // Update active filter button
        filterButtons.forEach(btn => btn.classList.remove('active'));
        button.classList.add('active');
        
        // Filter products
        this.filterProducts(filter);
      });
    });
  }

  filterProducts(filter) {
    this.currentFilter = filter;
    this.renderProducts();
  }

  setupCart() {
    const cartButton = document.getElementById('cartButton');
    const cartSidebar = document.getElementById('cartSidebar');
    const cartClose = document.getElementById('cartClose');
    const cartOverlay = document.getElementById('cartOverlay');
    const checkoutBtn = document.getElementById('checkoutBtn');

    // Open cart
    cartButton?.addEventListener('click', () => {
      this.openCart();
    });

    // Close cart
    cartClose?.addEventListener('click', () => {
      this.closeCart();
    });

    cartOverlay?.addEventListener('click', () => {
      this.closeCart();
    });

    // Checkout
    checkoutBtn?.addEventListener('click', () => {
      this.proceedToCheckout();
    });
  }

  setupProductModal() {
    const modal = document.getElementById('productModal');
    const modalClose = document.getElementById('modalClose');
    const modalOverlay = document.getElementById('modalOverlay');
    const addToCartBtn = document.getElementById('addToCartBtn');
    const buyNowBtn = document.getElementById('buyNowBtn');
    const quantityMinus = document.getElementById('quantityMinus');
    const quantityPlus = document.getElementById('quantityPlus');
    const quantityInput = document.getElementById('modalQuantity');

    // Close modal
    [modalClose, modalOverlay].forEach(element => {
      element?.addEventListener('click', () => {
        this.closeProductModal();
      });
    });

    // Quantity controls
    quantityMinus?.addEventListener('click', () => {
      const current = parseInt(quantityInput.value);
      if (current > 1) {
        quantityInput.value = current - 1;
      }
    });

    quantityPlus?.addEventListener('click', () => {
      const current = parseInt(quantityInput.value);
      if (current < 10) {
        quantityInput.value = current + 1;
      }
    });

    // Add to cart
    addToCartBtn?.addEventListener('click', () => {
      this.addToCartFromModal();
    });

    // Buy now
    buyNowBtn?.addEventListener('click', () => {
      this.buyNowFromModal();
    });
  }

  setupContactForm() {
    const contactForm = document.getElementById('contactForm');
    
    contactForm?.addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleContactForm(e.target);
    });
  }

  setupNewsletterForm() {
    const newsletterForm = document.querySelector('.newsletter-form');
    
    newsletterForm?.addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleNewsletterForm(e.target);
    });
  }

  async loadProducts() {
    try {
      // Simulate loading products from an API
      this.products = this.generateDemoProducts();
      this.renderProducts();
    } catch (error) {
      console.error('Failed to load products:', error);
      this.showNotification('Failed to load products', 'error');
    }
  }

  generateDemoProducts() {
    return [
      {
        id: 1,
        name: 'Bamboo Toothbrush Set',
        description: 'Eco-friendly bamboo toothbrushes with soft bristles. Pack of 4.',
        price: 12.99,
        image: 'https://images.unsplash.com/photo-1607613009820-a29f7bb81c04?w=400&h=300&fit=crop',
        category: 'personal',
        badge: 'Bestseller',
        inStock: true
      },
      {
        id: 2,
        name: 'Organic Cotton Tote Bag',
        description: 'Durable organic cotton tote bag perfect for shopping and daily use.',
        price: 18.99,
        image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=300&fit=crop',
        category: 'clothing',
        badge: 'New',
        inStock: true
      },
      {
        id: 3,
        name: 'Stainless Steel Water Bottle',
        description: 'Insulated stainless steel water bottle keeps drinks cold for 24 hours.',
        price: 24.99,
        image: 'https://images.unsplash.com/photo-1602143407151-7111542de6e8?w=400&h=300&fit=crop',
        category: 'kitchen',
        badge: 'Popular',
        inStock: true
      },
      {
        id: 4,
        name: 'Solar Garden Lights',
        description: 'Beautiful solar-powered LED garden lights. Set of 6.',
        price: 34.99,
        image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop',
        category: 'home',
        badge: 'Eco-Friendly',
        inStock: true
      },
      {
        id: 5,
        name: 'Natural Soap Bar Set',
        description: 'Handmade natural soap bars with essential oils. Pack of 3.',
        price: 16.99,
        image: 'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400&h=300&fit=crop',
        category: 'personal',
        badge: 'Organic',
        inStock: true
      },
      {
        id: 6,
        name: 'Recycled Notebook Set',
        description: 'Notebooks made from 100% recycled paper. Set of 3.',
        price: 14.99,
        image: 'https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?w=400&h=300&fit=crop',
        category: 'home',
        badge: 'Recycled',
        inStock: true
      }
    ];
  }

  renderProducts() {
    const productsGrid = document.getElementById('productsGrid');
    if (!productsGrid) return;

    const filteredProducts = this.currentFilter === 'all'
      ? this.products
      : this.products.filter(product => product.category === this.currentFilter);

    productsGrid.innerHTML = filteredProducts.map(product => `
      <div class="product-card" data-product-id="${product.id}">
        <div class="product-image">
          <img src="${product.image}" alt="${product.name}" loading="lazy">
          ${product.badge ? `<div class="product-badge">${product.badge}</div>` : ''}
        </div>
        <div class="product-info">
          <h3 class="product-name">${product.name}</h3>
          <p class="product-description">${product.description}</p>
          <div class="product-price">$${product.price.toFixed(2)}</div>
          <div class="product-actions">
            <button class="btn btn-outline btn-sm quick-add-btn" data-product-id="${product.id}">
              Quick Add
            </button>
            <button class="btn btn-primary btn-sm view-product-btn" data-product-id="${product.id}">
              View Details
            </button>
          </div>
        </div>
      </div>
    `).join('');

    // Add event listeners to product cards
    this.setupProductCardListeners();
  }

  setupProductCardListeners() {
    const quickAddBtns = document.querySelectorAll('.quick-add-btn');
    const viewProductBtns = document.querySelectorAll('.view-product-btn');
    const productCards = document.querySelectorAll('.product-card');

    quickAddBtns.forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.stopPropagation();
        const productId = parseInt(btn.dataset.productId);
        this.addToCart(productId, 1);
      });
    });

    viewProductBtns.forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.stopPropagation();
        const productId = parseInt(btn.dataset.productId);
        this.openProductModal(productId);
      });
    });

    productCards.forEach(card => {
      card.addEventListener('click', () => {
        const productId = parseInt(card.dataset.productId);
        this.openProductModal(productId);
      });
    });
  }

  openProductModal(productId) {
    const product = this.products.find(p => p.id === productId);
    if (!product) return;

    const modal = document.getElementById('productModal');
    const modalOverlay = document.getElementById('modalOverlay');

    // Update modal content
    document.getElementById('modalProductName').textContent = product.name;
    document.getElementById('modalProductImage').src = product.image;
    document.getElementById('modalProductImage').alt = product.name;
    document.getElementById('modalProductPrice').textContent = `$${product.price.toFixed(2)}`;
    document.getElementById('modalProductDescription').textContent = product.description;
    document.getElementById('modalQuantity').value = 1;

    // Store current product ID
    modal.dataset.productId = productId;

    // Show modal
    modal.classList.add('active');
    modalOverlay.classList.add('active');
    document.body.style.overflow = 'hidden';
  }

  closeProductModal() {
    const modal = document.getElementById('productModal');
    const modalOverlay = document.getElementById('modalOverlay');

    modal.classList.remove('active');
    modalOverlay.classList.remove('active');
    document.body.style.overflow = '';
  }

  addToCartFromModal() {
    const modal = document.getElementById('productModal');
    const productId = parseInt(modal.dataset.productId);
    const quantity = parseInt(document.getElementById('modalQuantity').value);

    this.addToCart(productId, quantity);
    this.closeProductModal();
  }

  buyNowFromModal() {
    const modal = document.getElementById('productModal');
    const productId = parseInt(modal.dataset.productId);
    const quantity = parseInt(document.getElementById('modalQuantity').value);

    this.addToCart(productId, quantity);
    this.closeProductModal();
    this.openCart();
  }

  addToCart(productId, quantity = 1) {
    const product = this.products.find(p => p.id === productId);
    if (!product) return;

    const existingItem = this.cart.find(item => item.id === productId);

    if (existingItem) {
      existingItem.quantity += quantity;
    } else {
      this.cart.push({
        ...product,
        quantity: quantity
      });
    }

    this.saveCart();
    this.updateCartUI();
    this.showNotification(`${product.name} added to cart!`, 'success');
  }

  removeFromCart(productId) {
    this.cart = this.cart.filter(item => item.id !== productId);
    this.saveCart();
    this.updateCartUI();
  }

  updateCartQuantity(productId, quantity) {
    const item = this.cart.find(item => item.id === productId);
    if (item) {
      if (quantity <= 0) {
        this.removeFromCart(productId);
      } else {
        item.quantity = quantity;
        this.saveCart();
        this.updateCartUI();
      }
    }
  }

  openCart() {
    const cartSidebar = document.getElementById('cartSidebar');
    const cartOverlay = document.getElementById('cartOverlay');

    cartSidebar.classList.add('open');
    cartOverlay.classList.add('active');
    document.body.style.overflow = 'hidden';
  }

  closeCart() {
    const cartSidebar = document.getElementById('cartSidebar');
    const cartOverlay = document.getElementById('cartOverlay');

    cartSidebar.classList.remove('open');
    cartOverlay.classList.remove('active');
    document.body.style.overflow = '';
  }

  updateCartUI() {
    const cartCount = document.getElementById('cartCount');
    const cartItems = document.getElementById('cartItems');
    const cartEmpty = document.getElementById('cartEmpty');
    const cartFooter = document.getElementById('cartFooter');
    const cartSubtotal = document.getElementById('cartSubtotal');
    const cartTotal = document.getElementById('cartTotal');

    // Update cart count
    const totalItems = this.cart.reduce((sum, item) => sum + item.quantity, 0);
    if (cartCount) {
      cartCount.textContent = totalItems;
      cartCount.style.display = totalItems > 0 ? 'flex' : 'none';
    }

    // Update cart content
    if (this.cart.length === 0) {
      cartEmpty.style.display = 'block';
      cartFooter.style.display = 'none';
      cartItems.innerHTML = '';
    } else {
      cartEmpty.style.display = 'none';
      cartFooter.style.display = 'block';

      cartItems.innerHTML = this.cart.map(item => `
        <div class="cart-item">
          <div class="cart-item-image">
            <img src="${item.image}" alt="${item.name}">
          </div>
          <div class="cart-item-details">
            <div class="cart-item-name">${item.name}</div>
            <div class="cart-item-price">$${item.price.toFixed(2)}</div>
            <div class="cart-item-controls">
              <div class="quantity-controls">
                <button class="quantity-btn" onclick="app.updateCartQuantity(${item.id}, ${item.quantity - 1})">-</button>
                <input type="number" class="quantity-input" value="${item.quantity}" min="1" max="10"
                       onchange="app.updateCartQuantity(${item.id}, parseInt(this.value))">
                <button class="quantity-btn" onclick="app.updateCartQuantity(${item.id}, ${item.quantity + 1})">+</button>
              </div>
              <button class="remove-item" onclick="app.removeFromCart(${item.id})">Remove</button>
            </div>
          </div>
        </div>
      `).join('');

      // Update totals
      const subtotal = this.cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      cartSubtotal.textContent = `$${subtotal.toFixed(2)}`;
      cartTotal.textContent = `$${subtotal.toFixed(2)}`; // Free shipping
    }
  }

  async proceedToCheckout() {
    if (this.cart.length === 0) {
      this.showNotification('Your cart is empty', 'warning');
      return;
    }

    try {
      this.showLoading('Processing checkout...');

      // Create line items for Stripe
      const lineItems = this.cart.map(item => ({
        price_data: {
          currency: this.stripeConfig.currency,
          product_data: {
            name: item.name,
            description: item.description,
            images: [item.image]
          },
          unit_amount: Math.round(item.price * 100) // Convert to cents
        },
        quantity: item.quantity
      }));

      // In a real application, you would create a checkout session on your server
      // For demo purposes, we'll simulate the checkout process
      await this.simulateCheckout({ lineItems });

    } catch (error) {
      console.error('Checkout failed:', error);
      this.showNotification('Checkout failed. Please try again.', 'error');
    } finally {
      this.hideLoading();
    }
  }

  async simulateCheckout(options) {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Simulate successful checkout
    console.log('Demo checkout completed:', options);

    // Clear cart
    this.cart = [];
    this.saveCart();
    this.updateCartUI();
    this.closeCart();

    // Show success message
    this.showNotification('Order placed successfully! (Demo mode)', 'success');

    // In a real application, you would redirect to a success page
    // or show an order confirmation modal
  }

  loadMoreProducts() {
    // Simulate loading more products
    const additionalProducts = [
      {
        id: 7,
        name: 'Eco-Friendly Cleaning Kit',
        description: 'Natural cleaning products safe for your family and the environment.',
        price: 29.99,
        image: 'https://images.unsplash.com/photo-1563453392212-326f5e854473?w=400&h=300&fit=crop',
        category: 'home',
        badge: 'New',
        inStock: true
      },
      {
        id: 8,
        name: 'Organic Hemp T-Shirt',
        description: 'Comfortable organic hemp t-shirt in natural colors.',
        price: 32.99,
        image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=300&fit=crop',
        category: 'clothing',
        badge: 'Organic',
        inStock: true
      }
    ];

    this.products.push(...additionalProducts);
    this.renderProducts();

    // Hide load more button if no more products
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    if (this.products.length >= 8) {
      loadMoreBtn.style.display = 'none';
    }
  }

  handleContactForm(form) {
    const formData = new FormData(form);
    const data = {
      name: formData.get('name'),
      email: formData.get('email'),
      message: formData.get('message')
    };

    // Validate form
    if (!data.name || !data.email || !data.message) {
      this.showNotification('Please fill in all fields', 'warning');
      return;
    }

    if (!this.isValidEmail(data.email)) {
      this.showNotification('Please enter a valid email address', 'warning');
      return;
    }

    // Simulate form submission
    console.log('Contact form submitted:', data);
    this.showNotification('Message sent successfully! (Demo mode)', 'success');
    form.reset();
  }

  handleNewsletterForm(form) {
    const formData = new FormData(form);
    const email = formData.get('email') || form.querySelector('input[type="email"]').value;

    if (!email) {
      this.showNotification('Please enter your email address', 'warning');
      return;
    }

    if (!this.isValidEmail(email)) {
      this.showNotification('Please enter a valid email address', 'warning');
      return;
    }

    // Simulate newsletter subscription
    console.log('Newsletter subscription:', email);
    this.showNotification('Successfully subscribed to newsletter! (Demo mode)', 'success');
    form.reset();
  }

  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  loadCart() {
    try {
      const savedCart = localStorage.getItem('ecoshop-cart');
      this.cart = savedCart ? JSON.parse(savedCart) : [];
    } catch (error) {
      console.error('Failed to load cart:', error);
      this.cart = [];
    }
  }

  saveCart() {
    try {
      localStorage.setItem('ecoshop-cart', JSON.stringify(this.cart));
    } catch (error) {
      console.error('Failed to save cart:', error);
    }
  }

  showLoading(message = 'Loading...') {
    const loadingOverlay = document.getElementById('loadingOverlay');
    const loadingText = document.getElementById('loadingText');

    if (loadingText) {
      loadingText.textContent = message;
    }

    if (loadingOverlay) {
      loadingOverlay.style.display = 'flex';
    }

    this.isLoading = true;
  }

  hideLoading() {
    const loadingOverlay = document.getElementById('loadingOverlay');

    if (loadingOverlay) {
      loadingOverlay.style.display = 'none';
    }

    this.isLoading = false;
  }

  showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
      <div class="notification-content">
        <span class="notification-icon">${this.getNotificationIcon(type)}</span>
        <span class="notification-message">${message}</span>
        <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
      </div>
    `;

    // Add styles if not already added
    if (!document.querySelector('#notification-styles')) {
      const styles = document.createElement('style');
      styles.id = 'notification-styles';
      styles.textContent = `
        .notification {
          position: fixed;
          top: 20px;
          right: 20px;
          z-index: 10000;
          max-width: 400px;
          background: white;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          animation: slideInRight 0.3s ease-out;
        }
        .notification-success { border-left: 4px solid #10b981; }
        .notification-error { border-left: 4px solid #ef4444; }
        .notification-warning { border-left: 4px solid #f59e0b; }
        .notification-info { border-left: 4px solid #3b82f6; }
        .notification-content {
          display: flex;
          align-items: center;
          padding: 16px;
          gap: 12px;
        }
        .notification-icon { font-size: 18px; }
        .notification-message { flex: 1; font-size: 14px; }
        .notification-close {
          background: none;
          border: none;
          font-size: 18px;
          cursor: pointer;
          color: #6b7280;
          padding: 0;
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        @keyframes slideInRight {
          from { transform: translateX(100%); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }
      `;
      document.head.appendChild(styles);
    }

    // Add to page
    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, 5000);
  }

  getNotificationIcon(type) {
    const icons = {
      success: '✅',
      error: '❌',
      warning: '⚠️',
      info: 'ℹ️'
    };
    return icons[type] || icons.info;
  }
}

// Initialize the application
const app = new EcoShopApp();

// Start the application when DOM is loaded
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => app.init());
} else {
  app.init();
}

// Make app globally available for inline event handlers
window.app = app;
