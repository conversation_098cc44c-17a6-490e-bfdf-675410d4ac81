/**
 * Typing test routes with comprehensive analytics and validation
 */

import { Router, Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { asyncHand<PERSON> } from '@/middleware/errorHandler';
import { validationMiddleware } from '@/middleware/validation';
import { MetricsCollector } from '@/middleware/metrics';
import { logger } from '@/utils/logger';
import { 
  TypingTest, 
  SubmitTestRequest, 
  SubmitTestResponse, 
  TestResults,
  PerformanceComparison,
  TimeDistribution,
  PersonalBest
} from '@/types';

const router = Router();

/**
 * Submit a typing test result
 */
router.post('/submit', validationMiddleware.submitTest, asyncHandler(async (req: Request, res: Response) => {
  const testData: SubmitTestRequest = req.body;
  const testId = uuidv4();

  try {
    // Calculate comprehensive test results
    const results = await calculateTestResults(testData);
    
    // Create typing test record
    const typingTest: TypingTest = {
      id: testId,
      userId: undefined, // Will be set when user auth is implemented
      username: testData.username,
      textId: testData.textId,
      startTime: new Date(testData.startTime),
      endTime: new Date(testData.endTime),
      duration: new Date(testData.endTime).getTime() - new Date(testData.startTime).getTime(),
      wpm: results.wpm,
      accuracy: results.accuracy,
      rawWpm: results.rawWpm,
      netWpm: results.netWpm,
      charactersTyped: results.charactersTyped,
      correctCharacters: results.correctCharacters,
      incorrectCharacters: results.incorrectCharacters,
      totalCharacters: testData.keystrokes.length,
      errors: testData.errors,
      keystrokes: testData.keystrokes,
      difficulty: 'medium', // Will be determined from text
      category: 'quotes', // Will be determined from text
      metadata: testData.metadata,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Record metrics
    MetricsCollector.recordTypingTest(
      typingTest.duration,
      typingTest.wpm,
      typingTest.accuracy,
      typingTest.difficulty,
      typingTest.category
    );

    // Log business event
    logger.typingTest('test-submitted', testId, typingTest.userId, {
      username: testData.username,
      wpm: results.wpm,
      accuracy: results.accuracy,
      duration: typingTest.duration
    });

    // Prepare response
    const response: SubmitTestResponse = {
      success: true,
      data: {
        testId,
        results,
        achievements: [], // Will be populated when achievement system is implemented
        newPersonalBests: [], // Will be populated when user system is implemented
        leaderboardPosition: undefined // Will be calculated when leaderboard is implemented
      },
      message: 'Typing test submitted successfully'
    };

    res.status(201).json(response);

  } catch (error) {
    logger.error('Failed to submit typing test:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to submit typing test',
      requestId: (req as any).requestId
    });
  }
}));

/**
 * Get typing test statistics
 */
router.get('/stats/:username', asyncHandler(async (req: Request, res: Response) => {
  const { username } = req.params;
  const { timeframe = 'all-time', category, difficulty } = req.query;

  try {
    // This would query the database for user statistics
    // For now, return mock data
    const stats = {
      username,
      totalTests: 150,
      averageWpm: 65.5,
      bestWpm: 89.2,
      averageAccuracy: 94.8,
      bestAccuracy: 99.1,
      totalTime: 7200000, // 2 hours in milliseconds
      improvementRate: 2.3, // WPM improvement per week
      consistencyScore: 87.5,
      recentTests: [],
      categoryBreakdown: {},
      difficultyBreakdown: {},
      timeframe
    };

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    logger.error('Failed to get typing statistics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get typing statistics',
      requestId: (req as any).requestId
    });
  }
}));

/**
 * Get recent typing tests
 */
router.get('/recent/:username', asyncHandler(async (req: Request, res: Response) => {
  const { username } = req.params;
  const { limit = 10, offset = 0 } = req.query;

  try {
    // This would query the database for recent tests
    // For now, return mock data
    const recentTests = [];

    res.json({
      success: true,
      data: {
        tests: recentTests,
        total: 0,
        limit: parseInt(limit as string),
        offset: parseInt(offset as string)
      }
    });

  } catch (error) {
    logger.error('Failed to get recent tests:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get recent tests',
      requestId: (req as any).requestId
    });
  }
}));

/**
 * Calculate comprehensive test results
 */
async function calculateTestResults(testData: SubmitTestRequest): Promise<TestResults> {
  const duration = new Date(testData.endTime).getTime() - new Date(testData.startTime).getTime();
  const durationMinutes = duration / (1000 * 60);
  
  const totalCharacters = testData.keystrokes.length;
  const correctCharacters = testData.keystrokes.filter(k => k.isCorrect).length;
  const incorrectCharacters = totalCharacters - correctCharacters;
  
  // Calculate WPM (assuming average word length of 5 characters)
  const rawWpm = (totalCharacters / 5) / durationMinutes;
  const netWpm = (correctCharacters / 5) / durationMinutes;
  const wpm = Math.max(0, netWpm); // Ensure non-negative
  
  // Calculate accuracy
  const accuracy = totalCharacters > 0 ? (correctCharacters / totalCharacters) * 100 : 0;
  
  // Calculate error rate
  const errorRate = totalCharacters > 0 ? (incorrectCharacters / totalCharacters) * 100 : 0;
  
  // Calculate consistency score (based on WPM variation)
  const consistencyScore = calculateConsistencyScore(testData.keystrokes);
  
  // Calculate speed variation
  const speedVariation = calculateSpeedVariation(testData.keystrokes);
  
  // Calculate keyboard efficiency
  const keyboardEfficiency = calculateKeyboardEfficiency(testData.keystrokes, testData.errors);
  
  // Analyze weakest and strongest keys
  const { weakestKeys, strongestKeys } = analyzeKeyPerformance(testData.keystrokes);
  
  // Calculate time distribution
  const timeDistribution = calculateTimeDistribution(testData.keystrokes, duration);
  
  // Calculate performance comparison (mock data for now)
  const comparison: PerformanceComparison = {
    personalAverage: wpm * 0.9, // Mock: 90% of current performance
    globalAverage: 45, // Mock global average
    categoryAverage: 50, // Mock category average
    difficultyAverage: 48, // Mock difficulty average
    percentile: calculatePercentile(wpm) // Mock percentile calculation
  };

  return {
    wpm,
    rawWpm,
    netWpm,
    accuracy,
    duration,
    charactersTyped: totalCharacters,
    correctCharacters,
    incorrectCharacters,
    errorRate,
    consistencyScore,
    speedVariation,
    keyboardEfficiency,
    weakestKeys,
    strongestKeys,
    timeDistribution,
    comparison
  };
}

/**
 * Calculate consistency score based on keystroke timing
 */
function calculateConsistencyScore(keystrokes: any[]): number {
  if (keystrokes.length < 10) return 0;
  
  const intervals = keystrokes.slice(1).map((k, i) => k.timeSinceLastKey);
  const avgInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
  
  const variance = intervals.reduce((sum, interval) => sum + Math.pow(interval - avgInterval, 2), 0) / intervals.length;
  const standardDeviation = Math.sqrt(variance);
  
  // Convert to 0-100 scale (lower deviation = higher consistency)
  const consistencyScore = Math.max(0, 100 - (standardDeviation / avgInterval) * 100);
  return Math.round(consistencyScore * 100) / 100;
}

/**
 * Calculate speed variation throughout the test
 */
function calculateSpeedVariation(keystrokes: any[]): number {
  if (keystrokes.length < 20) return 0;
  
  const chunkSize = Math.floor(keystrokes.length / 4);
  const chunks = [];
  
  for (let i = 0; i < 4; i++) {
    const start = i * chunkSize;
    const end = i === 3 ? keystrokes.length : (i + 1) * chunkSize;
    const chunk = keystrokes.slice(start, end);
    
    const chunkDuration = chunk[chunk.length - 1].timestamp - chunk[0].timestamp;
    const chunkWpm = (chunk.length / 5) / (chunkDuration / (1000 * 60));
    chunks.push(chunkWpm);
  }
  
  const avgWpm = chunks.reduce((sum, wpm) => sum + wpm, 0) / chunks.length;
  const variance = chunks.reduce((sum, wpm) => sum + Math.pow(wpm - avgWpm, 2), 0) / chunks.length;
  
  return Math.round(Math.sqrt(variance) * 100) / 100;
}

/**
 * Calculate keyboard efficiency
 */
function calculateKeyboardEfficiency(keystrokes: any[], errors: any[]): number {
  const totalKeystrokes = keystrokes.length;
  const corrections = errors.filter(e => e.corrected).length;
  const uncorrectedErrors = errors.length - corrections;
  
  // Efficiency = (correct keystrokes) / (total keystrokes + corrections)
  const efficiency = totalKeystrokes / (totalKeystrokes + corrections + uncorrectedErrors);
  return Math.round(efficiency * 10000) / 100; // Convert to percentage
}

/**
 * Analyze key performance
 */
function analyzeKeyPerformance(keystrokes: any[]): { weakestKeys: string[], strongestKeys: string[] } {
  const keyStats: { [key: string]: { correct: number, total: number } } = {};
  
  keystrokes.forEach(k => {
    if (!keyStats[k.key]) {
      keyStats[k.key] = { correct: 0, total: 0 };
    }
    keyStats[k.key].total++;
    if (k.isCorrect) {
      keyStats[k.key].correct++;
    }
  });
  
  const keyAccuracies = Object.entries(keyStats)
    .filter(([key, stats]) => stats.total >= 3) // Only consider keys typed at least 3 times
    .map(([key, stats]) => ({
      key,
      accuracy: stats.correct / stats.total
    }))
    .sort((a, b) => a.accuracy - b.accuracy);
  
  const weakestKeys = keyAccuracies.slice(0, 3).map(k => k.key);
  const strongestKeys = keyAccuracies.slice(-3).reverse().map(k => k.key);
  
  return { weakestKeys, strongestKeys };
}

/**
 * Calculate time distribution across test quarters
 */
function calculateTimeDistribution(keystrokes: any[], totalDuration: number): TimeDistribution {
  const quarterDuration = totalDuration / 4;
  const startTime = keystrokes[0]?.timestamp || 0;
  
  const quarters = [0, 0, 0, 0];
  
  keystrokes.forEach(k => {
    const relativeTime = k.timestamp - startTime;
    const quarter = Math.min(3, Math.floor(relativeTime / quarterDuration));
    quarters[quarter]++;
  });
  
  const total = quarters.reduce((sum, count) => sum + count, 0);
  
  return {
    firstQuarter: total > 0 ? Math.round((quarters[0] / total) * 100) : 0,
    secondQuarter: total > 0 ? Math.round((quarters[1] / total) * 100) : 0,
    thirdQuarter: total > 0 ? Math.round((quarters[2] / total) * 100) : 0,
    fourthQuarter: total > 0 ? Math.round((quarters[3] / total) * 100) : 0
  };
}

/**
 * Calculate performance percentile (mock implementation)
 */
function calculatePercentile(wpm: number): number {
  // Mock percentile calculation based on typical WPM distributions
  if (wpm >= 80) return 95;
  if (wpm >= 70) return 85;
  if (wpm >= 60) return 75;
  if (wpm >= 50) return 60;
  if (wpm >= 40) return 45;
  if (wpm >= 30) return 30;
  return 15;
}

export default router;
