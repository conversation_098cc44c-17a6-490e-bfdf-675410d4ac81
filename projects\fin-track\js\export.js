/**
 * FinTrack Export/Import Module
 * Handles CSV export and import functionality
 */

class ExportImportManager {
  constructor() {
    this.csvHeaders = {
      transactions: [
        'Date',
        'Type',
        'Category',
        'Description',
        'Amount',
        'Account',
        'Notes'
      ],
      goals: [
        'Name',
        'Category',
        'Target Amount',
        'Current Amount',
        'Deadline',
        'Status'
      ]
    };
  }

  init() {
    this.setupEventListeners();
  }

  setupEventListeners() {
    // Export button
    document.getElementById('exportBtn')?.addEventListener('click', () => {
      this.showExportOptions();
    });

    // Import button
    document.getElementById('importBtn')?.addEventListener('click', () => {
      this.showImportDialog();
    });

    // File input for CSV import
    document.getElementById('csvFileInput')?.addEventListener('change', (e) => {
      this.handleFileImport(e);
    });
  }

  showExportOptions() {
    const options = [
      { label: 'Export Transactions', action: () => this.exportTransactions() },
      { label: 'Export Goals', action: () => this.exportGoals() },
      { label: 'Export All Data', action: () => this.exportAllData() }
    ];

    this.showActionMenu('Export Options', options);
  }

  showImportDialog() {
    const fileInput = document.getElementById('csvFileInput');
    if (fileInput) {
      fileInput.click();
    }
  }

  async exportTransactions() {
    try {
      this.showLoading('Preparing transaction export...');
      
      const transactions = await window.finTrackDB.getTransactions();
      
      if (transactions.length === 0) {
        this.showNotification('No transactions to export', 'warning');
        this.hideLoading();
        return;
      }

      const csvData = this.convertTransactionsToCSV(transactions);
      const filename = `fintrack-transactions-${this.getDateString()}.csv`;
      
      this.downloadCSV(csvData, filename);
      this.showNotification(`Exported ${transactions.length} transactions`, 'success');
      
    } catch (error) {
      console.error('Export failed:', error);
      this.showNotification('Export failed', 'error');
    } finally {
      this.hideLoading();
    }
  }

  async exportGoals() {
    try {
      this.showLoading('Preparing goals export...');
      
      const goals = await window.finTrackDB.getGoals(false); // Include inactive goals
      
      if (goals.length === 0) {
        this.showNotification('No goals to export', 'warning');
        this.hideLoading();
        return;
      }

      const csvData = this.convertGoalsToCSV(goals);
      const filename = `fintrack-goals-${this.getDateString()}.csv`;
      
      this.downloadCSV(csvData, filename);
      this.showNotification(`Exported ${goals.length} goals`, 'success');
      
    } catch (error) {
      console.error('Export failed:', error);
      this.showNotification('Export failed', 'error');
    } finally {
      this.hideLoading();
    }
  }

  async exportAllData() {
    try {
      this.showLoading('Preparing complete data export...');
      
      const [transactions, goals] = await Promise.all([
        window.finTrackDB.getTransactions(),
        window.finTrackDB.getGoals(false)
      ]);

      // Create a comprehensive export with multiple sheets
      const exportData = {
        transactions,
        goals,
        summary: await this.generateExportSummary(transactions, goals),
        exportDate: new Date().toISOString(),
        version: '1.0.0'
      };

      // For CSV, we'll create separate files in a zip-like structure
      // For now, let's create a JSON export for complete data
      const jsonData = JSON.stringify(exportData, null, 2);
      const filename = `fintrack-complete-${this.getDateString()}.json`;
      
      this.downloadFile(jsonData, filename, 'application/json');
      
      // Also create CSV files for transactions and goals
      if (transactions.length > 0) {
        const transactionCSV = this.convertTransactionsToCSV(transactions);
        this.downloadCSV(transactionCSV, `fintrack-transactions-${this.getDateString()}.csv`);
      }
      
      if (goals.length > 0) {
        const goalsCSV = this.convertGoalsToCSV(goals);
        this.downloadCSV(goalsCSV, `fintrack-goals-${this.getDateString()}.csv`);
      }
      
      this.showNotification('Complete data export completed', 'success');
      
    } catch (error) {
      console.error('Export failed:', error);
      this.showNotification('Export failed', 'error');
    } finally {
      this.hideLoading();
    }
  }

  convertTransactionsToCSV(transactions) {
    const headers = this.csvHeaders.transactions;
    const rows = transactions.map(transaction => [
      transaction.date,
      transaction.type,
      transaction.category,
      this.escapeCsvValue(transaction.description),
      transaction.amount,
      transaction.account,
      this.escapeCsvValue(transaction.notes || '')
    ]);

    return this.arrayToCSV([headers, ...rows]);
  }

  convertGoalsToCSV(goals) {
    const headers = this.csvHeaders.goals;
    const rows = goals.map(goal => [
      this.escapeCsvValue(goal.name),
      goal.category,
      goal.target,
      goal.current,
      goal.deadline || '',
      goal.current >= goal.target ? 'Completed' : 'In Progress'
    ]);

    return this.arrayToCSV([headers, ...rows]);
  }

  async handleFileImport(event) {
    const file = event.target.files[0];
    if (!file) return;

    try {
      this.showLoading('Importing data...');
      
      const fileContent = await this.readFile(file);
      
      if (file.name.endsWith('.json')) {
        await this.importJSONData(fileContent);
      } else if (file.name.endsWith('.csv')) {
        await this.importCSVData(fileContent, file.name);
      } else {
        throw new Error('Unsupported file format. Please use CSV or JSON files.');
      }
      
    } catch (error) {
      console.error('Import failed:', error);
      this.showNotification(`Import failed: ${error.message}`, 'error');
    } finally {
      this.hideLoading();
      // Clear the file input
      event.target.value = '';
    }
  }

  async importJSONData(jsonContent) {
    const data = JSON.parse(jsonContent);
    
    if (!data.transactions && !data.goals) {
      throw new Error('Invalid data format. Expected transactions or goals data.');
    }

    let importedCount = 0;

    // Import transactions
    if (data.transactions && Array.isArray(data.transactions)) {
      for (const transaction of data.transactions) {
        try {
          await window.finTrackDB.addTransaction(transaction);
          importedCount++;
        } catch (error) {
          console.warn('Failed to import transaction:', transaction, error);
        }
      }
    }

    // Import goals
    if (data.goals && Array.isArray(data.goals)) {
      for (const goal of data.goals) {
        try {
          await window.finTrackDB.addGoal(goal);
          importedCount++;
        } catch (error) {
          console.warn('Failed to import goal:', goal, error);
        }
      }
    }

    // Refresh UI
    await this.refreshAllData();
    
    this.showNotification(`Successfully imported ${importedCount} items`, 'success');
  }

  async importCSVData(csvContent, filename) {
    const rows = this.parseCSV(csvContent);
    
    if (rows.length < 2) {
      throw new Error('CSV file appears to be empty or invalid.');
    }

    const headers = rows[0];
    const dataRows = rows.slice(1);
    
    // Determine data type based on headers or filename
    let importType = 'transactions';
    if (filename.toLowerCase().includes('goal') || 
        headers.some(h => h.toLowerCase().includes('target'))) {
      importType = 'goals';
    }

    let importedCount = 0;

    if (importType === 'transactions') {
      importedCount = await this.importTransactionRows(headers, dataRows);
    } else {
      importedCount = await this.importGoalRows(headers, dataRows);
    }

    // Refresh UI
    await this.refreshAllData();
    
    this.showNotification(`Successfully imported ${importedCount} ${importType}`, 'success');
  }

  async importTransactionRows(headers, rows) {
    let importedCount = 0;
    
    // Map headers to expected fields
    const headerMap = this.createHeaderMap(headers, [
      'date', 'type', 'category', 'description', 'amount', 'account', 'notes'
    ]);

    for (const row of rows) {
      try {
        const transaction = {
          date: row[headerMap.date] || new Date().toISOString().split('T')[0],
          type: (row[headerMap.type] || 'expense').toLowerCase(),
          category: row[headerMap.category] || 'other',
          description: row[headerMap.description] || 'Imported transaction',
          amount: parseFloat(row[headerMap.amount]) || 0,
          account: row[headerMap.account] || 'checking',
          notes: row[headerMap.notes] || ''
        };

        await window.finTrackDB.addTransaction(transaction);
        importedCount++;
      } catch (error) {
        console.warn('Failed to import transaction row:', row, error);
      }
    }

    return importedCount;
  }

  async importGoalRows(headers, rows) {
    let importedCount = 0;
    
    // Map headers to expected fields
    const headerMap = this.createHeaderMap(headers, [
      'name', 'category', 'target', 'current', 'deadline'
    ]);

    for (const row of rows) {
      try {
        const goal = {
          name: row[headerMap.name] || 'Imported goal',
          category: row[headerMap.category] || 'other',
          target: parseFloat(row[headerMap.target]) || 0,
          current: parseFloat(row[headerMap.current]) || 0,
          deadline: row[headerMap.deadline] || null
        };

        await window.finTrackDB.addGoal(goal);
        importedCount++;
      } catch (error) {
        console.warn('Failed to import goal row:', row, error);
      }
    }

    return importedCount;
  }

  // Utility methods
  createHeaderMap(headers, expectedFields) {
    const map = {};
    
    expectedFields.forEach(field => {
      const headerIndex = headers.findIndex(h => 
        h.toLowerCase().includes(field.toLowerCase()) ||
        field.toLowerCase().includes(h.toLowerCase())
      );
      map[field] = headerIndex >= 0 ? headerIndex : 0;
    });

    return map;
  }

  arrayToCSV(data) {
    return data.map(row => 
      row.map(cell => this.escapeCsvValue(cell)).join(',')
    ).join('\n');
  }

  escapeCsvValue(value) {
    if (value === null || value === undefined) return '';
    
    const stringValue = String(value);
    
    // If the value contains comma, newline, or quote, wrap in quotes and escape quotes
    if (stringValue.includes(',') || stringValue.includes('\n') || stringValue.includes('"')) {
      return '"' + stringValue.replace(/"/g, '""') + '"';
    }
    
    return stringValue;
  }

  parseCSV(csvContent) {
    const rows = [];
    const lines = csvContent.split('\n');
    
    for (const line of lines) {
      if (line.trim()) {
        rows.push(this.parseCSVLine(line));
      }
    }
    
    return rows;
  }

  parseCSVLine(line) {
    const result = [];
    let current = '';
    let inQuotes = false;
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      
      if (char === '"') {
        if (inQuotes && line[i + 1] === '"') {
          current += '"';
          i++; // Skip next quote
        } else {
          inQuotes = !inQuotes;
        }
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }
    
    result.push(current.trim());
    return result;
  }

  downloadCSV(csvData, filename) {
    this.downloadFile(csvData, filename, 'text/csv');
  }

  downloadFile(data, filename, mimeType) {
    const blob = new Blob([data], { type: mimeType });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.style.display = 'none';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
  }

  readFile(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = e => resolve(e.target.result);
      reader.onerror = e => reject(new Error('Failed to read file'));
      reader.readAsText(file);
    });
  }

  async generateExportSummary(transactions, goals) {
    const summary = await window.finTrackDB.getTransactionSummary('all');
    
    return {
      totalTransactions: transactions.length,
      totalGoals: goals.length,
      totalIncome: summary.totalIncome,
      totalExpenses: summary.totalExpenses,
      netIncome: summary.netIncome,
      completedGoals: goals.filter(g => g.current >= g.target).length,
      exportTimestamp: new Date().toISOString()
    };
  }

  async refreshAllData() {
    // Refresh transactions
    if (window.transactionManager) {
      await window.transactionManager.loadTransactions();
      window.transactionManager.renderTransactions();
    }

    // Refresh goals
    if (window.goalManager) {
      await window.goalManager.loadGoals();
      window.goalManager.renderGoals();
    }

    // Refresh dashboard
    if (window.finTrackApp && window.finTrackApp.updateDashboard) {
      await window.finTrackApp.updateDashboard();
    }

    // Refresh charts
    if (window.finTrackCharts && window.finTrackCharts.updateCharts) {
      await window.finTrackCharts.updateCharts();
    }
  }

  getDateString() {
    return new Date().toISOString().split('T')[0];
  }

  showActionMenu(title, options) {
    const menu = document.createElement('div');
    menu.className = 'action-menu';
    menu.innerHTML = `
      <div class="action-menu-content">
        <h3>${title}</h3>
        <div class="action-menu-options">
          ${options.map((option, index) => `
            <button class="action-menu-option" data-index="${index}">
              ${option.label}
            </button>
          `).join('')}
        </div>
        <button class="action-menu-close">Cancel</button>
      </div>
    `;
    
    menu.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 3000;
    `;
    
    document.body.appendChild(menu);
    
    // Add event listeners
    menu.addEventListener('click', (e) => {
      if (e.target.classList.contains('action-menu-option')) {
        const index = parseInt(e.target.dataset.index);
        options[index].action();
        menu.remove();
      } else if (e.target.classList.contains('action-menu-close') || e.target === menu) {
        menu.remove();
      }
    });
  }

  showLoading(message) {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
      overlay.querySelector('p').textContent = message;
      overlay.style.display = 'flex';
    }
  }

  hideLoading() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
      overlay.style.display = 'none';
    }
  }

  showNotification(message, type = 'info') {
    // Simple notification system
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 24px;
      border-radius: 8px;
      color: white;
      font-weight: 500;
      z-index: 4000;
      animation: slideIn 0.3s ease-out;
      background-color: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : type === 'warning' ? '#f59e0b' : '#3b82f6'};
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
      notification.style.animation = 'slideOut 0.3s ease-in';
      setTimeout(() => notification.remove(), 300);
    }, 3000);
  }
}

// Add action menu styles
const actionMenuStyles = document.createElement('style');
actionMenuStyles.textContent = `
  .action-menu-content {
    background: var(--bg-card);
    border-radius: 12px;
    padding: 24px;
    min-width: 300px;
    box-shadow: var(--shadow-xl);
  }
  
  .action-menu-content h3 {
    margin-bottom: 16px;
    color: var(--text-primary);
    text-align: center;
  }
  
  .action-menu-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 16px;
  }
  
  .action-menu-option {
    padding: 12px 16px;
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    background: var(--bg-primary);
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.2s ease;
    font-family: inherit;
  }
  
  .action-menu-option:hover {
    background: var(--bg-secondary);
    border-color: var(--color-primary);
  }
  
  .action-menu-close {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    background: var(--bg-secondary);
    color: var(--text-secondary);
    cursor: pointer;
    font-family: inherit;
  }
`;
document.head.appendChild(actionMenuStyles);

// Create and export singleton instance
const exportImportManager = new ExportImportManager();

export default exportImportManager;
