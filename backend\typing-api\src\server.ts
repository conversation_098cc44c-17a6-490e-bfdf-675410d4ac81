/**
 * Enterprise Typing Analytics API Server
 * Production-ready TypeScript server with comprehensive features
 */

import express, { Application, Request, Response, NextFunction } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import path from 'path';
import dotenv from 'dotenv';

// Import middleware
import { requestLogger } from '@/middleware/requestLogger';
import { errorHandler } from '@/middleware/errorHandler';
import { metricsMiddleware } from '@/middleware/metrics';
import { validationMiddleware } from '@/middleware/validation';

// Import routes
import typingRoutes from '@/routes/typing';
import analyticsRoutes from '@/routes/analytics';
import userRoutes from '@/routes/user';
import leaderboardRoutes from '@/routes/leaderboard';
import textRoutes from '@/routes/text';

// Import services
import { DatabaseService } from '@/services/DatabaseService';
import { CacheService } from '@/services/CacheService';
import { MetricsService } from '@/services/MetricsService';
import { RealTimeService } from '@/services/RealTimeService';

// Import utilities
import { logger } from '@/utils/logger';
import { AppConfig } from '@/types';

// Load environment variables
dotenv.config();

/**
 * Enterprise Typing Analytics API Server Class
 * Handles all server initialization, middleware setup, and graceful shutdown
 */
export class TypingAnalyticsServer {
  private app: Application;
  private server: any;
  private io: SocketIOServer;
  private config: AppConfig;
  private databaseService: DatabaseService;
  private cacheService: CacheService;
  private metricsService: MetricsService;
  private realTimeService: RealTimeService;

  constructor() {
    this.app = express();
    this.config = this.loadConfiguration();
    this.initializeServices();
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
    this.setupRealTime();
  }

  /**
   * Load application configuration from environment variables
   */
  private loadConfiguration(): AppConfig {
    return {
      port: parseInt(process.env.PORT || '3003', 10),
      environment: (process.env.NODE_ENV as any) || 'development',
      database: {
        type: 'sqlite',
        database: process.env.DB_PATH || './data/typing.db'
      },
      redis: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379', 10),
        password: process.env.REDIS_PASSWORD,
        db: parseInt(process.env.REDIS_DB || '0', 10)
      },
      jwt: {
        secret: process.env.JWT_SECRET || 'typing-api-secret-key',
        expiresIn: process.env.JWT_EXPIRES_IN || '24h'
      },
      rateLimit: {
        windowMs: parseInt(process.env.RATE_LIMIT_WINDOW || '900000', 10), // 15 minutes
        max: parseInt(process.env.RATE_LIMIT_MAX || '100', 10)
      },
      cors: {
        origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
        credentials: true
      },
      logging: {
        level: process.env.LOG_LEVEL || 'info',
        file: process.env.LOG_FILE === 'true',
        console: process.env.LOG_CONSOLE !== 'false'
      },
      monitoring: {
        enabled: process.env.MONITORING_ENABLED === 'true',
        metricsPath: process.env.METRICS_PATH || '/metrics'
      }
    };
  }

  /**
   * Initialize all core services
   */
  private async initializeServices(): Promise<void> {
    try {
      // Initialize database service
      this.databaseService = new DatabaseService(this.config.database);
      await this.databaseService.initialize();

      // Initialize cache service
      this.cacheService = new CacheService(this.config.redis);
      await this.cacheService.initialize();

      // Initialize metrics service
      this.metricsService = new MetricsService();
      this.metricsService.initialize();

      logger.info('All services initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize services:', error);
      process.exit(1);
    }
  }

  /**
   * Setup Express middleware stack
   */
  private setupMiddleware(): void {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
      crossOriginEmbedderPolicy: false
    }));

    // CORS configuration
    this.app.use(cors(this.config.cors));

    // Compression middleware
    this.app.use(compression());

    // Body parsing middleware
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Rate limiting
    const limiter = rateLimit({
      windowMs: this.config.rateLimit.windowMs,
      max: this.config.rateLimit.max,
      message: {
        success: false,
        error: 'Too many requests from this IP, please try again later.',
        retryAfter: Math.ceil(this.config.rateLimit.windowMs / 1000)
      },
      standardHeaders: true,
      legacyHeaders: false,
    });
    this.app.use('/api/', limiter);

    // Custom middleware
    this.app.use(requestLogger);
    this.app.use(metricsMiddleware);

    // Static files
    this.app.use(express.static(path.join(__dirname, '../public')));

    // Health check endpoint
    this.app.get('/health', (req: Request, res: Response) => {
      res.status(200).json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.npm_package_version || '2.0.0',
        environment: this.config.environment,
        services: {
          database: this.databaseService.isHealthy(),
          cache: this.cacheService.isHealthy(),
          metrics: this.metricsService.isHealthy()
        }
      });
    });

    // Metrics endpoint
    if (this.config.monitoring.enabled) {
      this.app.get(this.config.monitoring.metricsPath, async (req: Request, res: Response) => {
        try {
          const metrics = await this.metricsService.getMetrics();
          res.set('Content-Type', 'text/plain');
          res.send(metrics);
        } catch (error) {
          logger.error('Failed to get metrics:', error);
          res.status(500).json({ error: 'Failed to get metrics' });
        }
      });
    }

    logger.info('Middleware setup completed');
  }

  /**
   * Setup API routes
   */
  private setupRoutes(): void {
    // API routes
    this.app.use('/api/v1/typing', typingRoutes);
    this.app.use('/api/v1/analytics', analyticsRoutes);
    this.app.use('/api/v1/users', userRoutes);
    this.app.use('/api/v1/leaderboard', leaderboardRoutes);
    this.app.use('/api/v1/texts', textRoutes);

    // API documentation
    this.app.get('/api/v1/docs', (req: Request, res: Response) => {
      res.redirect('/docs');
    });

    // Serve documentation
    this.app.get('/docs', (req: Request, res: Response) => {
      res.sendFile(path.join(__dirname, '../public/docs.html'));
    });

    // Root endpoint
    this.app.get('/', (req: Request, res: Response) => {
      res.json({
        name: 'Typing Analytics API',
        version: '2.0.0',
        description: 'Enterprise-grade typing analytics API with real-time features',
        documentation: '/docs',
        health: '/health',
        metrics: this.config.monitoring.enabled ? this.config.monitoring.metricsPath : null,
        endpoints: {
          typing: '/api/v1/typing',
          analytics: '/api/v1/analytics',
          users: '/api/v1/users',
          leaderboard: '/api/v1/leaderboard',
          texts: '/api/v1/texts'
        }
      });
    });

    // 404 handler
    this.app.use('*', (req: Request, res: Response) => {
      res.status(404).json({
        success: false,
        error: `Endpoint not found: ${req.method} ${req.originalUrl}`,
        timestamp: new Date().toISOString(),
        requestId: (req as any).requestId
      });
    });

    logger.info('Routes setup completed');
  }

  /**
   * Setup error handling middleware
   */
  private setupErrorHandling(): void {
    this.app.use(errorHandler);
    logger.info('Error handling setup completed');
  }

  /**
   * Setup real-time WebSocket functionality
   */
  private setupRealTime(): void {
    this.server = createServer(this.app);
    this.io = new SocketIOServer(this.server, {
      cors: this.config.cors,
      transports: ['websocket', 'polling']
    });

    this.realTimeService = new RealTimeService(this.io, this.databaseService, this.cacheService);
    this.realTimeService.initialize();

    logger.info('Real-time services setup completed');
  }

  /**
   * Start the server
   */
  public async start(): Promise<void> {
    try {
      this.server.listen(this.config.port, () => {
        logger.info(`🚀 Typing Analytics API Server started successfully`);
        logger.info(`📍 Server running on port ${this.config.port}`);
        logger.info(`🌍 Environment: ${this.config.environment}`);
        logger.info(`📊 Health check: http://localhost:${this.config.port}/health`);
        logger.info(`📚 Documentation: http://localhost:${this.config.port}/docs`);
        if (this.config.monitoring.enabled) {
          logger.info(`📈 Metrics: http://localhost:${this.config.port}${this.config.monitoring.metricsPath}`);
        }
      });

      // Setup graceful shutdown
      this.setupGracefulShutdown();

    } catch (error) {
      logger.error('Failed to start server:', error);
      process.exit(1);
    }
  }

  /**
   * Setup graceful shutdown handlers
   */
  private setupGracefulShutdown(): void {
    const shutdown = async (signal: string) => {
      logger.info(`Received ${signal}. Starting graceful shutdown...`);

      // Close server
      if (this.server) {
        this.server.close(() => {
          logger.info('HTTP server closed');
        });
      }

      // Close WebSocket server
      if (this.io) {
        this.io.close(() => {
          logger.info('WebSocket server closed');
        });
      }

      // Close services
      try {
        await this.databaseService?.close();
        await this.cacheService?.close();
        logger.info('All services closed successfully');
        process.exit(0);
      } catch (error) {
        logger.error('Error during shutdown:', error);
        process.exit(1);
      }
    };

    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGUSR2', () => shutdown('SIGUSR2')); // nodemon restart

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception:', error);
      shutdown('uncaughtException');
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
      shutdown('unhandledRejection');
    });
  }
}

// Start server if this file is run directly
if (require.main === module) {
  const server = new TypingAnalyticsServer();
  server.start().catch((error) => {
    logger.error('Failed to start server:', error);
    process.exit(1);
  });
}

export default TypingAnalyticsServer;
