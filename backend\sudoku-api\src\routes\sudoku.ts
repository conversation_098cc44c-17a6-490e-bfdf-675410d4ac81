/**
 * Sudoku API Routes
 * Comprehensive REST API endpoints for Sudoku operations
 */

import { Router } from 'express';
import { SudokuSolver } from '@/algorithms/solver';
import { SudokuGenerator } from '@/algorithms/generator';
import { SudokuValidator } from '@/algorithms/validator';
import {
  validateSolve,
  validateGenerate,
  validateValidate,
  validateHint,
  validateAnalyze,
} from '@/middleware/validation';
import { asyncHandler } from '@/middleware/errorHandler';
import { recordSudokuOperation, BusinessMetrics } from '@/middleware/metrics';
import { ApiResponse, SudokuGrid, DifficultyLevel } from '@/types';
import { log } from '@/utils/logger';

const router = Router();
const solver = new SudokuSolver();
const generator = new SudokuGenerator();
const validator = new SudokuValidator();

/**
 * POST /solve
 * Solve a Sudoku puzzle
 */
router.post(
  '/solve',
  validateSolve,
  asyncHandler(async (req, res) => {
    const { puzzle, options = {} } = req.body;
    const startTime = Date.now();

    log.info('Solving Sudoku puzzle', {
      method: options.method || 'advanced',
      stepByStep: options.stepByStep || false,
      requestId: req.id,
    });

    try {
      const result = await solver.solve(puzzle, options);
      const duration = Date.now() - startTime;

      // Record metrics
      recordSudokuOperation('solve', result.difficulty, result.solved, duration);
      if (result.solved) {
        BusinessMetrics.incrementPuzzlesSolved();
      }

      const response: ApiResponse = {
        success: true,
        data: {
          solved: result.solved,
          solution: result.solution,
          difficulty: result.difficulty,
          techniques: result.techniques,
          statistics: {
            ...result.statistics,
            solvingTime: duration,
          },
          ...(options.stepByStep && { steps: result.steps }),
          ...(result.partialSolution && { partialSolution: result.partialSolution }),
          ...(result.reason && { reason: result.reason }),
        },
        timestamp: new Date().toISOString(),
        requestId: req.id,
      };

      log.info('Puzzle solved successfully', {
        solved: result.solved,
        difficulty: result.difficulty,
        duration,
        techniques: result.techniques.length,
        requestId: req.id,
      });

      res.json(response);
    } catch (error) {
      const duration = Date.now() - startTime;
      recordSudokuOperation('solve', 'unknown', false, duration);
      throw error;
    }
  })
);

/**
 * POST /generate
 * Generate a new Sudoku puzzle
 */
router.post(
  '/generate',
  validateGenerate,
  asyncHandler(async (req, res) => {
    const { difficulty = 'medium', options = {} } = req.body;
    const startTime = Date.now();

    log.info('Generating Sudoku puzzle', {
      difficulty,
      options,
      requestId: req.id,
    });

    try {
      const result = await generator.generate(difficulty as DifficultyLevel, options);
      const duration = Date.now() - startTime;

      // Record metrics
      recordSudokuOperation('generate', difficulty, true, duration);
      BusinessMetrics.incrementPuzzlesGenerated();

      const response: ApiResponse = {
        success: true,
        data: {
          puzzle: result.puzzle,
          solution: result.solution,
          difficulty: result.difficulty,
          metadata: {
            ...result.metadata,
            generationTime: duration,
          },
        },
        timestamp: new Date().toISOString(),
        requestId: req.id,
      };

      log.info('Puzzle generated successfully', {
        difficulty: result.difficulty,
        clues: result.metadata.clues,
        duration,
        requestId: req.id,
      });

      res.json(response);
    } catch (error) {
      const duration = Date.now() - startTime;
      recordSudokuOperation('generate', difficulty, false, duration);
      throw error;
    }
  })
);

/**
 * POST /validate
 * Validate a Sudoku puzzle
 */
router.post(
  '/validate',
  validateValidate,
  asyncHandler(async (req, res) => {
    const { puzzle, options = {} } = req.body;
    const startTime = Date.now();

    log.info('Validating Sudoku puzzle', {
      options,
      requestId: req.id,
    });

    try {
      const result = validator.validate(puzzle, options);
      const duration = Date.now() - startTime;

      // Record metrics
      recordSudokuOperation('validate', 'unknown', result.isValid, duration);
      BusinessMetrics.incrementValidationRequests();

      const response: ApiResponse = {
        success: true,
        data: {
          isValid: result.isValid,
          completeness: result.completeness,
          conflicts: result.conflicts,
          errors: result.errors,
          statistics: {
            validationTime: duration,
          },
        },
        timestamp: new Date().toISOString(),
        requestId: req.id,
      };

      log.info('Puzzle validated', {
        isValid: result.isValid,
        completeness: result.completeness,
        conflicts: result.conflicts.length,
        duration,
        requestId: req.id,
      });

      res.json(response);
    } catch (error) {
      const duration = Date.now() - startTime;
      recordSudokuOperation('validate', 'unknown', false, duration);
      throw error;
    }
  })
);

/**
 * POST /hint
 * Get a hint for the next move
 */
router.post(
  '/hint',
  validateHint,
  asyncHandler(async (req, res) => {
    const { puzzle, options = {} } = req.body;
    const startTime = Date.now();

    log.info('Providing hint for Sudoku puzzle', {
      options,
      requestId: req.id,
    });

    try {
      const hint = solver.getHint(puzzle);
      const duration = Date.now() - startTime;

      // Record metrics
      recordSudokuOperation('hint', 'unknown', hint !== null, duration);
      if (hint) {
        BusinessMetrics.incrementHintsProvided();
      }

      const response: ApiResponse = {
        success: true,
        data: {
          hint,
          available: hint !== null,
          statistics: {
            processingTime: duration,
          },
        },
        timestamp: new Date().toISOString(),
        requestId: req.id,
      };

      log.info('Hint provided', {
        hintAvailable: hint !== null,
        technique: hint?.technique,
        duration,
        requestId: req.id,
      });

      res.json(response);
    } catch (error) {
      const duration = Date.now() - startTime;
      recordSudokuOperation('hint', 'unknown', false, duration);
      throw error;
    }
  })
);

/**
 * POST /analyze
 * Analyze a Sudoku puzzle
 */
router.post(
  '/analyze',
  validateAnalyze,
  asyncHandler(async (req, res) => {
    const { puzzle, options = {} } = req.body;
    const startTime = Date.now();

    log.info('Analyzing Sudoku puzzle', {
      options,
      requestId: req.id,
    });

    try {
      const analysis = await solver.analyzePuzzle(puzzle);
      const duration = Date.now() - startTime;

      // Record metrics
      recordSudokuOperation('analyze', analysis.difficulty, analysis.solvable, duration);

      const response: ApiResponse = {
        success: true,
        data: {
          ...analysis,
          statistics: {
            analysisTime: duration,
          },
        },
        timestamp: new Date().toISOString(),
        requestId: req.id,
      };

      log.info('Puzzle analyzed', {
        difficulty: analysis.difficulty,
        solvable: analysis.solvable,
        clueCount: analysis.clueCount,
        duration,
        requestId: req.id,
      });

      res.json(response);
    } catch (error) {
      const duration = Date.now() - startTime;
      recordSudokuOperation('analyze', 'unknown', false, duration);
      throw error;
    }
  })
);

/**
 * GET /difficulties
 * Get available difficulty levels
 */
router.get(
  '/difficulties',
  asyncHandler(async (req, res) => {
    const response: ApiResponse = {
      success: true,
      data: {
        difficulties: [
          {
            level: 'easy',
            description: 'Simple puzzles solvable with basic techniques',
            estimatedTime: '2-5 minutes',
            clueRange: '36-46',
          },
          {
            level: 'medium',
            description: 'Moderate puzzles requiring some advanced techniques',
            estimatedTime: '5-10 minutes',
            clueRange: '32-35',
          },
          {
            level: 'hard',
            description: 'Challenging puzzles with complex logical patterns',
            estimatedTime: '10-20 minutes',
            clueRange: '28-31',
          },
          {
            level: 'expert',
            description: 'Very difficult puzzles requiring advanced techniques',
            estimatedTime: '20-45 minutes',
            clueRange: '25-27',
          },
          {
            level: 'evil',
            description: 'Extremely challenging puzzles for experts only',
            estimatedTime: '45+ minutes',
            clueRange: '22-24',
          },
        ],
      },
      timestamp: new Date().toISOString(),
      requestId: req.id,
    };

    res.json(response);
  })
);

/**
 * GET /techniques
 * Get available solving techniques
 */
router.get(
  '/techniques',
  asyncHandler(async (req, res) => {
    const response: ApiResponse = {
      success: true,
      data: {
        techniques: [
          {
            name: 'NAKED_SINGLE',
            description: 'A cell with only one possible value',
            difficulty: 'easy',
          },
          {
            name: 'HIDDEN_SINGLE',
            description: 'A value that can only go in one cell in a unit',
            difficulty: 'easy',
          },
          {
            name: 'INTERSECTION_REMOVAL',
            description: 'Remove candidates based on box-line interactions',
            difficulty: 'medium',
          },
          {
            name: 'NAKED_PAIR',
            description: 'Two cells with the same two candidates',
            difficulty: 'medium',
          },
          {
            name: 'HIDDEN_PAIR',
            description: 'Two values that can only go in two cells',
            difficulty: 'hard',
          },
          {
            name: 'X_WING',
            description: 'Advanced pattern elimination technique',
            difficulty: 'expert',
          },
          {
            name: 'SWORDFISH',
            description: 'Complex pattern elimination technique',
            difficulty: 'evil',
          },
          {
            name: 'BACKTRACKING',
            description: 'Trial and error with systematic backtracking',
            difficulty: 'fallback',
          },
        ],
      },
      timestamp: new Date().toISOString(),
      requestId: req.id,
    };

    res.json(response);
  })
);

/**
 * GET /stats
 * Get API usage statistics
 */
router.get(
  '/stats',
  asyncHandler(async (req, res) => {
    const stats = BusinessMetrics.getStats();

    const response: ApiResponse = {
      success: true,
      data: {
        ...stats,
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        timestamp: new Date().toISOString(),
      },
      timestamp: new Date().toISOString(),
      requestId: req.id,
    };

    res.json(response);
  })
);

export default router;
