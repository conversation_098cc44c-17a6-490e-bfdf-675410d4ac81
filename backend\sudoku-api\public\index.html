<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sudoku Solver - Interactive Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            color: #666;
            font-size: 1.1rem;
        }

        .game-container {
            display: flex;
            gap: 30px;
            align-items: flex-start;
        }

        .sudoku-board {
            background: #fff;
            border: 3px solid #333;
            border-radius: 10px;
            padding: 10px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .sudoku-grid {
            display: grid;
            grid-template-columns: repeat(9, 1fr);
            grid-template-rows: repeat(9, 1fr);
            gap: 1px;
            background: #333;
            border-radius: 5px;
            overflow: hidden;
        }

        .sudoku-cell {
            width: 45px;
            height: 45px;
            background: #fff;
            border: none;
            font-size: 1.2rem;
            font-weight: bold;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .sudoku-cell:hover {
            background: #e3f2fd;
        }

        .sudoku-cell:focus {
            outline: 2px solid #2196f3;
            background: #e3f2fd;
        }

        .sudoku-cell.given {
            background: #f5f5f5;
            color: #333;
            font-weight: bold;
            cursor: default;
        }

        .sudoku-cell.error {
            background: #ffebee;
            color: #d32f2f;
        }

        .sudoku-cell.hint {
            background: #e8f5e8;
            color: #2e7d32;
        }

        /* 3x3 box borders */
        .sudoku-cell:nth-child(3n) {
            border-right: 2px solid #333;
        }

        .sudoku-cell:nth-child(n+19):nth-child(-n+27),
        .sudoku-cell:nth-child(n+46):nth-child(-n+54) {
            border-bottom: 2px solid #333;
        }

        .controls {
            flex: 1;
            min-width: 250px;
        }

        .control-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .control-section h3 {
            margin-bottom: 15px;
            color: #333;
            font-size: 1.2rem;
        }

        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }

        .btn {
            padding: 10px 15px;
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
            transform: translateY(-2px);
        }

        .btn-warning {
            background: #ffc107;
            color: #333;
        }

        .btn-warning:hover {
            background: #e0a800;
            transform: translateY(-2px);
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
            transform: translateY(-2px);
        }

        .difficulty-selector {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }

        .difficulty-btn {
            flex: 1;
            min-width: 60px;
            padding: 8px 12px;
            font-size: 0.8rem;
        }

        .status-display {
            background: #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .status-item:last-child {
            margin-bottom: 0;
        }

        .status-label {
            font-weight: bold;
            color: #495057;
        }

        .status-value {
            color: #6c757d;
        }

        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .message {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            font-size: 0.9rem;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .message.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        @media (max-width: 768px) {
            .game-container {
                flex-direction: column;
                align-items: center;
            }
            
            .sudoku-cell {
                width: 35px;
                height: 35px;
                font-size: 1rem;
            }
            
            .controls {
                width: 100%;
                max-width: 400px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧩 Sudoku Solver</h1>
            <p>Interactive puzzle solver powered by advanced algorithms</p>
        </div>

        <div class="game-container">
            <div class="sudoku-board">
                <div class="sudoku-grid" id="sudokuGrid">
                    <!-- Grid cells will be generated by JavaScript -->
                </div>
            </div>

            <div class="controls">
                <div class="control-section">
                    <h3>🎮 Game Controls</h3>
                    <div class="button-group">
                        <button class="btn btn-primary" onclick="generatePuzzle()">New Puzzle</button>
                        <button class="btn btn-success" onclick="solvePuzzle()">Solve</button>
                        <button class="btn btn-warning" onclick="getHint()">Hint</button>
                        <button class="btn btn-secondary" onclick="clearBoard()">Clear</button>
                    </div>
                    
                    <h4 style="margin-bottom: 10px;">Difficulty Level:</h4>
                    <div class="difficulty-selector">
                        <button class="btn btn-secondary difficulty-btn" onclick="setDifficulty('easy')">Easy</button>
                        <button class="btn btn-secondary difficulty-btn" onclick="setDifficulty('medium')">Medium</button>
                        <button class="btn btn-secondary difficulty-btn" onclick="setDifficulty('hard')">Hard</button>
                        <button class="btn btn-secondary difficulty-btn" onclick="setDifficulty('expert')">Expert</button>
                        <button class="btn btn-secondary difficulty-btn" onclick="setDifficulty('evil')">Evil</button>
                    </div>
                </div>

                <div class="control-section">
                    <h3>📊 Puzzle Status</h3>
                    <div class="status-display" id="statusDisplay">
                        <div class="status-item">
                            <span class="status-label">Difficulty:</span>
                            <span class="status-value" id="currentDifficulty">Medium</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">Clues:</span>
                            <span class="status-value" id="clueCount">0</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">Filled:</span>
                            <span class="status-value" id="filledCount">0/81</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">Valid:</span>
                            <span class="status-value" id="validStatus">Unknown</span>
                        </div>
                    </div>
                </div>

                <div class="control-section">
                    <h3>🔧 Advanced Tools</h3>
                    <div class="button-group">
                        <button class="btn btn-secondary" onclick="validatePuzzle()">Validate</button>
                        <button class="btn btn-secondary" onclick="analyzePuzzle()">Analyze</button>
                        <button class="btn btn-danger" onclick="resetGame()">Reset</button>
                    </div>
                </div>

                <div id="messageArea"></div>
            </div>
        </div>
    </div>

    <script>
        // Game state
        let currentPuzzle = Array(9).fill().map(() => Array(9).fill(0));
        let originalPuzzle = Array(9).fill().map(() => Array(9).fill(0));
        let currentDifficulty = 'medium';
        let selectedCell = null;

        // API base URL
        const API_BASE = 'http://localhost:3002/api';

        // Initialize the game
        function initGame() {
            createGrid();
            generatePuzzle();
        }

        // Create the Sudoku grid
        function createGrid() {
            const grid = document.getElementById('sudokuGrid');
            grid.innerHTML = '';

            for (let row = 0; row < 9; row++) {
                for (let col = 0; col < 9; col++) {
                    const cell = document.createElement('input');
                    cell.type = 'text';
                    cell.className = 'sudoku-cell';
                    cell.maxLength = 1;
                    cell.id = `cell-${row}-${col}`;
                    
                    cell.addEventListener('input', (e) => handleCellInput(e, row, col));
                    cell.addEventListener('focus', (e) => selectCell(row, col));
                    cell.addEventListener('keydown', (e) => handleKeyDown(e, row, col));
                    
                    grid.appendChild(cell);
                }
            }
        }

        // Handle cell input
        function handleCellInput(event, row, col) {
            const value = event.target.value;
            
            if (value === '' || (value >= '1' && value <= '9')) {
                currentPuzzle[row][col] = value === '' ? 0 : parseInt(value);
                updateStatus();
                validateCell(row, col);
            } else {
                event.target.value = '';
            }
        }

        // Handle keyboard navigation
        function handleKeyDown(event, row, col) {
            switch (event.key) {
                case 'ArrowUp':
                    event.preventDefault();
                    if (row > 0) focusCell(row - 1, col);
                    break;
                case 'ArrowDown':
                    event.preventDefault();
                    if (row < 8) focusCell(row + 1, col);
                    break;
                case 'ArrowLeft':
                    event.preventDefault();
                    if (col > 0) focusCell(row, col - 1);
                    break;
                case 'ArrowRight':
                    event.preventDefault();
                    if (col < 8) focusCell(row, col + 1);
                    break;
                case 'Delete':
                case 'Backspace':
                    if (!isCellGiven(row, col)) {
                        currentPuzzle[row][col] = 0;
                        event.target.value = '';
                        updateStatus();
                    }
                    break;
            }
        }

        // Focus on a specific cell
        function focusCell(row, col) {
            const cell = document.getElementById(`cell-${row}-${col}`);
            cell.focus();
        }

        // Select a cell
        function selectCell(row, col) {
            selectedCell = { row, col };
        }

        // Check if cell is given (part of original puzzle)
        function isCellGiven(row, col) {
            return originalPuzzle[row][col] !== 0;
        }

        // Validate a specific cell
        function validateCell(row, col) {
            const cell = document.getElementById(`cell-${row}-${col}`);
            cell.classList.remove('error');
            
            if (currentPuzzle[row][col] !== 0) {
                // Check for conflicts
                if (hasConflict(row, col)) {
                    cell.classList.add('error');
                }
            }
        }

        // Check if a cell has conflicts
        function hasConflict(row, col) {
            const value = currentPuzzle[row][col];
            if (value === 0) return false;

            // Check row
            for (let c = 0; c < 9; c++) {
                if (c !== col && currentPuzzle[row][c] === value) {
                    return true;
                }
            }

            // Check column
            for (let r = 0; r < 9; r++) {
                if (r !== row && currentPuzzle[r][col] === value) {
                    return true;
                }
            }

            // Check 3x3 box
            const boxRow = Math.floor(row / 3) * 3;
            const boxCol = Math.floor(col / 3) * 3;
            for (let r = boxRow; r < boxRow + 3; r++) {
                for (let c = boxCol; c < boxCol + 3; c++) {
                    if ((r !== row || c !== col) && currentPuzzle[r][c] === value) {
                        return true;
                    }
                }
            }

            return false;
        }

        // Update the puzzle display
        function updateDisplay() {
            for (let row = 0; row < 9; row++) {
                for (let col = 0; col < 9; col++) {
                    const cell = document.getElementById(`cell-${row}-${col}`);
                    const value = currentPuzzle[row][col];
                    
                    cell.value = value === 0 ? '' : value.toString();
                    
                    // Mark given cells
                    if (isCellGiven(row, col)) {
                        cell.classList.add('given');
                        cell.readOnly = true;
                    } else {
                        cell.classList.remove('given');
                        cell.readOnly = false;
                    }
                    
                    // Clear special classes
                    cell.classList.remove('error', 'hint');
                    
                    // Validate cell
                    validateCell(row, col);
                }
            }
            updateStatus();
        }

        // Update status display
        function updateStatus() {
            const clueCount = countClues(originalPuzzle);
            const filledCount = countClues(currentPuzzle);
            
            document.getElementById('currentDifficulty').textContent = currentDifficulty.charAt(0).toUpperCase() + currentDifficulty.slice(1);
            document.getElementById('clueCount').textContent = clueCount;
            document.getElementById('filledCount').textContent = `${filledCount}/81`;
            
            // Check if puzzle is valid
            validatePuzzle(false);
        }

        // Count non-zero cells
        function countClues(puzzle) {
            let count = 0;
            for (let row = 0; row < 9; row++) {
                for (let col = 0; col < 9; col++) {
                    if (puzzle[row][col] !== 0) count++;
                }
            }
            return count;
        }

        // Set difficulty level
        function setDifficulty(difficulty) {
            currentDifficulty = difficulty;
            
            // Update button styles
            document.querySelectorAll('.difficulty-btn').forEach(btn => {
                btn.classList.remove('btn-primary');
                btn.classList.add('btn-secondary');
            });
            
            event.target.classList.remove('btn-secondary');
            event.target.classList.add('btn-primary');
            
            updateStatus();
        }

        // Generate new puzzle
        async function generatePuzzle() {
            showMessage('Generating new puzzle...', 'info');
            setLoading(true);
            
            try {
                const response = await fetch(`${API_BASE}/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        difficulty: currentDifficulty
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                
                if (data.success) {
                    currentPuzzle = data.puzzle.map(row => [...row]);
                    originalPuzzle = data.puzzle.map(row => [...row]);
                    updateDisplay();
                    showMessage(`New ${currentDifficulty} puzzle generated with ${data.metadata.clues} clues!`, 'success');
                } else {
                    throw new Error(data.error || 'Failed to generate puzzle');
                }
            } catch (error) {
                console.error('Error generating puzzle:', error);
                showMessage('Failed to generate puzzle. Please check if the server is running.', 'error');
            } finally {
                setLoading(false);
            }
        }

        // Solve current puzzle
        async function solvePuzzle() {
            showMessage('Solving puzzle...', 'info');
            setLoading(true);
            
            try {
                const response = await fetch(`${API_BASE}/solve`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        puzzle: currentPuzzle,
                        method: 'advanced'
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                
                if (data.success) {
                    currentPuzzle = data.solution.map(row => [...row]);
                    updateDisplay();
                    showMessage(`Puzzle solved in ${data.solvingTime}ms using ${data.techniques.join(', ')}!`, 'success');
                } else {
                    throw new Error(data.error || 'Failed to solve puzzle');
                }
            } catch (error) {
                console.error('Error solving puzzle:', error);
                showMessage('Failed to solve puzzle. Please check if the server is running.', 'error');
            } finally {
                setLoading(false);
            }
        }

        // Get hint for next move
        async function getHint() {
            showMessage('Getting hint...', 'info');
            setLoading(true);
            
            try {
                const response = await fetch(`${API_BASE}/hint`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        puzzle: currentPuzzle
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                
                if (data.success && data.hint) {
                    const { row, col, value, technique, explanation } = data.hint;
                    
                    // Highlight the hint cell
                    const cell = document.getElementById(`cell-${row}-${col}`);
                    cell.classList.add('hint');
                    cell.value = value.toString();
                    currentPuzzle[row][col] = value;
                    
                    updateStatus();
                    showMessage(`Hint: ${explanation} (${technique})`, 'success');
                    
                    // Remove highlight after 3 seconds
                    setTimeout(() => {
                        cell.classList.remove('hint');
                    }, 3000);
                } else {
                    showMessage('No hints available for current puzzle state.', 'info');
                }
            } catch (error) {
                console.error('Error getting hint:', error);
                showMessage('Failed to get hint. Please check if the server is running.', 'error');
            } finally {
                setLoading(false);
            }
        }

        // Validate current puzzle
        async function validatePuzzle(showResult = true) {
            if (!showResult) {
                // Quick local validation for status display
                let hasErrors = false;
                for (let row = 0; row < 9; row++) {
                    for (let col = 0; col < 9; col++) {
                        if (hasConflict(row, col)) {
                            hasErrors = true;
                            break;
                        }
                    }
                    if (hasErrors) break;
                }
                document.getElementById('validStatus').textContent = hasErrors ? 'Invalid' : 'Valid';
                return;
            }
            
            showMessage('Validating puzzle...', 'info');
            setLoading(true);
            
            try {
                const response = await fetch(`${API_BASE}/validate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        puzzle: currentPuzzle
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                
                if (data.success) {
                    const message = data.isValid ? 
                        `Puzzle is valid! ${data.completeness.percentage}% complete.` :
                        `Puzzle has ${data.conflicts.length} conflicts.`;
                    
                    showMessage(message, data.isValid ? 'success' : 'error');
                    document.getElementById('validStatus').textContent = data.isValid ? 'Valid' : 'Invalid';
                } else {
                    throw new Error(data.error || 'Failed to validate puzzle');
                }
            } catch (error) {
                console.error('Error validating puzzle:', error);
                showMessage('Failed to validate puzzle. Please check if the server is running.', 'error');
            } finally {
                setLoading(false);
            }
        }

        // Analyze current puzzle
        async function analyzePuzzle() {
            showMessage('Analyzing puzzle...', 'info');
            setLoading(true);
            
            try {
                const response = await fetch(`${API_BASE}/analyze`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        puzzle: currentPuzzle
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                
                if (data.success) {
                    const analysis = data.analysis;
                    const message = `Analysis: ${analysis.difficulty} difficulty, ${analysis.clueCount} clues, ` +
                                  `requires ${analysis.requiredTechniques.join(', ')}. ` +
                                  `Estimated time: ${analysis.estimatedTime}`;
                    
                    showMessage(message, 'info');
                } else {
                    throw new Error(data.error || 'Failed to analyze puzzle');
                }
            } catch (error) {
                console.error('Error analyzing puzzle:', error);
                showMessage('Failed to analyze puzzle. Please check if the server is running.', 'error');
            } finally {
                setLoading(false);
            }
        }

        // Clear the board
        function clearBoard() {
            currentPuzzle = originalPuzzle.map(row => [...row]);
            updateDisplay();
            showMessage('Board cleared to original state.', 'info');
        }

        // Reset the entire game
        function resetGame() {
            currentPuzzle = Array(9).fill().map(() => Array(9).fill(0));
            originalPuzzle = Array(9).fill().map(() => Array(9).fill(0));
            updateDisplay();
            showMessage('Game reset. Generate a new puzzle to start playing.', 'info');
        }

        // Show message to user
        function showMessage(message, type = 'info') {
            const messageArea = document.getElementById('messageArea');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = message;
            
            messageArea.innerHTML = '';
            messageArea.appendChild(messageDiv);
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                if (messageArea.contains(messageDiv)) {
                    messageArea.removeChild(messageDiv);
                }
            }, 5000);
        }

        // Set loading state
        function setLoading(loading) {
            const container = document.querySelector('.container');
            if (loading) {
                container.classList.add('loading');
            } else {
                container.classList.remove('loading');
            }
        }

        // Initialize the game when page loads
        document.addEventListener('DOMContentLoaded', initGame);
    </script>
</body>
</html>
