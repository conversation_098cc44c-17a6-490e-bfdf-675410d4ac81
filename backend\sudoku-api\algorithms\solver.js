// This file is being migrated to TypeScript
// See src/algorithms/solver.ts for the new implementation
module.exports = require('../dist/algorithms/solver.js');

  // Main solving method with multiple algorithms
  solve(puzzle, options = {}) {
    const { method = 'advanced', stepByStep = false } = options;
    
    // Deep copy the puzzle
    const grid = puzzle.map(row => [...row]);
    const steps = stepByStep ? [] : null;
    let statistics = {
      iterations: 0,
      backtrackCount: 0,
      logicalSteps: 0
    };

    let result;
    
    switch (method) {
      case 'logical':
        result = this.solveLogical(grid, steps, statistics);
        break;
      case 'backtrack':
        result = this.solveBacktrack(grid, steps, statistics);
        break;
      case 'advanced':
      default:
        result = this.solveAdvanced(grid, steps, statistics);
        break;
    }

    return {
      solved: result.solved,
      solution: result.solution,
      steps: steps,
      difficulty: this.assessDifficulty(puzzle, result.techniques),
      techniques: result.techniques,
      iterations: statistics.iterations,
      backtrackCount: statistics.backtrackCount,
      logicalSteps: statistics.logicalSteps,
      partialSolution: result.solved ? null : grid,
      reason: result.reason
    };
  }

  // Advanced solver combining logical techniques with backtracking
  solveAdvanced(grid, steps, statistics) {
    const techniques = [];
    let progress = true;
    
    // First, apply logical techniques
    while (progress && !this.isComplete(grid)) {
      progress = false;
      statistics.iterations++;

      // Naked singles
      if (this.applyNakedSingles(grid, steps, statistics)) {
        techniques.push(this.techniques.NAKED_SINGLE);
        progress = true;
        continue;
      }

      // Hidden singles
      if (this.applyHiddenSingles(grid, steps, statistics)) {
        techniques.push(this.techniques.HIDDEN_SINGLE);
        progress = true;
        continue;
      }

      // Intersection removal
      if (this.applyIntersectionRemoval(grid, steps, statistics)) {
        techniques.push(this.techniques.INTERSECTION_REMOVAL);
        progress = true;
        continue;
      }
    }

    // If logical techniques aren't enough, use backtracking
    if (!this.isComplete(grid)) {
      techniques.push(this.techniques.BACKTRACKING);
      const backtrackResult = this.solveBacktrack(grid, steps, statistics);
      return {
        solved: backtrackResult.solved,
        solution: backtrackResult.solution,
        techniques: [...new Set(techniques)],
        reason: backtrackResult.reason
      };
    }

    return {
      solved: true,
      solution: grid,
      techniques: [...new Set(techniques)]
    };
  }

  // Logical-only solver
  solveLogical(grid, steps, statistics) {
    const techniques = [];
    let progress = true;
    
    while (progress && !this.isComplete(grid)) {
      progress = false;
      statistics.iterations++;

      if (this.applyNakedSingles(grid, steps, statistics)) {
        techniques.push(this.techniques.NAKED_SINGLE);
        progress = true;
      }

      if (this.applyHiddenSingles(grid, steps, statistics)) {
        techniques.push(this.techniques.HIDDEN_SINGLE);
        progress = true;
      }

      if (this.applyIntersectionRemoval(grid, steps, statistics)) {
        techniques.push(this.techniques.INTERSECTION_REMOVAL);
        progress = true;
      }
    }

    return {
      solved: this.isComplete(grid),
      solution: grid,
      techniques: [...new Set(techniques)],
      reason: this.isComplete(grid) ? null : 'Requires advanced techniques'
    };
  }

  // Backtracking solver
  solveBacktrack(grid, steps, statistics) {
    const solve = (grid) => {
      statistics.iterations++;
      
      const emptyCell = this.findEmptyCell(grid);
      if (!emptyCell) {
        return true; // Solved
      }

      const [row, col] = emptyCell;
      
      for (let num = 1; num <= 9; num++) {
        if (this.isValidMove(grid, row, col, num)) {
          grid[row][col] = num;
          
          if (steps) {
            steps.push({
              technique: this.techniques.BACKTRACKING,
              row: row,
              col: col,
              value: num,
              action: 'place'
            });
          }

          if (solve(grid)) {
            return true;
          }

          // Backtrack
          grid[row][col] = 0;
          statistics.backtrackCount++;
          
          if (steps) {
            steps.push({
              technique: this.techniques.BACKTRACKING,
              row: row,
              col: col,
              value: 0,
              action: 'backtrack'
            });
          }
        }
      }
      
      return false;
    };

    const solved = solve(grid);
    return {
      solved: solved,
      solution: grid,
      techniques: [this.techniques.BACKTRACKING],
      reason: solved ? null : 'Unsolvable puzzle'
    };
  }

  // Apply naked singles technique
  applyNakedSingles(grid, steps, statistics) {
    let progress = false;
    
    for (let row = 0; row < 9; row++) {
      for (let col = 0; col < 9; col++) {
        if (grid[row][col] === 0) {
          const candidates = this.getCandidates(grid, row, col);
          if (candidates.length === 1) {
            grid[row][col] = candidates[0];
            statistics.logicalSteps++;
            progress = true;
            
            if (steps) {
              steps.push({
                technique: this.techniques.NAKED_SINGLE,
                row: row,
                col: col,
                value: candidates[0],
                explanation: `Only possible value for cell (${row + 1}, ${col + 1})`
              });
            }
          }
        }
      }
    }
    
    return progress;
  }

  // Apply hidden singles technique
  applyHiddenSingles(grid, steps, statistics) {
    let progress = false;
    
    // Check rows, columns, and boxes
    for (let i = 0; i < 9; i++) {
      progress = this.findHiddenSinglesInUnit(grid, this.getRow(grid, i), 'row', i, steps, statistics) || progress;
      progress = this.findHiddenSinglesInUnit(grid, this.getCol(grid, i), 'col', i, steps, statistics) || progress;
      progress = this.findHiddenSinglesInUnit(grid, this.getBox(grid, i), 'box', i, steps, statistics) || progress;
    }
    
    return progress;
  }

  // Apply intersection removal technique
  applyIntersectionRemoval(grid, steps, statistics) {
    // This is a simplified version - full implementation would be more complex
    return false;
  }

  // Get possible candidates for a cell
  getCandidates(grid, row, col) {
    if (grid[row][col] !== 0) return [];
    
    const candidates = [];
    for (let num = 1; num <= 9; num++) {
      if (this.isValidMove(grid, row, col, num)) {
        candidates.push(num);
      }
    }
    return candidates;
  }

  // Check if a move is valid
  isValidMove(grid, row, col, num) {
    // Check row
    for (let c = 0; c < 9; c++) {
      if (grid[row][c] === num) return false;
    }
    
    // Check column
    for (let r = 0; r < 9; r++) {
      if (grid[r][col] === num) return false;
    }
    
    // Check 3x3 box
    const boxRow = Math.floor(row / 3) * 3;
    const boxCol = Math.floor(col / 3) * 3;
    for (let r = boxRow; r < boxRow + 3; r++) {
      for (let c = boxCol; c < boxCol + 3; c++) {
        if (grid[r][c] === num) return false;
      }
    }
    
    return true;
  }

  // Find empty cell
  findEmptyCell(grid) {
    for (let row = 0; row < 9; row++) {
      for (let col = 0; col < 9; col++) {
        if (grid[row][col] === 0) {
          return [row, col];
        }
      }
    }
    return null;
  }

  // Check if puzzle is complete
  isComplete(grid) {
    for (let row = 0; row < 9; row++) {
      for (let col = 0; col < 9; col++) {
        if (grid[row][col] === 0) {
          return false;
        }
      }
    }
    return true;
  }

  // Get hint for next move
  getHint(puzzle) {
    const grid = puzzle.map(row => [...row]);
    
    // Try to find a naked single
    for (let row = 0; row < 9; row++) {
      for (let col = 0; col < 9; col++) {
        if (grid[row][col] === 0) {
          const candidates = this.getCandidates(grid, row, col);
          if (candidates.length === 1) {
            return {
              row: row,
              col: col,
              value: candidates[0],
              technique: this.techniques.NAKED_SINGLE,
              explanation: `This cell can only contain ${candidates[0]}`,
              difficulty: 'easy'
            };
          }
        }
      }
    }
    
    return null;
  }

  // Analyze puzzle difficulty
  analyzePuzzle(puzzle) {
    const grid = puzzle.map(row => [...row]);
    const result = this.solve(grid, { method: 'advanced' });
    
    return {
      difficulty: result.difficulty,
      solvable: result.solved,
      uniqueSolution: true, // Simplified - would need more complex check
      clueCount: this.countClues(puzzle),
      techniques: result.techniques,
      estimatedTime: this.estimateTime(result.difficulty),
      complexity: this.calculateComplexity(result.techniques)
    };
  }

  // Helper methods
  getRow(grid, row) {
    return grid[row];
  }

  getCol(grid, col) {
    return grid.map(row => row[col]);
  }

  getBox(grid, boxIndex) {
    const box = [];
    const startRow = Math.floor(boxIndex / 3) * 3;
    const startCol = (boxIndex % 3) * 3;
    
    for (let r = startRow; r < startRow + 3; r++) {
      for (let c = startCol; c < startCol + 3; c++) {
        box.push(grid[r][c]);
      }
    }
    return box;
  }

  findHiddenSinglesInUnit(grid, unit, type, index, steps, statistics) {
    let progress = false;

    for (let num = 1; num <= 9; num++) {
      if (!unit.includes(num)) {
        // Find cells where this number can go
        const possibleCells = [];

        if (type === 'row') {
          for (let col = 0; col < 9; col++) {
            if (grid[index][col] === 0 && this.isValidMove(grid, index, col, num)) {
              possibleCells.push([index, col]);
            }
          }
        } else if (type === 'col') {
          for (let row = 0; row < 9; row++) {
            if (grid[row][index] === 0 && this.isValidMove(grid, row, index, num)) {
              possibleCells.push([row, index]);
            }
          }
        } else if (type === 'box') {
          const startRow = Math.floor(index / 3) * 3;
          const startCol = (index % 3) * 3;
          for (let r = startRow; r < startRow + 3; r++) {
            for (let c = startCol; c < startCol + 3; c++) {
              if (grid[r][c] === 0 && this.isValidMove(grid, r, c, num)) {
                possibleCells.push([r, c]);
              }
            }
          }
        }

        if (possibleCells.length === 1) {
          const [row, col] = possibleCells[0];
          grid[row][col] = num;
          statistics.logicalSteps++;
          progress = true;

          if (steps) {
            steps.push({
              technique: this.techniques.HIDDEN_SINGLE,
              row: row,
              col: col,
              value: num,
              explanation: `${num} can only go in this cell within ${type} ${index + 1}`
            });
          }
        }
      }
    }

    return progress;
  }

  assessDifficulty(puzzle, techniques) {
    if (techniques.includes(this.techniques.BACKTRACKING)) {
      return 'hard';
    } else if (techniques.includes(this.techniques.HIDDEN_SINGLE)) {
      return 'medium';
    } else {
      return 'easy';
    }
  }

  countClues(puzzle) {
    let count = 0;
    for (let row = 0; row < 9; row++) {
      for (let col = 0; col < 9; col++) {
        if (puzzle[row][col] !== 0) count++;
      }
    }
    return count;
  }

  estimateTime(difficulty) {
    const times = {
      'easy': '1-3 minutes',
      'medium': '5-10 minutes',
      'hard': '15-30 minutes',
      'expert': '30+ minutes'
    };
    return times[difficulty] || 'Unknown';
  }

  calculateComplexity(techniques) {
    return techniques.length * 10; // Simplified complexity score
  }
}

module.exports = SudokuSolver;
