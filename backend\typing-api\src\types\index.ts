/**
 * Comprehensive TypeScript type definitions for Typing Analytics API
 * Enterprise-grade typing system with advanced analytics and real-time features
 */

// Core typing test interfaces
export interface TypingTest {
  id: string;
  userId?: string;
  username: string;
  textId: number;
  startTime: Date;
  endTime: Date;
  duration: number; // in milliseconds
  wpm: number;
  accuracy: number;
  rawWpm: number;
  netWpm: number;
  charactersTyped: number;
  correctCharacters: number;
  incorrectCharacters: number;
  totalCharacters: number;
  errors: TypingError[];
  keystrokes: Keystroke[];
  difficulty: DifficultyLevel;
  category: TextCategory;
  metadata: TestMetadata;
  createdAt: Date;
  updatedAt: Date;
}

export interface TypingError {
  position: number;
  expected: string;
  actual: string;
  timestamp: number;
  corrected: boolean;
  correctionTime?: number;
}

export interface Keystroke {
  key: string;
  timestamp: number;
  position: number;
  isCorrect: boolean;
  timeSinceLastKey: number;
}

export interface TestMetadata {
  browserInfo: string;
  deviceType: 'desktop' | 'tablet' | 'mobile';
  keyboardLayout?: string;
  language: string;
  timezone: string;
  sessionId: string;
  ipAddress?: string;
}

// Text sample interfaces
export interface TextSample {
  id: number;
  text: string;
  difficulty: DifficultyLevel;
  category: TextCategory;
  language: string;
  characterCount: number;
  wordCount: number;
  averageWordLength: number;
  complexityScore: number;
  commonWords: number;
  punctuationCount: number;
  numbersCount: number;
  specialCharsCount: number;
  metadata: TextMetadata;
  createdAt: Date;
  updatedAt: Date;
}

export interface TextMetadata {
  source?: string;
  author?: string;
  genre?: string;
  tags: string[];
  estimatedTime: number; // seconds for average typist
  popularityScore: number;
}

// User and session interfaces
export interface User {
  id: string;
  username: string;
  email?: string;
  passwordHash?: string;
  profile: UserProfile;
  statistics: UserStatistics;
  preferences: UserPreferences;
  achievements: Achievement[];
  createdAt: Date;
  updatedAt: Date;
  lastActiveAt: Date;
}

export interface UserProfile {
  displayName: string;
  avatar?: string;
  bio?: string;
  country?: string;
  timezone: string;
  keyboardLayout: string;
  experienceLevel: ExperienceLevel;
  goals: TypingGoal[];
}

export interface UserStatistics {
  totalTests: number;
  totalTime: number; // milliseconds
  totalCharacters: number;
  averageWpm: number;
  bestWpm: number;
  averageAccuracy: number;
  bestAccuracy: number;
  improvementRate: number; // WPM improvement per week
  consistencyScore: number; // 0-100
  streakDays: number;
  longestStreak: number;
  favoriteCategory: TextCategory;
  weakestKeys: string[];
  strongestKeys: string[];
  peakPerformanceHour: number; // 0-23
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  soundEnabled: boolean;
  showLiveWpm: boolean;
  showLiveAccuracy: boolean;
  highlightErrors: boolean;
  stopOnError: boolean;
  fontSize: number;
  fontFamily: string;
  caretStyle: 'line' | 'block' | 'underline';
  keyboardSounds: boolean;
  notifications: NotificationSettings;
}

export interface NotificationSettings {
  achievements: boolean;
  milestones: boolean;
  reminders: boolean;
  leaderboard: boolean;
  email: boolean;
}

// Analytics and leaderboard interfaces
export interface LeaderboardEntry {
  rank: number;
  userId: string;
  username: string;
  wpm: number;
  accuracy: number;
  testsCompleted: number;
  averageWpm: number;
  bestWpm: number;
  improvementRate: number;
  lastTestDate: Date;
  badge?: string;
  country?: string;
}

export interface GlobalAnalytics {
  totalTests: number;
  totalUsers: number;
  averageWpm: number;
  averageAccuracy: number;
  totalCharacters: number;
  totalTime: number;
  popularCategories: CategoryStats[];
  difficultyDistribution: DifficultyStats[];
  hourlyActivity: HourlyStats[];
  dailyActivity: DailyStats[];
  topPerformers: LeaderboardEntry[];
  recentActivity: RecentActivity[];
  trends: AnalyticsTrends;
}

export interface CategoryStats {
  category: TextCategory;
  testsCount: number;
  averageWpm: number;
  averageAccuracy: number;
  popularityScore: number;
}

export interface DifficultyStats {
  difficulty: DifficultyLevel;
  testsCount: number;
  averageWpm: number;
  averageAccuracy: number;
  completionRate: number;
}

export interface HourlyStats {
  hour: number;
  testsCount: number;
  averageWpm: number;
  activeUsers: number;
}

export interface DailyStats {
  date: string;
  testsCount: number;
  uniqueUsers: number;
  averageWpm: number;
  averageAccuracy: number;
}

export interface RecentActivity {
  id: string;
  type: 'test_completed' | 'achievement_earned' | 'milestone_reached';
  userId: string;
  username: string;
  details: any;
  timestamp: Date;
}

export interface AnalyticsTrends {
  wpmTrend: TrendData[];
  accuracyTrend: TrendData[];
  userGrowth: TrendData[];
  testVolume: TrendData[];
}

export interface TrendData {
  period: string;
  value: number;
  change: number; // percentage change from previous period
}

// Achievement and gamification interfaces
export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: AchievementCategory;
  difficulty: AchievementDifficulty;
  requirements: AchievementRequirement[];
  reward: AchievementReward;
  unlockedAt?: Date;
  progress: number; // 0-100
}

export interface AchievementRequirement {
  type: 'wpm' | 'accuracy' | 'tests_completed' | 'streak' | 'time_spent' | 'category_mastery';
  value: number;
  operator: 'gte' | 'lte' | 'eq';
  context?: any;
}

export interface AchievementReward {
  type: 'badge' | 'title' | 'theme' | 'feature';
  value: string;
  description: string;
}

export interface TypingGoal {
  id: string;
  type: 'wpm' | 'accuracy' | 'consistency' | 'time_spent';
  target: number;
  current: number;
  deadline?: Date;
  isActive: boolean;
  createdAt: Date;
  completedAt?: Date;
}

// API request/response interfaces
export interface SubmitTestRequest {
  textId: number;
  startTime: string;
  endTime: string;
  keystrokes: Keystroke[];
  errors: TypingError[];
  metadata: TestMetadata;
  username: string;
}

export interface SubmitTestResponse {
  success: boolean;
  data: {
    testId: string;
    results: TestResults;
    achievements?: Achievement[];
    newPersonalBests: PersonalBest[];
    leaderboardPosition?: number;
  };
  message: string;
}

export interface TestResults {
  wpm: number;
  rawWpm: number;
  netWpm: number;
  accuracy: number;
  duration: number;
  charactersTyped: number;
  correctCharacters: number;
  incorrectCharacters: number;
  errorRate: number;
  consistencyScore: number;
  speedVariation: number;
  keyboardEfficiency: number;
  weakestKeys: string[];
  strongestKeys: string[];
  timeDistribution: TimeDistribution;
  comparison: PerformanceComparison;
}

export interface TimeDistribution {
  firstQuarter: number;
  secondQuarter: number;
  thirdQuarter: number;
  fourthQuarter: number;
}

export interface PerformanceComparison {
  personalAverage: number;
  globalAverage: number;
  categoryAverage: number;
  difficultyAverage: number;
  percentile: number;
}

export interface PersonalBest {
  type: 'wpm' | 'accuracy' | 'consistency';
  value: number;
  previousBest: number;
  improvement: number;
  testId: string;
}

// Enum types
export type DifficultyLevel = 'easy' | 'medium' | 'hard' | 'expert' | 'insane';
export type TextCategory = 'quotes' | 'programming' | 'technical' | 'literature' | 'news' | 'poetry' | 'lyrics' | 'custom';
export type ExperienceLevel = 'beginner' | 'intermediate' | 'advanced' | 'expert' | 'master';
export type AchievementCategory = 'speed' | 'accuracy' | 'consistency' | 'endurance' | 'improvement' | 'special';
export type AchievementDifficulty = 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond';

// Real-time interfaces
export interface RealTimeSession {
  sessionId: string;
  userId: string;
  username: string;
  currentTest?: {
    textId: number;
    startTime: Date;
    currentPosition: number;
    currentWpm: number;
    currentAccuracy: number;
    errors: number;
  };
  isActive: boolean;
  lastActivity: Date;
}

export interface LiveTypingData {
  sessionId: string;
  position: number;
  wpm: number;
  accuracy: number;
  errors: number;
  timestamp: Date;
}

// Database interfaces
export interface DatabaseConfig {
  type: 'sqlite' | 'postgresql' | 'mysql';
  host?: string;
  port?: number;
  database: string;
  username?: string;
  password?: string;
  ssl?: boolean;
  pool?: {
    min: number;
    max: number;
  };
}

// Error interfaces
export interface ApiError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
  requestId: string;
}

export interface ValidationIssue {
  field: string;
  message: string;
  value?: any;
  code: string;
}

// Configuration interfaces
export interface AppConfig {
  port: number;
  environment: 'development' | 'production' | 'test';
  database: DatabaseConfig;
  redis: {
    host: string;
    port: number;
    password?: string;
    db: number;
  };
  jwt: {
    secret: string;
    expiresIn: string;
  };
  rateLimit: {
    windowMs: number;
    max: number;
  };
  cors: {
    origin: string | string[];
    credentials: boolean;
  };
  logging: {
    level: string;
    file: boolean;
    console: boolean;
  };
  monitoring: {
    enabled: boolean;
    metricsPath: string;
  };
}
