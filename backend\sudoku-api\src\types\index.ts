/**
 * Core type definitions for the Sudoku Solver API
 */

// Basic Sudoku types
export type SudokuCell = 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9;
export type SudokuGrid = SudokuCell[][];
export type SudokuPosition = { row: number; col: number };

// Difficulty levels
export type DifficultyLevel = 'easy' | 'medium' | 'hard' | 'expert' | 'evil';

// Solving techniques
export type SolvingTechnique =
  | 'NAKED_SINGLE'
  | 'HIDDEN_SINGLE'
  | 'INTERSECTION_REMOVAL'
  | 'NAKED_PAIR'
  | 'HIDDEN_PAIR'
  | 'POINTING_PAIRS'
  | 'BOX_LINE_REDUCTION'
  | 'NAKED_TRIPLE'
  | 'HIDDEN_TRIPLE'
  | 'X_WING'
  | 'SWORDFISH'
  | 'BACKTRACKING';

// Game states
export type GameState = 'playing' | 'solved' | 'unsolvable' | 'invalid';

// API Response types
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: string;
  requestId: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Solving related interfaces
export interface SolveOptions {
  method?: 'logical' | 'backtrack' | 'advanced';
  stepByStep?: boolean;
  maxIterations?: number;
  timeout?: number;
}

export interface SolveResult {
  solved: boolean;
  solution: SudokuGrid | null;
  steps?: SolveStep[];
  difficulty: DifficultyLevel;
  techniques: SolvingTechnique[];
  statistics: SolveStatistics;
  partialSolution?: SudokuGrid;
  reason?: string;
}

export interface SolveStep {
  stepNumber: number;
  technique: SolvingTechnique;
  position: SudokuPosition;
  value: SudokuCell;
  explanation: string;
  gridState: SudokuGrid;
  timestamp: Date;
}

export interface SolveStatistics {
  iterations: number;
  backtrackCount: number;
  logicalSteps: number;
  solvingTime: number;
  memoryUsage?: number;
  complexity: number;
}

// Generation related interfaces
export interface GenerateOptions {
  seed?: number;
  uniqueSolution?: boolean;
  symmetry?: 'none' | 'rotational' | 'horizontal' | 'vertical' | 'diagonal';
}

export interface GenerateResult {
  puzzle: SudokuGrid;
  solution: SudokuGrid;
  difficulty: DifficultyLevel;
  metadata: GenerateMetadata;
}

export interface GenerateMetadata {
  clues: number;
  uniqueSolution: boolean;
  generationTime: number;
  seed: number;
  requiredTechniques: SolvingTechnique[];
  symmetry: string;
  complexity: number;
}

// Validation related interfaces
export interface ValidationResult {
  isValid: boolean;
  conflicts: Conflict[];
  completeness: number;
  errors: ValidationIssue[];
}

export interface Conflict {
  type: 'row' | 'column' | 'box';
  position: SudokuPosition;
  value: SudokuCell;
  conflictingPositions: SudokuPosition[];
}

export interface ValidationIssue {
  code: string;
  message: string;
  position?: SudokuPosition;
  severity: 'error' | 'warning';
}

// Hint related interfaces
export interface Hint {
  position: SudokuPosition;
  value: SudokuCell;
  technique: SolvingTechnique;
  explanation: string;
  difficulty: DifficultyLevel;
  confidence: number;
  alternativeValues?: SudokuCell[];
}

// Analysis related interfaces
export interface PuzzleAnalysis {
  difficulty: DifficultyLevel;
  solvable: boolean;
  uniqueSolution: boolean;
  clueCount: number;
  requiredTechniques: SolvingTechnique[];
  estimatedTime: string;
  complexity: number;
  rating: number;
  symmetryScore: number;
}

// User and session related interfaces
export interface User {
  id: string;
  username: string;
  email: string;
  createdAt: Date;
  lastActive: Date;
  preferences: UserPreferences;
  statistics: UserStatistics;
}

export interface UserPreferences {
  difficulty: DifficultyLevel;
  showHints: boolean;
  highlightConflicts: boolean;
  theme: 'light' | 'dark';
  soundEnabled: boolean;
}

export interface UserStatistics {
  gamesPlayed: number;
  gamesWon: number;
  averageSolveTime: number;
  bestTime: number;
  favoriteLevel: DifficultyLevel;
  totalHintsUsed: number;
  streakCurrent: number;
  streakBest: number;
}

// Game session interfaces
export interface GameSession {
  id: string;
  userId?: string;
  puzzle: SudokuGrid;
  solution: SudokuGrid;
  currentState: SudokuGrid;
  difficulty: DifficultyLevel;
  startTime: Date;
  endTime?: Date;
  moves: GameMove[];
  hintsUsed: number;
  status: GameState;
  metadata: SessionMetadata;
}

export interface GameMove {
  position: SudokuPosition;
  value: SudokuCell;
  previousValue: SudokuCell;
  timestamp: Date;
  isHint: boolean;
}

export interface SessionMetadata {
  ip?: string;
  userAgent?: string;
  source: 'web' | 'api' | 'mobile';
  version: string;
}

// Configuration interfaces
export interface AppConfig {
  port: number;
  nodeEnv: 'development' | 'production' | 'test';
  database: DatabaseConfig;
  redis: RedisConfig;
  jwt: JwtConfig;
  rateLimit: RateLimitConfig;
  cors: CorsConfig;
  logging: LoggingConfig;
}

export interface DatabaseConfig {
  url: string;
  maxConnections: number;
  timeout: number;
}

export interface RedisConfig {
  url: string;
  ttl: number;
  maxRetries: number;
}

export interface JwtConfig {
  secret: string;
  expiresIn: string;
  issuer: string;
}

export interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  skipSuccessfulRequests: boolean;
}

export interface CorsConfig {
  origin: string | string[];
  credentials: boolean;
}

export interface LoggingConfig {
  level: 'error' | 'warn' | 'info' | 'debug';
  format: 'json' | 'simple';
  maxFiles: number;
  maxSize: string;
}

// Metrics and monitoring interfaces
export interface Metrics {
  requests: RequestMetrics;
  performance: PerformanceMetrics;
  errors: ErrorMetrics;
  business: BusinessMetrics;
}

export interface RequestMetrics {
  total: number;
  successful: number;
  failed: number;
  averageResponseTime: number;
  requestsPerSecond: number;
}

export interface PerformanceMetrics {
  memoryUsage: number;
  cpuUsage: number;
  uptime: number;
  activeConnections: number;
}

export interface ErrorMetrics {
  total: number;
  byType: Record<string, number>;
  byEndpoint: Record<string, number>;
  errorRate: number;
}

export interface BusinessMetrics {
  puzzlesGenerated: number;
  puzzlesSolved: number;
  averageSolveTime: number;
  popularDifficulty: DifficultyLevel;
  hintsRequested: number;
}

// Error types
export class SudokuError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500,
    public details?: unknown
  ) {
    super(message);
    this.name = 'SudokuError';
  }
}

export class ValidationError extends SudokuError {
  constructor(message: string, details?: unknown) {
    super(message, 'VALIDATION_ERROR', 400, details);
    this.name = 'ValidationError';
  }
}

export class SolvingError extends SudokuError {
  constructor(message: string, details?: unknown) {
    super(message, 'SOLVING_ERROR', 422, details);
    this.name = 'SolvingError';
  }
}

export class GenerationError extends SudokuError {
  constructor(message: string, details?: unknown) {
    super(message, 'GENERATION_ERROR', 500, details);
    this.name = 'GenerationError';
  }
}
