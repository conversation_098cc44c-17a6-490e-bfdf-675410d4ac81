/**
 * Leaderboard routes for competitive typing rankings
 */

import { Router, Request, Response } from 'express';
import { asyncHand<PERSON> } from '@/middleware/errorHandler';
import { validationMiddleware } from '@/middleware/validation';
import { logger } from '@/utils/logger';

const router = Router();

/**
 * Get leaderboard rankings
 */
router.get('/', validationMiddleware.getLeaderboard, asyncHandler(async (req: Request, res: Response) => {
  const { 
    category, 
    difficulty, 
    timeframe = 'all-time', 
    limit = 50, 
    offset = 0 
  } = req.query;

  try {
    // Mock leaderboard data - in production this would query the database
    const leaderboard = generateMockLeaderboard(
      category as string,
      difficulty as string,
      timeframe as string,
      parseInt(limit as string),
      parseInt(offset as string)
    );

    res.json({
      success: true,
      data: {
        entries: leaderboard,
        total: 2847,
        limit: parseInt(limit as string),
        offset: parseInt(offset as string),
        filters: {
          category,
          difficulty,
          timeframe
        }
      }
    });

  } catch (error) {
    logger.error('Failed to get leaderboard:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get leaderboard',
      requestId: (req as any).requestId
    });
  }
}));

/**
 * Get user's leaderboard position
 */
router.get('/position/:username', asyncHandler(async (req: Request, res: Response) => {
  const { username } = req.params;
  const { category, difficulty, timeframe = 'all-time' } = req.query;

  try {
    // Mock user position data
    const position = {
      username,
      rank: 127,
      wpm: 67.3,
      accuracy: 94.2,
      testsCompleted: 89,
      percentile: 85.4,
      category: category || 'all',
      difficulty: difficulty || 'all',
      timeframe,
      nearbyRankings: [
        { rank: 125, username: 'speedster125', wpm: 67.8, accuracy: 93.9 },
        { rank: 126, username: 'typingpro', wpm: 67.5, accuracy: 94.1 },
        { rank: 127, username: username, wpm: 67.3, accuracy: 94.2 },
        { rank: 128, username: 'keyboardking', wpm: 67.1, accuracy: 94.0 },
        { rank: 129, username: 'fastfingers', wpm: 66.9, accuracy: 93.8 }
      ]
    };

    res.json({
      success: true,
      data: position
    });

  } catch (error) {
    logger.error('Failed to get user position:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get user position',
      requestId: (req as any).requestId
    });
  }
}));

/**
 * Get top performers by category
 */
router.get('/top/:category', asyncHandler(async (req: Request, res: Response) => {
  const { category } = req.params;
  const { limit = 10 } = req.query;

  try {
    const topPerformers = generateTopPerformers(category, parseInt(limit as string));

    res.json({
      success: true,
      data: {
        category,
        performers: topPerformers,
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Failed to get top performers:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get top performers',
      requestId: (req as any).requestId
    });
  }
}));

/**
 * Get leaderboard statistics
 */
router.get('/stats', asyncHandler(async (req: Request, res: Response) => {
  try {
    const stats = {
      totalParticipants: 2847,
      averageWpm: 52.3,
      averageAccuracy: 91.7,
      topWpm: 142.8,
      topAccuracy: 99.7,
      categoriesAvailable: [
        'quotes', 'programming', 'technical', 'literature', 'news', 'poetry'
      ],
      difficultiesAvailable: [
        'easy', 'medium', 'hard', 'expert'
      ],
      timeframesAvailable: [
        'daily', 'weekly', 'monthly', 'all-time'
      ],
      lastUpdated: new Date().toISOString()
    };

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    logger.error('Failed to get leaderboard stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get leaderboard stats',
      requestId: (req as any).requestId
    });
  }
}));

/**
 * Generate mock leaderboard data
 */
function generateMockLeaderboard(
  category?: string,
  difficulty?: string,
  timeframe: string = 'all-time',
  limit: number = 50,
  offset: number = 0
) {
  const entries = [];
  const usernames = [
    'speedtyper', 'keyboardmaster', 'typinglegend', 'fastfingers', 'quickkeys',
    'wordwarrior', 'textterror', 'typetitan', 'keystrokekid', 'rapidtyper',
    'swiftscribe', 'blazingkeys', 'turbotyper', 'lightningfingers', 'speedster',
    'typingace', 'keyboardking', 'wordwizard', 'textmaster', 'typingpro'
  ];

  for (let i = 0; i < limit; i++) {
    const rank = offset + i + 1;
    const username = usernames[i % usernames.length] + (Math.floor(i / usernames.length) || '');
    
    // Generate realistic WPM based on rank (higher rank = higher WPM)
    const baseWpm = 100 - (rank - 1) * 0.5;
    const wpm = Math.max(30, baseWpm + (Math.random() - 0.5) * 10);
    
    // Generate realistic accuracy (generally high, with some variation)
    const accuracy = Math.min(100, 95 + Math.random() * 4.5);
    
    // Generate other stats
    const testsCompleted = Math.floor(50 + Math.random() * 200);
    const averageWpm = wpm * (0.85 + Math.random() * 0.15);
    const bestWpm = wpm * (1.05 + Math.random() * 0.1);
    
    // Generate improvement rate (newer players might have higher rates)
    const improvementRate = Math.max(0, 5 - rank * 0.01 + Math.random() * 2);
    
    // Generate last test date (more recent for active players)
    const daysAgo = Math.floor(Math.random() * 30);
    const lastTestDate = new Date();
    lastTestDate.setDate(lastTestDate.getDate() - daysAgo);

    entries.push({
      rank,
      userId: `user-${rank}`,
      username,
      wpm: Math.round(wpm * 10) / 10,
      accuracy: Math.round(accuracy * 10) / 10,
      testsCompleted,
      averageWpm: Math.round(averageWpm * 10) / 10,
      bestWpm: Math.round(bestWpm * 10) / 10,
      improvementRate: Math.round(improvementRate * 10) / 10,
      lastTestDate: lastTestDate.toISOString(),
      badge: getBadgeForRank(rank),
      country: getRandomCountry()
    });
  }

  return entries;
}

/**
 * Generate top performers for a category
 */
function generateTopPerformers(category: string, limit: number) {
  const performers = [];
  const categoryMultipliers: { [key: string]: number } = {
    'programming': 0.9, // Programming text is typically slower
    'technical': 0.95,
    'quotes': 1.05,
    'literature': 1.0,
    'news': 1.0,
    'poetry': 0.98
  };

  const multiplier = categoryMultipliers[category] || 1.0;

  for (let i = 0; i < limit; i++) {
    const baseWpm = (100 - i * 2) * multiplier;
    const wpm = Math.max(40, baseWpm + (Math.random() - 0.5) * 5);
    
    performers.push({
      rank: i + 1,
      username: `${category}master${i + 1}`,
      wpm: Math.round(wpm * 10) / 10,
      accuracy: Math.round((95 + Math.random() * 4) * 10) / 10,
      testsCompleted: Math.floor(20 + Math.random() * 100),
      specialization: category
    });
  }

  return performers;
}

/**
 * Get badge for rank
 */
function getBadgeForRank(rank: number): string {
  if (rank === 1) return '👑';
  if (rank <= 3) return '🥇';
  if (rank <= 10) return '🥈';
  if (rank <= 50) return '🥉';
  if (rank <= 100) return '⭐';
  return '';
}

/**
 * Get random country code
 */
function getRandomCountry(): string {
  const countries = ['US', 'CA', 'GB', 'DE', 'FR', 'JP', 'AU', 'IN', 'BR', 'KR'];
  return countries[Math.floor(Math.random() * countries.length)];
}

export default router;
