/**
 * Advanced Sudoku Generator
 * Generates puzzles with specified difficulty and constraints
 */

import {
  SudokuGrid,
  SudokuCell,
  DifficultyLevel,
  GenerateOptions,
  GenerateResult,
  GenerateMetadata,
  SolvingTechnique,
  GenerationError,
} from '@/types';
import { SudokuSolver } from './solver';
import { log } from '@/utils/logger';

export class SudokuGenerator {
  private solver: SudokuSolver;
  private readonly difficultySettings = {
    easy: { minClues: 36, maxClues: 46, techniques: ['NAKED_SINGLE', 'HIDDEN_SINGLE'] },
    medium: {
      minClues: 32,
      maxClues: 35,
      techniques: ['NAKED_SINGLE', 'HIDDEN_SINGLE', 'INTERSECTION_REMOVAL'],
    },
    hard: {
      minClues: 28,
      maxClues: 31,
      techniques: ['NAKED_SINGLE', 'HIDDEN_SINGLE', 'INTERSECTION_REMOVAL', 'NAKED_PAIR'],
    },
    expert: {
      minClues: 25,
      maxClues: 27,
      techniques: [
        'NAKED_SINGLE',
        'HIDDEN_SINGLE',
        'INTERSECTION_REMOVAL',
        'NAKED_PAIR',
        'HIDDEN_PAIR',
      ],
    },
    evil: {
      minClues: 22,
      maxClues: 24,
      techniques: [
        'NAKED_SINGLE',
        'HIDDEN_SINGLE',
        'INTERSECTION_REMOVAL',
        'NAKED_PAIR',
        'HIDDEN_PAIR',
        'X_WING',
      ],
    },
  };

  constructor() {
    this.solver = new SudokuSolver();
  }

  /**
   * Generate a new Sudoku puzzle
   */
  public async generate(
    difficulty: DifficultyLevel,
    options: GenerateOptions = {}
  ): Promise<GenerateResult> {
    const startTime = Date.now();
    const { seed, uniqueSolution = true, symmetry = 'none' } = options;

    // Set random seed if provided
    if (seed !== undefined) {
      this.setSeed(seed);
    }

    log.info('Generating Sudoku puzzle', {
      difficulty,
      seed,
      uniqueSolution,
      symmetry,
    });

    try {
      // Generate a complete solution first
      const solution = this.generateCompleteSolution();

      // Create puzzle by removing clues
      const puzzle = this.createPuzzle(solution, difficulty, symmetry, uniqueSolution);

      // Validate the generated puzzle
      const validation = await this.validateGeneratedPuzzle(puzzle, solution, difficulty);

      if (!validation.valid) {
        throw new GenerationError(`Generated puzzle validation failed: ${validation.reason}`);
      }

      const endTime = Date.now();
      const generationTime = endTime - startTime;

      const metadata: GenerateMetadata = {
        clues: this.countClues(puzzle),
        uniqueSolution,
        generationTime,
        seed: seed || 0,
        requiredTechniques: this.difficultySettings[difficulty].techniques as SolvingTechnique[],
        symmetry,
        complexity: this.calculateComplexity(puzzle),
      };

      log.info('Puzzle generated successfully', {
        difficulty,
        clues: metadata.clues,
        generationTime,
        complexity: metadata.complexity,
      });

      return {
        puzzle,
        solution,
        difficulty,
        metadata,
      };
    } catch (error) {
      log.error('Puzzle generation failed', error, { difficulty, options });
      throw error instanceof GenerationError
        ? error
        : new GenerationError('Puzzle generation failed');
    }
  }

  /**
   * Generate a complete valid Sudoku solution
   */
  private generateCompleteSolution(): SudokuGrid {
    // Start with empty grid
    const grid: SudokuGrid = Array(9)
      .fill(null)
      .map(() => Array(9).fill(0));

    // Fill diagonal boxes first (they don't interfere with each other)
    this.fillDiagonalBoxes(grid);

    // Fill remaining cells using backtracking
    this.fillRemaining(grid, 0, 3);

    return grid;
  }

  /**
   * Fill the three diagonal 3x3 boxes
   */
  private fillDiagonalBoxes(grid: SudokuGrid): void {
    for (let i = 0; i < 9; i += 3) {
      this.fillBox(grid, i, i);
    }
  }

  /**
   * Fill a 3x3 box with random valid numbers
   */
  private fillBox(grid: SudokuGrid, row: number, col: number): void {
    const numbers = this.shuffleArray([1, 2, 3, 4, 5, 6, 7, 8, 9]);
    let index = 0;

    for (let i = 0; i < 3; i++) {
      for (let j = 0; j < 3; j++) {
        grid[row + i][col + j] = numbers[index++] as SudokuCell;
      }
    }
  }

  /**
   * Fill remaining cells using backtracking
   */
  private fillRemaining(grid: SudokuGrid, row: number, col: number): boolean {
    // Move to next row if we've filled current row
    if (col >= 9 && row < 8) {
      return this.fillRemaining(grid, row + 1, 0);
    }

    // If we've filled all rows, we're done
    if (row >= 9 && col >= 9) {
      return true;
    }

    // Skip cells that are already filled (diagonal boxes)
    if (grid[row][col] !== 0) {
      return this.fillRemaining(grid, row, col + 1);
    }

    // Try numbers 1-9 in random order
    const numbers = this.shuffleArray([1, 2, 3, 4, 5, 6, 7, 8, 9]);

    for (const num of numbers) {
      if (this.isSafe(grid, row, col, num as SudokuCell)) {
        grid[row][col] = num as SudokuCell;

        if (this.fillRemaining(grid, row, col + 1)) {
          return true;
        }

        grid[row][col] = 0; // Backtrack
      }
    }

    return false;
  }

  /**
   * Check if placing a number is safe
   */
  private isSafe(grid: SudokuGrid, row: number, col: number, num: SudokuCell): boolean {
    // Check row
    for (let x = 0; x < 9; x++) {
      if (grid[row][x] === num) return false;
    }

    // Check column
    for (let x = 0; x < 9; x++) {
      if (grid[x][col] === num) return false;
    }

    // Check 3x3 box
    const startRow = row - (row % 3);
    const startCol = col - (col % 3);

    for (let i = 0; i < 3; i++) {
      for (let j = 0; j < 3; j++) {
        if (grid[i + startRow][j + startCol] === num) return false;
      }
    }

    return true;
  }

  /**
   * Create puzzle by removing clues from complete solution
   */
  private createPuzzle(
    solution: SudokuGrid,
    difficulty: DifficultyLevel,
    symmetry: string,
    uniqueSolution: boolean
  ): SudokuGrid {
    const puzzle = solution.map(row => [...row]);
    const settings = this.difficultySettings[difficulty];
    const targetClues = this.randomBetween(settings.minClues, settings.maxClues);

    // Get positions to remove based on symmetry
    const positions = this.getRemovalPositions(symmetry);

    let cluesRemoved = 0;
    const maxClues = 81;

    for (const pos of positions) {
      if (maxClues - cluesRemoved <= targetClues) break;

      const { row, col } = pos;
      const originalValue = puzzle[row][col];

      // Try removing this clue
      puzzle[row][col] = 0;

      // Check if puzzle still has unique solution (if required)
      if (uniqueSolution && !this.hasUniqueSolution(puzzle)) {
        puzzle[row][col] = originalValue; // Restore
        continue;
      }

      cluesRemoved++;

      // If symmetry is enabled, remove symmetric position too
      if (symmetry !== 'none') {
        const symPos = this.getSymmetricPosition(row, col, symmetry);
        if (symPos && puzzle[symPos.row][symPos.col] !== 0) {
          puzzle[symPos.row][symPos.col] = 0;
          cluesRemoved++;
        }
      }
    }

    return puzzle;
  }

  /**
   * Get positions for clue removal based on symmetry
   */
  private getRemovalPositions(symmetry: string): Array<{ row: number; col: number }> {
    const positions: Array<{ row: number; col: number }> = [];

    for (let row = 0; row < 9; row++) {
      for (let col = 0; col < 9; col++) {
        positions.push({ row, col });
      }
    }

    return this.shuffleArray(positions);
  }

  /**
   * Get symmetric position
   */
  private getSymmetricPosition(
    row: number,
    col: number,
    symmetry: string
  ): { row: number; col: number } | null {
    switch (symmetry) {
      case 'horizontal':
        return { row, col: 8 - col };
      case 'vertical':
        return { row: 8 - row, col };
      case 'rotational':
        return { row: 8 - row, col: 8 - col };
      case 'diagonal':
        return { row: col, col: row };
      default:
        return null;
    }
  }

  /**
   * Check if puzzle has unique solution
   */
  private async hasUniqueSolution(puzzle: SudokuGrid): Promise<boolean> {
    // This is a simplified check - in a full implementation,
    // you would need to count all possible solutions
    try {
      const result = await this.solver.solve(puzzle, { method: 'backtrack' });
      return result.solved;
    } catch {
      return false;
    }
  }

  /**
   * Validate generated puzzle
   */
  private async validateGeneratedPuzzle(
    puzzle: SudokuGrid,
    solution: SudokuGrid,
    difficulty: DifficultyLevel
  ): Promise<{ valid: boolean; reason?: string }> {
    const clueCount = this.countClues(puzzle);
    const settings = this.difficultySettings[difficulty];

    // Check clue count
    if (clueCount < settings.minClues || clueCount > settings.maxClues) {
      return {
        valid: false,
        reason: `Clue count ${clueCount} not in range ${settings.minClues}-${settings.maxClues}`,
      };
    }

    // Check if puzzle is solvable
    try {
      const result = await this.solver.solve(puzzle);
      if (!result.solved) {
        return { valid: false, reason: 'Puzzle is not solvable' };
      }

      // Check if solution matches
      if (!this.gridsEqual(result.solution!, solution)) {
        return { valid: false, reason: 'Solution does not match expected' };
      }
    } catch (error) {
      return {
        valid: false,
        reason: `Solving failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }

    return { valid: true };
  }

  /**
   * Utility methods
   */
  private shuffleArray<T>(array: T[]): T[] {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }

  private randomBetween(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  private countClues(grid: SudokuGrid): number {
    return grid.flat().filter(cell => cell !== 0).length;
  }

  private calculateComplexity(puzzle: SudokuGrid): number {
    // Simplified complexity calculation
    const clues = this.countClues(puzzle);
    return Math.round(((81 - clues) / 81) * 100);
  }

  private gridsEqual(grid1: SudokuGrid, grid2: SudokuGrid): boolean {
    for (let row = 0; row < 9; row++) {
      for (let col = 0; col < 9; col++) {
        if (grid1[row][col] !== grid2[row][col]) {
          return false;
        }
      }
    }
    return true;
  }

  private setSeed(seed: number): void {
    // Simple seed implementation - in production, use a proper PRNG
    Math.random = () => {
      seed = (seed * 9301 + 49297) % 233280;
      return seed / 233280;
    };
  }
}
