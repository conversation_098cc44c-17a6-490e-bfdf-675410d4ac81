const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3003;

// Security and performance middleware
app.use(helmet());
app.use(compression());
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.static('public'));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});
app.use('/api/', limiter);

// In-memory storage (in production, use a proper database)
let typingTests = [];
let leaderboard = [];
let analytics = {
  totalTests: 0,
  averageWPM: 0,
  averageAccuracy: 0,
  totalCharacters: 0,
  totalTime: 0
};

// Text samples for typing tests
const textSamples = [
  // Easy - Pangram
  {
    id: 1,
    difficulty: 'easy',
    text: 'The quick brown fox jumps over the lazy dog.',
    category: 'pangram'
  },
  {
    id: 2,
    difficulty: 'easy',
    text: 'Pack my box with five dozen liquor jugs.',
    category: 'pangram'
  },

  // Easy - Programming
  {
    id: 3,
    difficulty: 'easy',
    text: 'HTML and CSS are the building blocks of web pages.',
    category: 'programming'
  },
  {
    id: 4,
    difficulty: 'easy',
    text: 'Variables store data values in programming languages.',
    category: 'programming'
  },

  // Easy - Technical
  {
    id: 5,
    difficulty: 'easy',
    text: 'Computers process information using binary code.',
    category: 'technical'
  },
  {
    id: 6,
    difficulty: 'easy',
    text: 'The internet connects millions of devices worldwide.',
    category: 'technical'
  },

  // Easy - Nature
  {
    id: 7,
    difficulty: 'easy',
    text: 'The sun rises in the east and sets in the west.',
    category: 'nature'
  },
  {
    id: 8,
    difficulty: 'easy',
    text: 'Birds sing beautiful songs in the morning light.',
    category: 'nature'
  },

  // Easy - Technology
  {
    id: 9,
    difficulty: 'easy',
    text: 'Smartphones have changed how we communicate daily.',
    category: 'technology'
  },
  {
    id: 10,
    difficulty: 'easy',
    text: 'Social media platforms connect people across the globe.',
    category: 'technology'
  },

  // Medium - Pangram
  {
    id: 11,
    difficulty: 'medium',
    text: 'Waltz, bad nymph, for quick jigs vex. The five boxing wizards jump quickly.',
    category: 'pangram'
  },
  {
    id: 12,
    difficulty: 'medium',
    text: 'Sphinx of black quartz, judge my vow. How vexingly quick daft zebras jump!',
    category: 'pangram'
  },

  // Medium - Programming
  {
    id: 13,
    difficulty: 'medium',
    text: 'JavaScript is a versatile programming language that enables interactive web development.',
    category: 'programming'
  },
  {
    id: 14,
    difficulty: 'medium',
    text: 'Object-oriented programming uses classes and objects to organize code structure.',
    category: 'programming'
  },

  // Medium - Technical
  {
    id: 15,
    difficulty: 'medium',
    text: 'Database management systems organize and store large amounts of structured data.',
    category: 'technical'
  },
  {
    id: 16,
    difficulty: 'medium',
    text: 'Cloud computing provides scalable resources through internet-based services.',
    category: 'technical'
  },

  // Medium - Nature
  {
    id: 17,
    difficulty: 'medium',
    text: 'Photosynthesis converts sunlight into chemical energy within plant chloroplasts.',
    category: 'nature'
  },
  {
    id: 18,
    difficulty: 'medium',
    text: 'Ecosystems maintain balance through complex interactions between organisms.',
    category: 'nature'
  },

  // Medium - Technology
  {
    id: 19,
    difficulty: 'medium',
    text: 'Machine learning algorithms analyze data to identify patterns and make predictions.',
    category: 'technology'
  },
  {
    id: 20,
    difficulty: 'medium',
    text: 'Artificial intelligence systems can process information faster than humans.',
    category: 'technology'
  },

  // Hard - Pangram
  {
    id: 21,
    difficulty: 'hard',
    text: 'The five boxing wizards jump quickly. Sphinx of black quartz, judge my vow!',
    category: 'pangram'
  },
  {
    id: 22,
    difficulty: 'hard',
    text: 'Jackdaws love my big sphinx of quartz. The quick brown fox jumps over lazy dogs.',
    category: 'pangram'
  },

  // Hard - Programming
  {
    id: 23,
    difficulty: 'hard',
    text: 'Asynchronous programming involves callbacks, promises, and async/await syntax patterns.',
    category: 'programming'
  },
  {
    id: 24,
    difficulty: 'hard',
    text: 'Functional programming emphasizes immutability, pure functions, and higher-order abstractions.',
    category: 'programming'
  },

  // Hard - Technical
  {
    id: 25,
    difficulty: 'hard',
    text: 'Distributed systems require consensus algorithms to maintain consistency across nodes.',
    category: 'technical'
  },
  {
    id: 26,
    difficulty: 'hard',
    text: 'Cryptographic hash functions provide data integrity through one-way mathematical operations.',
    category: 'technical'
  },

  // Hard - Nature
  {
    id: 27,
    difficulty: 'hard',
    text: 'Mitochondrial respiration generates ATP through oxidative phosphorylation processes.',
    category: 'nature'
  },
  {
    id: 28,
    difficulty: 'hard',
    text: 'Evolutionary adaptation occurs through natural selection and genetic variation mechanisms.',
    category: 'nature'
  },

  // Hard - Technology
  {
    id: 29,
    difficulty: 'hard',
    text: 'Quantum computing leverages superposition and entanglement for exponential speedup.',
    category: 'technology'
  },
  {
    id: 30,
    difficulty: 'hard',
    text: 'Blockchain technology ensures immutable transaction records through cryptographic hashing.',
    category: 'technology'
  }
];

// Utility functions
function calculateWPM(characters, timeInSeconds) {
  const words = characters / 5; // Standard: 5 characters = 1 word
  const minutes = timeInSeconds / 60;
  return Math.round(words / minutes);
}

function calculateAccuracy(correctChars, totalChars) {
  return Math.round((correctChars / totalChars) * 100);
}

function updateAnalytics(testResult) {
  analytics.totalTests++;
  analytics.totalCharacters += testResult.totalCharacters;
  analytics.totalTime += testResult.timeInSeconds;
  
  // Recalculate averages
  const allWPMs = typingTests.map(test => test.wpm);
  const allAccuracies = typingTests.map(test => test.accuracy);
  
  analytics.averageWPM = Math.round(allWPMs.reduce((a, b) => a + b, 0) / allWPMs.length);
  analytics.averageAccuracy = Math.round(allAccuracies.reduce((a, b) => a + b, 0) / allAccuracies.length);
}

function updateLeaderboard(testResult) {
  leaderboard.push({
    id: testResult.id,
    username: testResult.username,
    wpm: testResult.wpm,
    accuracy: testResult.accuracy,
    difficulty: testResult.difficulty,
    timestamp: testResult.timestamp
  });
  
  // Sort by WPM (descending), then by accuracy (descending)
  leaderboard.sort((a, b) => {
    if (b.wpm !== a.wpm) return b.wpm - a.wpm;
    return b.accuracy - a.accuracy;
  });
  
  // Keep only top 100
  leaderboard = leaderboard.slice(0, 100);
}

// API Routes

// Get available text samples
app.get('/api/texts', (req, res) => {
  const { difficulty, category } = req.query;
  
  let filteredTexts = textSamples;
  
  if (difficulty) {
    filteredTexts = filteredTexts.filter(text => text.difficulty === difficulty);
  }
  
  if (category) {
    filteredTexts = filteredTexts.filter(text => text.category === category);
  }
  
  res.json({
    success: true,
    data: filteredTexts,
    meta: {
      total: filteredTexts.length,
      difficulties: ['easy', 'medium', 'hard'],
      categories: ['pangram', 'programming', 'technical', 'nature', 'technology']
    }
  });
});

// Get specific text sample
app.get('/api/texts/:id', (req, res) => {
  const textId = parseInt(req.params.id);
  const text = textSamples.find(t => t.id === textId);
  
  if (!text) {
    return res.status(404).json({
      success: false,
      error: 'Text sample not found'
    });
  }
  
  res.json({
    success: true,
    data: text
  });
});

// Submit typing test result
app.post('/api/submit', (req, res) => {
  const {
    username,
    textId,
    typedText,
    timeInSeconds,
    correctCharacters,
    totalCharacters,
    errors
  } = req.body;
  
  // Validation
  if (!username || !textId || !typedText || !timeInSeconds || correctCharacters === undefined || !totalCharacters) {
    return res.status(400).json({
      success: false,
      error: 'Missing required fields'
    });
  }
  
  const originalText = textSamples.find(t => t.id === textId);
  if (!originalText) {
    return res.status(400).json({
      success: false,
      error: 'Invalid text ID'
    });
  }
  
  // Calculate metrics
  const wpm = calculateWPM(correctCharacters, timeInSeconds);
  const accuracy = calculateAccuracy(correctCharacters, totalCharacters);
  
  const testResult = {
    id: Date.now(),
    username: username.trim(),
    textId,
    difficulty: originalText.difficulty,
    wpm,
    accuracy,
    timeInSeconds,
    correctCharacters,
    totalCharacters,
    errors: errors || [],
    timestamp: new Date().toISOString()
  };
  
  // Store result
  typingTests.push(testResult);
  updateAnalytics(testResult);
  updateLeaderboard(testResult);
  
  res.json({
    success: true,
    data: testResult,
    message: 'Test result submitted successfully'
  });
});

// Get leaderboard
app.get('/api/leaderboard', (req, res) => {
  const { limit = 10, difficulty } = req.query;
  
  let filteredLeaderboard = leaderboard;
  
  if (difficulty) {
    filteredLeaderboard = leaderboard.filter(entry => entry.difficulty === difficulty);
  }
  
  const limitedLeaderboard = filteredLeaderboard.slice(0, parseInt(limit));
  
  res.json({
    success: true,
    data: limitedLeaderboard,
    meta: {
      total: filteredLeaderboard.length,
      showing: limitedLeaderboard.length
    }
  });
});

// Get analytics
app.get('/api/analytics', (req, res) => {
  const difficultyStats = {};
  
  ['easy', 'medium', 'hard'].forEach(difficulty => {
    const difficultyTests = typingTests.filter(test => test.difficulty === difficulty);
    if (difficultyTests.length > 0) {
      difficultyStats[difficulty] = {
        totalTests: difficultyTests.length,
        averageWPM: Math.round(difficultyTests.reduce((sum, test) => sum + test.wpm, 0) / difficultyTests.length),
        averageAccuracy: Math.round(difficultyTests.reduce((sum, test) => sum + test.accuracy, 0) / difficultyTests.length)
      };
    }
  });
  
  res.json({
    success: true,
    data: {
      global: analytics,
      byDifficulty: difficultyStats,
      recentTests: typingTests.slice(-10).reverse()
    }
  });
});

// Get user statistics
app.get('/api/user/:username/stats', (req, res) => {
  const username = req.params.username;
  const userTests = typingTests.filter(test => test.username.toLowerCase() === username.toLowerCase());
  
  if (userTests.length === 0) {
    return res.status(404).json({
      success: false,
      error: 'No tests found for this user'
    });
  }
  
  const stats = {
    totalTests: userTests.length,
    averageWPM: Math.round(userTests.reduce((sum, test) => sum + test.wpm, 0) / userTests.length),
    averageAccuracy: Math.round(userTests.reduce((sum, test) => sum + test.accuracy, 0) / userTests.length),
    bestWPM: Math.max(...userTests.map(test => test.wpm)),
    bestAccuracy: Math.max(...userTests.map(test => test.accuracy)),
    recentTests: userTests.slice(-5).reverse()
  };
  
  res.json({
    success: true,
    data: stats
  });
});

// API documentation endpoint
app.get('/api/info', (req, res) => {
  res.json({
    name: 'Typing Analytics API',
    version: '1.0.0',
    description: 'Professional typing analytics API with performance tracking and leaderboards',
    endpoints: {
      'GET /api/texts': 'Get available text samples',
      'GET /api/texts/:id': 'Get specific text sample',
      'POST /api/submit': 'Submit typing test result',
      'GET /api/leaderboard': 'Get leaderboard',
      'GET /api/analytics': 'Get global analytics',
      'GET /api/user/:username/stats': 'Get user statistics'
    },
    author: 'Zayden Sharp',
    documentation: '/demo.html'
  });
});

// Serve demo page
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'demo.html'));
});

// Error handling
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    error: 'Internal server error'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found'
  });
});

app.listen(PORT, () => {
  console.log(`🚀 Typing Analytics API running on port ${PORT}`);
  console.log(`📊 Demo available at http://localhost:${PORT}`);
});

module.exports = app;
