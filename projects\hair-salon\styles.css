/* A Elements Hair Salon - Premium Styling */

:root {
    --primary-pink: #f4c2c2;
    --light-pink: #fdf2f8;
    --dark-pink: #ec4899;
    --accent-pink: #f9a8d4;
    --text-dark: #1f2937;
    --text-light: #6b7280;
    --white: #ffffff;
    --cream: #fef7f0;
    --gold: #d4af37;
    --shadow: rgba(0, 0, 0, 0.1);
    --shadow-hover: rgba(0, 0, 0, 0.15);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--white);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4 {
    font-family: 'Playfair Display', serif;
    font-weight: 600;
    line-height: 1.2;
}

h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
}

h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    text-align: center;
}

h3 {
    font-size: 1.8rem;
    margin-bottom: 0.8rem;
}

h4 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

.highlight {
    color: var(--dark-pink);
    font-style: italic;
}

.section-subtitle {
    text-align: center;
    font-size: 1.1rem;
    color: var(--text-light);
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    box-shadow: 0 2px 20px var(--shadow);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-logo h1 {
    font-size: 1.8rem;
    color: var(--dark-pink);
    margin: 0;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-menu a:hover {
    color: var(--dark-pink);
}

.book-btn {
    background: var(--dark-pink);
    color: var(--white);
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.book-btn:hover {
    background: var(--accent-pink);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px var(--shadow-hover);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--light-pink) 0%, var(--cream) 100%);
    padding: 120px 0 80px;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.hero-text h1 {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.hero-subtitle {
    font-size: 1.3rem;
    color: var(--text-light);
    margin-bottom: 1rem;
}

.hero-description {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    color: var(--text-dark);
}

.hero-features {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--white);
    padding: 1rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px var(--shadow);
}

.feature-icon {
    font-size: 1.5rem;
}

.hero-image {
    display: flex;
    justify-content: center;
}

.stylist-photo {
    width: 100%;
    max-width: 400px;
    border-radius: 20px;
    box-shadow: 0 20px 40px var(--shadow-hover);
    object-fit: cover;
    height: 500px;
}

/* About Section */
.about-section {
    padding: 80px 0;
    background: var(--white);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    margin-top: 3rem;
}

.about-text p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    color: var(--text-light);
}

.expertise-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.expertise-item {
    background: var(--light-pink);
    padding: 1.5rem;
    border-radius: 15px;
    text-align: center;
}

.expertise-item h4 {
    color: var(--dark-pink);
    margin-bottom: 0.5rem;
}

.expertise-item p {
    font-size: 0.9rem;
    color: var(--text-light);
}

.team-photo {
    width: 100%;
    border-radius: 20px;
    box-shadow: 0 20px 40px var(--shadow-hover);
    object-fit: cover;
    height: 400px;
}

/* Services Section */
.services-section {
    padding: 80px 0;
    background: var(--light-pink);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.service-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px var(--shadow);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    text-align: center;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px var(--shadow-hover);
}

.service-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.service-card h3 {
    color: var(--dark-pink);
    margin-bottom: 1rem;
}

.service-card p {
    color: var(--text-light);
    margin-bottom: 1.5rem;
}

.service-features {
    list-style: none;
    margin-bottom: 2rem;
}

.service-features li {
    padding: 0.5rem 0;
    color: var(--text-light);
    border-bottom: 1px solid var(--light-pink);
}

.service-features li:last-child {
    border-bottom: none;
}

.service-btn {
    background: var(--dark-pink);
    color: var(--white);
    border: none;
    padding: 1rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.service-btn:hover {
    background: var(--accent-pink);
    transform: translateY(-2px);
}

/* Buttons */
.btn-primary, .btn-secondary {
    padding: 1rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    font-size: 1rem;
}

.btn-primary {
    background: var(--dark-pink);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--accent-pink);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px var(--shadow-hover);
}

.btn-secondary {
    background: transparent;
    color: var(--dark-pink);
    border: 2px solid var(--dark-pink);
}

.btn-secondary:hover {
    background: var(--dark-pink);
    color: var(--white);
    transform: translateY(-2px);
}

/* Sustainability Section */
.sustainability-section {
    padding: 80px 0;
    background: var(--white);
}

.sustainability-hero {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    margin-top: 3rem;
}

.elements-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin: 2rem 0;
}

.element-item {
    background: var(--light-pink);
    padding: 1.5rem;
    border-radius: 15px;
    text-align: center;
}

.element-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.element-item h4 {
    color: var(--dark-pink);
    margin-bottom: 0.5rem;
}

.sustainability-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.salon-photo {
    width: 100%;
    border-radius: 20px;
    box-shadow: 0 20px 40px var(--shadow-hover);
    object-fit: cover;
    height: 400px;
}

/* Specialties Section */
.specialties-section {
    padding: 80px 0;
    background: var(--light-pink);
}

.specialty-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    margin-top: 3rem;
}

.transformation-photo {
    width: 100%;
    border-radius: 20px;
    box-shadow: 0 20px 40px var(--shadow-hover);
    object-fit: cover;
    height: 400px;
}

.certifications {
    margin: 2rem 0;
}

.cert-item {
    background: var(--white);
    padding: 1.5rem;
    border-radius: 15px;
    margin-bottom: 1rem;
    box-shadow: 0 5px 15px var(--shadow);
}

.cert-item h4 {
    color: var(--dark-pink);
    margin-bottom: 0.5rem;
}

/* Process Section */
.process-section {
    padding: 80px 0;
    background: var(--white);
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.step {
    text-align: center;
    padding: 2rem;
    background: var(--light-pink);
    border-radius: 20px;
    position: relative;
}

.step-number {
    background: var(--dark-pink);
    color: var(--white);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    margin: 0 auto 1rem;
}

.step h3 {
    color: var(--dark-pink);
    margin-bottom: 1rem;
}

/* Contact Section */
.contact-section {
    padding: 80px 0;
    background: var(--light-pink);
}

.contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.contact-item {
    background: var(--white);
    padding: 2rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 30px var(--shadow);
}

.contact-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.contact-item h4 {
    color: var(--dark-pink);
    margin-bottom: 0.5rem;
}

.booking-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    margin-top: 4rem;
    background: var(--white);
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 20px 40px var(--shadow-hover);
}

.booking-features {
    margin: 2rem 0;
}

.booking-feature {
    margin-bottom: 1.5rem;
}

.booking-feature h4 {
    color: var(--dark-pink);
    margin-bottom: 0.5rem;
}

.form-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.styling-photo {
    width: 100%;
    border-radius: 15px;
    object-fit: cover;
    height: 300px;
}

/* Salon Visit Section */
.salon-visit-section {
    padding: 80px 0;
    background: var(--white);
}

.visit-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    margin-top: 3rem;
}

.location-features {
    margin: 2rem 0;
}

.location-feature {
    background: var(--light-pink);
    padding: 1rem;
    border-radius: 10px;
    margin-bottom: 1rem;
}

.location-feature h4 {
    color: var(--dark-pink);
    margin-bottom: 0.5rem;
}

.salon-exterior {
    width: 100%;
    border-radius: 20px;
    box-shadow: 0 20px 40px var(--shadow-hover);
    object-fit: cover;
    height: 400px;
}

/* FAQ Section */
.faq-section {
    padding: 80px 0;
    background: var(--light-pink);
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.faq-item {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px var(--shadow);
}

.faq-item h4 {
    color: var(--dark-pink);
    margin-bottom: 1rem;
}

/* CTA Section */
.cta-section {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--dark-pink) 0%, var(--accent-pink) 100%);
    text-align: center;
    color: var(--white);
}

.cta-section h2 {
    color: var(--white);
    margin-bottom: 1rem;
}

.cta-section p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.cta-buttons .btn-primary {
    background: var(--white);
    color: var(--dark-pink);
}

.cta-buttons .btn-secondary {
    border-color: var(--white);
    color: var(--white);
}

.cta-buttons .btn-secondary:hover {
    background: var(--white);
    color: var(--dark-pink);
}

/* Footer */
.footer {
    background: var(--text-dark);
    color: var(--white);
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    color: var(--accent-pink);
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    padding: 0.3rem 0;
    color: var(--text-light);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #374151;
    color: var(--text-light);
}

/* Services Page Styles */
.services-hero {
    background: linear-gradient(135deg, var(--light-pink) 0%, var(--cream) 100%);
    padding: 120px 0 80px;
    text-align: center;
}

.services-hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-top: 3rem;
}

.stat-item {
    text-align: center;
}

.stat-item h3 {
    font-size: 2.5rem;
    color: var(--dark-pink);
    margin-bottom: 0.5rem;
}

.stat-item p {
    color: var(--text-light);
    font-weight: 500;
}

/* Detailed Services */
.detailed-services {
    padding: 80px 0;
}

.service-detail {
    margin-bottom: 6rem;
}

.service-detail-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.service-detail.reverse .service-detail-content {
    direction: rtl;
}

.service-detail.reverse .service-detail-text {
    direction: ltr;
}

.service-detail h2 {
    text-align: left;
    margin-bottom: 1rem;
}

.service-tagline {
    font-size: 1.2rem;
    color: var(--dark-pink);
    font-style: italic;
    margin-bottom: 1.5rem;
}

.service-options {
    margin: 2rem 0;
}

.option-item {
    background: var(--light-pink);
    padding: 1.5rem;
    border-radius: 15px;
    margin-bottom: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.option-item h4 {
    color: var(--dark-pink);
    margin-bottom: 0.5rem;
}

.option-item p {
    color: var(--text-light);
    margin: 0;
}

.price {
    background: var(--dark-pink);
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
}

.service-features {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px var(--shadow);
    margin: 2rem 0;
}

.service-features h4 {
    color: var(--dark-pink);
    margin-bottom: 1rem;
}

.service-features ul {
    list-style: none;
    padding: 0;
}

.service-features li {
    padding: 0.5rem 0;
    color: var(--text-light);
    border-bottom: 1px solid var(--light-pink);
}

.service-features li:before {
    content: "✨ ";
    margin-right: 0.5rem;
}

.service-features li:last-child {
    border-bottom: none;
}

.service-photo {
    width: 100%;
    border-radius: 20px;
    box-shadow: 0 20px 40px var(--shadow-hover);
    object-fit: cover;
    height: 400px;
}

/* Color Correction Process */
.correction-process {
    margin: 2rem 0;
}

.process-steps-mini {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-top: 1rem;
}

.mini-step {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: var(--light-pink);
    padding: 1rem;
    border-radius: 10px;
}

.step-num {
    background: var(--dark-pink);
    color: var(--white);
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
}

.mini-step p {
    margin: 0;
    font-size: 0.9rem;
    color: var(--text-dark);
}

/* Color Categories */
.color-categories {
    margin: 2rem 0;
}

.category-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-top: 1rem;
}

.category-item {
    background: var(--light-pink);
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
}

.category-item h5 {
    color: var(--dark-pink);
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.category-item p {
    color: var(--text-light);
    font-size: 0.9rem;
    margin: 0;
}

/* Additional Services */
.additional-services {
    padding: 80px 0;
    background: var(--light-pink);
}

.additional-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.additional-item {
    background: var(--white);
    padding: 2rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 30px var(--shadow);
    transition: transform 0.3s ease;
}

.additional-item:hover {
    transform: translateY(-5px);
}

.additional-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.additional-item h4 {
    color: var(--dark-pink);
    margin-bottom: 1rem;
}

.additional-item p {
    color: var(--text-light);
    margin-bottom: 1.5rem;
}

.additional-item .price {
    display: inline-block;
    margin-top: 1rem;
}

/* Booking CTA */
.booking-cta {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--dark-pink) 0%, var(--accent-pink) 100%);
    text-align: center;
    color: var(--white);
}

.booking-cta h2 {
    color: var(--white);
    margin-bottom: 1rem;
}

.booking-cta p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

/* Active nav link */
.nav-menu a.active {
    color: var(--dark-pink);
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content,
    .about-content,
    .sustainability-hero,
    .specialty-content,
    .booking-section,
    .visit-content,
    .service-detail-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .service-detail.reverse .service-detail-content {
        direction: ltr;
    }

    .hero-text h1,
    .services-hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-features,
    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .nav-menu {
        display: none;
    }

    .services-grid,
    .process-steps,
    .additional-grid {
        grid-template-columns: 1fr;
    }

    .expertise-grid,
    .elements-grid,
    .process-steps-mini,
    .category-grid {
        grid-template-columns: 1fr;
    }

    .sustainability-buttons,
    .form-buttons,
    .cta-buttons {
        flex-direction: column;
    }

    .contact-info {
        grid-template-columns: repeat(2, 1fr);
    }

    .faq-grid {
        grid-template-columns: 1fr;
    }

    .option-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
}
