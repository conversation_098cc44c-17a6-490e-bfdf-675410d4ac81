<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Real Website Portfolio Gallery - Professional Web Development</title>
  <meta name="description" content="Professional portfolio gallery showcasing real business websites including hair salon, plumbing, painting, barber shop, and e-commerce projects">
  <meta name="keywords" content="website portfolio, business websites, web development, professional websites, real projects">
  <meta name="author" content="Zayden Sharp">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://zaydenjs.github.io/PortFolio-2025/projects/project-gallery/">
  <meta property="og:title" content="Real Website Portfolio Gallery - Professional Web Development">
  <meta property="og:description" content="Professional portfolio showcasing real business websites and web development projects">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:url" content="https://zaydenjs.github.io/PortFolio-2025/projects/project-gallery/">
  <meta property="twitter:title" content="Real Website Portfolio Gallery - Professional Web Development">
  <meta property="twitter:description" content="Professional portfolio showcasing real business websites and web development projects">

  <link rel="icon" type="image/svg+xml" href="../../assets/favicon.svg">
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="container">
    <!-- Back Navigation -->
    <nav class="back-nav">
      <a href="../../index.html" class="back-button">
        ← Back to Portfolio
      </a>
      <button
        class="theme-toggle"
        aria-label="Toggle dark/light theme"
        title="Toggle theme"
      >
        <span class="theme-icon" aria-hidden="true">🌙</span>
      </button>
    </nav>

    <!-- Header -->
    <header class="header">
      <h1 class="title">REAL WEBSITE PORTFOLIO</h1>
      <p class="subtitle">Professional business websites showcasing real-world web development projects</p>
      <div class="gallery-stats">
        <div class="stat">
          <span class="stat-number">6</span>
          <span class="stat-label">Real Websites</span>
        </div>
        <div class="stat">
          <span class="stat-number">15+</span>
          <span class="stat-label">Screenshots</span>
        </div>
        <div class="stat">
          <span class="stat-number">100%</span>
          <span class="stat-label">Professional</span>
        </div>
      </div>
    </header>

    <!-- Filter Controls -->
    <div class="filter-controls">
      <button class="filter-btn active" data-filter="all">All Projects</button>
    </div>

    <!-- Gallery Grid -->
    <main class="gallery-grid" id="galleryGrid">
      <!-- Project 1: Hair Salon Website -->
      <article class="project-item" data-category="business design" data-project="hair-salon">
        <div class="project-preview">
          <img src="../../Website%20Resume/Photo%20Previews/4%20elements.png" alt="Hair Salon Website Preview" class="preview-image">
          <div class="project-overlay">
            <div class="project-info">
              <h3 class="project-title">A Elements Hair - Premium Salon</h3>
              <p class="project-description">Professional hair salon website with booking system, portfolio gallery, and sustainable practices</p>
              <div class="project-tags">
                <span class="tag">Business Website</span>
                <span class="tag">Booking System</span>
                <span class="tag">Portfolio Gallery</span>
              </div>
            </div>
            <div class="project-actions">
              <a href="https://4elementshair.netlify.app" target="_blank" class="btn btn-primary">Live Website</a>
              <a href="https://github.com/ZaydenSharp/hair-salon" class="btn btn-secondary" target="_blank">GitHub</a>
            </div>
          </div>
        </div>
      </article>

      <!-- Project 2: Plumbing Website -->
      <article class="project-item" data-category="business design" data-project="plumbing-website">
        <div class="project-preview">
          <img src="../../Website%20Resume/Photo%20Previews/Allstarsplumbing.png" alt="Plumbing Website Preview" class="preview-image">
          <div class="project-overlay">
            <div class="project-info">
              <h3 class="project-title">Professional Plumbing Services</h3>
              <p class="project-description">Complete plumbing services website with service listings and contact information</p>
              <div class="project-tags">
                <span class="tag">Business Website</span>
                <span class="tag">Service Listings</span>
                <span class="tag">Professional</span>
              </div>
            </div>
            <div class="project-actions">
              <a href="https://allstarsplumbing.netlify.app" target="_blank" class="btn btn-primary">Live Website</a>
              <a href="https://github.com/ZaydenSharp/plumbing-website" class="btn btn-secondary" target="_blank">GitHub</a>
            </div>
          </div>
        </div>
      </article>

      <!-- Project 3: Painting Website -->
      <article class="project-item" data-category="business design" data-project="painting-website">
        <div class="project-preview">
          <img src="../../Website%20Resume/Photo%20Previews/Cocky's%20painting.png" alt="Painting Website Preview" class="preview-image">
          <div class="project-overlay">
            <div class="project-info">
              <h3 class="project-title">Professional Painting Services</h3>
              <p class="project-description">Complete painting contractor website with portfolio gallery and service details</p>
              <div class="project-tags">
                <span class="tag">Portfolio Gallery</span>
                <span class="tag">Service Details</span>
                <span class="tag">Contractor</span>
              </div>
            </div>
            <div class="project-actions">
              <a href="https://cockyspainting.netlify.app" target="_blank" class="btn btn-primary">Live Website</a>
              <a href="https://github.com/ZaydenSharp/painting-website" class="btn btn-secondary" target="_blank">GitHub</a>
            </div>
          </div>
        </div>
      </article>

      <!-- Project 4: Qara Website -->
      <article class="project-item" data-category="business design" data-project="qara-website">
        <div class="project-preview">
          <img src="../../Website%20Resume/Photo%20Previews/QARA.png" alt="Qara Website Preview" class="preview-image">
          <div class="project-overlay">
            <div class="project-info">
              <h3 class="project-title">Qara - Modern Business Website</h3>
              <p class="project-description">Sleek single-page business website with modern design and professional layout</p>
              <div class="project-tags">
                <span class="tag">Single Page</span>
                <span class="tag">Modern Design</span>
                <span class="tag">Professional</span>
              </div>
            </div>
            <div class="project-actions">
              <a href="https://qaraindustries.netlify.app" target="_blank" class="btn btn-primary">Live Website</a>
              <a href="https://github.com/ZaydenSharp/qara-website" class="btn btn-secondary" target="_blank">GitHub</a>
            </div>
          </div>
        </div>
      </article>

      <!-- Project 5: Chinese Shop -->
      <article class="project-item" data-category="business ecommerce" data-project="chinese-shop">
        <div class="project-preview">
          <img src="../../Website%20Resume/Photo%20Previews/MR%20SO.png" alt="Chinese Restaurant Website Preview" class="preview-image">
          <div class="project-overlay">
            <div class="project-info">
              <h3 class="project-title">Chinese Shop - E-commerce Website</h3>
              <p class="project-description">Complete e-commerce website for Chinese products with modern design</p>
              <div class="project-tags">
                <span class="tag">E-commerce</span>
                <span class="tag">Product Showcase</span>
                <span class="tag">Modern Design</span>
              </div>
            </div>
            <div class="project-actions">
              <a href="https://mrsochineserestaurant.netlify.app" target="_blank" class="btn btn-primary">Live Website</a>
              <a href="https://github.com/ZaydenSharp/chinese-shop" class="btn btn-secondary" target="_blank">GitHub</a>
            </div>
          </div>
        </div>
      </article>

      <!-- Project 6: Barber Website -->
      <article class="project-item" data-category="business design" data-project="barber-website">
        <div class="project-preview">
          <img src="../../Website%20Resume/Photo%20Previews/Enlighten%20.png" alt="Barber Website Preview" class="preview-image">
          <div class="project-overlay">
            <div class="project-info">
              <h3 class="project-title">Professional Barber Services</h3>
              <p class="project-description">Modern barber shop website with service listings and professional styling</p>
              <div class="project-tags">
                <span class="tag">Service Listings</span>
                <span class="tag">Professional</span>
                <span class="tag">Modern Styling</span>
              </div>
            </div>
            <div class="project-actions">
              <a href="https://enlightenhair.netlify.app" target="_blank" class="btn btn-primary">Live Website</a>
              <a href="https://github.com/ZaydenSharp/barber-website" class="btn btn-secondary" target="_blank">GitHub</a>
            </div>
          </div>
        </div>
      </article>
    </main>

  </div>

  <script>
    // Theme toggle functionality
    document.addEventListener('DOMContentLoaded', function() {
      const themeToggle = document.querySelector('.theme-toggle');
      const themeIcon = document.querySelector('.theme-icon');
      const body = document.body;

      // Check for saved theme preference or default to 'light'
      const currentTheme = localStorage.getItem('theme') || 'light';
      body.setAttribute('data-theme', currentTheme);

      // Update icon based on current theme
      if (currentTheme === 'dark') {
        themeIcon.textContent = '☀️';
      } else {
        themeIcon.textContent = '🌙';
      }

      // Theme toggle event listener
      themeToggle.addEventListener('click', function() {
        const currentTheme = body.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

        body.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);

        // Update icon
        if (newTheme === 'dark') {
          themeIcon.textContent = '☀️';
        } else {
          themeIcon.textContent = '🌙';
        }
      });

      // Debug script to check image loading
      const images = document.querySelectorAll('.preview-image');
      console.log('Found', images.length, 'images');

      images.forEach((img, index) => {
        if (img.tagName === 'IMG') {
          img.addEventListener('load', function() {
            console.log('Image', index, 'loaded successfully:', this.src);
          });

          img.addEventListener('error', function() {
            console.error('Failed to load image', index, ':', this.src);
            // Add a fallback background or text
            this.outerHTML = '<div class="preview-image" style="display: flex; align-items: center; justify-content: center; height: 100%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; font-weight: bold;">Preview Not Available</div>';
          });
        }
      });
    });
  </script>

</body>
</html>
