/**
 * CollaboCanvas Main Application
 * Coordinates all modules for real-time collaborative whiteboard
 */

import WebRTCManager from './js/webrtc.js';
import CanvasManager from './js/canvas.js';
import ToolsManager from './js/tools.js';
import ChatManager from './js/chat.js';
import UsersManager from './js/users.js';

class CollaboCanvasApp {
  constructor() {
    this.webrtc = new WebRTCManager();
    this.canvas = new CanvasManager();
    this.tools = new ToolsManager();
    this.chat = new ChatManager();
    this.users = new UsersManager();
    
    this.currentTheme = 'light';
    this.isInRoom = false;
    this.roomId = null;
    this.userId = this.webrtc.localPeerId;
    
    // Throttle cursor updates
    this.cursorUpdateThrottle = null;
    this.cursorUpdateDelay = 50; // ms
  }

  async init() {
    try {
      console.log('Initializing CollaboCanvas...');
      
      // Setup theme
      this.setupTheme();
      
      // Setup room management
      this.setupRoomManagement();
      
      // Setup WebRTC callbacks
      this.setupWebRTCCallbacks();
      
      // Setup global event listeners
      this.setupGlobalEventListeners();
      
      console.log('CollaboCanvas initialized successfully');
      
    } catch (error) {
      console.error('Failed to initialize CollaboCanvas:', error);
      this.showError('Failed to initialize application');
    }
  }

  setupTheme() {
    const savedTheme = localStorage.getItem('collabo-theme');
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    this.currentTheme = savedTheme || (systemPrefersDark ? 'dark' : 'light');
    
    this.setTheme(this.currentTheme);
    
    // Theme toggle
    document.querySelector('.theme-toggle')?.addEventListener('click', () => {
      const newTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
      this.setTheme(newTheme);
    });
  }

  setTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('collabo-theme', theme);
    this.currentTheme = theme;
    
    const themeIcon = document.querySelector('.theme-icon');
    if (themeIcon) {
      themeIcon.textContent = theme === 'dark' ? '☀️' : '🌙';
    }
  }

  setupRoomManagement() {
    const createRoomBtn = document.getElementById('createRoomBtn');
    const joinRoomBtn = document.getElementById('joinRoomBtn');
    const roomIdInput = document.getElementById('roomIdInput');
    const leaveRoomBtn = document.getElementById('leaveRoomBtn');

    createRoomBtn?.addEventListener('click', () => {
      this.createRoom();
    });

    joinRoomBtn?.addEventListener('click', () => {
      const roomId = roomIdInput?.value.trim();
      if (roomId) {
        this.joinRoom(roomId);
      } else {
        this.showError('Please enter a room ID');
      }
    });

    roomIdInput?.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        joinRoomBtn?.click();
      }
    });

    leaveRoomBtn?.addEventListener('click', () => {
      this.leaveRoom();
    });
  }

  setupWebRTCCallbacks() {
    this.webrtc.onConnectionStateChange = (state) => {
      this.updateConnectionStatus(state);
    };

    this.webrtc.onPeerJoined = (peerId) => {
      this.users.addUser(peerId);
      this.chat.userJoined(peerId);
    };

    this.webrtc.onPeerLeft = (peerId) => {
      this.users.removeUser(peerId);
      this.chat.userLeft(peerId);
    };

    this.webrtc.onDataReceived = (peerId, data) => {
      this.handleIncomingData(peerId, data);
    };

    this.webrtc.onCursorMove = (peerId, x, y) => {
      this.users.updateUserCursor(peerId, x, y);
    };
  }

  setupGlobalEventListeners() {
    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      if (this.isInRoom) {
        this.tools.handleKeyboardShortcuts(e);
      }
    });

    // Window beforeunload
    window.addEventListener('beforeunload', (e) => {
      if (this.isInRoom) {
        e.preventDefault();
        e.returnValue = 'Are you sure you want to leave the room?';
      }
    });

    // Window resize
    window.addEventListener('resize', () => {
      if (this.isInRoom) {
        // Handle canvas resize is already handled in CanvasManager
      }
    });
  }

  async createRoom() {
    try {
      this.showLoading('Creating room...');
      
      const roomId = await this.webrtc.createRoom();
      await this.enterRoom(roomId);
      
      this.hideLoading();
      this.showSuccess(`Room created! Share this ID: ${roomId}`);
      
    } catch (error) {
      console.error('Failed to create room:', error);
      this.hideLoading();
      this.showError('Failed to create room');
    }
  }

  async joinRoom(roomId) {
    try {
      this.showLoading('Joining room...');
      
      await this.webrtc.joinRoom(roomId);
      await this.enterRoom(roomId);
      
      this.hideLoading();
      this.showSuccess(`Joined room: ${roomId}`);
      
    } catch (error) {
      console.error('Failed to join room:', error);
      this.hideLoading();
      this.showError('Failed to join room. Please check the room ID.');
    }
  }

  async enterRoom(roomId) {
    this.roomId = roomId;
    this.isInRoom = true;
    
    // Hide room section, show whiteboard
    document.getElementById('roomSection').style.display = 'none';
    document.getElementById('whiteboardSection').style.display = 'block';
    
    // Update room ID display
    const currentRoomId = document.getElementById('currentRoomId');
    if (currentRoomId) {
      currentRoomId.textContent = roomId;
    }
    
    // Initialize all modules
    await this.initializeWhiteboardModules();
  }

  async initializeWhiteboardModules() {
    try {
      // Initialize canvas
      this.canvas.init('whiteboard');
      
      // Initialize tools
      this.tools.init();
      
      // Initialize chat
      this.chat.init(this.userId);
      
      // Initialize users
      this.users.init(this.userId);
      
      // Setup module callbacks
      this.setupModuleCallbacks();
      
      console.log('Whiteboard modules initialized');
      
    } catch (error) {
      console.error('Failed to initialize whiteboard modules:', error);
      this.showError('Failed to initialize whiteboard');
    }
  }

  setupModuleCallbacks() {
    // Canvas callbacks
    this.canvas.onDrawStart = (data) => {
      this.webrtc.broadcastDrawData({ type: 'start', ...data });
    };

    this.canvas.onDrawing = (data) => {
      this.webrtc.broadcastDrawData({ type: 'draw', ...data });
    };

    this.canvas.onDrawEnd = (data) => {
      this.webrtc.broadcastDrawData({ type: 'end', ...data });
    };

    this.canvas.onCursorMove = (x, y) => {
      // Throttle cursor updates
      if (this.cursorUpdateThrottle) {
        clearTimeout(this.cursorUpdateThrottle);
      }
      
      this.cursorUpdateThrottle = setTimeout(() => {
        this.webrtc.broadcastCursorPosition(x, y);
      }, this.cursorUpdateDelay);
    };

    // Tools callbacks
    this.tools.onToolChange = (tool) => {
      this.canvas.setTool(tool);
    };

    this.tools.onColorChange = (color) => {
      this.canvas.setColor(color);
    };

    this.tools.onSizeChange = (size) => {
      this.canvas.setSize(size);
    };

    this.tools.onTextAdd = (textData) => {
      // Add text at center of canvas for now
      const canvas = document.getElementById('whiteboard');
      const x = canvas.width / 2;
      const y = canvas.height / 2;
      
      this.canvas.addText(
        textData.text, 
        x, 
        y, 
        textData.fontSize, 
        textData.bold, 
        textData.italic
      );
      
      // Broadcast text addition
      this.webrtc.broadcastDrawData({
        type: 'text',
        text: textData.text,
        x: x,
        y: y,
        fontSize: textData.fontSize,
        bold: textData.bold,
        italic: textData.italic,
        color: this.canvas.currentColor
      });
    };

    this.tools.onUndo = () => {
      if (this.canvas.undo()) {
        this.webrtc.broadcastDrawData({ type: 'undo' });
      }
    };

    this.tools.onRedo = () => {
      if (this.canvas.redo()) {
        this.webrtc.broadcastDrawData({ type: 'redo' });
      }
    };

    this.tools.onClear = () => {
      this.canvas.clear();
      this.webrtc.broadcastClearCanvas();
    };

    this.tools.onSave = () => {
      this.saveCanvas();
    };

    this.tools.onExport = () => {
      this.canvas.exportAsImage();
    };

    // Chat callbacks
    this.chat.onMessageSend = (message) => {
      this.webrtc.broadcastChatMessage(message.text);
    };
  }

  handleIncomingData(peerId, data) {
    switch (data.type) {
      case 'draw':
        this.handleRemoteDrawing(peerId, data);
        break;
      case 'text':
        this.handleRemoteText(peerId, data);
        break;
      case 'clear':
        this.canvas.clear();
        break;
      case 'undo':
        this.canvas.undo();
        break;
      case 'redo':
        this.canvas.redo();
        break;
      case 'chat':
        this.chat.receiveMessage(data);
        break;
    }
  }

  handleRemoteDrawing(peerId, data) {
    switch (data.type) {
      case 'draw':
        if (data.tool === 'pen') {
          this.canvas.drawRemoteLine(data);
        }
        break;
      case 'end':
        if (['line', 'rectangle', 'circle'].includes(data.tool)) {
          this.canvas.drawRemoteShape(data);
        }
        break;
    }
  }

  handleRemoteText(peerId, data) {
    this.canvas.addText(
      data.text,
      data.x,
      data.y,
      data.fontSize,
      data.bold,
      data.italic
    );
  }

  leaveRoom() {
    if (!this.isInRoom) return;
    
    if (confirm('Are you sure you want to leave the room?')) {
      this.webrtc.leaveRoom();
      this.isInRoom = false;
      this.roomId = null;
      
      // Show room section, hide whiteboard
      document.getElementById('roomSection').style.display = 'block';
      document.getElementById('whiteboardSection').style.display = 'none';
      
      // Cleanup modules
      this.chat.destroy();
      this.users.destroy();
      
      this.showSuccess('Left the room');
    }
  }

  saveCanvas() {
    const canvasData = this.canvas.getCanvasData();
    const fileName = `canvas-${this.roomId || 'local'}-${Date.now()}.json`;
    
    const data = {
      canvasData: canvasData,
      roomId: this.roomId,
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    };
    
    const dataStr = JSON.stringify(data, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = fileName;
    link.click();
    
    URL.revokeObjectURL(link.href);
    this.showSuccess('Canvas saved successfully');
  }

  updateConnectionStatus(state) {
    const statusIndicator = document.querySelector('.status-indicator');
    const statusText = document.querySelector('.status-text');
    
    if (statusIndicator && statusText) {
      statusIndicator.className = 'status-indicator';
      
      switch (state) {
        case 'connected':
          statusIndicator.classList.add('online');
          statusText.textContent = 'Connected';
          break;
        case 'connecting':
          statusIndicator.classList.add('connecting');
          statusText.textContent = 'Connecting';
          break;
        case 'disconnected':
          statusIndicator.classList.add('offline');
          statusText.textContent = 'Disconnected';
          break;
        case 'error':
          statusIndicator.classList.add('offline');
          statusText.textContent = 'Connection Error';
          break;
      }
    }
  }

  showLoading(message = 'Loading...') {
    const overlay = document.getElementById('loadingOverlay');
    const text = document.getElementById('loadingText');
    
    if (overlay && text) {
      text.textContent = message;
      overlay.style.display = 'flex';
    }
  }

  hideLoading() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
      overlay.style.display = 'none';
    }
  }

  showSuccess(message) {
    this.showNotification(message, 'success');
  }

  showError(message) {
    this.showNotification(message, 'error');
  }

  showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 24px;
      border-radius: 8px;
      color: white;
      font-weight: 500;
      z-index: 4000;
      animation: slideIn 0.3s ease-out;
      background-color: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
      notification.style.animation = 'slideOut 0.3s ease-in';
      setTimeout(() => notification.remove(), 300);
    }, 3000);
  }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  const app = new CollaboCanvasApp();
  app.init();
  
  // Make app globally available for debugging
  window.collaboCanvas = app;
});

// Add notification animations
const notificationStyles = document.createElement('style');
notificationStyles.textContent = `
  @keyframes slideIn {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  @keyframes slideOut {
    from {
      transform: translateX(0);
      opacity: 1;
    }
    to {
      transform: translateX(100%);
      opacity: 0;
    }
  }
`;
document.head.appendChild(notificationStyles);
