/**
 * CollaboCanvas Chat Module
 * Handles real-time chat functionality
 */

class ChatManager {
  constructor() {
    this.messages = [];
    this.maxMessages = 100;
    this.currentUser = null;
    this.isOpen = false;
    
    // Event callbacks
    this.onMessageSend = null;
  }

  init(userId) {
    this.currentUser = userId;
    this.setupChatInterface();
    this.setupEventListeners();
    
    console.log('Chat initialized for user:', userId);
  }

  setupChatInterface() {
    const chatPanel = document.getElementById('chatPanel');
    const chatToggle = document.getElementById('chatPanelToggle');
    
    if (chatToggle) {
      chatToggle.addEventListener('click', () => {
        this.toggleChat();
      });
    }
  }

  setupEventListeners() {
    const chatInput = document.getElementById('chatInput');
    const sendButton = document.getElementById('sendMessageBtn');
    
    // Send message on button click
    if (sendButton) {
      sendButton.addEventListener('click', () => {
        this.sendMessage();
      });
    }
    
    // Send message on Enter key
    if (chatInput) {
      chatInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          this.sendMessage();
        }
      });
      
      // Auto-resize textarea
      chatInput.addEventListener('input', () => {
        this.autoResizeInput(chatInput);
      });
    }
  }

  toggleChat() {
    const chatPanel = document.getElementById('chatPanel');
    if (chatPanel) {
      this.isOpen = !this.isOpen;
      
      if (this.isOpen) {
        chatPanel.classList.add('open');
        this.scrollToBottom();
      } else {
        chatPanel.classList.remove('open');
      }
    }
  }

  sendMessage() {
    const chatInput = document.getElementById('chatInput');
    if (!chatInput) return;
    
    const messageText = chatInput.value.trim();
    if (!messageText) return;
    
    const message = {
      id: this.generateMessageId(),
      text: messageText,
      author: this.currentUser,
      timestamp: Date.now(),
      type: 'user'
    };
    
    // Add to local messages
    this.addMessage(message);
    
    // Clear input
    chatInput.value = '';
    this.autoResizeInput(chatInput);
    
    // Send to other users
    if (this.onMessageSend) {
      this.onMessageSend(message);
    }
  }

  addMessage(message, isOwn = null) {
    // Determine if message is from current user
    if (isOwn === null) {
      isOwn = message.author === this.currentUser;
    }
    
    // Add to messages array
    this.messages.push(message);
    
    // Limit message history
    if (this.messages.length > this.maxMessages) {
      this.messages.shift();
    }
    
    // Render message
    this.renderMessage(message, isOwn);
    
    // Scroll to bottom
    this.scrollToBottom();
    
    // Show notification if chat is closed
    if (!this.isOpen && !isOwn) {
      this.showNotification();
    }
  }

  renderMessage(message, isOwn = false) {
    const messagesContainer = document.getElementById('chatMessages');
    if (!messagesContainer) return;
    
    const messageElement = document.createElement('div');
    messageElement.className = 'chat-message';
    messageElement.dataset.messageId = message.id;
    
    const timestamp = new Date(message.timestamp).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    });
    
    messageElement.innerHTML = `
      <div class="message-header">
        <span class="message-author">${this.formatAuthorName(message.author)}</span>
        <span class="message-time">${timestamp}</span>
      </div>
      <div class="message-content ${isOwn ? 'own' : ''}">
        ${this.formatMessageText(message.text)}
      </div>
    `;
    
    messagesContainer.appendChild(messageElement);
    
    // Add animation
    messageElement.classList.add('fade-in');
  }

  renderSystemMessage(text) {
    const messagesContainer = document.getElementById('chatMessages');
    if (!messagesContainer) return;
    
    const messageElement = document.createElement('div');
    messageElement.className = 'chat-message system-message';
    
    messageElement.innerHTML = `
      <div class="message-content system">
        <em>${text}</em>
      </div>
    `;
    
    messagesContainer.appendChild(messageElement);
    this.scrollToBottom();
  }

  formatAuthorName(authorId) {
    // In a real app, this would map to actual user names
    if (authorId === this.currentUser) {
      return 'You';
    }
    return `User ${authorId.slice(-4)}`;
  }

  formatMessageText(text) {
    // Basic text formatting
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/\n/g, '<br>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`(.*?)`/g, '<code>$1</code>');
  }

  autoResizeInput(input) {
    input.style.height = 'auto';
    input.style.height = Math.min(input.scrollHeight, 120) + 'px';
  }

  scrollToBottom() {
    const messagesContainer = document.getElementById('chatMessages');
    if (messagesContainer) {
      setTimeout(() => {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
      }, 100);
    }
  }

  showNotification() {
    const chatToggle = document.getElementById('chatPanelToggle');
    if (chatToggle) {
      chatToggle.classList.add('has-notification');
      
      // Remove notification after 5 seconds
      setTimeout(() => {
        chatToggle.classList.remove('has-notification');
      }, 5000);
    }
  }

  generateMessageId() {
    return 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 5);
  }

  // Handle incoming messages from other users
  receiveMessage(messageData) {
    const message = {
      id: messageData.id || this.generateMessageId(),
      text: messageData.message || messageData.text,
      author: messageData.author,
      timestamp: messageData.timestamp || Date.now(),
      type: 'user'
    };
    
    this.addMessage(message, false);
  }

  // System messages for user events
  userJoined(userId) {
    this.renderSystemMessage(`${this.formatAuthorName(userId)} joined the room`);
  }

  userLeft(userId) {
    this.renderSystemMessage(`${this.formatAuthorName(userId)} left the room`);
  }

  // Chat commands
  handleCommand(command, args) {
    switch (command.toLowerCase()) {
      case '/clear':
        this.clearChat();
        break;
      case '/help':
        this.showHelp();
        break;
      case '/users':
        this.showUsers();
        break;
      default:
        this.renderSystemMessage(`Unknown command: ${command}`);
    }
  }

  clearChat() {
    const messagesContainer = document.getElementById('chatMessages');
    if (messagesContainer) {
      messagesContainer.innerHTML = '';
      this.messages = [];
    }
    this.renderSystemMessage('Chat cleared');
  }

  showHelp() {
    const helpText = `
Available commands:
/clear - Clear chat history
/help - Show this help message
/users - Show connected users
**bold text** - Bold formatting
*italic text* - Italic formatting
\`code\` - Code formatting
    `.trim();
    
    this.renderSystemMessage(helpText);
  }

  showUsers() {
    // This would show actual connected users in a real implementation
    this.renderSystemMessage('Connected users: You and others in the room');
  }

  // Message search and filtering
  searchMessages(query) {
    if (!query.trim()) return this.messages;
    
    const searchTerm = query.toLowerCase();
    return this.messages.filter(message => 
      message.text.toLowerCase().includes(searchTerm) ||
      message.author.toLowerCase().includes(searchTerm)
    );
  }

  filterMessagesByUser(userId) {
    return this.messages.filter(message => message.author === userId);
  }

  filterMessagesByTimeRange(startTime, endTime) {
    return this.messages.filter(message => 
      message.timestamp >= startTime && message.timestamp <= endTime
    );
  }

  // Export chat history
  exportChatHistory() {
    const chatData = {
      messages: this.messages,
      exportDate: new Date().toISOString(),
      roomId: this.roomId || 'unknown'
    };
    
    const dataStr = JSON.stringify(chatData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `chat-history-${Date.now()}.json`;
    link.click();
    
    URL.revokeObjectURL(link.href);
  }

  // Message reactions (for future enhancement)
  addReaction(messageId, reaction) {
    const message = this.messages.find(m => m.id === messageId);
    if (message) {
      if (!message.reactions) {
        message.reactions = {};
      }
      
      if (!message.reactions[reaction]) {
        message.reactions[reaction] = [];
      }
      
      if (!message.reactions[reaction].includes(this.currentUser)) {
        message.reactions[reaction].push(this.currentUser);
        this.updateMessageReactions(messageId);
      }
    }
  }

  removeReaction(messageId, reaction) {
    const message = this.messages.find(m => m.id === messageId);
    if (message && message.reactions && message.reactions[reaction]) {
      const index = message.reactions[reaction].indexOf(this.currentUser);
      if (index > -1) {
        message.reactions[reaction].splice(index, 1);
        if (message.reactions[reaction].length === 0) {
          delete message.reactions[reaction];
        }
        this.updateMessageReactions(messageId);
      }
    }
  }

  updateMessageReactions(messageId) {
    const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
    if (messageElement) {
      const message = this.messages.find(m => m.id === messageId);
      if (message && message.reactions) {
        // Update reactions display (implementation would go here)
      }
    }
  }

  // Cleanup
  destroy() {
    this.messages = [];
    this.onMessageSend = null;
    
    const chatPanel = document.getElementById('chatPanel');
    if (chatPanel) {
      chatPanel.classList.remove('open');
    }
  }

  // Get chat statistics
  getChatStats() {
    const totalMessages = this.messages.length;
    const userMessages = this.messages.filter(m => m.author === this.currentUser).length;
    const otherMessages = totalMessages - userMessages;
    
    return {
      totalMessages,
      userMessages,
      otherMessages,
      averageMessageLength: this.messages.reduce((sum, m) => sum + m.text.length, 0) / totalMessages || 0
    };
  }
}

export default ChatManager;
