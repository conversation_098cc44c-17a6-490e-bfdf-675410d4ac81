/**
 * SkillSync - AI Interview Preparation Tool
 * Features: Flash cards, practice sessions, AI integration, localStorage persistence
 */

class SkillSyncApp {
  constructor() {
    this.flashCards = JSON.parse(localStorage.getItem('skillsync-cards') || '[]');
    this.currentSession = null;
    this.currentQuestionIndex = 0;
    this.sessionQuestions = [];
    
    this.initializeElements();
    this.setupEventListeners();
    this.setupTheme();
    this.loadInitialData();
    this.renderFlashCards();
    this.updateStats();
  }

  initializeElements() {
    // Navigation elements
    this.themeToggle = document.getElementById('themeToggle') || document.querySelector('.theme-toggle');
    this.themeIcon = document.querySelector('.theme-icon');
    
    // Main action buttons
    this.startPracticeBtn = document.getElementById('startPractice');
    this.viewCardsBtn = document.getElementById('viewCards');
    
    // Practice session elements
    this.practiceSection = document.getElementById('practiceSession');
    this.flashCardsSection = document.getElementById('flashCards');
    this.currentQuestionEl = document.getElementById('currentQuestion');
    this.answerSection = document.getElementById('answerSection');
    this.answerText = document.getElementById('answerText');
    this.progressFill = document.getElementById('progressFill');
    
    // Flash cards elements
    this.cardsGrid = document.getElementById('cardsGrid');
    this.categoryFilter = document.getElementById('categoryFilter');
    this.addCardBtn = document.getElementById('addCard');
    this.addCardModal = document.getElementById('addCardModal');
    this.addCardForm = document.getElementById('addCardForm');
    
    // Stats elements
    this.totalCardsEl = document.getElementById('totalCards');
    this.masteredCardsEl = document.getElementById('masteredCards');
    this.reviewCardsEl = document.getElementById('reviewCards');
    
    // Loading overlay
    this.loadingOverlay = document.getElementById('loadingOverlay');
  }

  setupEventListeners() {
    // Theme toggle
    this.themeToggle?.addEventListener('click', () => this.toggleTheme());
    
    // Main navigation
    this.startPracticeBtn?.addEventListener('click', () => this.startPracticeSession());
    this.viewCardsBtn?.addEventListener('click', () => this.showFlashCards());
    
    // Practice session controls
    document.getElementById('pauseSession')?.addEventListener('click', () => this.pauseSession());
    document.getElementById('endSession')?.addEventListener('click', () => this.endSession());
    document.getElementById('showHint')?.addEventListener('click', () => this.showHint());
    document.getElementById('showAnswer')?.addEventListener('click', () => this.showAnswer());
    
    // Answer feedback buttons
    document.getElementById('correctAnswer')?.addEventListener('click', () => this.recordAnswer('correct'));
    document.getElementById('partialAnswer')?.addEventListener('click', () => this.recordAnswer('partial'));
    document.getElementById('wrongAnswer')?.addEventListener('click', () => this.recordAnswer('wrong'));
    
    // Flash cards controls
    this.addCardBtn?.addEventListener('click', () => this.showAddCardModal());
    this.categoryFilter?.addEventListener('change', () => this.filterCards());
    this.addCardForm?.addEventListener('submit', (e) => this.handleAddCard(e));
    
    // Modal controls
    document.getElementById('closeModal')?.addEventListener('click', () => this.hideAddCardModal());
    document.getElementById('cancelAdd')?.addEventListener('click', () => this.hideAddCardModal());
    
    // AI features
    document.getElementById('generateQuestions')?.addEventListener('click', () => this.generateAIQuestions());
    document.getElementById('viewAnalysis')?.addEventListener('click', () => this.showPerformanceAnalysis());
    document.getElementById('getTips')?.addEventListener('click', () => this.getPersonalizedTips());
    
    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));
    
    // Click outside modal to close
    this.addCardModal?.addEventListener('click', (e) => {
      if (e.target === this.addCardModal) {
        this.hideAddCardModal();
      }
    });
  }

  setupTheme() {
    const savedTheme = localStorage.getItem('skillsync-theme');
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    const initialTheme = savedTheme || (systemPrefersDark ? 'dark' : 'light');
    
    this.setTheme(initialTheme);
  }

  setTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('skillsync-theme', theme);
    
    if (this.themeIcon) {
      this.themeIcon.textContent = theme === 'dark' ? '☀️' : '🌙';
    }
  }

  toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    this.setTheme(newTheme);
  }

  loadInitialData() {
    // Load default flash cards if none exist
    if (this.flashCards.length === 0) {
      this.flashCards = this.getDefaultFlashCards();
      this.saveFlashCards();
    }
  }

  getDefaultFlashCards() {
    return [
      {
        id: Date.now() + 1,
        question: "Tell me about yourself and your background in software development.",
        answer: "I'm a passionate software developer with [X] years of experience in [technologies]. I've worked on [types of projects] and am particularly interested in [specific areas]. I enjoy solving complex problems and collaborating with teams to build impactful solutions.",
        category: "behavioral",
        difficulty: "easy",
        mastered: false,
        reviewCount: 0,
        lastReviewed: null,
        created: new Date().toISOString()
      },
      {
        id: Date.now() + 2,
        question: "Explain the difference between let, const, and var in JavaScript.",
        answer: "var is function-scoped and can be redeclared, let is block-scoped and can be reassigned but not redeclared, const is block-scoped and cannot be reassigned or redeclared. const must be initialized at declaration.",
        category: "technical",
        difficulty: "medium",
        mastered: false,
        reviewCount: 0,
        lastReviewed: null,
        created: new Date().toISOString()
      },
      {
        id: Date.now() + 3,
        question: "How would you design a URL shortener like bit.ly?",
        answer: "Key components: 1) URL encoding service to generate short codes, 2) Database to store mappings, 3) Redirect service, 4) Analytics service. Consider base62 encoding, database sharding, caching, and rate limiting.",
        category: "system-design",
        difficulty: "hard",
        mastered: false,
        reviewCount: 0,
        lastReviewed: null,
        created: new Date().toISOString()
      },
      {
        id: Date.now() + 4,
        question: "Describe a time when you had to lead a difficult project.",
        answer: "Use STAR method: Situation (project context), Task (your responsibility), Action (specific steps taken), Result (outcome and lessons learned). Focus on leadership skills, problem-solving, and team collaboration.",
        category: "leadership",
        difficulty: "medium",
        mastered: false,
        reviewCount: 0,
        lastReviewed: null,
        created: new Date().toISOString()
      }
    ];
  }

  saveFlashCards() {
    localStorage.setItem('skillsync-cards', JSON.stringify(this.flashCards));
  }

  // Practice Session Methods
  startPracticeSession() {
    this.sessionQuestions = this.getSessionQuestions();
    this.currentQuestionIndex = 0;
    this.currentSession = {
      startTime: new Date(),
      questions: this.sessionQuestions,
      answers: [],
      score: 0
    };
    
    this.showPracticeSession();
    this.loadCurrentQuestion();
  }

  getSessionQuestions() {
    // Get a mix of questions based on review priority
    const needReview = this.flashCards.filter(card => !card.mastered);
    const mastered = this.flashCards.filter(card => card.mastered);
    
    // Prioritize cards that need review
    let sessionCards = [...needReview];
    
    // Add some mastered cards for reinforcement
    if (mastered.length > 0 && sessionCards.length < 10) {
      const additionalCards = mastered
        .sort(() => Math.random() - 0.5)
        .slice(0, 10 - sessionCards.length);
      sessionCards = [...sessionCards, ...additionalCards];
    }
    
    // Shuffle and limit to 10 questions
    return sessionCards
      .sort(() => Math.random() - 0.5)
      .slice(0, Math.min(10, sessionCards.length));
  }

  showPracticeSession() {
    this.practiceSection.style.display = 'block';
    this.flashCardsSection.style.display = 'none';
    this.practiceSection.scrollIntoView({ behavior: 'smooth' });
  }

  showFlashCards() {
    this.practiceSection.style.display = 'none';
    this.flashCardsSection.style.display = 'block';
    this.flashCardsSection.scrollIntoView({ behavior: 'smooth' });
  }

  loadCurrentQuestion() {
    if (this.currentQuestionIndex >= this.sessionQuestions.length) {
      this.completeSession();
      return;
    }
    
    const question = this.sessionQuestions[this.currentQuestionIndex];
    const questionNumber = this.currentQuestionIndex + 1;
    const totalQuestions = this.sessionQuestions.length;
    
    // Update question display
    document.querySelector('.question-number').textContent = `Question ${questionNumber} of ${totalQuestions}`;
    document.querySelector('.question-category').textContent = question.category;
    this.currentQuestionEl.textContent = question.question;
    
    // Update progress bar
    const progress = (questionNumber / totalQuestions) * 100;
    this.progressFill.style.width = `${progress}%`;
    
    // Hide answer section
    this.answerSection.style.display = 'none';
  }

  showHint() {
    const question = this.sessionQuestions[this.currentQuestionIndex];
    const hint = this.generateHint(question);
    
    alert(`Hint: ${hint}`);
  }

  generateHint(question) {
    const hints = {
      behavioral: "Use the STAR method: Situation, Task, Action, Result",
      technical: "Think about the core concepts and provide examples",
      'system-design': "Consider scalability, reliability, and performance",
      leadership: "Focus on specific examples and measurable outcomes"
    };
    
    return hints[question.category] || "Break down the problem into smaller parts";
  }

  showAnswer() {
    const question = this.sessionQuestions[this.currentQuestionIndex];
    this.answerText.textContent = question.answer;
    this.answerSection.style.display = 'block';
  }

  recordAnswer(result) {
    const question = this.sessionQuestions[this.currentQuestionIndex];
    
    // Update question stats
    question.reviewCount++;
    question.lastReviewed = new Date().toISOString();
    
    if (result === 'correct') {
      question.mastered = true;
      this.currentSession.score++;
    } else if (result === 'wrong') {
      question.mastered = false;
    }
    
    // Record answer in session
    this.currentSession.answers.push({
      questionId: question.id,
      result: result,
      timestamp: new Date().toISOString()
    });
    
    // Update the card in storage
    const cardIndex = this.flashCards.findIndex(card => card.id === question.id);
    if (cardIndex !== -1) {
      this.flashCards[cardIndex] = question;
      this.saveFlashCards();
    }
    
    // Move to next question
    this.currentQuestionIndex++;
    setTimeout(() => this.loadCurrentQuestion(), 1000);
  }

  completeSession() {
    this.currentSession.endTime = new Date();
    this.currentSession.duration = this.currentSession.endTime - this.currentSession.startTime;
    
    // Save session to history
    const sessions = JSON.parse(localStorage.getItem('skillsync-sessions') || '[]');
    sessions.push(this.currentSession);
    localStorage.setItem('skillsync-sessions', JSON.stringify(sessions));
    
    // Show completion message
    const accuracy = Math.round((this.currentSession.score / this.sessionQuestions.length) * 100);
    alert(`Session Complete!\nScore: ${this.currentSession.score}/${this.sessionQuestions.length} (${accuracy}%)`);
    
    this.endSession();
    this.updateStats();
  }

  pauseSession() {
    alert('Session paused. Click "Start Practice Session" to resume.');
    this.endSession();
  }

  endSession() {
    this.currentSession = null;
    this.currentQuestionIndex = 0;
    this.sessionQuestions = [];
    this.showFlashCards();
  }

  // Flash Cards Methods
  renderFlashCards() {
    if (!this.cardsGrid) return;
    
    const filter = this.categoryFilter?.value || 'all';
    const filteredCards = filter === 'all' 
      ? this.flashCards 
      : this.flashCards.filter(card => card.category === filter);
    
    this.cardsGrid.innerHTML = filteredCards.map(card => this.createFlashCardHTML(card)).join('');
    
    // Add event listeners to card elements
    this.cardsGrid.querySelectorAll('.flash-card').forEach(cardEl => {
      const cardId = parseInt(cardEl.dataset.cardId);
      
      cardEl.addEventListener('click', (e) => {
        if (!e.target.classList.contains('card-btn')) {
          this.flipCard(cardEl);
        }
      });
      
      cardEl.querySelector('.card-btn.edit')?.addEventListener('click', (e) => {
        e.stopPropagation();
        this.editCard(cardId);
      });
      
      cardEl.querySelector('.card-btn.delete')?.addEventListener('click', (e) => {
        e.stopPropagation();
        this.deleteCard(cardId);
      });
    });
  }

  createFlashCardHTML(card) {
    const difficultyClass = card.difficulty;
    const categoryDisplay = card.category.replace('-', ' ').toUpperCase();

    return `
      <div class="flash-card" data-card-id="${card.id}" tabindex="0">
        <div class="card-front">
          <div class="card-header">
            <span class="card-category">${categoryDisplay}</span>
            <span class="card-difficulty ${difficultyClass}">${card.difficulty.toUpperCase()}</span>
          </div>
          <div class="card-content">
            <div class="card-question">${card.question}</div>
          </div>
          <div class="card-actions">
            <div class="card-stats">
              Reviewed: ${card.reviewCount} times
              ${card.mastered ? '✅ Mastered' : '📚 Learning'}
            </div>
            <div class="card-buttons">
              <button class="card-btn edit">Edit</button>
              <button class="card-btn delete">Delete</button>
            </div>
          </div>
        </div>
        <div class="card-back">
          <div class="card-header">
            <span class="card-category">${categoryDisplay}</span>
            <span class="card-difficulty ${difficultyClass}">${card.difficulty.toUpperCase()}</span>
          </div>
          <div class="card-content">
            <div class="card-answer">${card.answer}</div>
          </div>
          <div class="card-actions">
            <div class="card-stats">
              Click to flip back
            </div>
            <div class="card-buttons">
              <button class="card-btn edit">Edit</button>
              <button class="card-btn delete">Delete</button>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  flipCard(cardEl) {
    cardEl.classList.toggle('flipped');
  }

  filterCards() {
    this.renderFlashCards();
  }

  showAddCardModal() {
    this.addCardModal.style.display = 'flex';
    document.getElementById('cardQuestion').focus();
  }

  hideAddCardModal() {
    this.addCardModal.style.display = 'none';
    this.addCardForm.reset();
  }

  handleAddCard(e) {
    e.preventDefault();

    const newCard = {
      id: Date.now(),
      question: document.getElementById('cardQuestion').value.trim(),
      answer: document.getElementById('cardAnswer').value.trim(),
      category: document.getElementById('cardCategory').value,
      difficulty: document.getElementById('cardDifficulty').value,
      mastered: false,
      reviewCount: 0,
      lastReviewed: null,
      created: new Date().toISOString()
    };

    this.flashCards.push(newCard);
    this.saveFlashCards();
    this.renderFlashCards();
    this.updateStats();
    this.hideAddCardModal();

    // Show success message
    this.showNotification('Flash card added successfully!', 'success');
  }

  editCard(cardId) {
    const card = this.flashCards.find(c => c.id === cardId);
    if (!card) return;

    // Pre-fill the form with existing data
    document.getElementById('cardQuestion').value = card.question;
    document.getElementById('cardAnswer').value = card.answer;
    document.getElementById('cardCategory').value = card.category;
    document.getElementById('cardDifficulty').value = card.difficulty;

    // Change form behavior to edit mode
    this.addCardForm.onsubmit = (e) => {
      e.preventDefault();

      card.question = document.getElementById('cardQuestion').value.trim();
      card.answer = document.getElementById('cardAnswer').value.trim();
      card.category = document.getElementById('cardCategory').value;
      card.difficulty = document.getElementById('cardDifficulty').value;
      card.modified = new Date().toISOString();

      this.saveFlashCards();
      this.renderFlashCards();
      this.hideAddCardModal();
      this.showNotification('Flash card updated successfully!', 'success');

      // Reset form behavior
      this.addCardForm.onsubmit = (e) => this.handleAddCard(e);
    };

    this.showAddCardModal();
  }

  deleteCard(cardId) {
    if (confirm('Are you sure you want to delete this flash card?')) {
      this.flashCards = this.flashCards.filter(card => card.id !== cardId);
      this.saveFlashCards();
      this.renderFlashCards();
      this.updateStats();
      this.showNotification('Flash card deleted successfully!', 'success');
    }
  }

  updateStats() {
    const total = this.flashCards.length;
    const mastered = this.flashCards.filter(card => card.mastered).length;
    const needReview = total - mastered;

    if (this.totalCardsEl) this.totalCardsEl.textContent = total;
    if (this.masteredCardsEl) this.masteredCardsEl.textContent = mastered;
    if (this.reviewCardsEl) this.reviewCardsEl.textContent = needReview;
  }

  // AI Integration Methods (Placeholder implementations)
  async generateAIQuestions() {
    this.showLoading('Generating AI questions...');

    try {
      // Simulate AI API call
      await this.simulateAICall();

      const aiQuestions = [
        {
          question: "How would you optimize a React application for better performance?",
          answer: "Key strategies include: 1) Use React.memo for component memoization, 2) Implement code splitting with lazy loading, 3) Optimize bundle size, 4) Use useMemo and useCallback hooks, 5) Implement virtual scrolling for large lists.",
          category: "technical",
          difficulty: "medium"
        },
        {
          question: "Describe your approach to handling a disagreement with a team member.",
          answer: "I would: 1) Listen actively to understand their perspective, 2) Find common ground, 3) Present my viewpoint with supporting evidence, 4) Collaborate on a solution that addresses both concerns, 5) Follow up to ensure the resolution is working.",
          category: "behavioral",
          difficulty: "medium"
        }
      ];

      // Add AI-generated questions to flash cards
      aiQuestions.forEach(q => {
        this.flashCards.push({
          id: Date.now() + Math.random(),
          ...q,
          mastered: false,
          reviewCount: 0,
          lastReviewed: null,
          created: new Date().toISOString(),
          aiGenerated: true
        });
      });

      this.saveFlashCards();
      this.renderFlashCards();
      this.updateStats();
      this.showNotification('AI questions generated successfully!', 'success');

    } catch (error) {
      this.showNotification('Failed to generate AI questions. Please try again.', 'error');
    } finally {
      this.hideLoading();
    }
  }

  async showPerformanceAnalysis() {
    this.showLoading('Analyzing your performance...');

    try {
      await this.simulateAICall();

      const sessions = JSON.parse(localStorage.getItem('skillsync-sessions') || '[]');
      const totalSessions = sessions.length;
      const averageScore = sessions.length > 0
        ? sessions.reduce((sum, s) => sum + (s.score / s.questions.length), 0) / sessions.length
        : 0;

      const analysis = `
        📊 Performance Analysis:

        Total Practice Sessions: ${totalSessions}
        Average Score: ${Math.round(averageScore * 100)}%
        Total Flash Cards: ${this.flashCards.length}
        Mastered Cards: ${this.flashCards.filter(c => c.mastered).length}

        💡 Recommendations:
        - Focus on ${this.getWeakestCategory()} questions
        - Practice more ${this.getMostMissedDifficulty()} level questions
        - Review cards you haven't seen in a while
      `;

      alert(analysis);

    } catch (error) {
      this.showNotification('Failed to analyze performance. Please try again.', 'error');
    } finally {
      this.hideLoading();
    }
  }

  async getPersonalizedTips() {
    this.showLoading('Getting personalized tips...');

    try {
      await this.simulateAICall();

      const tips = [
        "💡 Practice the STAR method for behavioral questions",
        "🔧 Review system design patterns regularly",
        "📚 Create more flash cards for your weak areas",
        "⏰ Set aside 15 minutes daily for interview prep",
        "🎯 Focus on explaining your thought process clearly"
      ];

      const randomTip = tips[Math.floor(Math.random() * tips.length)];
      alert(`Personalized Tip:\n\n${randomTip}`);

    } catch (error) {
      this.showNotification('Failed to get tips. Please try again.', 'error');
    } finally {
      this.hideLoading();
    }
  }

  // Utility Methods
  async simulateAICall() {
    // Simulate API call delay
    return new Promise(resolve => setTimeout(resolve, 2000));
  }

  getWeakestCategory() {
    const categories = ['technical', 'behavioral', 'system-design', 'leadership'];
    const categoryStats = categories.map(cat => ({
      category: cat,
      mastered: this.flashCards.filter(c => c.category === cat && c.mastered).length,
      total: this.flashCards.filter(c => c.category === cat).length
    }));

    const weakest = categoryStats.reduce((min, cat) =>
      (cat.total > 0 && cat.mastered / cat.total < min.mastered / min.total) ? cat : min
    );

    return weakest.category;
  }

  getMostMissedDifficulty() {
    const difficulties = ['easy', 'medium', 'hard'];
    const difficultyStats = difficulties.map(diff => ({
      difficulty: diff,
      mastered: this.flashCards.filter(c => c.difficulty === diff && c.mastered).length,
      total: this.flashCards.filter(c => c.difficulty === diff).length
    }));

    const mostMissed = difficultyStats.reduce((min, diff) =>
      (diff.total > 0 && diff.mastered / diff.total < min.mastered / min.total) ? diff : min
    );

    return mostMissed.difficulty;
  }

  showLoading(message = 'Loading...') {
    if (this.loadingOverlay) {
      this.loadingOverlay.querySelector('p').textContent = message;
      this.loadingOverlay.style.display = 'flex';
    }
  }

  hideLoading() {
    if (this.loadingOverlay) {
      this.loadingOverlay.style.display = 'none';
    }
  }

  showNotification(message, type = 'info') {
    // Simple notification system
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 24px;
      border-radius: 8px;
      color: white;
      font-weight: 500;
      z-index: 4000;
      animation: slideIn 0.3s ease-out;
      background-color: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
      notification.style.animation = 'slideOut 0.3s ease-in';
      setTimeout(() => notification.remove(), 300);
    }, 3000);
  }

  handleKeyboardShortcuts(e) {
    // Keyboard shortcuts for better accessibility
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 'n':
          e.preventDefault();
          this.showAddCardModal();
          break;
        case 'p':
          e.preventDefault();
          this.startPracticeSession();
          break;
        case 'f':
          e.preventDefault();
          this.categoryFilter?.focus();
          break;
      }
    }

    // Escape key to close modals
    if (e.key === 'Escape') {
      if (this.addCardModal.style.display === 'flex') {
        this.hideAddCardModal();
      }
    }
  }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new SkillSyncApp();
});

// Add CSS animations for notifications
const style = document.createElement('style');
style.textContent = `
  @keyframes slideIn {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes slideOut {
    from {
      transform: translateX(0);
      opacity: 1;
    }
    to {
      transform: translateX(100%);
      opacity: 0;
    }
  }
`;
document.head.appendChild(style);
