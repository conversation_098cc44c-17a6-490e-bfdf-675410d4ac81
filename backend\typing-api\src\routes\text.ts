/**
 * Text sample routes for typing tests
 */

import { Router, Request, Response } from 'express';
import { async<PERSON>and<PERSON> } from '@/middleware/errorHandler';
import { logger } from '@/utils/logger';

const router = Router();

/**
 * Get text samples for typing tests
 */
router.get('/', asyncHandler(async (req: Request, res: Response) => {
  const { difficulty, category, limit = 20 } = req.query;

  try {
    // Mock text samples
    const samples = [
      {
        id: 1,
        text: "The quick brown fox jumps over the lazy dog. This pangram contains every letter of the alphabet at least once.",
        difficulty: 'easy',
        category: 'quotes',
        characterCount: 97,
        wordCount: 18,
        estimatedTime: 60
      },
      {
        id: 2,
        text: "function calculateWPM(characters, timeInMinutes) { return Math.round((characters / 5) / timeInMinutes); }",
        difficulty: 'medium',
        category: 'programming',
        characterCount: 103,
        wordCount: 12,
        estimatedTime: 75
      }
    ];

    res.json({
      success: true,
      data: samples
    });

  } catch (error) {
    logger.error('Failed to get text samples:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get text samples',
      requestId: (req as any).requestId
    });
  }
}));

export default router;
