/**
 * FinTrack Service Worker
 * Provides offline functionality, caching, and background sync
 */

const CACHE_NAME = 'fintrack-v1.0.0';
const STATIC_CACHE = 'fintrack-static-v1.0.0';
const DYNAMIC_CACHE = 'fintrack-dynamic-v1.0.0';

// Files to cache for offline functionality
const STATIC_FILES = [
  './',
  './index.html',
  './styles.css',
  './main.js',
  './manifest.json',
  './js/database.js',
  './js/charts.js',
  './js/transactions.js',
  './js/goals.js',
  './js/export.js',
  '../../assets/favicon.svg'
];

// Install event - cache static files
self.addEventListener('install', event => {
  console.log('[SW] Installing Service Worker');
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then(cache => {
        console.log('[SW] Caching static files');
        return cache.addAll(STATIC_FILES);
      })
      .then(() => {
        console.log('[SW] Static files cached successfully');
        return self.skipWaiting();
      })
      .catch(error => {
        console.error('[SW] Error caching static files:', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
  console.log('[SW] Activating Service Worker');
  
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
              console.log('[SW] Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('[SW] Service Worker activated');
        return self.clients.claim();
      })
  );
});

// Fetch event - serve cached files or fetch from network
self.addEventListener('fetch', event => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Skip chrome-extension and other non-http requests
  if (!url.protocol.startsWith('http')) {
    return;
  }
  
  event.respondWith(
    caches.match(request)
      .then(cachedResponse => {
        if (cachedResponse) {
          console.log('[SW] Serving from cache:', request.url);
          return cachedResponse;
        }
        
        // Not in cache, fetch from network
        return fetch(request)
          .then(networkResponse => {
            // Don't cache non-successful responses
            if (!networkResponse || networkResponse.status !== 200 || networkResponse.type !== 'basic') {
              return networkResponse;
            }
            
            // Clone the response for caching
            const responseToCache = networkResponse.clone();
            
            // Cache dynamic content
            caches.open(DYNAMIC_CACHE)
              .then(cache => {
                console.log('[SW] Caching dynamic content:', request.url);
                cache.put(request, responseToCache);
              });
            
            return networkResponse;
          })
          .catch(error => {
            console.log('[SW] Network fetch failed:', error);
            
            // Return offline fallback for HTML pages
            if (request.headers.get('accept').includes('text/html')) {
              return caches.match('./index.html');
            }
            
            // Return a generic offline response for other requests
            return new Response('Offline', {
              status: 503,
              statusText: 'Service Unavailable',
              headers: new Headers({
                'Content-Type': 'text/plain'
              })
            });
          });
      })
  );
});

// Background sync for offline data synchronization
self.addEventListener('sync', event => {
  console.log('[SW] Background sync triggered:', event.tag);
  
  if (event.tag === 'sync-transactions') {
    event.waitUntil(syncTransactions());
  }
  
  if (event.tag === 'sync-goals') {
    event.waitUntil(syncGoals());
  }
});

// Push notification handling
self.addEventListener('push', event => {
  console.log('[SW] Push notification received');
  
  const options = {
    body: event.data ? event.data.text() : 'New financial update available',
    icon: './assets/icon-192.png',
    badge: './assets/icon-72.png',
    vibrate: [200, 100, 200],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'view',
        title: 'View Details',
        icon: './assets/icon-72.png'
      },
      {
        action: 'close',
        title: 'Close',
        icon: './assets/icon-72.png'
      }
    ]
  };
  
  event.waitUntil(
    self.registration.showNotification('FinTrack', options)
  );
});

// Notification click handling
self.addEventListener('notificationclick', event => {
  console.log('[SW] Notification clicked:', event.action);
  
  event.notification.close();
  
  if (event.action === 'view') {
    event.waitUntil(
      clients.openWindow('./')
    );
  }
});

// Message handling from main thread
self.addEventListener('message', event => {
  console.log('[SW] Message received:', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'GET_VERSION') {
    event.ports[0].postMessage({ version: CACHE_NAME });
  }
  
  if (event.data && event.data.type === 'CLEAR_CACHE') {
    event.waitUntil(
      caches.keys().then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => caches.delete(cacheName))
        );
      })
    );
  }
});

// Utility functions for background sync
async function syncTransactions() {
  try {
    console.log('[SW] Syncing transactions...');
    
    // Get pending transactions from IndexedDB
    const pendingTransactions = await getPendingTransactions();
    
    if (pendingTransactions.length === 0) {
      console.log('[SW] No pending transactions to sync');
      return;
    }
    
    // Sync each transaction
    for (const transaction of pendingTransactions) {
      try {
        // In a real app, this would sync with a backend API
        await syncTransaction(transaction);
        await markTransactionSynced(transaction.id);
        console.log('[SW] Transaction synced:', transaction.id);
      } catch (error) {
        console.error('[SW] Failed to sync transaction:', transaction.id, error);
      }
    }
    
    // Notify the main thread about successful sync
    const clients = await self.clients.matchAll();
    clients.forEach(client => {
      client.postMessage({
        type: 'SYNC_COMPLETE',
        data: { type: 'transactions', count: pendingTransactions.length }
      });
    });
    
  } catch (error) {
    console.error('[SW] Error during transaction sync:', error);
  }
}

async function syncGoals() {
  try {
    console.log('[SW] Syncing goals...');
    
    // Similar implementation for goals
    const pendingGoals = await getPendingGoals();
    
    if (pendingGoals.length === 0) {
      console.log('[SW] No pending goals to sync');
      return;
    }
    
    for (const goal of pendingGoals) {
      try {
        await syncGoal(goal);
        await markGoalSynced(goal.id);
        console.log('[SW] Goal synced:', goal.id);
      } catch (error) {
        console.error('[SW] Failed to sync goal:', goal.id, error);
      }
    }
    
    const clients = await self.clients.matchAll();
    clients.forEach(client => {
      client.postMessage({
        type: 'SYNC_COMPLETE',
        data: { type: 'goals', count: pendingGoals.length }
      });
    });
    
  } catch (error) {
    console.error('[SW] Error during goal sync:', error);
  }
}

// Placeholder functions for database operations
// In a real implementation, these would interact with IndexedDB
async function getPendingTransactions() {
  // Return mock data for now
  return [];
}

async function getPendingGoals() {
  // Return mock data for now
  return [];
}

async function syncTransaction(transaction) {
  // Mock sync operation
  return new Promise(resolve => setTimeout(resolve, 100));
}

async function syncGoal(goal) {
  // Mock sync operation
  return new Promise(resolve => setTimeout(resolve, 100));
}

async function markTransactionSynced(id) {
  // Mock database update
  return new Promise(resolve => setTimeout(resolve, 50));
}

async function markGoalSynced(id) {
  // Mock database update
  return new Promise(resolve => setTimeout(resolve, 50));
}

// Periodic background sync (if supported)
self.addEventListener('periodicsync', event => {
  console.log('[SW] Periodic sync triggered:', event.tag);
  
  if (event.tag === 'daily-sync') {
    event.waitUntil(performDailySync());
  }
});

async function performDailySync() {
  console.log('[SW] Performing daily sync...');
  
  try {
    await syncTransactions();
    await syncGoals();
    
    // Clean up old cached data
    await cleanupOldCache();
    
    console.log('[SW] Daily sync completed successfully');
  } catch (error) {
    console.error('[SW] Daily sync failed:', error);
  }
}

async function cleanupOldCache() {
  const cache = await caches.open(DYNAMIC_CACHE);
  const requests = await cache.keys();
  
  // Remove cached items older than 7 days
  const oneWeekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
  
  for (const request of requests) {
    const response = await cache.match(request);
    const dateHeader = response.headers.get('date');
    
    if (dateHeader) {
      const responseDate = new Date(dateHeader).getTime();
      if (responseDate < oneWeekAgo) {
        await cache.delete(request);
        console.log('[SW] Removed old cached item:', request.url);
      }
    }
  }
}
