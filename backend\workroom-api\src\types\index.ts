/**
 * Comprehensive TypeScript type definitions for Enterprise Collaborative Workspace
 * Advanced team collaboration platform with real-time features, whiteboard, task management, and more
 */

// Core workspace interfaces
export interface Workspace {
  id: string;
  name: string;
  description?: string;
  ownerId: string;
  members: WorkspaceMember[];
  rooms: Room[];
  settings: WorkspaceSettings;
  subscription: SubscriptionPlan;
  usage: WorkspaceUsage;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
}

export interface WorkspaceMember {
  userId: string;
  username: string;
  email: string;
  role: WorkspaceRole;
  permissions: Permission[];
  joinedAt: Date;
  lastActiveAt: Date;
  isOnline: boolean;
  avatar?: string;
  status: UserStatus;
  customStatus?: string;
}

export interface WorkspaceSettings {
  isPublic: boolean;
  allowGuestAccess: boolean;
  maxMembers: number;
  defaultRoomPermissions: Permission[];
  integrations: Integration[];
  notifications: NotificationSettings;
  security: SecuritySettings;
  branding: BrandingSettings;
}

// Room and collaboration interfaces
export interface Room {
  id: string;
  workspaceId: string;
  name: string;
  description?: string;
  type: RoomType;
  isPrivate: boolean;
  password?: string;
  ownerId: string;
  members: RoomMember[];
  maxMembers: number;
  settings: RoomSettings;
  whiteboard?: Whiteboard;
  chat: ChatMessage[];
  tasks: Task[];
  files: FileAttachment[];
  meetings: Meeting[];
  createdAt: Date;
  updatedAt: Date;
  lastActivityAt: Date;
  isActive: boolean;
}

export interface RoomMember {
  userId: string;
  username: string;
  role: RoomRole;
  permissions: Permission[];
  joinedAt: Date;
  lastSeenAt: Date;
  isOnline: boolean;
  cursor?: CursorPosition;
  selection?: Selection;
  status: ParticipantStatus;
}

export interface RoomSettings {
  allowScreenShare: boolean;
  allowVideoCall: boolean;
  allowFileUpload: boolean;
  allowWhiteboard: boolean;
  allowTaskManagement: boolean;
  maxFileSize: number; // in MB
  allowedFileTypes: string[];
  autoSaveInterval: number; // in seconds
  sessionTimeout: number; // in minutes
  moderationEnabled: boolean;
}

// Real-time communication interfaces
export interface ChatMessage {
  id: string;
  roomId: string;
  userId: string;
  username: string;
  content: string;
  type: MessageType;
  attachments: MessageAttachment[];
  mentions: string[]; // user IDs
  reactions: MessageReaction[];
  replyTo?: string; // message ID
  isEdited: boolean;
  editedAt?: Date;
  isDeleted: boolean;
  deletedAt?: Date;
  timestamp: Date;
  metadata: MessageMetadata;
}

export interface MessageAttachment {
  id: string;
  type: AttachmentType;
  name: string;
  url: string;
  size: number;
  mimeType: string;
  thumbnail?: string;
  dimensions?: { width: number; height: number };
}

export interface MessageReaction {
  emoji: string;
  users: string[]; // user IDs
  count: number;
}

export interface MessageMetadata {
  deviceType: 'desktop' | 'tablet' | 'mobile';
  browserInfo: string;
  ipAddress?: string;
  location?: string;
}

// Whiteboard and drawing interfaces
export interface Whiteboard {
  id: string;
  roomId: string;
  name: string;
  canvas: CanvasData;
  layers: WhiteboardLayer[];
  tools: DrawingTool[];
  history: DrawingAction[];
  collaborators: WhiteboardCollaborator[];
  settings: WhiteboardSettings;
  createdAt: Date;
  updatedAt: Date;
  version: number;
}

export interface CanvasData {
  width: number;
  height: number;
  backgroundColor: string;
  zoom: number;
  panX: number;
  panY: number;
  objects: CanvasObject[];
}

export interface CanvasObject {
  id: string;
  type: ObjectType;
  x: number;
  y: number;
  width: number;
  height: number;
  rotation: number;
  scaleX: number;
  scaleY: number;
  fill: string;
  stroke: string;
  strokeWidth: number;
  opacity: number;
  visible: boolean;
  selectable: boolean;
  data: any; // object-specific data
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface WhiteboardLayer {
  id: string;
  name: string;
  visible: boolean;
  locked: boolean;
  opacity: number;
  objects: string[]; // object IDs
  order: number;
}

export interface DrawingTool {
  type: ToolType;
  settings: ToolSettings;
  isActive: boolean;
}

export interface ToolSettings {
  color: string;
  strokeWidth: number;
  opacity: number;
  fontSize?: number;
  fontFamily?: string;
  fillColor?: string;
  lineDash?: number[];
  arrowHead?: boolean;
}

export interface DrawingAction {
  id: string;
  type: ActionType;
  userId: string;
  timestamp: Date;
  data: any;
  canUndo: boolean;
  canRedo: boolean;
}

export interface WhiteboardCollaborator {
  userId: string;
  username: string;
  cursor: CursorPosition;
  selection: Selection;
  tool: ToolType;
  color: string; // user's cursor color
  isActive: boolean;
  lastActivity: Date;
}

// Task management interfaces
export interface Task {
  id: string;
  roomId: string;
  workspaceId: string;
  title: string;
  description?: string;
  status: TaskStatus;
  priority: TaskPriority;
  assignees: string[]; // user IDs
  createdBy: string;
  dueDate?: Date;
  startDate?: Date;
  completedDate?: Date;
  estimatedHours?: number;
  actualHours?: number;
  tags: string[];
  attachments: FileAttachment[];
  comments: TaskComment[];
  subtasks: SubTask[];
  dependencies: TaskDependency[];
  checklist: ChecklistItem[];
  customFields: CustomField[];
  createdAt: Date;
  updatedAt: Date;
  completedBy?: string;
}

export interface SubTask {
  id: string;
  title: string;
  isCompleted: boolean;
  assignee?: string;
  dueDate?: Date;
  completedAt?: Date;
  completedBy?: string;
}

export interface TaskComment {
  id: string;
  userId: string;
  username: string;
  content: string;
  attachments: FileAttachment[];
  timestamp: Date;
  isEdited: boolean;
  editedAt?: Date;
}

export interface TaskDependency {
  taskId: string;
  type: DependencyType;
  description?: string;
}

export interface ChecklistItem {
  id: string;
  text: string;
  isCompleted: boolean;
  completedBy?: string;
  completedAt?: Date;
}

export interface CustomField {
  id: string;
  name: string;
  type: FieldType;
  value: any;
  isRequired: boolean;
}

// File management interfaces
export interface FileAttachment {
  id: string;
  name: string;
  originalName: string;
  path: string;
  url: string;
  size: number;
  mimeType: string;
  uploadedBy: string;
  uploadedAt: Date;
  thumbnail?: string;
  metadata: FileMetadata;
  isPublic: boolean;
  downloadCount: number;
  lastAccessedAt?: Date;
}

export interface FileMetadata {
  dimensions?: { width: number; height: number };
  duration?: number; // for video/audio files
  pages?: number; // for documents
  encoding?: string;
  bitrate?: number;
  sampleRate?: number;
}

// Meeting and video call interfaces
export interface Meeting {
  id: string;
  roomId: string;
  title: string;
  description?: string;
  hostId: string;
  participants: MeetingParticipant[];
  scheduledStart: Date;
  scheduledEnd: Date;
  actualStart?: Date;
  actualEnd?: Date;
  status: MeetingStatus;
  type: MeetingType;
  settings: MeetingSettings;
  recording?: MeetingRecording;
  agenda: AgendaItem[];
  notes: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface MeetingParticipant {
  userId: string;
  username: string;
  role: ParticipantRole;
  joinedAt?: Date;
  leftAt?: Date;
  duration: number; // in seconds
  isMuted: boolean;
  isVideoOn: boolean;
  isScreenSharing: boolean;
  isHandRaised: boolean;
  status: ParticipantStatus;
}

export interface MeetingSettings {
  allowRecording: boolean;
  allowScreenShare: boolean;
  allowChat: boolean;
  requirePassword: boolean;
  password?: string;
  waitingRoom: boolean;
  muteOnJoin: boolean;
  maxParticipants: number;
}

export interface MeetingRecording {
  id: string;
  url: string;
  duration: number;
  size: number;
  format: string;
  quality: string;
  startTime: Date;
  endTime: Date;
  isProcessing: boolean;
  isAvailable: boolean;
}

export interface AgendaItem {
  id: string;
  title: string;
  description?: string;
  duration: number; // in minutes
  presenter?: string;
  order: number;
  isCompleted: boolean;
}

// User and authentication interfaces
export interface User {
  id: string;
  username: string;
  email: string;
  passwordHash: string;
  profile: UserProfile;
  preferences: UserPreferences;
  workspaces: string[]; // workspace IDs
  sessions: UserSession[];
  notifications: Notification[];
  integrations: UserIntegration[];
  subscription: UserSubscription;
  usage: UserUsage;
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt: Date;
  isActive: boolean;
  isVerified: boolean;
}

export interface UserProfile {
  firstName: string;
  lastName: string;
  displayName: string;
  avatar?: string;
  bio?: string;
  title?: string;
  company?: string;
  location?: string;
  timezone: string;
  language: string;
  phoneNumber?: string;
  socialLinks: SocialLink[];
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  notifications: NotificationPreferences;
  privacy: PrivacySettings;
  accessibility: AccessibilitySettings;
  shortcuts: KeyboardShortcut[];
}

export interface UserSession {
  id: string;
  deviceInfo: string;
  ipAddress: string;
  location?: string;
  isActive: boolean;
  createdAt: Date;
  lastActivityAt: Date;
  expiresAt: Date;
}

// Notification interfaces
export interface Notification {
  id: string;
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  data: any;
  isRead: boolean;
  readAt?: Date;
  createdAt: Date;
  expiresAt?: Date;
  priority: NotificationPriority;
  channels: NotificationChannel[];
}

export interface NotificationSettings {
  email: boolean;
  push: boolean;
  inApp: boolean;
  desktop: boolean;
  mobile: boolean;
  frequency: NotificationFrequency;
  quietHours: QuietHours;
}

export interface NotificationPreferences {
  mentions: boolean;
  directMessages: boolean;
  taskAssignments: boolean;
  taskDeadlines: boolean;
  meetingReminders: boolean;
  fileShares: boolean;
  workspaceInvites: boolean;
  systemUpdates: boolean;
}

// Real-time interfaces
export interface RealTimeEvent {
  type: EventType;
  roomId: string;
  userId: string;
  data: any;
  timestamp: Date;
  id: string;
}

export interface CursorPosition {
  x: number;
  y: number;
  userId: string;
  username: string;
  color: string;
  timestamp: Date;
}

export interface Selection {
  objectIds: string[];
  bounds: BoundingBox;
  userId: string;
  timestamp: Date;
}

export interface BoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
}

// API request/response interfaces
export interface CreateWorkspaceRequest {
  name: string;
  description?: string;
  isPublic?: boolean;
  maxMembers?: number;
}

export interface CreateRoomRequest {
  workspaceId: string;
  name: string;
  description?: string;
  type: RoomType;
  isPrivate?: boolean;
  password?: string;
  maxMembers?: number;
}

export interface JoinRoomRequest {
  roomId: string;
  password?: string;
}

export interface SendMessageRequest {
  roomId: string;
  content: string;
  type?: MessageType;
  replyTo?: string;
  mentions?: string[];
}

export interface CreateTaskRequest {
  roomId: string;
  title: string;
  description?: string;
  priority?: TaskPriority;
  assignees?: string[];
  dueDate?: string;
  tags?: string[];
}

export interface UpdateWhiteboardRequest {
  roomId: string;
  action: DrawingAction;
}

// Enum types
export type WorkspaceRole = 'owner' | 'admin' | 'member' | 'guest';
export type RoomRole = 'host' | 'moderator' | 'participant' | 'observer';
export type RoomType = 'general' | 'project' | 'meeting' | 'brainstorm' | 'presentation' | 'custom';
export type UserStatus = 'online' | 'away' | 'busy' | 'offline';
export type ParticipantStatus = 'active' | 'idle' | 'away' | 'disconnected';
export type MessageType = 'text' | 'file' | 'image' | 'video' | 'audio' | 'system' | 'announcement';
export type AttachmentType = 'image' | 'video' | 'audio' | 'document' | 'archive' | 'other';
export type ObjectType = 'rectangle' | 'circle' | 'line' | 'arrow' | 'text' | 'image' | 'path' | 'group';
export type ToolType = 'select' | 'pen' | 'brush' | 'eraser' | 'text' | 'rectangle' | 'circle' | 'line' | 'arrow';
export type ActionType = 'create' | 'update' | 'delete' | 'move' | 'resize' | 'rotate' | 'style';
export type TaskStatus = 'todo' | 'in-progress' | 'review' | 'done' | 'cancelled';
export type TaskPriority = 'low' | 'medium' | 'high' | 'urgent';
export type DependencyType = 'blocks' | 'blocked-by' | 'related';
export type FieldType = 'text' | 'number' | 'date' | 'boolean' | 'select' | 'multi-select';
export type MeetingStatus = 'scheduled' | 'active' | 'ended' | 'cancelled';
export type MeetingType = 'video' | 'audio' | 'screen-share' | 'presentation';
export type ParticipantRole = 'host' | 'co-host' | 'participant' | 'observer';
export type NotificationType = 'mention' | 'message' | 'task' | 'meeting' | 'file' | 'system';
export type NotificationPriority = 'low' | 'medium' | 'high' | 'urgent';
export type NotificationChannel = 'email' | 'push' | 'in-app' | 'desktop' | 'sms';
export type NotificationFrequency = 'immediate' | 'hourly' | 'daily' | 'weekly';
export type EventType = 'join' | 'leave' | 'message' | 'draw' | 'cursor' | 'task' | 'file' | 'meeting';
export type SubscriptionPlan = 'free' | 'pro' | 'team' | 'enterprise';
export type Permission = 'read' | 'write' | 'admin' | 'invite' | 'delete' | 'moderate';

// Configuration and settings interfaces
export interface SecuritySettings {
  requireTwoFactor: boolean;
  allowGuestAccess: boolean;
  sessionTimeout: number;
  passwordPolicy: PasswordPolicy;
  ipWhitelist: string[];
  auditLog: boolean;
}

export interface PasswordPolicy {
  minLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSymbols: boolean;
  maxAge: number; // days
}

export interface BrandingSettings {
  logo?: string;
  primaryColor: string;
  secondaryColor: string;
  customCSS?: string;
  favicon?: string;
}

export interface Integration {
  id: string;
  name: string;
  type: string;
  isEnabled: boolean;
  settings: any;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserIntegration {
  integrationId: string;
  settings: any;
  isEnabled: boolean;
  connectedAt: Date;
}

export interface UserSubscription {
  plan: SubscriptionPlan;
  status: 'active' | 'cancelled' | 'expired';
  startDate: Date;
  endDate?: Date;
  features: string[];
}

export interface WorkspaceUsage {
  storageUsed: number; // in bytes
  storageLimit: number;
  membersCount: number;
  membersLimit: number;
  roomsCount: number;
  roomsLimit: number;
  monthlyActiveUsers: number;
}

export interface UserUsage {
  storageUsed: number;
  workspacesCount: number;
  lastActivity: Date;
  totalSessions: number;
  totalTime: number; // in seconds
}

export interface QuietHours {
  enabled: boolean;
  startTime: string; // HH:mm format
  endTime: string;
  timezone: string;
}

export interface SocialLink {
  platform: string;
  url: string;
  isPublic: boolean;
}

export interface PrivacySettings {
  profileVisibility: 'public' | 'workspace' | 'private';
  showOnlineStatus: boolean;
  allowDirectMessages: boolean;
  allowMentions: boolean;
}

export interface AccessibilitySettings {
  highContrast: boolean;
  largeText: boolean;
  reducedMotion: boolean;
  screenReader: boolean;
  keyboardNavigation: boolean;
}

export interface KeyboardShortcut {
  action: string;
  keys: string[];
  isEnabled: boolean;
}
