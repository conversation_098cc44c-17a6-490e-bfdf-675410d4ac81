/**
 * Database service for SQLite operations with connection pooling and migrations
 */

import sqlite3 from 'sqlite3';
import { Database, open } from 'sqlite';
import path from 'path';
import fs from 'fs/promises';
import { logger } from '@/utils/logger';
import { DatabaseConfig, TypingTest, User, TextSample, LeaderboardEntry } from '@/types';
import { MetricsCollector } from '@/middleware/metrics';

export class DatabaseService {
  private db: Database<sqlite3.Database, sqlite3.Statement> | null = null;
  private config: DatabaseConfig;
  private isHealthy: boolean = false;

  constructor(config: DatabaseConfig) {
    this.config = config;
  }

  /**
   * Initialize database connection and run migrations
   */
  async initialize(): Promise<void> {
    try {
      // Ensure database directory exists
      const dbDir = path.dirname(this.config.database);
      await fs.mkdir(dbDir, { recursive: true });

      // Open database connection
      this.db = await open({
        filename: this.config.database,
        driver: sqlite3.Database
      });

      // Enable foreign keys
      await this.db.exec('PRAGMA foreign_keys = ON');
      await this.db.exec('PRAGMA journal_mode = WAL');
      await this.db.exec('PRAGMA synchronous = NORMAL');
      await this.db.exec('PRAGMA cache_size = 1000');
      await this.db.exec('PRAGMA temp_store = MEMORY');

      // Run migrations
      await this.runMigrations();

      this.isHealthy = true;
      logger.info('Database service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize database service:', error);
      throw error;
    }
  }

  /**
   * Run database migrations
   */
  private async runMigrations(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    try {
      // Create migrations table
      await this.db.exec(`
        CREATE TABLE IF NOT EXISTS migrations (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT UNIQUE NOT NULL,
          executed_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Define migrations
      const migrations = [
        {
          name: '001_initial_schema',
          sql: `
            -- Users table
            CREATE TABLE IF NOT EXISTS users (
              id TEXT PRIMARY KEY,
              username TEXT UNIQUE NOT NULL,
              email TEXT UNIQUE,
              password_hash TEXT,
              display_name TEXT NOT NULL,
              avatar TEXT,
              bio TEXT,
              country TEXT,
              timezone TEXT NOT NULL,
              keyboard_layout TEXT NOT NULL,
              experience_level TEXT NOT NULL,
              created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              last_active_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );

            -- Text samples table
            CREATE TABLE IF NOT EXISTS text_samples (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              text TEXT NOT NULL,
              difficulty TEXT NOT NULL,
              category TEXT NOT NULL,
              language TEXT DEFAULT 'en',
              character_count INTEGER NOT NULL,
              word_count INTEGER NOT NULL,
              average_word_length REAL NOT NULL,
              complexity_score REAL NOT NULL,
              common_words INTEGER DEFAULT 0,
              punctuation_count INTEGER DEFAULT 0,
              numbers_count INTEGER DEFAULT 0,
              special_chars_count INTEGER DEFAULT 0,
              source TEXT,
              author TEXT,
              genre TEXT,
              tags TEXT, -- JSON array
              estimated_time INTEGER DEFAULT 0,
              popularity_score REAL DEFAULT 0,
              created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );

            -- Typing tests table
            CREATE TABLE IF NOT EXISTS typing_tests (
              id TEXT PRIMARY KEY,
              user_id TEXT,
              username TEXT NOT NULL,
              text_id INTEGER NOT NULL,
              start_time DATETIME NOT NULL,
              end_time DATETIME NOT NULL,
              duration INTEGER NOT NULL,
              wpm REAL NOT NULL,
              accuracy REAL NOT NULL,
              raw_wpm REAL NOT NULL,
              net_wpm REAL NOT NULL,
              characters_typed INTEGER NOT NULL,
              correct_characters INTEGER NOT NULL,
              incorrect_characters INTEGER NOT NULL,
              total_characters INTEGER NOT NULL,
              errors TEXT, -- JSON array
              keystrokes TEXT, -- JSON array
              difficulty TEXT NOT NULL,
              category TEXT NOT NULL,
              metadata TEXT, -- JSON object
              created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
              FOREIGN KEY (text_id) REFERENCES text_samples(id) ON DELETE CASCADE
            );

            -- User statistics table
            CREATE TABLE IF NOT EXISTS user_statistics (
              user_id TEXT PRIMARY KEY,
              total_tests INTEGER DEFAULT 0,
              total_time INTEGER DEFAULT 0,
              total_characters INTEGER DEFAULT 0,
              average_wpm REAL DEFAULT 0,
              best_wpm REAL DEFAULT 0,
              average_accuracy REAL DEFAULT 0,
              best_accuracy REAL DEFAULT 0,
              improvement_rate REAL DEFAULT 0,
              consistency_score REAL DEFAULT 0,
              streak_days INTEGER DEFAULT 0,
              longest_streak INTEGER DEFAULT 0,
              favorite_category TEXT,
              weakest_keys TEXT, -- JSON array
              strongest_keys TEXT, -- JSON array
              peak_performance_hour INTEGER DEFAULT 12,
              updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            );

            -- User preferences table
            CREATE TABLE IF NOT EXISTS user_preferences (
              user_id TEXT PRIMARY KEY,
              theme TEXT DEFAULT 'light',
              sound_enabled BOOLEAN DEFAULT 1,
              show_live_wpm BOOLEAN DEFAULT 1,
              show_live_accuracy BOOLEAN DEFAULT 1,
              highlight_errors BOOLEAN DEFAULT 1,
              stop_on_error BOOLEAN DEFAULT 0,
              font_size INTEGER DEFAULT 16,
              font_family TEXT DEFAULT 'monospace',
              caret_style TEXT DEFAULT 'line',
              keyboard_sounds BOOLEAN DEFAULT 0,
              notifications TEXT, -- JSON object
              updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            );

            -- Achievements table
            CREATE TABLE IF NOT EXISTS achievements (
              id TEXT PRIMARY KEY,
              name TEXT NOT NULL,
              description TEXT NOT NULL,
              icon TEXT NOT NULL,
              category TEXT NOT NULL,
              difficulty TEXT NOT NULL,
              requirements TEXT NOT NULL, -- JSON array
              reward TEXT NOT NULL, -- JSON object
              created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );

            -- User achievements table
            CREATE TABLE IF NOT EXISTS user_achievements (
              user_id TEXT,
              achievement_id TEXT,
              progress REAL DEFAULT 0,
              unlocked_at DATETIME,
              created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              PRIMARY KEY (user_id, achievement_id),
              FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
              FOREIGN KEY (achievement_id) REFERENCES achievements(id) ON DELETE CASCADE
            );

            -- Sessions table for real-time tracking
            CREATE TABLE IF NOT EXISTS sessions (
              id TEXT PRIMARY KEY,
              user_id TEXT,
              username TEXT NOT NULL,
              is_active BOOLEAN DEFAULT 1,
              current_test_data TEXT, -- JSON object
              last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
              created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
            );

            -- Create indexes for better performance
            CREATE INDEX IF NOT EXISTS idx_typing_tests_user_id ON typing_tests(user_id);
            CREATE INDEX IF NOT EXISTS idx_typing_tests_created_at ON typing_tests(created_at);
            CREATE INDEX IF NOT EXISTS idx_typing_tests_wpm ON typing_tests(wpm);
            CREATE INDEX IF NOT EXISTS idx_typing_tests_accuracy ON typing_tests(accuracy);
            CREATE INDEX IF NOT EXISTS idx_typing_tests_difficulty ON typing_tests(difficulty);
            CREATE INDEX IF NOT EXISTS idx_typing_tests_category ON typing_tests(category);
            CREATE INDEX IF NOT EXISTS idx_text_samples_difficulty ON text_samples(difficulty);
            CREATE INDEX IF NOT EXISTS idx_text_samples_category ON text_samples(category);
            CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
            CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
            CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id);
            CREATE INDEX IF NOT EXISTS idx_sessions_is_active ON sessions(is_active);
          `
        }
      ];

      // Execute migrations
      for (const migration of migrations) {
        const existing = await this.db.get(
          'SELECT name FROM migrations WHERE name = ?',
          migration.name
        );

        if (!existing) {
          logger.info(`Running migration: ${migration.name}`);
          await this.db.exec(migration.sql);
          await this.db.run(
            'INSERT INTO migrations (name) VALUES (?)',
            migration.name
          );
          logger.info(`Migration completed: ${migration.name}`);
        }
      }

      logger.info('All migrations completed successfully');
    } catch (error) {
      logger.error('Migration failed:', error);
      throw error;
    }
  }

  /**
   * Execute a query with metrics tracking
   */
  private async executeQuery<T>(
    operation: 'select' | 'insert' | 'update' | 'delete',
    table: string,
    query: string,
    params?: any[]
  ): Promise<T> {
    if (!this.db) throw new Error('Database not initialized');

    const startTime = Date.now();
    try {
      const result = await this.db.all(query, params) as T;
      const duration = Date.now() - startTime;
      
      MetricsCollector.recordDatabaseOperation(operation, table, 'success');
      logger.database(operation, table, duration);
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      MetricsCollector.recordDatabaseOperation(operation, table, 'error');
      logger.database(operation, table, duration, error);
      throw error;
    }
  }

  /**
   * Insert a typing test
   */
  async insertTypingTest(test: TypingTest): Promise<void> {
    const query = `
      INSERT INTO typing_tests (
        id, user_id, username, text_id, start_time, end_time, duration,
        wpm, accuracy, raw_wpm, net_wpm, characters_typed, correct_characters,
        incorrect_characters, total_characters, errors, keystrokes,
        difficulty, category, metadata
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [
      test.id, test.userId, test.username, test.textId,
      test.startTime.toISOString(), test.endTime.toISOString(), test.duration,
      test.wpm, test.accuracy, test.rawWpm, test.netWpm,
      test.charactersTyped, test.correctCharacters, test.incorrectCharacters,
      test.totalCharacters, JSON.stringify(test.errors), JSON.stringify(test.keystrokes),
      test.difficulty, test.category, JSON.stringify(test.metadata)
    ];

    await this.executeQuery('insert', 'typing_tests', query, params);
  }

  /**
   * Get leaderboard entries
   */
  async getLeaderboard(
    category?: string,
    difficulty?: string,
    timeframe: string = 'all-time',
    limit: number = 50,
    offset: number = 0
  ): Promise<LeaderboardEntry[]> {
    let whereClause = '';
    const params: any[] = [];

    if (category) {
      whereClause += ' AND category = ?';
      params.push(category);
    }

    if (difficulty) {
      whereClause += ' AND difficulty = ?';
      params.push(difficulty);
    }

    if (timeframe !== 'all-time') {
      const timeMap = {
        'daily': '1 day',
        'weekly': '7 days',
        'monthly': '30 days'
      };
      whereClause += ` AND created_at >= datetime('now', '-${timeMap[timeframe as keyof typeof timeMap]}')`;
    }

    const query = `
      SELECT 
        ROW_NUMBER() OVER (ORDER BY MAX(wpm) DESC) as rank,
        user_id,
        username,
        MAX(wpm) as wpm,
        AVG(accuracy) as accuracy,
        COUNT(*) as tests_completed,
        AVG(wpm) as average_wpm,
        MAX(wpm) as best_wpm,
        MAX(created_at) as last_test_date
      FROM typing_tests 
      WHERE 1=1 ${whereClause}
      GROUP BY user_id, username
      ORDER BY MAX(wpm) DESC
      LIMIT ? OFFSET ?
    `;

    params.push(limit, offset);
    return await this.executeQuery('select', 'typing_tests', query, params);
  }

  /**
   * Get text samples
   */
  async getTextSamples(
    difficulty?: string,
    category?: string,
    limit: number = 20
  ): Promise<TextSample[]> {
    let whereClause = '';
    const params: any[] = [];

    if (difficulty) {
      whereClause += ' AND difficulty = ?';
      params.push(difficulty);
    }

    if (category) {
      whereClause += ' AND category = ?';
      params.push(category);
    }

    const query = `
      SELECT * FROM text_samples 
      WHERE 1=1 ${whereClause}
      ORDER BY popularity_score DESC, RANDOM()
      LIMIT ?
    `;

    params.push(limit);
    return await this.executeQuery('select', 'text_samples', query, params);
  }

  /**
   * Health check
   */
  isHealthy(): boolean {
    return this.isHealthy && this.db !== null;
  }

  /**
   * Close database connection
   */
  async close(): Promise<void> {
    if (this.db) {
      await this.db.close();
      this.db = null;
      this.isHealthy = false;
      logger.info('Database connection closed');
    }
  }
}
