/**
 * Request logging middleware for collaborative workspace API
 * Comprehensive request/response logging with performance tracking and security monitoring
 */

import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '@/utils/logger';

// Extend Request interface to include custom properties
declare global {
  namespace Express {
    interface Request {
      requestId: string;
      startTime: number;
      userId?: string;
      workspaceId?: string;
      roomId?: string;
    }
  }
}

/**
 * Request logger middleware
 */
export const requestLogger = (req: Request, res: Response, next: NextFunction): void => {
  // Generate unique request ID
  req.requestId = uuidv4();
  req.startTime = Date.now();

  // Extract user information from JWT token if available
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    try {
      // This would be populated by auth middleware later
      // For now, we'll extract it from the token if needed
    } catch (error) {
      // Ignore token parsing errors in logger
    }
  }

  // Extract workspace and room IDs from request
  req.workspaceId = req.params.workspaceId || req.body.workspaceId || req.query.workspaceId as string;
  req.roomId = req.params.roomId || req.body.roomId || req.query.roomId as string;

  // Log incoming request
  const requestInfo = {
    requestId: req.requestId,
    method: req.method,
    url: req.originalUrl,
    path: req.path,
    query: sanitizeQuery(req.query),
    headers: sanitizeHeaders(req.headers),
    userAgent: req.get('User-Agent'),
    ip: getClientIP(req),
    userId: req.userId,
    workspaceId: req.workspaceId,
    roomId: req.roomId,
    contentLength: req.get('Content-Length'),
    contentType: req.get('Content-Type'),
    timestamp: new Date().toISOString()
  };

  logger.http('Incoming request', requestInfo);

  // Override res.json to log response
  const originalJson = res.json;
  res.json = function(body: any) {
    const duration = Date.now() - req.startTime;
    
    const responseInfo = {
      requestId: req.requestId,
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      contentLength: res.get('Content-Length'),
      userId: req.userId,
      workspaceId: req.workspaceId,
      roomId: req.roomId,
      timestamp: new Date().toISOString()
    };

    // Log response based on status code
    if (res.statusCode >= 500) {
      logger.error('Server error response', {
        ...responseInfo,
        error: sanitizeResponseBody(body)
      });
    } else if (res.statusCode >= 400) {
      logger.warn('Client error response', {
        ...responseInfo,
        error: sanitizeResponseBody(body)
      });
    } else {
      logger.http('Successful response', responseInfo);
    }

    // Log slow requests
    if (duration > 1000) {
      logger.warn('Slow request detected', {
        ...responseInfo,
        performance: 'slow',
        threshold: '1000ms'
      });
    }

    return originalJson.call(this, body);
  };

  // Override res.send to log response
  const originalSend = res.send;
  res.send = function(body: any) {
    const duration = Date.now() - req.startTime;
    
    const responseInfo = {
      requestId: req.requestId,
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      contentLength: res.get('Content-Length'),
      userId: req.userId,
      workspaceId: req.workspaceId,
      roomId: req.roomId,
      timestamp: new Date().toISOString()
    };

    if (res.statusCode >= 400) {
      logger.warn('Response sent', responseInfo);
    } else {
      logger.http('Response sent', responseInfo);
    }

    return originalSend.call(this, body);
  };

  next();
};

/**
 * Sanitize query parameters for logging
 */
function sanitizeQuery(query: any): any {
  const sanitized = { ...query };
  const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth'];
  
  for (const field of sensitiveFields) {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]';
    }
  }
  
  return sanitized;
}

/**
 * Sanitize headers for logging
 */
function sanitizeHeaders(headers: any): any {
  const sanitized = { ...headers };
  const sensitiveHeaders = [
    'authorization',
    'cookie',
    'x-api-key',
    'x-auth-token',
    'x-access-token'
  ];
  
  for (const header of sensitiveHeaders) {
    if (sanitized[header]) {
      sanitized[header] = '[REDACTED]';
    }
  }
  
  return sanitized;
}

/**
 * Sanitize response body for logging
 */
function sanitizeResponseBody(body: any): any {
  if (!body || typeof body !== 'object') {
    return body;
  }
  
  const sanitized = { ...body };
  const sensitiveFields = ['password', 'token', 'secret', 'key', 'hash'];
  
  for (const field of sensitiveFields) {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]';
    }
  }
  
  return sanitized;
}

/**
 * Get client IP address
 */
function getClientIP(req: Request): string {
  return (
    req.headers['x-forwarded-for'] as string ||
    req.headers['x-real-ip'] as string ||
    req.connection.remoteAddress ||
    req.socket.remoteAddress ||
    'unknown'
  );
}

/**
 * Request timing middleware for performance monitoring
 */
export const requestTiming = (req: Request, res: Response, next: NextFunction): void => {
  const start = process.hrtime.bigint();
  
  res.on('finish', () => {
    const end = process.hrtime.bigint();
    const duration = Number(end - start) / 1000000; // Convert to milliseconds
    
    // Log performance metrics
    logger.performance('Request completed', duration, {
      requestId: req.requestId,
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      userId: req.userId,
      workspaceId: req.workspaceId,
      roomId: req.roomId
    });
    
    // Alert on very slow requests
    if (duration > 5000) {
      logger.warn('Very slow request detected', {
        requestId: req.requestId,
        method: req.method,
        url: req.originalUrl,
        duration: `${duration}ms`,
        threshold: '5000ms',
        userId: req.userId
      });
    }
  });
  
  next();
};

/**
 * Request correlation middleware for distributed tracing
 */
export const requestCorrelation = (req: Request, res: Response, next: NextFunction): void => {
  // Use existing request ID or generate new one
  const correlationId = req.headers['x-correlation-id'] as string || req.requestId || uuidv4();
  
  // Set correlation ID in request and response headers
  req.requestId = correlationId;
  res.setHeader('X-Correlation-ID', correlationId);
  
  next();
};

/**
 * Security logging middleware
 */
export const securityLogger = (req: Request, res: Response, next: NextFunction): void => {
  const suspiciousPatterns = [
    /\.\.\//,  // Path traversal
    /<script/i, // XSS attempts
    /union.*select/i, // SQL injection
    /javascript:/i, // JavaScript injection
    /eval\(/i, // Code injection
  ];
  
  const requestData = JSON.stringify({
    url: req.originalUrl,
    query: req.query,
    body: req.body
  });
  
  // Check for suspicious patterns
  for (const pattern of suspiciousPatterns) {
    if (pattern.test(requestData)) {
      logger.security('Suspicious request detected', req.userId, getClientIP(req), {
        requestId: req.requestId,
        method: req.method,
        url: req.originalUrl,
        pattern: pattern.toString(),
        userAgent: req.get('User-Agent'),
        referer: req.get('Referer')
      });
      break;
    }
  }
  
  // Log authentication attempts
  if (req.path.includes('/auth/')) {
    logger.security('Authentication attempt', req.body?.email || req.body?.username, getClientIP(req), {
      requestId: req.requestId,
      endpoint: req.path,
      userAgent: req.get('User-Agent')
    });
  }
  
  next();
};

/**
 * Rate limiting logger
 */
export const rateLimitLogger = (req: Request, res: Response, next: NextFunction): void => {
  // This would be called by rate limiting middleware when limits are hit
  const remaining = res.getHeader('X-RateLimit-Remaining');
  const limit = res.getHeader('X-RateLimit-Limit');
  
  if (remaining && Number(remaining) < Number(limit) * 0.1) {
    logger.warn('Rate limit approaching', {
      requestId: req.requestId,
      ip: getClientIP(req),
      userId: req.userId,
      remaining,
      limit,
      endpoint: req.originalUrl
    });
  }
  
  next();
};
