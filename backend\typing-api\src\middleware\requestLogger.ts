/**
 * Request logging middleware for comprehensive request/response tracking
 */

import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '@/utils/logger';

// Extend Request interface to include requestId
declare global {
  namespace Express {
    interface Request {
      requestId: string;
      startTime: number;
    }
  }
}

/**
 * Request logger middleware
 * Logs all incoming requests with detailed information
 */
export const requestLogger = (req: Request, res: Response, next: NextFunction): void => {
  // Generate unique request ID
  req.requestId = uuidv4();
  req.startTime = Date.now();

  // Add request ID to response headers
  res.setHeader('X-Request-ID', req.requestId);

  // Log request details
  const requestInfo = {
    requestId: req.requestId,
    method: req.method,
    url: req.originalUrl,
    userAgent: req.get('User-Agent'),
    ip: req.ip || req.connection.remoteAddress,
    timestamp: new Date().toISOString(),
    body: req.method !== 'GET' ? sanitizeBody(req.body) : undefined,
    query: Object.keys(req.query).length > 0 ? req.query : undefined
  };

  logger.info('Incoming request', requestInfo);

  // Override res.json to log response
  const originalJson = res.json;
  res.json = function(body: any) {
    const duration = Date.now() - req.startTime;
    
    const responseInfo = {
      requestId: req.requestId,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      responseSize: JSON.stringify(body).length,
      timestamp: new Date().toISOString()
    };

    // Log response based on status code
    if (res.statusCode >= 400) {
      logger.error('Request completed with error', {
        ...responseInfo,
        error: body.error || body.message
      });
    } else {
      logger.info('Request completed successfully', responseInfo);
    }

    return originalJson.call(this, body);
  };

  // Override res.send to log response for non-JSON responses
  const originalSend = res.send;
  res.send = function(body: any) {
    if (!res.headersSent && res.get('Content-Type')?.includes('application/json')) {
      // Already handled by res.json override
      return originalSend.call(this, body);
    }

    const duration = Date.now() - req.startTime;
    
    const responseInfo = {
      requestId: req.requestId,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      responseSize: typeof body === 'string' ? body.length : 0,
      timestamp: new Date().toISOString()
    };

    if (res.statusCode >= 400) {
      logger.error('Request completed with error', responseInfo);
    } else {
      logger.info('Request completed successfully', responseInfo);
    }

    return originalSend.call(this, body);
  };

  next();
};

/**
 * Sanitize request body for logging (remove sensitive information)
 */
function sanitizeBody(body: any): any {
  if (!body || typeof body !== 'object') {
    return body;
  }

  const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth'];
  const sanitized = { ...body };

  for (const field of sensitiveFields) {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]';
    }
  }

  return sanitized;
}
