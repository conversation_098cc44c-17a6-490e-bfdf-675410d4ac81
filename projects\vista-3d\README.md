# Vista3D - 3D Product Configurator

A cutting-edge 3D product configurator built with three.js and WebGL, enabling real-time visualization and customization of 3D models directly in the browser.

## 🎯 Features

### Core Functionality
- **Real-time 3D Rendering**: High-performance WebGL rendering with smooth 60fps animations
- **Interactive Controls**: Mouse/touch controls for rotation, zoom, and pan
- **Material Customization**: Real-time material and color swapping
- **Model Loading**: Support for GLTF 2.0 models with Draco compression
- **Environment Settings**: Multiple lighting and background environments

### User Interface
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices
- **Dark/Light Theme**: Automatic theme detection with manual toggle
- **Configuration Panel**: Intuitive controls for all customization options
- **Performance Stats**: Real-time FPS and triangle count display
- **Fullscreen Mode**: Immersive viewing experience

### Export & Sharing
- **Screenshot Capture**: Instant viewport screenshots
- **High-Resolution Export**: PNG export up to 4K resolution
- **Custom Dimensions**: User-defined export resolutions
- **Gallery View**: Browse available 3D models

## 🛠️ Technology Stack

- **Three.js**: 3D graphics library for WebGL rendering
- **GLTF 2.0**: Industry-standard 3D model format
- **Draco Compression**: Optimized model loading
- **PBR Materials**: Physically-based rendering for realistic materials
- **ES2022**: Modern JavaScript with modules
- **CSS Custom Properties**: Dynamic theming system
- **Web APIs**: Fullscreen, ResizeObserver, and more

## 🚀 Getting Started

### Prerequisites
- Modern web browser with WebGL support
- Local web server (for CORS compliance)

### Installation
1. Clone or download the project files
2. Serve the files using a local web server:
   ```bash
   # Using Python
   python -m http.server 8000
   
   # Using Node.js
   npx serve .
   
   # Using PHP
   php -S localhost:8000
   ```
3. Open `http://localhost:8000/projects/vista-3d/` in your browser

### Usage
1. **Navigation**: Use mouse/touch to rotate, zoom, and pan the 3D model
2. **Model Selection**: Choose from available 3D models in the configuration panel
3. **Customization**: Change colors, materials, and environment settings
4. **Export**: Take screenshots or export high-resolution PNG images

## 🎨 Customization

### Adding New Models
1. Place GLTF files in `assets/models/` directory
2. Add model configuration to the `models` object in `main.js`:
   ```javascript
   newModel: {
     name: 'New Model',
     path: './assets/models/new-model.glb',
     scale: 1,
     position: { x: 0, y: 0, z: 0 },
     materials: ['material1', 'material2']
   }
   ```
3. Add corresponding UI elements in the HTML

### Material Configuration
Materials are automatically detected from the GLTF model's material groups. You can customize material behavior by modifying the `applyColor` and `toggleMaterialVisibility` methods.

### Environment Settings
Add new environments by:
1. Adding HDR environment maps to `assets/environments/`
2. Updating the `environments` object in `main.js`
3. Adding options to the environment selector

## 📱 Browser Support

- **Chrome**: 88+
- **Firefox**: 85+
- **Safari**: 14+
- **Edge**: 88+

### WebGL Requirements
- WebGL 1.0 or higher
- Hardware acceleration enabled
- Minimum 512MB GPU memory recommended

## 🔧 Configuration

### Performance Settings
The application automatically adjusts quality based on device capabilities:
- **High-end devices**: Full quality with shadows and post-processing
- **Mobile devices**: Optimized quality for smooth performance
- **Low-end devices**: Reduced quality with basic lighting

### Customizable Options
- Camera settings (FOV, near/far planes)
- Lighting intensity and colors
- Shadow quality and resolution
- Render resolution and pixel ratio

## 📊 Performance

### Optimization Features
- **Frustum Culling**: Only render visible objects
- **Level of Detail**: Automatic quality adjustment
- **Texture Compression**: Optimized texture formats
- **Geometry Compression**: Draco compression for models
- **Efficient Rendering**: Minimal draw calls and state changes

### Monitoring
- Real-time FPS counter
- Triangle count display
- Memory usage tracking (in development tools)

## 🎯 Use Cases

- **E-commerce**: Product visualization and customization
- **Architecture**: Interior design and space planning
- **Manufacturing**: Product configuration and prototyping
- **Education**: Interactive 3D learning materials
- **Entertainment**: 3D model showcases and galleries

## 🔮 Future Enhancements

- **Animation System**: Keyframe animations and morphing
- **Physics Integration**: Realistic object interactions
- **VR/AR Support**: Immersive viewing experiences
- **Cloud Rendering**: Server-side rendering for complex scenes
- **Collaborative Features**: Multi-user configuration sessions

## 📄 License

This project is part of Portfolio Eight and is intended for demonstration purposes. Feel free to use and modify for your own projects.

## 🤝 Contributing

This is a portfolio project, but suggestions and improvements are welcome! Please feel free to:
- Report bugs or issues
- Suggest new features
- Submit pull requests
- Share your implementations

## 📞 Support

For questions or support regarding this project, please refer to the main Portfolio Eight repository or contact the developer.

---

**Vista3D** - Bringing 3D product visualization to the web with cutting-edge technology and intuitive design.
