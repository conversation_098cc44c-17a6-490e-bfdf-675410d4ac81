module.exports = {
  ci: {
    collect: {
      url: [
        'http://localhost:3000',
        'http://localhost:3000/projects/skill-sync/',
        'http://localhost:3000/projects/fin-track/',
        'http://localhost:3000/projects/collabo-canvas/',
        'http://localhost:3000/projects/eco-shop/',
        'http://localhost:3000/projects/vista-3d/',
        'http://localhost:3000/projects/clima-cast/'
      ],
      startServerCommand: 'npm run serve',
      startServerReadyPattern: 'Local:',
      startServerReadyTimeout: 30000
    },
    assert: {
      assertions: {
        'categories:performance': ['warn', { minScore: 0.9 }],
        'categories:accessibility': ['error', { minScore: 0.9 }],
        'categories:best-practices': ['warn', { minScore: 0.9 }],
        'categories:seo': ['warn', { minScore: 0.9 }],
        'categories:pwa': 'off'
      }
    },
    upload: {
      target: 'temporary-public-storage'
    }
  }
};
