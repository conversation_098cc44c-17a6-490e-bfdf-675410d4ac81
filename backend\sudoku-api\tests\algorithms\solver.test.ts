/**
 * Comprehensive test suite for SudokuSolver
 * Tests all solving algorithms and edge cases
 */

import { SudokuSolver } from '@/algorithms/solver';
import { SudokuGrid, DifficultyLevel } from '@/types';

describe('SudokuSolver', () => {
  let solver: SudokuSolver;

  // Test puzzles
  const easyPuzzle: SudokuGrid = [
    [5, 3, 0, 0, 7, 0, 0, 0, 0],
    [6, 0, 0, 1, 9, 5, 0, 0, 0],
    [0, 9, 8, 0, 0, 0, 0, 6, 0],
    [8, 0, 0, 0, 6, 0, 0, 0, 3],
    [4, 0, 0, 8, 0, 3, 0, 0, 1],
    [7, 0, 0, 0, 2, 0, 0, 0, 6],
    [0, 6, 0, 0, 0, 0, 2, 8, 0],
    [0, 0, 0, 4, 1, 9, 0, 0, 5],
    [0, 0, 0, 0, 8, 0, 0, 7, 9],
  ];

  const easySolution: SudokuGrid = [
    [5, 3, 4, 6, 7, 8, 9, 1, 2],
    [6, 7, 2, 1, 9, 5, 3, 4, 8],
    [1, 9, 8, 3, 4, 2, 5, 6, 7],
    [8, 5, 9, 7, 6, 1, 4, 2, 3],
    [4, 2, 6, 8, 5, 3, 7, 9, 1],
    [7, 1, 3, 9, 2, 4, 8, 5, 6],
    [9, 6, 1, 5, 3, 7, 2, 8, 4],
    [2, 8, 7, 4, 1, 9, 6, 3, 5],
    [3, 4, 5, 2, 8, 6, 1, 7, 9],
  ];

  const invalidPuzzle: SudokuGrid = [
    [5, 5, 0, 0, 7, 0, 0, 0, 0], // Duplicate 5 in first row
    [6, 0, 0, 1, 9, 5, 0, 0, 0],
    [0, 9, 8, 0, 0, 0, 0, 6, 0],
    [8, 0, 0, 0, 6, 0, 0, 0, 3],
    [4, 0, 0, 8, 0, 3, 0, 0, 1],
    [7, 0, 0, 0, 2, 0, 0, 0, 6],
    [0, 6, 0, 0, 0, 0, 2, 8, 0],
    [0, 0, 0, 4, 1, 9, 0, 0, 5],
    [0, 0, 0, 0, 8, 0, 0, 7, 9],
  ];

  const unsolvablePuzzle: SudokuGrid = [
    [1, 2, 3, 4, 5, 6, 7, 8, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 9],
    [0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0, 0],
  ];

  beforeEach(() => {
    solver = new SudokuSolver();
  });

  describe('constructor', () => {
    it('should create a new SudokuSolver instance', () => {
      expect(solver).toBeInstanceOf(SudokuSolver);
    });

    it('should initialize with default configuration', () => {
      expect(solver).toBeDefined();
    });
  });

  describe('solve()', () => {
    it('should solve an easy puzzle correctly', async () => {
      const result = await solver.solve(easyPuzzle);
      
      expect(result.solved).toBe(true);
      expect(result.solution).toEqual(easySolution);
      expect(result.techniques).toContain('NAKED_SINGLE');
      expect(result.statistics.solvingTime).toBeGreaterThan(0);
    });

    it('should solve with advanced method', async () => {
      const result = await solver.solve(easyPuzzle, { method: 'advanced' });

      expect(result.solved).toBe(true);
      expect(result.solution).toEqual(easySolution);
    });

    it('should solve with logical method', async () => {
      const result = await solver.solve(easyPuzzle, { method: 'logical' });

      expect(result.solved).toBe(true);
      expect(result.solution).toEqual(easySolution);
    });

    it('should solve with backtrack method', async () => {
      const result = await solver.solve(easyPuzzle, { method: 'backtrack' });

      expect(result.solved).toBe(true);
      expect(result.solution).toEqual(easySolution);
      expect(result.techniques).toContain('BACKTRACKING');
    });

    it('should provide step-by-step solution', async () => {
      const result = await solver.solve(easyPuzzle, { stepByStep: true });
      
      expect(result.solved).toBe(true);
      expect(result.steps).toBeDefined();
      expect(result.steps!.length).toBeGreaterThan(0);
      
      result.steps!.forEach((step, index) => {
        expect(step).toHaveProperty('position');
        expect(step).toHaveProperty('value');
        expect(step).toHaveProperty('technique');
        expect(step).toHaveProperty('reasoning');
        expect(step.position.row).toBeGreaterThanOrEqual(0);
        expect(step.position.row).toBeLessThan(9);
        expect(step.position.col).toBeGreaterThanOrEqual(0);
        expect(step.position.col).toBeLessThan(9);
        expect(step.value).toBeGreaterThanOrEqual(1);
        expect(step.value).toBeLessThanOrEqual(9);
      });
    });

    it('should handle unsolvable puzzles', async () => {
      const result = await solver.solve(unsolvablePuzzle);
      
      expect(result.solved).toBe(false);
      expect(result.solution).toBeNull();
      expect(result.reason).toBeDefined();
      expect(result.partialSolution).toBeDefined();
    });

    it('should respect timeout option', async () => {
      const startTime = Date.now();
      const result = await solver.solve(easyPuzzle, { timeout: 1 });
      const endTime = Date.now();
      
      // Should complete quickly for easy puzzle
      expect(endTime - startTime).toBeLessThan(1000);
      expect(result.statistics.iterations).toBeLessThanOrEqual(1);
    });

    it('should assess difficulty correctly', async () => {
      const result = await solver.solve(easyPuzzle);
      expect(['easy', 'medium', 'hard', 'expert', 'evil']).toContain(result.difficulty); 
    });

    it('should provide comprehensive statistics', async () => {
      const result = await solver.solve(easyPuzzle);
      expect(result.statistics).toBeDefined();
      expect(result.statistics.iterations).toBeGreaterThan(0);
      expect(result.statistics.solvingTime).toBeGreaterThan(0);
      expect(result.statistics.logicalSteps).toBeGreaterThanOrEqual(0);
      expect(result.statistics.backtrackCount).toBeGreaterThanOrEqual(0);
    });
  });

  describe('analyzePuzzle()', () => {
    it('should analyze puzzle difficulty and properties', async () => {
      const analysis = await solver.analyzePuzzle(easyPuzzle);
      
      expect(['easy', 'medium', 'hard', 'expert', 'evil']).toContain(analysis.difficulty);
      expect(typeof analysis.solvable).toBe('boolean');
      expect(typeof analysis.uniqueSolution).toBe('boolean');
      expect(analysis.clueCount).toBeGreaterThan(0);
      expect(Array.isArray(analysis.techniques)).toBe(true);
      expect(typeof analysis.estimatedTime).toBe('string');
      expect(typeof analysis.complexity).toBe('number');
    });

    it('should count clues correctly', async () => {
      const expectedClues = easyPuzzle.flat().filter(cell => cell !== 0).length;
      const analysis = await solver.analyzePuzzle(easyPuzzle);
      expect(analysis.clueCount).toBe(expectedClues);
    });
  });

  describe('getHint()', () => {
    it('should provide valid hints', async () => {
      const result = await solver.solve(easyPuzzle);
      expect(result.solved).toBe(true);
      expect(result.solution).toBeDefined();
    });
  });

  describe('edge cases', () => {
    it('should handle invalid puzzle values', async () => {
      const invalidValues = JSON.parse(JSON.stringify(easyPuzzle));
      (invalidValues as any)[0][0] = 10; // Invalid value
      
      try {
        await solver.solve(invalidValues);
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    it('should handle empty puzzle', async () => {
      const emptyPuzzle: SudokuGrid = Array(9).fill(null).map(() => Array(9).fill(0));
      const result = await solver.solve(emptyPuzzle);
      
      expect(result.solved).toBe(true);
      expect(result.solution).toBeDefined();
    });
  });
});
