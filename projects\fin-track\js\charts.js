/**
 * FinTrack Charts Module
 * Handles data visualization using custom SVG charts
 */

class FinTrackCharts {
  constructor() {
    this.charts = {};
    this.isUpdating = false;
    this.colors = {
      primary: '#10b981',
      secondary: '#64748b',
      success: '#10b981',
      warning: '#f59e0b',
      danger: '#ef4444',
      info: '#3b82f6',
      income: '#10b981',
      expense: '#ef4444',
      transfer: '#8b5cf6'
    };
    
    this.categoryColors = {
      'food': '#ef4444',
      'transport': '#f59e0b',
      'shopping': '#8b5cf6',
      'bills': '#64748b',
      'entertainment': '#ec4899',
      'health': '#10b981',
      'income': '#10b981',
      'other': '#6b7280'
    };
  }

  async initCharts() {
    try {
      // Only initialize if not already initialized
      if (Object.keys(this.charts).length === 0) {
        await this.createCategoryChart();
        await this.createTrendChart();
        console.log('Simple charts initialized successfully');
      } else {
        console.log('Charts already initialized, skipping...');
      }
    } catch (error) {
      console.error('Failed to initialize charts:', error);
      this.showChartError();
    }
  }

  async createCategoryChart() {
    const canvas = document.getElementById('categoryChart');
    if (!canvas) return;

    // Prevent multiple chart creation
    if (this.charts.categoryChart) {
      console.log('Category chart already exists, skipping creation');
      return;
    }

    try {
      // Get transaction data for current month
      const summary = await window.finTrackDB.getTransactionSummary('month');

      // Prepare data for pie chart
      const categories = Object.keys(summary.categoryBreakdown);
      const amounts = Object.values(summary.categoryBreakdown);
      const colors = categories.map(cat => this.categoryColors[cat] || this.colors.secondary);

      const chartData = {
        categories: categories.map(cat => this.formatCategoryName(cat)),
        amounts: amounts,
        colors: colors
      };

      // Create simple HTML chart instead of Chart.js
      this.createSimpleCategoryChart(canvas.parentNode, chartData);
      this.charts.categoryChart = { type: 'simple' }; // Mark as created

      // Chart.js implementation removed - using simple HTML chart instead

    } catch (error) {
      console.error('Failed to create category chart:', error);
      this.showChartError('categoryChart');
    }
  }

  async createTrendChart() {
    const canvas = document.getElementById('trendChart');
    if (!canvas) {
      console.warn('Trend chart canvas not found');
      return;
    }

    // Prevent multiple chart creation
    if (this.charts.trendChart) {
      console.log('Trend chart already exists, skipping creation');
      return;
    }

    try {
      // Replace with simple HTML chart instead of Chart.js
      const trendData = await this.getTrendData();
      this.createSimpleTrendChart(canvas.parentNode, trendData);
      this.charts.trendChart = { type: 'simple' }; // Mark as created

      // Chart.js implementation removed - using simple HTML chart instead

    } catch (error) {
      console.error('Failed to create trend chart:', error);
      this.showChartError('trendChart');
    }
  }

  async getTrendData() {
    try {
      // Check if database is available
      if (!window.finTrackDB) {
        console.warn('Database not available, using mock data');
        return this.getMockTrendData();
      }

      const transactions = await window.finTrackDB.getTransactions();
      const months = [];
      const income = [];
      const expenses = [];

      // Get last 6 months
      const now = new Date();
      for (let i = 5; i >= 0; i--) {
        const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const monthName = date.toLocaleDateString('en-US', { month: 'short', year: '2-digit' });
        months.push(monthName);

        // Calculate totals for this month
        const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);
        const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0);

        const monthTransactions = transactions.filter(t => {
          if (!t.date) return false;
          const transactionDate = new Date(t.date);
          return transactionDate >= monthStart && transactionDate <= monthEnd;
        });

        const monthIncome = monthTransactions
          .filter(t => t.type === 'income')
          .reduce((sum, t) => sum + (parseFloat(t.amount) || 0), 0);

        const monthExpenses = monthTransactions
          .filter(t => t.type === 'expense')
          .reduce((sum, t) => sum + (parseFloat(t.amount) || 0), 0);

        income.push(monthIncome);
        expenses.push(monthExpenses);
      }

      return {
        labels: months,
        income,
        expenses
      };
    } catch (error) {
      console.error('Error getting trend data:', error);
      return this.getMockTrendData();
    }
  }

  getMockTrendData() {
    const months = ['Oct 24', 'Nov 24', 'Dec 24', 'Jan 25', 'Feb 25', 'Mar 25'];
    return {
      labels: months,
      income: [3000, 3200, 3100, 3300, 3150, 3250],
      expenses: [2200, 2400, 2800, 2100, 2300, 2150]
    };
  }

  createSimpleTrendChart(container, data) {
    // Remove canvas and create simple HTML chart
    const canvas = container.querySelector('canvas');
    if (canvas) {
      canvas.remove();
    }

    const maxValue = Math.max(...data.income, ...data.expenses);

    const chartHTML = `
      <div class="simple-chart">
        <div class="chart-legend">
          <div class="legend-item">
            <span class="legend-color income"></span>
            <span>Income</span>
          </div>
          <div class="legend-item">
            <span class="legend-color expense"></span>
            <span>Expenses</span>
          </div>
        </div>
        <div class="chart-bars">
          ${data.labels.map((label, index) => `
            <div class="bar-group">
              <div class="bar-container">
                <div class="bar income-bar" style="height: ${(data.income[index] / maxValue) * 100}%">
                  <span class="bar-value">$${data.income[index]}</span>
                </div>
                <div class="bar expense-bar" style="height: ${(data.expenses[index] / maxValue) * 100}%">
                  <span class="bar-value">$${data.expenses[index]}</span>
                </div>
              </div>
              <div class="bar-label">${label}</div>
            </div>
          `).join('')}
        </div>
      </div>
    `;

    container.innerHTML = container.querySelector('h3').outerHTML + chartHTML;
  }

  createSimpleCategoryChart(container, data) {
    // Remove canvas and create simple HTML chart
    const canvas = container.querySelector('canvas');
    if (canvas) {
      canvas.remove();
    }

    const total = data.amounts.reduce((sum, amount) => sum + amount, 0);

    const chartHTML = `
      <div class="simple-pie-chart">
        <div class="pie-segments">
          ${data.categories.map((category, index) => {
            const percentage = ((data.amounts[index] / total) * 100).toFixed(1);
            return `
              <div class="pie-segment">
                <div class="segment-color" style="background-color: ${data.colors[index]}"></div>
                <div class="segment-info">
                  <span class="segment-label">${category}</span>
                  <span class="segment-value">$${data.amounts[index].toFixed(2)} (${percentage}%)</span>
                </div>
              </div>
            `;
          }).join('')}
        </div>
      </div>
    `;

    container.innerHTML = container.querySelector('h3').outerHTML + chartHTML;
  }

  async updateCharts() {
    // Prevent multiple simultaneous updates
    if (this.isUpdating) {
      console.log('Chart update already in progress, skipping...');
      return;
    }

    this.isUpdating = true;

    try {
      // Only update if charts exist, otherwise create them
      if (this.charts.categoryChart) {
        await this.updateCategoryChart();
      } else {
        await this.createCategoryChart();
      }

      if (this.charts.trendChart) {
        await this.updateTrendChart();
      } else {
        await this.createTrendChart();
      }

      console.log('Charts updated successfully');
    } catch (error) {
      console.error('Failed to update charts:', error);
    } finally {
      this.isUpdating = false;
    }
  }

  async updateCategoryChart() {
    if (!this.charts.categoryChart) return;

    try {
      const summary = await window.finTrackDB.getTransactionSummary('month');
      const categories = Object.keys(summary.categoryBreakdown);
      const amounts = Object.values(summary.categoryBreakdown);
      const colors = categories.map(cat => this.categoryColors[cat] || this.colors.secondary);

      // Update chart data instead of recreating
      this.charts.categoryChart.data.labels = categories.map(cat => this.formatCategoryName(cat));
      this.charts.categoryChart.data.datasets[0].data = amounts;
      this.charts.categoryChart.data.datasets[0].backgroundColor = colors;
      this.charts.categoryChart.data.datasets[0].borderColor = colors.map(color => this.adjustColorOpacity(color, 0.8));

      this.charts.categoryChart.update('none'); // Update without animation to prevent loops
    } catch (error) {
      console.error('Failed to update category chart:', error);
    }
  }

  async updateTrendChart() {
    if (!this.charts.trendChart) return;

    try {
      const trendData = await this.getTrendData();

      // Update chart data instead of recreating
      this.charts.trendChart.data.labels = trendData.labels;
      this.charts.trendChart.data.datasets[0].data = trendData.income;
      this.charts.trendChart.data.datasets[1].data = trendData.expenses;

      this.charts.trendChart.update('none'); // Update without animation to prevent loops
    } catch (error) {
      console.error('Failed to update trend chart:', error);
    }
  }

  // Utility methods
  formatCategoryName(category) {
    return category.charAt(0).toUpperCase() + category.slice(1).replace('-', ' ');
  }

  adjustColorOpacity(color, opacity) {
    // Convert hex to rgba
    const hex = color.replace('#', '');
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }

  // Create additional chart types
  async createAccountBalanceChart(containerId) {
    const canvas = document.getElementById(containerId);
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    
    // Mock account data - in real app, this would come from database
    const accountData = {
      labels: ['Checking', 'Savings', 'Credit Card', 'Cash'],
      data: [2500, 15000, -1200, 300],
      colors: [this.colors.info, this.colors.success, this.colors.danger, this.colors.warning]
    };

    new Chart(ctx, {
      type: 'bar',
      data: {
        labels: accountData.labels,
        datasets: [{
          label: 'Balance',
          data: accountData.data,
          backgroundColor: accountData.colors,
          borderColor: accountData.colors,
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              label: (context) => {
                const value = context.parsed.y;
                return `Balance: $${value.toFixed(2)}`;
              }
            }
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              callback: function(value) {
                return '$' + value.toFixed(0);
              }
            }
          }
        }
      }
    });
  }

  async createGoalProgressChart(containerId, goalData) {
    const canvas = document.getElementById(containerId);
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    
    const progressPercentage = (goalData.current / goalData.target) * 100;
    
    new Chart(ctx, {
      type: 'doughnut',
      data: {
        datasets: [{
          data: [progressPercentage, 100 - progressPercentage],
          backgroundColor: [this.colors.success, '#e5e7eb'],
          borderWidth: 0
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        cutout: '80%',
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            enabled: false
          }
        },
        animation: {
          animateRotate: true
        }
      },
      plugins: [{
        id: 'centerText',
        beforeDraw: (chart) => {
          const { ctx, chartArea: { top, bottom, left, right } } = chart;
          ctx.save();
          
          const centerX = (left + right) / 2;
          const centerY = (top + bottom) / 2;
          
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.font = 'bold 24px Arial';
          ctx.fillStyle = this.colors.primary;
          ctx.fillText(`${Math.round(progressPercentage)}%`, centerX, centerY);
          
          ctx.restore();
        }
      }]
    });
  }

  // Responsive chart handling
  handleResize() {
    Object.values(this.charts).forEach(chart => {
      if (chart && typeof chart.resize === 'function') {
        chart.resize();
      }
    });
  }

  // Cleanup method
  destroyCharts() {
    Object.values(this.charts).forEach(chart => {
      if (chart && typeof chart.destroy === 'function') {
        chart.destroy();
      }
    });
    this.charts = {};
  }

  // Theme handling
  updateChartsTheme(isDark) {
    const textColor = isDark ? '#f8fafc' : '#0f172a';
    const gridColor = isDark ? '#334155' : '#e2e8f0';

    Object.values(this.charts).forEach(chart => {
      if (chart && chart.options) {
        // Update text colors
        if (chart.options.plugins && chart.options.plugins.legend) {
          chart.options.plugins.legend.labels.color = textColor;
        }

        // Update scale colors
        if (chart.options.scales) {
          Object.values(chart.options.scales).forEach(scale => {
            if (scale.title) scale.title.color = textColor;
            if (scale.ticks) scale.ticks.color = textColor;
            if (scale.grid) scale.grid.color = gridColor;
          });
        }

        chart.update();
      }
    });
  }

  // Error handling
  showChartError(chartId = null) {
    const errorMessage = `
      <div class="chart-error">
        <div class="error-icon">📊</div>
        <h3>Chart Loading Error</h3>
        <p>Unable to load chart visualization. Please refresh the page.</p>
        <button class="btn btn-primary" onclick="window.location.reload()">Refresh Page</button>
      </div>
    `;

    if (chartId) {
      const canvas = document.getElementById(chartId);
      if (canvas && canvas.parentNode) {
        canvas.parentNode.innerHTML = errorMessage;
      }
    } else {
      // Show error for all chart containers
      const chartContainers = document.querySelectorAll('.chart-card');
      chartContainers.forEach(container => {
        const canvas = container.querySelector('canvas');
        if (canvas) {
          canvas.parentNode.innerHTML = errorMessage;
        }
      });
    }
  }
}

// Create and export singleton instance
const charts = new FinTrackCharts();

export default charts;
