/**
 * Enterprise logging utility with <PERSON>
 * Comprehensive logging for game events, performance, and system monitoring
 */

import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import path from 'path';

// Define log levels
const logLevels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4
};

// Define log colors
const logColors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white'
};

winston.addColors(logColors);

// Create logs directory if it doesn't exist
const logsDir = process.env.LOG_DIR || './logs';

// Define log format
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf((info) => {
    const { timestamp, level, message, ...meta } = info;
    const metaString = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
    return `${timestamp} [${level}]: ${message} ${metaString}`;
  })
);

// Define file format (without colors)
const fileFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Create transports
const transports: winston.transport[] = [];

// Console transport
if (process.env.NODE_ENV !== 'test') {
  transports.push(
    new winston.transports.Console({
      level: process.env.LOG_LEVEL || 'info',
      format: logFormat
    })
  );
}

// File transports (only if LOG_FILE is enabled)
if (process.env.LOG_FILE === 'true') {
  // General log file with rotation
  transports.push(
    new DailyRotateFile({
      filename: path.join(logsDir, 'application-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '14d',
      level: 'info',
      format: fileFormat
    })
  );

  // Error log file
  transports.push(
    new DailyRotateFile({
      filename: path.join(logsDir, 'error-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '30d',
      level: 'error',
      format: fileFormat
    })
  );

  // Game events log file
  transports.push(
    new DailyRotateFile({
      filename: path.join(logsDir, 'game-events-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '50m',
      maxFiles: '7d',
      level: 'info',
      format: fileFormat,
      // Only log game-related events
      filter: (info) => info.category === 'game'
    })
  );
}

// Create logger instance
const logger = winston.createLogger({
  levels: logLevels,
  level: process.env.LOG_LEVEL || 'info',
  transports,
  exitOnError: false,
  // Handle uncaught exceptions and rejections
  exceptionHandlers: process.env.LOG_FILE === 'true' ? [
    new DailyRotateFile({
      filename: path.join(logsDir, 'exceptions-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '30d',
      format: fileFormat
    })
  ] : [],
  rejectionHandlers: process.env.LOG_FILE === 'true' ? [
    new DailyRotateFile({
      filename: path.join(logsDir, 'rejections-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '30d',
      format: fileFormat
    })
  ] : []
});

/**
 * Enhanced logger with specialized methods
 */
export const enhancedLogger = {
  // Standard logging methods
  error: (message: string, meta?: any) => logger.error(message, meta),
  warn: (message: string, meta?: any) => logger.warn(message, meta),
  info: (message: string, meta?: any) => logger.info(message, meta),
  http: (message: string, meta?: any) => logger.http(message, meta),
  debug: (message: string, meta?: any) => logger.debug(message, meta),

  // Game-specific logging methods
  gameEvent: (event: string, gameId: string, playerId?: string, meta?: any) => {
    logger.info(`Game Event: ${event}`, {
      category: 'game',
      event,
      gameId,
      playerId,
      timestamp: new Date().toISOString(),
      ...meta
    });
  },

  playerAction: (action: string, playerId: string, gameId?: string, meta?: any) => {
    logger.info(`Player Action: ${action}`, {
      category: 'player',
      action,
      playerId,
      gameId,
      timestamp: new Date().toISOString(),
      ...meta
    });
  },

  multiplayerEvent: (event: string, roomId: string, playerId?: string, meta?: any) => {
    logger.info(`Multiplayer Event: ${event}`, {
      category: 'multiplayer',
      event,
      roomId,
      playerId,
      timestamp: new Date().toISOString(),
      ...meta
    });
  },

  // Performance logging
  performance: (operation: string, duration: number, meta?: any) => {
    logger.info(`Performance: ${operation}`, {
      category: 'performance',
      operation,
      duration,
      timestamp: new Date().toISOString(),
      ...meta
    });
  },

  // Database logging
  database: (operation: string, table: string, duration?: number, meta?: any) => {
    logger.debug(`Database: ${operation} on ${table}`, {
      category: 'database',
      operation,
      table,
      duration,
      timestamp: new Date().toISOString(),
      ...meta
    });
  },

  // Cache logging
  cache: (operation: string, key: string, hit?: boolean, meta?: any) => {
    logger.debug(`Cache: ${operation} for ${key}`, {
      category: 'cache',
      operation,
      key,
      hit,
      timestamp: new Date().toISOString(),
      ...meta
    });
  },

  // API logging
  api: (event: string, requestId: string, userId?: string, meta?: any) => {
    logger.http(`API: ${event}`, {
      category: 'api',
      event,
      requestId,
      userId,
      timestamp: new Date().toISOString(),
      ...meta
    });
  },

  // Real-time logging
  realtime: (event: string, connectionId: string, userId?: string, meta?: any) => {
    logger.info(`Real-time: ${event}`, {
      category: 'realtime',
      event,
      connectionId,
      userId,
      timestamp: new Date().toISOString(),
      ...meta
    });
  },

  // Security logging
  security: (event: string, userId?: string, ip?: string, meta?: any) => {
    logger.warn(`Security: ${event}`, {
      category: 'security',
      event,
      userId,
      ip,
      timestamp: new Date().toISOString(),
      ...meta
    });
  },

  // Business logic logging
  business: (event: string, entity: string, entityId: string, meta?: any) => {
    logger.info(`Business: ${event}`, {
      category: 'business',
      event,
      entity,
      entityId,
      timestamp: new Date().toISOString(),
      ...meta
    });
  }
};

// Export both the standard logger and enhanced logger
export { logger };
export default enhancedLogger;
