/**
 * Portfolio Gallery Functionality
 * Interactive filtering and gallery features for hair transformation showcase
 */

document.addEventListener('DOMContentLoaded', function() {
    initPortfolioFilter();
    initGalleryInteractions();
    initBeforeAfterSliders();
    
    console.log('Portfolio gallery loaded successfully');
});

/**
 * Initialize portfolio filtering system
 */
function initPortfolioFilter() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    const galleryItems = document.querySelectorAll('.gallery-item');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter gallery items
            filterGalleryItems(galleryItems, filter);
        });
    });
}

/**
 * Filter gallery items based on category
 */
function filterGalleryItems(items, filter) {
    items.forEach(item => {
        const category = item.getAttribute('data-category');
        
        if (filter === 'all' || category === filter) {
            item.style.display = 'block';
            item.style.opacity = '0';
            item.style.transform = 'translateY(20px)';
            
            // Animate in with delay
            setTimeout(() => {
                item.style.opacity = '1';
                item.style.transform = 'translateY(0)';
            }, 100);
        } else {
            item.style.opacity = '0';
            item.style.transform = 'translateY(-20px)';
            
            setTimeout(() => {
                item.style.display = 'none';
            }, 300);
        }
    });
}

/**
 * Initialize gallery interactions
 */
function initGalleryInteractions() {
    const transformationCards = document.querySelectorAll('.transformation-card');
    
    transformationCards.forEach(card => {
        // Hover effects
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
            this.style.boxShadow = '0 25px 50px rgba(0, 0, 0, 0.2)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.1)';
        });
        
        // Click to expand
        card.addEventListener('click', function() {
            expandTransformation(this);
        });
    });
}

/**
 * Expand transformation for detailed view
 */
function expandTransformation(card) {
    const transformationInfo = card.querySelector('.transformation-info');
    const title = transformationInfo.querySelector('h3').textContent;
    const description = transformationInfo.querySelector('p').textContent;
    const serviceType = transformationInfo.querySelector('.service-type').textContent;
    const duration = transformationInfo.querySelector('.duration').textContent;
    
    // Create modal
    const modal = document.createElement('div');
    modal.className = 'transformation-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="close-modal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="expanded-before-after">
                    <div class="expanded-before">
                        <img src="${card.querySelector('.before img').src}" alt="Before Transformation">
                        <span class="expanded-label">Before</span>
                    </div>
                    <div class="expanded-after">
                        <img src="${card.querySelector('.after img').src}" alt="After Transformation">
                        <span class="expanded-label">After</span>
                    </div>
                </div>
                <div class="expanded-info">
                    <p class="expanded-description">${description}</p>
                    <div class="expanded-details">
                        <div class="detail-item">
                            <h4>Service Type</h4>
                            <p>${serviceType}</p>
                        </div>
                        <div class="detail-item">
                            <h4>Duration</h4>
                            <p>${duration}</p>
                        </div>
                        <div class="detail-item">
                            <h4>Techniques Used</h4>
                            <p>Professional color application, custom formulation, conditioning treatment</p>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button class="btn-primary">Book Similar Service</button>
                        <button class="btn-secondary">View All Services</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Close modal functionality
    const closeBtn = modal.querySelector('.close-modal');
    closeBtn.addEventListener('click', () => {
        document.body.removeChild(modal);
    });
    
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            document.body.removeChild(modal);
        }
    });
    
    // Animate modal in
    setTimeout(() => {
        modal.classList.add('show');
    }, 10);
}

/**
 * Initialize before/after sliders (for future enhancement)
 */
function initBeforeAfterSliders() {
    const beforeAfterContainers = document.querySelectorAll('.before-after');
    
    beforeAfterContainers.forEach(container => {
        // Add hover effect to reveal after image
        container.addEventListener('mouseenter', function() {
            const afterImage = this.querySelector('.after');
            afterImage.style.opacity = '1';
            afterImage.style.transform = 'translateX(0)';
        });
        
        container.addEventListener('mouseleave', function() {
            const afterImage = this.querySelector('.after');
            afterImage.style.opacity = '0.8';
            afterImage.style.transform = 'translateX(10px)';
        });
    });
}

/**
 * Add dynamic styles for portfolio page
 */
function addPortfolioStyles() {
    const style = document.createElement('style');
    style.textContent = `
        /* Portfolio Hero */
        .portfolio-hero {
            background: linear-gradient(135deg, var(--light-pink) 0%, var(--cream) 100%);
            padding: 120px 0 80px;
            text-align: center;
        }
        
        .portfolio-hero-content h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
        }
        
        .hero-description {
            max-width: 600px;
            margin: 0 auto;
            font-size: 1.1rem;
            color: var(--text-light);
        }
        
        /* Portfolio Filter */
        .portfolio-filter {
            padding: 40px 0;
            background: var(--white);
            border-bottom: 1px solid var(--light-pink);
        }
        
        .filter-buttons {
            display: flex;
            justify-content: center;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .filter-btn {
            background: transparent;
            border: 2px solid var(--dark-pink);
            color: var(--dark-pink);
            padding: 0.8rem 1.5rem;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .filter-btn:hover,
        .filter-btn.active {
            background: var(--dark-pink);
            color: var(--white);
            transform: translateY(-2px);
        }
        
        /* Portfolio Gallery */
        .portfolio-gallery {
            padding: 80px 0;
            background: var(--light-pink);
        }
        
        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
        }
        
        .gallery-item {
            transition: all 0.3s ease;
        }
        
        .transformation-card {
            background: var(--white);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px var(--shadow);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .before-after {
            position: relative;
            height: 300px;
            overflow: hidden;
        }
        
        .before,
        .after {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        
        .before img,
        .after img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .after {
            opacity: 0.8;
            transform: translateX(10px);
            transition: all 0.3s ease;
        }
        
        .label {
            position: absolute;
            top: 10px;
            left: 10px;
            background: var(--dark-pink);
            color: var(--white);
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .transformation-info {
            padding: 1.5rem;
        }
        
        .transformation-info h3 {
            color: var(--dark-pink);
            margin-bottom: 0.5rem;
        }
        
        .transformation-info p {
            color: var(--text-light);
            margin-bottom: 1rem;
        }
        
        .transformation-details {
            display: flex;
            gap: 1rem;
        }
        
        .service-type,
        .duration {
            background: var(--light-pink);
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            color: var(--dark-pink);
            font-weight: 600;
        }
        
        /* Testimonials */
        .testimonials-section {
            padding: 80px 0;
            background: var(--white);
        }
        
        .testimonials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }
        
        .testimonial-card {
            background: var(--light-pink);
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 10px 30px var(--shadow);
        }
        
        .testimonial-content p {
            font-style: italic;
            margin-bottom: 1.5rem;
            color: var(--text-dark);
        }
        
        .testimonial-author h4 {
            color: var(--dark-pink);
            margin-bottom: 0.3rem;
        }
        
        .testimonial-author span {
            color: var(--text-light);
            font-size: 0.9rem;
        }
        
        /* Transformation Modal */
        .transformation-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .transformation-modal.show {
            opacity: 1;
        }
        
        .transformation-modal .modal-content {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            max-width: 800px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            transform: translateY(50px);
            transition: transform 0.3s ease;
        }
        
        .transformation-modal.show .modal-content {
            transform: translateY(0);
        }
        
        .expanded-before-after {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .expanded-before,
        .expanded-after {
            position: relative;
        }
        
        .expanded-before img,
        .expanded-after img {
            width: 100%;
            height: 250px;
            object-fit: cover;
            border-radius: 15px;
        }
        
        .expanded-label {
            position: absolute;
            top: 10px;
            left: 10px;
            background: var(--dark-pink);
            color: var(--white);
            padding: 0.5rem 1rem;
            border-radius: 15px;
            font-weight: 600;
        }
        
        .expanded-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .detail-item {
            background: var(--light-pink);
            padding: 1rem;
            border-radius: 10px;
        }
        
        .detail-item h4 {
            color: var(--dark-pink);
            margin-bottom: 0.5rem;
        }
        
        .modal-actions {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .gallery-grid {
                grid-template-columns: 1fr;
            }
            
            .filter-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .expanded-before-after {
                grid-template-columns: 1fr;
            }
            
            .modal-actions {
                flex-direction: column;
            }
        }
    `;
    
    document.head.appendChild(style);
}

// Initialize portfolio styles
addPortfolioStyles();
