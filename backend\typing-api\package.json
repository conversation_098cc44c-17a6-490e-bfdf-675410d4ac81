{"name": "typing-analytics-api", "version": "2.0.0", "description": "Enterprise-grade typing analytics API with advanced performance tracking, real-time features, comprehensive testing, and production monitoring", "main": "dist/server.js", "types": "dist/server.d.ts", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --testPathPattern=integration", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "type-check": "tsc --noEmit", "docker:build": "docker build -t typing-api .", "docker:run": "docker run -p 3003:3003 typing-api", "docker:dev": "docker-compose up --build", "migrate": "node dist/database/migrate.js", "seed": "node dist/database/seed.js", "docs": "typedoc src --out docs", "health-check": "curl -f http://localhost:3003/health || exit 1", "precommit": "npm run lint && npm run type-check && npm run test", "postinstall": "echo 'Skipping husky install for now'"}, "keywords": ["typing", "analytics", "performance", "api", "leaderboard", "wpm", "accuracy", "typescript", "enterprise", "production-ready", "real-time", "monitoring"], "author": "<PERSON><PERSON><PERSON> Sharp <<EMAIL>>", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "prom-client": "^15.1.0", "joi": "^17.11.0", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2", "sqlite3": "^5.1.6", "redis": "^4.6.12", "dotenv": "^16.3.1", "uuid": "^9.0.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "socket.io": "^4.7.4"}, "devDependencies": {"typescript": "^5.3.3", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "ts-jest": "^29.1.1", "@types/node": "^20.10.5", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.5", "@types/uuid": "^9.0.7", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "jest": "^29.7.0", "supertest": "^6.3.3", "@types/supertest": "^6.0.2", "eslint": "^8.56.0", "@typescript-eslint/parser": "^6.15.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.2", "prettier": "^3.1.1", "husky": "^8.0.3", "lint-staged": "^15.2.0", "nodemon": "^3.0.2", "typedoc": "^0.25.4"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src", "<rootDir>/tests"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/server.ts"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"], "coverageThreshold": {"global": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}, "moduleNameMapper": {"^@/(.*)$": "<rootDir>/src/$1"}}, "lint-staged": {"src/**/*.ts": ["eslint --fix", "prettier --write", "git add"]}}