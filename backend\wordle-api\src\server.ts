/**
 * Enterprise Wordle Game API Server
 * Production-ready server with comprehensive game management, multiplayer support, and monitoring
 */

import express, { Application, Request, Response } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import { config } from 'dotenv';

// Import middleware
import { requestLogger } from '@/middleware/requestLogger';
import { errorHandler } from '@/middleware/errorHandler';
import { MetricsCollector } from '@/middleware/metrics';
import { validationMiddleware } from '@/middleware/validation';

// Import services
import { DatabaseService } from '@/services/DatabaseService';
import { CacheService } from '@/services/CacheService';
import { GameService } from '@/services/GameService';
import { PlayerService } from '@/services/PlayerService';
import { WordService } from '@/services/WordService';
import { RealTimeService } from '@/services/RealTimeService';
import { AnalyticsService } from '@/services/AnalyticsService';

// Import routes
import gameRoutes from '@/routes/game';
import playerRoutes from '@/routes/player';
import leaderboardRoutes from '@/routes/leaderboard';
import analyticsRoutes from '@/routes/analytics';
import multiplayerRoutes from '@/routes/multiplayer';
import wordRoutes from '@/routes/word';

// Import utilities
import { logger } from '@/utils/logger';

// Load environment variables
config();

/**
 * Enterprise Wordle Game Server
 */
export class WordleGameServer {
  private app: Application;
  private server: any;
  private io: SocketIOServer;
  private port: number;
  private isShuttingDown: boolean = false;

  // Services
  private databaseService: DatabaseService;
  private cacheService: CacheService;
  private gameService: GameService;
  private playerService: PlayerService;
  private wordService: WordService;
  private realTimeService: RealTimeService;
  private analyticsService: AnalyticsService;

  constructor() {
    this.app = express();
    this.port = parseInt(process.env.PORT || '3001', 10);
    this.server = createServer(this.app);
    this.io = new SocketIOServer(this.server, {
      cors: {
        origin: process.env.CORS_ORIGIN || "*",
        methods: ["GET", "POST"]
      }
    });

    // Initialize services
    this.initializeServices();
  }

  /**
   * Initialize all services
   */
  private async initializeServices(): Promise<void> {
    try {
      // Initialize database service
      this.databaseService = new DatabaseService({
        type: 'sqlite',
        database: process.env.DB_PATH || './data/wordle.db'
      });
      await this.databaseService.initialize();

      // Initialize cache service
      this.cacheService = new CacheService({
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379', 10),
        password: process.env.REDIS_PASSWORD
      });
      await this.cacheService.initialize();

      // Initialize core services
      this.wordService = new WordService(this.databaseService, this.cacheService);
      this.playerService = new PlayerService(this.databaseService, this.cacheService);
      this.gameService = new GameService(
        this.databaseService,
        this.cacheService,
        this.wordService,
        this.playerService
      );
      this.analyticsService = new AnalyticsService(this.databaseService, this.cacheService);
      this.realTimeService = new RealTimeService(
        this.io,
        this.gameService,
        this.playerService
      );

      // Initialize real-time service
      this.realTimeService.initialize();

      logger.info('All services initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize services:', error);
      throw error;
    }
  }

  /**
   * Configure middleware
   */
  private configureMiddleware(): void {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
    }));

    // CORS configuration
    this.app.use(cors({
      origin: process.env.CORS_ORIGIN || "*",
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
    }));

    // Compression
    this.app.use(compression());

    // Rate limiting
    const limiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: process.env.NODE_ENV === 'production' ? 100 : 1000, // requests per window
      message: {
        error: 'Too many requests from this IP, please try again later.',
        retryAfter: '15 minutes'
      },
      standardHeaders: true,
      legacyHeaders: false,
    });
    this.app.use(limiter);

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Request logging
    this.app.use(requestLogger);

    // Metrics collection
    this.app.use(MetricsCollector.middleware);

    // Static files
    this.app.use(express.static('public'));
  }

  /**
   * Configure API routes
   */
  private configureRoutes(): void {
    // API routes
    this.app.use('/api/game', gameRoutes);
    this.app.use('/api/player', playerRoutes);
    this.app.use('/api/leaderboard', leaderboardRoutes);
    this.app.use('/api/analytics', analyticsRoutes);
    this.app.use('/api/multiplayer', multiplayerRoutes);
    this.app.use('/api/word', wordRoutes);

    // Health check endpoint
    this.app.get('/health', (req: Request, res: Response) => {
      const healthStatus = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        services: {
          database: this.databaseService.isHealthy(),
          cache: this.cacheService.isHealthy(),
          realtime: this.realTimeService.getActiveConnectionsCount()
        },
        memory: process.memoryUsage(),
        cpu: process.cpuUsage()
      };

      res.json(healthStatus);
    });

    // Metrics endpoint
    this.app.get('/metrics', MetricsCollector.metricsEndpoint);

    // API documentation
    if (process.env.NODE_ENV !== 'production') {
      const swaggerOptions = {
        definition: {
          openapi: '3.0.0',
          info: {
            title: 'Wordle Game API',
            version: '2.0.0',
            description: 'Enterprise-grade Wordle game API with multiplayer support',
          },
          servers: [
            {
              url: `http://localhost:${this.port}`,
              description: 'Development server',
            },
          ],
        },
        apis: ['./src/routes/*.ts'],
      };

      const specs = swaggerJsdoc(swaggerOptions);
      this.app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs));
    }

    // 404 handler
    this.app.use('*', (req: Request, res: Response) => {
      res.status(404).json({
        success: false,
        error: 'Endpoint not found',
        path: req.originalUrl,
        method: req.method,
        timestamp: new Date().toISOString()
      });
    });

    // Global error handler
    this.app.use(errorHandler);
  }

  /**
   * Start the server
   */
  public async start(): Promise<void> {
    try {
      // Configure middleware and routes
      this.configureMiddleware();
      this.configureRoutes();

      // Start server
      this.server.listen(this.port, () => {
        logger.info(`🎮 Wordle Game API server started on port ${this.port}`);
        logger.info(`📚 API Documentation: http://localhost:${this.port}/api-docs`);
        logger.info(`🏥 Health Check: http://localhost:${this.port}/health`);
        logger.info(`📊 Metrics: http://localhost:${this.port}/metrics`);
        logger.info(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
      });

      // Setup graceful shutdown
      this.setupGracefulShutdown();

    } catch (error) {
      logger.error('Failed to start server:', error);
      process.exit(1);
    }
  }

  /**
   * Setup graceful shutdown
   */
  private setupGracefulShutdown(): void {
    const shutdown = async (signal: string) => {
      if (this.isShuttingDown) return;
      this.isShuttingDown = true;

      logger.info(`Received ${signal}. Starting graceful shutdown...`);

      // Stop accepting new connections
      this.server.close(async () => {
        logger.info('HTTP server closed');

        try {
          // Close real-time connections
          this.io.close();
          logger.info('WebSocket server closed');

          // Close database connections
          await this.databaseService.close();
          logger.info('Database connections closed');

          // Close cache connections
          await this.cacheService.close();
          logger.info('Cache connections closed');

          logger.info('Graceful shutdown completed');
          process.exit(0);
        } catch (error) {
          logger.error('Error during shutdown:', error);
          process.exit(1);
        }
      });

      // Force shutdown after 30 seconds
      setTimeout(() => {
        logger.error('Forced shutdown after timeout');
        process.exit(1);
      }, 30000);
    };

    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGUSR2', () => shutdown('SIGUSR2')); // nodemon restart

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception:', error);
      shutdown('uncaughtException');
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
      shutdown('unhandledRejection');
    });
  }

  /**
   * Get server instance
   */
  public getApp(): Application {
    return this.app;
  }

  /**
   * Get Socket.IO instance
   */
  public getIO(): SocketIOServer {
    return this.io;
  }
}

// Start server if this file is run directly
if (require.main === module) {
  const server = new WordleGameServer();
  server.start().catch((error) => {
    logger.error('Failed to start server:', error);
    process.exit(1);
  });
}

export default WordleGameServer;
