{"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 100, "tabWidth": 2, "useTabs": false, "quoteProps": "as-needed", "jsxSingleQuote": true, "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "always", "endOfLine": "lf", "embeddedLanguageFormatting": "auto", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "proseWrap": "preserve", "requirePragma": false, "vueIndentScriptAndStyle": false, "overrides": [{"files": "*.json", "options": {"printWidth": 80, "tabWidth": 2}}, {"files": "*.md", "options": {"printWidth": 80, "proseWrap": "always", "tabWidth": 2}}, {"files": "*.yml", "options": {"tabWidth": 2, "singleQuote": false}}, {"files": "*.yaml", "options": {"tabWidth": 2, "singleQuote": false}}]}