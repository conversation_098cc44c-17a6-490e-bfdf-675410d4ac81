const http = require('http');

const API_BASE = 'http://localhost:3003';
let testsPassed = 0;
let testsFailed = 0;

// ANSI color codes for console output
const colors = {
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    reset: '\x1b[0m',
    bold: '\x1b[1m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function makeRequest(method, path, data = null) {
    return new Promise((resolve, reject) => {
        const url = new URL(path, API_BASE);
        const options = {
            method,
            headers: {
                'Content-Type': 'application/json',
            }
        };

        const req = http.request(url, options, (res) => {
            let body = '';
            res.on('data', chunk => body += chunk);
            res.on('end', () => {
                try {
                    const jsonBody = body ? JSON.parse(body) : {};
                    resolve({ status: res.statusCode, data: jsonBody });
                } catch (e) {
                    resolve({ status: res.statusCode, data: body });
                }
            });
        });

        req.on('error', reject);
        
        if (data) {
            req.write(JSON.stringify(data));
        }
        
        req.end();
    });
}

async function runTest(testName, testFunction) {
    try {
        log(`\n🧪 Testing: ${testName}`, 'blue');
        await testFunction();
        log(`✅ PASSED: ${testName}`, 'green');
        testsPassed++;
    } catch (error) {
        log(`❌ FAILED: ${testName}`, 'red');
        log(`   Error: ${error.message}`, 'red');
        testsFailed++;
    }
}

function assert(condition, message) {
    if (!condition) {
        throw new Error(message);
    }
}

// Test Functions
async function testServerHealth() {
    const response = await makeRequest('GET', '/api/info');
    assert(response.status === 200, `Expected status 200, got ${response.status}`);
    assert(response.data.name === 'Typing Analytics API', 'API name mismatch');
    assert(response.data.version === '1.0.0', 'API version mismatch');
}

async function testGetTexts() {
    const response = await makeRequest('GET', '/api/texts');
    assert(response.status === 200, `Expected status 200, got ${response.status}`);
    assert(response.data.success === true, 'Response should indicate success');
    assert(Array.isArray(response.data.data), 'Data should be an array');
    assert(response.data.data.length > 0, 'Should have at least one text sample');
    
    // Check text structure
    const firstText = response.data.data[0];
    assert(firstText.id !== undefined, 'Text should have an ID');
    assert(firstText.difficulty !== undefined, 'Text should have difficulty');
    assert(firstText.text !== undefined, 'Text should have content');
    assert(firstText.category !== undefined, 'Text should have category');
}

async function testGetTextById() {
    const response = await makeRequest('GET', '/api/texts/1');
    assert(response.status === 200, `Expected status 200, got ${response.status}`);
    assert(response.data.success === true, 'Response should indicate success');
    assert(response.data.data.id === 1, 'Should return text with ID 1');
}

async function testGetNonexistentText() {
    const response = await makeRequest('GET', '/api/texts/999');
    assert(response.status === 404, `Expected status 404, got ${response.status}`);
    assert(response.data.success === false, 'Response should indicate failure');
}

async function testFilterTextsByDifficulty() {
    const response = await makeRequest('GET', '/api/texts?difficulty=easy');
    assert(response.status === 200, `Expected status 200, got ${response.status}`);
    assert(response.data.success === true, 'Response should indicate success');
    
    // All returned texts should be easy difficulty
    response.data.data.forEach(text => {
        assert(text.difficulty === 'easy', `Expected easy difficulty, got ${text.difficulty}`);
    });
}

async function testSubmitTypingResult() {
    const testData = {
        username: 'test_user',
        textId: 1,
        typedText: 'The quick brown fox jumps over the lazy dog.',
        timeInSeconds: 30,
        correctCharacters: 40,
        totalCharacters: 44,
        errors: [
            { position: 40, expected: ' ', typed: '' },
            { position: 41, expected: 'T', typed: 't' }
        ]
    };

    const response = await makeRequest('POST', '/api/submit', testData);
    assert(response.status === 200, `Expected status 200, got ${response.status}`);
    assert(response.data.success === true, 'Response should indicate success');
    assert(response.data.data.username === testData.username, 'Username should match');
    assert(response.data.data.wpm > 0, 'WPM should be calculated');
    assert(response.data.data.accuracy > 0, 'Accuracy should be calculated');
}

async function testSubmitInvalidData() {
    const invalidData = {
        username: 'test_user',
        // Missing required fields
        textId: 1
    };

    const response = await makeRequest('POST', '/api/submit', invalidData);
    assert(response.status === 400, `Expected status 400, got ${response.status}`);
    assert(response.data.success === false, 'Response should indicate failure');
}

async function testGetLeaderboard() {
    const response = await makeRequest('GET', '/api/leaderboard');
    assert(response.status === 200, `Expected status 200, got ${response.status}`);
    assert(response.data.success === true, 'Response should indicate success');
    assert(Array.isArray(response.data.data), 'Data should be an array');
}

async function testGetLeaderboardWithLimit() {
    const response = await makeRequest('GET', '/api/leaderboard?limit=5');
    assert(response.status === 200, `Expected status 200, got ${response.status}`);
    assert(response.data.data.length <= 5, 'Should respect limit parameter');
}

async function testGetAnalytics() {
    const response = await makeRequest('GET', '/api/analytics');
    assert(response.status === 200, `Expected status 200, got ${response.status}`);
    assert(response.data.success === true, 'Response should indicate success');
    assert(response.data.data.global !== undefined, 'Should have global analytics');
    assert(response.data.data.byDifficulty !== undefined, 'Should have difficulty breakdown');
}

async function testGetUserStats() {
    // First submit a test to create user data
    const testData = {
        username: 'stats_test_user',
        textId: 1,
        typedText: 'Test text',
        timeInSeconds: 20,
        correctCharacters: 8,
        totalCharacters: 9,
        errors: []
    };
    
    await makeRequest('POST', '/api/submit', testData);
    
    // Now get user stats
    const response = await makeRequest('GET', '/api/user/stats_test_user/stats');
    assert(response.status === 200, `Expected status 200, got ${response.status}`);
    assert(response.data.success === true, 'Response should indicate success');
    assert(response.data.data.totalTests > 0, 'Should have test count');
    assert(response.data.data.averageWPM > 0, 'Should have average WPM');
}

async function testGetNonexistentUserStats() {
    const response = await makeRequest('GET', '/api/user/nonexistent_user/stats');
    assert(response.status === 404, `Expected status 404, got ${response.status}`);
    assert(response.data.success === false, 'Response should indicate failure');
}

async function testInvalidEndpoint() {
    const response = await makeRequest('GET', '/api/invalid-endpoint');
    assert(response.status === 404, `Expected status 404, got ${response.status}`);
    assert(response.data.success === false, 'Response should indicate failure');
}

// WPM Calculation Tests
async function testWPMCalculation() {
    const testData = {
        username: 'wpm_test',
        textId: 1,
        typedText: 'Hello world test',
        timeInSeconds: 60, // 1 minute
        correctCharacters: 15, // 15 characters
        totalCharacters: 15,
        errors: []
    };

    const response = await makeRequest('POST', '/api/submit', testData);
    assert(response.status === 200, `Expected status 200, got ${response.status}`);
    
    // 15 characters / 5 = 3 words, 3 words / 1 minute = 3 WPM
    const expectedWPM = Math.round((15 / 5) / (60 / 60));
    assert(response.data.data.wpm === expectedWPM, `Expected WPM ${expectedWPM}, got ${response.data.data.wpm}`);
}

// Accuracy Calculation Tests
async function testAccuracyCalculation() {
    const testData = {
        username: 'accuracy_test',
        textId: 1,
        typedText: 'Test with errors',
        timeInSeconds: 30,
        correctCharacters: 14, // 14 out of 16 correct
        totalCharacters: 16,
        errors: [
            { position: 5, expected: 'w', typed: 'W' },
            { position: 10, expected: 'e', typed: 'E' }
        ]
    };

    const response = await makeRequest('POST', '/api/submit', testData);
    assert(response.status === 200, `Expected status 200, got ${response.status}`);
    
    // 14/16 = 87.5% -> rounded to 88%
    const expectedAccuracy = Math.round((14 / 16) * 100);
    assert(response.data.data.accuracy === expectedAccuracy, 
           `Expected accuracy ${expectedAccuracy}%, got ${response.data.data.accuracy}%`);
}

// Main test runner
async function runAllTests() {
    log('🚀 Starting Typing Analytics API Tests', 'bold');
    log('=' .repeat(50), 'yellow');

    // Wait a moment for server to be ready
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Basic API Tests
    await runTest('Server Health Check', testServerHealth);
    await runTest('Get All Texts', testGetTexts);
    await runTest('Get Text by ID', testGetTextById);
    await runTest('Get Nonexistent Text', testGetNonexistentText);
    await runTest('Filter Texts by Difficulty', testFilterTextsByDifficulty);
    
    // Submission Tests
    await runTest('Submit Valid Typing Result', testSubmitTypingResult);
    await runTest('Submit Invalid Data', testSubmitInvalidData);
    
    // Analytics Tests
    await runTest('Get Leaderboard', testGetLeaderboard);
    await runTest('Get Leaderboard with Limit', testGetLeaderboardWithLimit);
    await runTest('Get Analytics', testGetAnalytics);
    await runTest('Get User Stats', testGetUserStats);
    await runTest('Get Nonexistent User Stats', testGetNonexistentUserStats);
    
    // Calculation Tests
    await runTest('WPM Calculation', testWPMCalculation);
    await runTest('Accuracy Calculation', testAccuracyCalculation);
    
    // Error Handling Tests
    await runTest('Invalid Endpoint', testInvalidEndpoint);

    // Results Summary
    log('\n' + '=' .repeat(50), 'yellow');
    log('📊 Test Results Summary', 'bold');
    log(`✅ Tests Passed: ${testsPassed}`, 'green');
    log(`❌ Tests Failed: ${testsFailed}`, 'red');
    log(`📈 Success Rate: ${Math.round((testsPassed / (testsPassed + testsFailed)) * 100)}%`, 'blue');
    
    if (testsFailed === 0) {
        log('\n🎉 All tests passed! API is working correctly.', 'green');
        process.exit(0);
    } else {
        log('\n⚠️  Some tests failed. Please check the API implementation.', 'red');
        process.exit(1);
    }
}

// Check if server is running before starting tests
async function checkServerAndRun() {
    try {
        log('🔍 Checking if server is running...', 'yellow');
        await makeRequest('GET', '/api/info');
        log('✅ Server is running, starting tests...', 'green');
        await runAllTests();
    } catch (error) {
        log('❌ Server is not running!', 'red');
        log('Please start the server first: npm start', 'yellow');
        log('Then run the tests again: npm test', 'yellow');
        process.exit(1);
    }
}

// Run tests
checkServerAndRun();
