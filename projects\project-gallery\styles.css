/**
 * Interactive Project Gallery - Comprehensive Styling
 * Modern, responsive design with smooth animations and interactions
 */

/* CSS Variables for theming */
:root {
  /* Color palette */
  --primary-color: #667eea;
  --primary-dark: #5a67d8;
  --secondary-color: #764ba2;
  --accent-color: #f093fb;
  --success-color: #48bb78;
  --warning-color: #ed8936;
  --error-color: #f56565;
  
  /* Neutral colors */
  --bg-color: #ffffff;
  --surface-color: #f7fafc;
  --card-bg: #ffffff;
  --text-primary: #2d3748;
  --text-secondary: #4a5568;
  --text-muted: #718096;
  --border-color: #e2e8f0;
  --shadow-color: rgba(0, 0, 0, 0.1);
  
  /* Dark theme colors */
  --dark-bg: #1a202c;
  --dark-surface: #2d3748;
  --dark-card: #2d3748;
  --dark-text-primary: #f7fafc;
  --dark-text-secondary: #e2e8f0;
  --dark-text-muted: #a0aec0;
  --dark-border: #4a5568;
  --dark-shadow: rgba(0, 0, 0, 0.3);
  
  /* Spacing */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;
  
  /* Border radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  
  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  
  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-mono: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
  
  /* Z-index layers */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* Dark theme */
[data-theme="dark"] {
  --bg-color: var(--dark-bg);
  --surface-color: var(--dark-surface);
  --card-bg: var(--dark-card);
  --text-primary: var(--dark-text-primary);
  --text-secondary: var(--dark-text-secondary);
  --text-muted: var(--dark-text-muted);
  --border-color: var(--dark-border);
  --shadow-color: var(--dark-shadow);
}

/* Base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  background: linear-gradient(135deg, var(--bg-color) 0%, var(--surface-color) 100%);
  color: var(--text-primary);
  line-height: 1.6;
  min-height: 100vh;
  transition: all var(--transition-normal);
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--space-xl);
  min-height: 100vh;
}

/* Back Navigation */
.back-nav {
  margin-bottom: var(--space-xl);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.back-button {
  display: inline-flex;
  align-items: center;
  gap: var(--space-sm);
  background: rgba(99, 102, 241, 0.9);
  color: white;
  text-decoration: none;
  padding: var(--space-md) var(--space-lg);
  border-radius: var(--radius-lg);
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(99, 102, 241, 1);
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

.back-button:hover {
  background: rgba(79, 70, 229, 0.95);
  border-color: rgba(79, 70, 229, 1);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(99, 102, 241, 0.4);
}

/* Theme Toggle */
.theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: var(--card-bg);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.2rem;
}

.theme-toggle:hover {
  background: var(--surface-color);
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.2);
}

.theme-toggle:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Header */
.header {
  text-align: center;
  margin-bottom: var(--space-3xl);
  padding: var(--space-2xl) 0;
}

.title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--space-md);
  letter-spacing: 0.02em;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin-bottom: var(--space-2xl);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.gallery-stats {
  display: flex;
  justify-content: center;
  gap: var(--space-2xl);
  flex-wrap: wrap;
}

.stat {
  text-align: center;
  padding: var(--space-lg);
  background: var(--card-bg);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  min-width: 120px;
  transition: transform var(--transition-fast);
}

.stat:hover {
  transform: translateY(-2px);
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--space-xs);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 500;
}

/* Filter Controls */
.filter-controls {
  display: flex;
  justify-content: center;
  gap: var(--space-md);
  margin-bottom: var(--space-2xl);
  flex-wrap: wrap;
}

.filter-btn {
  background: var(--card-bg);
  color: var(--text-secondary);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--space-md) var(--space-xl);
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  position: relative;
  overflow: hidden;
}

.filter-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-normal);
}

.filter-btn:hover::before {
  left: 100%;
}

.filter-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.filter-btn.active {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  border-color: transparent;
  box-shadow: var(--shadow-lg);
}

/* Gallery Grid */
.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-2xl);
  margin-bottom: var(--space-3xl);
}

/* Project Items */
.project-item {
  background: var(--card-bg);
  border-radius: var(--radius-2xl);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
  position: relative;
  cursor: pointer;
}

.project-item:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-2xl);
}

.project-link {
  display: block;
  text-decoration: none;
  color: inherit;
  height: 100%;
}

.project-item.filtered-out {
  opacity: 0;
  transform: scale(0.8);
  pointer-events: none;
}

.project-preview {
  position: relative;
  overflow: hidden;
  aspect-ratio: 16 / 10;
  background: var(--card-bg);
}

.preview-image {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: #f8f9fa;
}

.preview-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-slow);
  display: block;
}

.project-item:hover .preview-image img {
  transform: scale(1.1);
}

.project-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(102, 126, 234, 0.9) 0%,
    rgba(118, 75, 162, 0.9) 100%
  );
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity var(--transition-normal);
  padding: var(--space-lg);
  text-align: center;
  overflow: hidden;
}

.project-item:hover .project-overlay {
  opacity: 1;
}

.project-info {
  margin-bottom: var(--space-lg);
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  max-height: 70%;
  overflow: hidden;
}

.project-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: white;
  margin-bottom: var(--space-md);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.2;
}

.project-description {
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--space-lg);
  line-height: 1.4;
  font-size: 0.9rem;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.project-tags {
  display: flex;
  gap: var(--space-xs);
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: var(--space-md);
}

.tag {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 4px 8px;
  border-radius: var(--radius-lg);
  font-size: 0.7rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  backdrop-filter: blur(10px);
}

.project-actions {
  display: flex;
  gap: var(--space-md);
  justify-content: center;
}

.screenshot-count,
.github-link {
  position: absolute;
  top: var(--space-md);
  right: var(--space-md);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: var(--space-xs) var(--space-md);
  border-radius: var(--radius-lg);
  font-size: 0.75rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.github-link {
  background: rgba(0, 200, 83, 0.9);
  color: white;
  font-weight: 600;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-md) var(--space-xl);
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  font-weight: 600;
  text-decoration: none;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  cursor: pointer;
  border: none;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
  min-width: 120px;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-normal);
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

/* Lightbox Modal */
.lightbox {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.95);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  backdrop-filter: blur(10px);
}

.lightbox.show {
  display: flex;
  animation: fadeIn var(--transition-normal);
}

.lightbox-content {
  background: var(--card-bg);
  border-radius: var(--radius-2xl);
  max-width: 95vw;
  max-height: 95vh;
  width: 1200px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: var(--shadow-2xl);
  animation: slideUp var(--transition-normal);
}

.lightbox-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-xl);
  border-bottom: 1px solid var(--border-color);
  background: var(--surface-color);
}

.lightbox-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
}

.close-btn {
  background: none;
  border: none;
  font-size: 2rem;
  color: var(--text-muted);
  cursor: pointer;
  padding: var(--space-sm);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: var(--border-color);
  color: var(--text-primary);
}

.lightbox-body {
  flex: 1;
  padding: var(--space-xl);
  overflow-y: auto;
}

.image-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-xl);
  min-height: 400px;
}

.nav-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  backdrop-filter: blur(10px);
}

.nav-btn:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: translateY(-50%) scale(1.1);
}

.prev-btn {
  left: var(--space-lg);
}

.next-btn {
  right: var(--space-lg);
}

.main-image-wrapper {
  position: relative;
  max-width: 100%;
  max-height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.main-image {
  max-width: 100%;
  max-height: 600px;
  object-fit: contain;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  transition: opacity var(--transition-normal);
}

.image-loader {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: none;
}

.image-loader.show {
  display: block;
}

.loader-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.image-info {
  text-align: center;
  margin-bottom: var(--space-xl);
}

.image-counter {
  font-size: 0.875rem;
  color: var(--text-muted);
  margin-bottom: var(--space-md);
  font-weight: 600;
}

.image-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
}

.image-description {
  color: var(--text-secondary);
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

.thumbnail-strip {
  display: flex;
  gap: var(--space-md);
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: var(--space-xl);
}

.thumbnail {
  width: 80px;
  height: 50px;
  object-fit: cover;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  border: 2px solid transparent;
}

.thumbnail:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-md);
}

.thumbnail.active {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-lg);
}

.lightbox-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-xl);
  border-top: 1px solid var(--border-color);
  background: var(--surface-color);
  flex-wrap: wrap;
  gap: var(--space-lg);
}

.project-links {
  display: flex;
  gap: var(--space-md);
}

.lightbox-controls {
  display: flex;
  gap: var(--space-md);
}

.control-btn {
  background: var(--border-color);
  color: var(--text-secondary);
  border: none;
  border-radius: var(--radius-md);
  width: 40px;
  height: 40px;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-btn:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.95);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  backdrop-filter: blur(10px);
}

.loading-overlay.show {
  display: flex;
}

.loading-content {
  text-align: center;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 6px solid var(--border-color);
  border-top: 6px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--space-lg);
}

.loading-text {
  font-size: 1.125rem;
  color: var(--text-secondary);
  font-weight: 600;
}

/* Toast Notifications */
.toast {
  position: fixed;
  bottom: var(--space-xl);
  left: 50%;
  transform: translateX(-50%);
  background: var(--text-primary);
  color: white;
  padding: var(--space-lg) var(--space-xl);
  border-radius: var(--radius-lg);
  font-weight: 600;
  z-index: var(--z-toast);
  opacity: 0;
  pointer-events: none;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-xl);
}

.toast.show {
  opacity: 1;
  transform: translateX(-50%) translateY(-10px);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .container {
    padding: var(--space-lg);
  }
  
  .gallery-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-xl);
  }
  
  .lightbox-content {
    width: 95vw;
    height: 95vh;
  }
  
  .nav-btn {
    width: 40px;
    height: 40px;
  }
  
  .prev-btn {
    left: var(--space-md);
  }
  
  .next-btn {
    right: var(--space-md);
  }
}

@media (max-width: 768px) {
  .gallery-stats {
    gap: var(--space-lg);
  }
  
  .stat {
    min-width: 100px;
    padding: var(--space-md);
  }
  
  .filter-controls {
    gap: var(--space-sm);
  }
  
  .filter-btn {
    padding: var(--space-sm) var(--space-lg);
    font-size: 0.75rem;
  }
  
  .gallery-grid {
    grid-template-columns: 1fr;
    gap: var(--space-lg);
  }
  
  .project-overlay {
    padding: var(--space-lg);
  }
  
  .project-actions {
    flex-direction: column;
    gap: var(--space-sm);
  }
  
  .lightbox-header,
  .lightbox-body,
  .lightbox-footer {
    padding: var(--space-lg);
  }
  
  .lightbox-footer {
    flex-direction: column;
    align-items: stretch;
  }
  
  .project-links {
    justify-content: center;
  }
  
  .lightbox-controls {
    justify-content: center;
  }
  
  .thumbnail-strip {
    gap: var(--space-sm);
  }
  
  .thumbnail {
    width: 60px;
    height: 40px;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 2.5rem;
  }
  
  .subtitle {
    font-size: 1rem;
  }
  
  .gallery-stats {
    flex-direction: column;
    align-items: center;
    gap: var(--space-md);
  }
  
  .filter-controls {
    flex-direction: column;
    align-items: center;
  }
  
  .nav-btn {
    width: 35px;
    height: 35px;
  }
  
  .main-image-wrapper {
    max-height: 300px;
  }
  
  .thumbnail {
    width: 50px;
    height: 35px;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus styles for keyboard navigation */
.filter-btn:focus,
.btn:focus,
.nav-btn:focus,
.close-btn:focus,
.control-btn:focus,
.thumbnail:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High contrast mode */
@media (prefers-contrast: high) {
  :root {
    --border-color: #000000;
    --shadow-color: rgba(0, 0, 0, 0.5);
  }
  
  [data-theme="dark"] {
    --border-color: #ffffff;
    --shadow-color: rgba(255, 255, 255, 0.3);
  }
}

/* Print styles */
@media print {
  .filter-controls,
  .lightbox,
  .loading-overlay,
  .toast {
    display: none !important;
  }

  .project-overlay {
    position: static;
    opacity: 1;
    background: var(--surface-color);
    color: var(--text-primary);
  }

  .project-title {
    color: var(--text-primary);
  }

  .project-description {
    color: var(--text-secondary);
  }

  .tag {
    background: var(--border-color);
    color: var(--text-primary);
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--surface-color);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}
