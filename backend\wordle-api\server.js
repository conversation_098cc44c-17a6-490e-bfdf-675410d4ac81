const express = require('express');
const cors = require('cors');
const path = require('path');
const { getTodaysWord, isValidGuess, isTargetWord } = require('./data/words');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// In-memory game storage (in production, use a database)
const games = new Map();
const stats = new Map();

// Game state management
class WordleGame {
  constructor(gameId) {
    this.gameId = gameId;
    this.targetWord = getTodaysWord();
    this.guesses = [];
    this.gameState = 'playing'; // playing, won, lost
    this.maxGuesses = 6;
    this.startTime = new Date();
  }

  makeGuess(word) {
    if (this.gameState !== 'playing') {
      throw new Error('Game is already finished');
    }

    if (this.guesses.length >= this.maxGuesses) {
      throw new Error('Maximum guesses reached');
    }

    const upperWord = word.toUpperCase();
    
    if (upperWord.length !== 5) {
      throw new Error('Word must be 5 letters long');
    }

    if (!isValidGuess(upperWord)) {
      throw new Error('Not a valid word');
    }

    // Calculate letter feedback
    const feedback = this.calculateFeedback(upperWord);
    
    const guess = {
      word: upperWord,
      feedback,
      timestamp: new Date()
    };

    this.guesses.push(guess);

    // Check win condition
    if (upperWord === this.targetWord) {
      this.gameState = 'won';
      this.endTime = new Date();
    } else if (this.guesses.length >= this.maxGuesses) {
      this.gameState = 'lost';
      this.endTime = new Date();
    }

    return guess;
  }

  calculateFeedback(guess) {
    const target = this.targetWord;
    const feedback = new Array(5).fill('absent');
    const targetLetters = target.split('');
    const guessLetters = guess.split('');

    // First pass: mark correct positions
    for (let i = 0; i < 5; i++) {
      if (guessLetters[i] === targetLetters[i]) {
        feedback[i] = 'correct';
        targetLetters[i] = null; // Mark as used
        guessLetters[i] = null; // Mark as used
      }
    }

    // Second pass: mark present letters
    for (let i = 0; i < 5; i++) {
      if (guessLetters[i] !== null) {
        const targetIndex = targetLetters.indexOf(guessLetters[i]);
        if (targetIndex !== -1) {
          feedback[i] = 'present';
          targetLetters[targetIndex] = null; // Mark as used
        }
      }
    }

    return feedback;
  }

  getGameState() {
    return {
      gameId: this.gameId,
      guesses: this.guesses,
      gameState: this.gameState,
      maxGuesses: this.maxGuesses,
      targetWord: this.gameState !== 'playing' ? this.targetWord : null,
      startTime: this.startTime,
      endTime: this.endTime || null
    };
  }
}

// API Routes

// Create new game
app.post('/api/game/new', (req, res) => {
  try {
    console.log('📝 Creating new game...');
    const gameId = Date.now().toString() + Math.random().toString(36).substr(2, 9);
    console.log('🎮 Game ID:', gameId);

    const game = new WordleGame(gameId);
    games.set(gameId, game);

    const response = {
      success: true,
      gameId,
      gameState: game.getGameState()
    };

    console.log('✅ Game created successfully');
    res.json(response);
  } catch (error) {
    console.error('❌ Error creating game:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Make a guess
app.post('/api/game/:gameId/guess', (req, res) => {
  try {
    const { gameId } = req.params;
    const { word } = req.body;

    if (!word) {
      return res.status(400).json({
        success: false,
        error: 'Word is required'
      });
    }

    const game = games.get(gameId);
    if (!game) {
      return res.status(404).json({
        success: false,
        error: 'Game not found'
      });
    }

    const guess = game.makeGuess(word);
    
    res.json({
      success: true,
      guess,
      gameState: game.getGameState()
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

// Get game state
app.get('/api/game/:gameId', (req, res) => {
  try {
    const { gameId } = req.params;
    const game = games.get(gameId);
    
    if (!game) {
      return res.status(404).json({
        success: false,
        error: 'Game not found'
      });
    }

    res.json({
      success: true,
      gameState: game.getGameState()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get today's word (for debugging - remove in production)
app.get('/api/debug/word', (req, res) => {
  res.json({
    word: getTodaysWord(),
    date: new Date().toISOString().split('T')[0]
  });
});

// Validate word
app.post('/api/validate', (req, res) => {
  try {
    const { word } = req.body;
    
    if (!word) {
      return res.status(400).json({
        success: false,
        error: 'Word is required'
      });
    }

    const upperWord = word.toUpperCase();
    const isValid = isValidGuess(upperWord);
    const isTarget = isTargetWord(upperWord);

    res.json({
      success: true,
      word: upperWord,
      isValid,
      isTarget
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get API info
app.get('/api/info', (req, res) => {
  res.json({
    name: 'Wordle API',
    version: '1.0.0',
    description: 'Complete Wordle game API with word validation, game state management, and statistics tracking',
    endpoints: {
      'POST /api/game/new': 'Create a new game',
      'POST /api/game/:gameId/guess': 'Make a guess',
      'GET /api/game/:gameId': 'Get game state',
      'POST /api/validate': 'Validate a word',
      'GET /api/info': 'Get API information'
    },
    totalWords: 315,
    totalValidGuesses: 2000,
    maxGuesses: 6
  });
});

// Serve demo client
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    error: 'Internal server error'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found'
  });
});

app.listen(PORT, () => {
  console.log(`🎯 Wordle API server running on port ${PORT}`);
  console.log(`📝 API Documentation: http://localhost:${PORT}/api/info`);
  console.log(`🎮 Demo Client: http://localhost:${PORT}`);
  console.log(`📅 Today's word: ${getTodaysWord()}`);
  console.log(`🔧 Server started successfully at ${new Date().toISOString()}`);
});
