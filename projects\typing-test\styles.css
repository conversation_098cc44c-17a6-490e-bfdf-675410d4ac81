/**
 * Typing Speed Test - Comprehensive Styling
 * Modern, responsive design with animations and accessibility
 */

/* CSS Variables for theming */
:root {
  /* Light theme colors */
  --bg-color: #ffffff;
  --text-color: #1a1a1b;
  --border-color: #d3d6da;
  --accent-color: #1976d2;
  --accent-hover: #1565c0;
  --success-color: #4caf50;
  --error-color: #f44336;
  --warning-color: #ff9800;
  --info-color: #2196f3;
  
  /* Text display colors */
  --text-bg: #f8f9fa;
  --text-correct: #4caf50;
  --text-incorrect: #f44336;
  --text-current: #2196f3;
  --text-pending: #666666;
  --cursor-color: #1976d2;
  
  /* UI elements */
  --card-bg: #ffffff;
  --card-shadow: rgba(0, 0, 0, 0.1);
  --modal-bg: rgba(255, 255, 255, 0.95);
  --modal-shadow: rgba(0, 0, 0, 0.3);
  --toast-bg: #323232;
  --toast-text: #ffffff;
  
  /* Input styling */
  --input-bg: #ffffff;
  --input-border: #d3d6da;
  --input-focus: #1976d2;
  
  /* Spacing */
  --gap-xs: 4px;
  --gap-sm: 8px;
  --gap-md: 12px;
  --gap-lg: 16px;
  --gap-xl: 24px;
  --gap-xxl: 32px;
  
  /* Border radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  
  /* Font sizes */
  --font-xs: 0.75rem;
  --font-sm: 0.875rem;
  --font-md: 1rem;
  --font-lg: 1.125rem;
  --font-xl: 1.25rem;
  --font-xxl: 1.5rem;
  --font-xxxl: 2rem;
  
  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* Dark theme */
[data-theme="dark"] {
  --bg-color: #121212;
  --text-color: #ffffff;
  --border-color: #333333;
  --text-bg: #1e1e1e;
  --text-pending: #aaaaaa;
  --card-bg: #1e1e1e;
  --card-shadow: rgba(0, 0, 0, 0.3);
  --modal-bg: rgba(18, 18, 18, 0.95);
  --input-bg: #1e1e1e;
  --input-border: #333333;
  --toast-bg: #f5f5f5;
  --toast-text: #000000;
}

/* Base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--bg-color);
  color: var(--text-color);
  line-height: 1.6;
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

.container {
  max-width: 1000px;
  margin: 0 auto;
  padding: var(--gap-xl);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  gap: var(--gap-xl);
}

/* Back Navigation */
.back-nav {
  margin-bottom: var(--gap-xl);
}

.back-button {
  display: inline-flex;
  align-items: center;
  gap: var(--gap-sm);
  color: var(--text-color);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  padding: var(--gap-sm) var(--gap-md);
  border-radius: var(--radius-sm);
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  transition: all var(--transition-fast);
  box-shadow: 0 2px 4px var(--card-shadow);
}

.back-button:hover {
  background: var(--accent-color);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px var(--card-shadow);
}

/* Header */
.header {
  text-align: center;
  margin-bottom: var(--gap-lg);
}

.title {
  font-size: 3rem;
  font-weight: 700;
  letter-spacing: 0.1em;
  color: var(--accent-color);
  margin-bottom: var(--gap-sm);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.subtitle {
  font-size: var(--font-lg);
  color: var(--text-color);
  opacity: 0.8;
}

/* Configuration Panel */
.config-panel {
  display: flex;
  justify-content: center;
  gap: var(--gap-xl);
  flex-wrap: wrap;
  padding: var(--gap-lg);
  background-color: var(--card-bg);
  border-radius: var(--radius-lg);
  box-shadow: 0 2px 8px var(--card-shadow);
}

.config-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--gap-sm);
}

.config-group label {
  font-weight: 600;
  color: var(--text-color);
  font-size: var(--font-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.config-select {
  background-color: var(--input-bg);
  color: var(--text-color);
  border: 2px solid var(--input-border);
  border-radius: var(--radius-md);
  padding: var(--gap-sm) var(--gap-md);
  font-size: var(--font-md);
  cursor: pointer;
  transition: border-color var(--transition-fast);
  min-width: 120px;
}

.config-select:focus {
  outline: none;
  border-color: var(--input-focus);
}

/* Statistics Panel */
.stats-panel {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--gap-lg);
  padding: var(--gap-lg);
  background-color: var(--card-bg);
  border-radius: var(--radius-lg);
  box-shadow: 0 2px 8px var(--card-shadow);
}

.stat-item {
  text-align: center;
  padding: var(--gap-md);
  border-radius: var(--radius-md);
  background-color: var(--text-bg);
  transition: transform var(--transition-fast);
}

.stat-item:hover {
  transform: translateY(-2px);
}

.stat-value {
  font-size: var(--font-xxl);
  font-weight: 700;
  color: var(--accent-color);
  margin-bottom: var(--gap-xs);
  font-family: 'Courier New', monospace;
}

.stat-label {
  font-size: var(--font-sm);
  color: var(--text-color);
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Test Container */
.test-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--gap-lg);
}

/* Text Display */
.text-display {
  background-color: var(--text-bg);
  border-radius: var(--radius-lg);
  padding: var(--gap-xxl);
  box-shadow: 0 2px 8px var(--card-shadow);
  position: relative;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.text-content {
  font-size: var(--font-lg);
  line-height: 1.8;
  font-family: 'Courier New', monospace;
  letter-spacing: 0.5px;
  text-align: left;
  max-width: 100%;
  word-wrap: break-word;
  position: relative;
}

/* Font size variations */
.text-content.small { font-size: var(--font-md); }
.text-content.medium { font-size: var(--font-lg); }
.text-content.large { font-size: var(--font-xl); }
.text-content.extra-large { font-size: var(--font-xxl); }

/* Character styling */
.char {
  position: relative;
  transition: all var(--transition-fast);
}

.char.correct {
  color: var(--text-correct);
  background-color: rgba(76, 175, 80, 0.1);
}

.char.incorrect {
  color: var(--text-incorrect);
  background-color: rgba(244, 67, 54, 0.2);
}

.char.current {
  background-color: var(--cursor-color);
  color: white;
  animation: blink 1s infinite;
}

.char.pending {
  color: var(--text-pending);
}

/* Cursor styles */
.cursor {
  position: absolute;
  background-color: var(--cursor-color);
  animation: blink 1s infinite;
  z-index: 10;
}

.cursor.line {
  width: 2px;
  height: 1.2em;
}

.cursor.block {
  width: 1ch;
  height: 1.2em;
  opacity: 0.7;
}

.cursor.underline {
  width: 1ch;
  height: 2px;
  bottom: 0;
}

/* Input Area */
.input-area {
  position: relative;
}

.typing-input {
  width: 100%;
  min-height: 120px;
  padding: var(--gap-lg);
  background-color: var(--input-bg);
  color: var(--text-color);
  border: 2px solid var(--input-border);
  border-radius: var(--radius-lg);
  font-size: var(--font-lg);
  font-family: 'Courier New', monospace;
  line-height: 1.6;
  resize: vertical;
  transition: border-color var(--transition-fast);
}

.typing-input:focus {
  outline: none;
  border-color: var(--input-focus);
  box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

.typing-input:disabled {
  background-color: var(--text-bg);
  cursor: not-allowed;
  opacity: 0.7;
}

/* Progress Bar */
.progress-bar {
  width: 100%;
  height: 8px;
  background-color: var(--border-color);
  border-radius: var(--radius-sm);
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--accent-color), var(--success-color));
  border-radius: var(--radius-sm);
  transition: width var(--transition-normal);
  width: 0%;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

/* Controls */
.controls {
  display: flex;
  justify-content: center;
  gap: var(--gap-lg);
  flex-wrap: wrap;
}

/* Buttons */
.btn {
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  padding: var(--gap-md) var(--gap-xl);
  font-size: var(--font-md);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

.btn:hover:not(:disabled) {
  background-color: var(--accent-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
}

.btn:active {
  transform: translateY(0);
}

.btn:disabled {
  background-color: var(--border-color);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-secondary {
  background-color: #757575;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #616161;
}

.btn-success {
  background-color: var(--success-color);
}

.btn-success:hover:not(:disabled) {
  background-color: #388e3c;
}

/* Modals */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--modal-shadow);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--gap-lg);
}

.modal.show {
  display: flex;
  animation: fadeIn var(--transition-normal);
}

.modal-content {
  background-color: var(--modal-bg);
  border-radius: var(--radius-xl);
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px var(--modal-shadow);
  animation: slideUp var(--transition-normal);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--gap-xl);
  border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
  font-size: var(--font-xxl);
  font-weight: 700;
  color: var(--text-color);
}

.close-btn {
  background: none;
  border: none;
  font-size: 2rem;
  color: var(--text-color);
  cursor: pointer;
  padding: var(--gap-sm);
  border-radius: var(--radius-md);
  transition: background-color var(--transition-fast);
}

.close-btn:hover {
  background-color: var(--border-color);
}

.modal-body {
  padding: var(--gap-xl);
}

/* Results Modal */
.results-summary {
  margin-bottom: var(--gap-xxl);
}

.result-main {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--gap-xl);
  margin-bottom: var(--gap-xl);
}

.result-wpm,
.result-accuracy {
  text-align: center;
  padding: var(--gap-xl);
  background-color: var(--text-bg);
  border-radius: var(--radius-lg);
}

.result-value {
  font-size: 3rem;
  font-weight: 700;
  color: var(--accent-color);
  font-family: 'Courier New', monospace;
}

.result-unit {
  font-size: var(--font-lg);
  color: var(--text-color);
  opacity: 0.8;
  margin-left: var(--gap-sm);
}

.results-details {
  background-color: var(--text-bg);
  border-radius: var(--radius-lg);
  padding: var(--gap-lg);
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--gap-sm) 0;
  border-bottom: 1px solid var(--border-color);
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-weight: 600;
  color: var(--text-color);
}

.detail-value {
  font-family: 'Courier New', monospace;
  color: var(--accent-color);
  font-weight: 600;
}

/* Chart Container */
.chart-container {
  margin: var(--gap-xl) 0;
  text-align: center;
}

.chart-container h3 {
  margin-bottom: var(--gap-lg);
  color: var(--text-color);
}

.chart-container canvas {
  max-width: 100%;
  height: auto;
  border-radius: var(--radius-md);
}

/* Mistake Analysis */
.mistake-analysis {
  margin: var(--gap-xl) 0;
}

.mistake-analysis h3 {
  margin-bottom: var(--gap-lg);
  color: var(--text-color);
}

.mistake-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--gap-md);
}

.mistake-item {
  background-color: var(--text-bg);
  padding: var(--gap-md);
  border-radius: var(--radius-md);
  border-left: 4px solid var(--error-color);
}

.mistake-char {
  font-family: 'Courier New', monospace;
  font-weight: 700;
  color: var(--error-color);
}

.mistake-count {
  font-size: var(--font-sm);
  color: var(--text-color);
  opacity: 0.8;
}

/* Results Actions */
.results-actions {
  display: flex;
  gap: var(--gap-md);
  justify-content: center;
  margin-top: var(--gap-xl);
  flex-wrap: wrap;
}

/* Statistics Overview */
.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--gap-lg);
  margin-bottom: var(--gap-xxl);
}

.overview-stat {
  text-align: center;
  padding: var(--gap-lg);
  background-color: var(--text-bg);
  border-radius: var(--radius-lg);
}

.overview-value {
  font-size: var(--font-xxl);
  font-weight: 700;
  color: var(--accent-color);
  font-family: 'Courier New', monospace;
  margin-bottom: var(--gap-xs);
}

.overview-label {
  font-size: var(--font-sm);
  color: var(--text-color);
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Progress Tracking */
.progress-tracking {
  margin: var(--gap-xl) 0;
  text-align: center;
}

.progress-tracking h3 {
  margin-bottom: var(--gap-lg);
  color: var(--text-color);
}

/* Achievements */
.achievements {
  margin: var(--gap-xl) 0;
}

.achievements h3 {
  margin-bottom: var(--gap-lg);
  color: var(--text-color);
}

.achievement-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--gap-md);
}

.achievement {
  background-color: var(--text-bg);
  padding: var(--gap-lg);
  border-radius: var(--radius-lg);
  text-align: center;
  transition: transform var(--transition-fast);
}

.achievement:hover {
  transform: translateY(-2px);
}

.achievement.unlocked {
  background: linear-gradient(135deg, var(--success-color), #66bb6a);
  color: white;
}

.achievement-icon {
  font-size: var(--font-xxl);
  margin-bottom: var(--gap-sm);
}

.achievement-title {
  font-weight: 600;
  margin-bottom: var(--gap-xs);
}

.achievement-description {
  font-size: var(--font-sm);
  opacity: 0.8;
}

/* Settings */
.setting {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--gap-lg) 0;
  border-bottom: 1px solid var(--border-color);
}

.setting:last-child {
  border-bottom: none;
}

.setting-text {
  flex: 1;
}

.setting-title {
  font-weight: 600;
  margin-bottom: var(--gap-xs);
}

.setting-description {
  font-size: var(--font-sm);
  opacity: 0.8;
}

.setting-switch {
  position: relative;
}

.switch-input {
  opacity: 0;
  width: 0;
  height: 0;
}

.switch-label {
  display: block;
  width: 50px;
  height: 26px;
  background-color: var(--border-color);
  border-radius: 13px;
  cursor: pointer;
  transition: background-color var(--transition-fast);
  position: relative;
}

.switch-label::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 22px;
  height: 22px;
  background-color: white;
  border-radius: 50%;
  transition: transform var(--transition-fast);
}

.switch-input:checked + .switch-label {
  background-color: var(--accent-color);
}

.switch-input:checked + .switch-label::after {
  transform: translateX(24px);
}

.setting-control {
  min-width: 120px;
}

/* Action Buttons */
.action-buttons {
  position: fixed;
  bottom: var(--gap-xl);
  right: var(--gap-xl);
  display: flex;
  flex-direction: column;
  gap: var(--gap-md);
}

.icon-btn {
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 50%;
  width: 56px;
  height: 56px;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.icon-btn:hover {
  background-color: var(--accent-hover);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

/* Toast Notifications */
.toast {
  position: fixed;
  bottom: 100px;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--toast-bg);
  color: var(--toast-text);
  padding: var(--gap-md) var(--gap-lg);
  border-radius: var(--radius-md);
  font-weight: 600;
  z-index: 2000;
  opacity: 0;
  pointer-events: none;
  transition: opacity var(--transition-normal);
}

.toast.show {
  opacity: 1;
  animation: toastSlide var(--transition-normal);
}

/* Animations */
@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(30px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes toastSlide {
  from { 
    opacity: 0;
    transform: translateX(-50%) translateY(10px);
  }
  to { 
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: var(--gap-lg);
  }
  
  .title {
    font-size: 2rem;
  }
  
  .config-panel {
    flex-direction: column;
    align-items: center;
  }
  
  .stats-panel {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .text-display {
    padding: var(--gap-lg);
  }
  
  .text-content {
    font-size: var(--font-md);
  }
  
  .typing-input {
    min-height: 100px;
  }
  
  .result-main {
    grid-template-columns: 1fr;
  }
  
  .results-actions {
    flex-direction: column;
  }
  
  .stats-overview {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .achievement-grid {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    bottom: var(--gap-md);
    right: var(--gap-md);
  }
  
  .icon-btn {
    width: 48px;
    height: 48px;
  }
}

@media (max-width: 480px) {
  .stats-panel {
    grid-template-columns: 1fr;
  }
  
  .text-content {
    font-size: var(--font-sm);
  }
  
  .stats-overview {
    grid-template-columns: 1fr;
  }
  
  .modal-content {
    margin: var(--gap-md);
  }
  
  .modal-header,
  .modal-body {
    padding: var(--gap-lg);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus styles for keyboard navigation */
.typing-input:focus,
.btn:focus,
.icon-btn:focus,
.close-btn:focus,
.config-select:focus {
  outline: 2px solid var(--accent-color);
  outline-offset: 2px;
}

/* High contrast mode adjustments */
@media (prefers-contrast: high) {
  :root {
    --border-color: #000000;
    --text-correct: #008000;
    --text-incorrect: #ff0000;
  }

  [data-theme="dark"] {
    --border-color: #ffffff;
    --text-correct: #00ff00;
    --text-incorrect: #ff4444;
  }
}

/* Print styles */
@media print {
  .action-buttons,
  .controls,
  .config-panel {
    display: none;
  }

  .modal {
    position: static;
    background: none;
    box-shadow: none;
  }

  .modal-content {
    box-shadow: none;
    border: 1px solid #000;
  }
}
