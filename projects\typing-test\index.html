<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Typing Speed Test - <PERSON><PERSON><PERSON></title>
  <meta name="description" content="Real-time typing speed test with WPM calculation, accuracy tracking, and performance analytics">
  <meta name="keywords" content="typing test, WPM, words per minute, typing speed, accuracy, portfolio">
  <meta name="author" content="Zayden Sharp">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://zaydenjs.github.io/PortFolio-2025/projects/typing-test/">
  <meta property="og:title" content="Typing Speed Test - Measure Your Typing Skills">
  <meta property="og:description" content="Real-time typing speed test with comprehensive analytics">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary">
  <meta property="twitter:url" content="https://zaydenjs.github.io/PortFolio-2025/projects/typing-test/">
  <meta property="twitter:title" content="Typing Speed Test - Measure Your Typing Skills">
  <meta property="twitter:description" content="Real-time typing speed test with comprehensive analytics">

  <link rel="icon" type="image/svg+xml" href="../../assets/favicon.svg">
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="container">
    <!-- Back Navigation -->
    <nav class="back-nav">
      <a href="../../index.html" class="back-button">← Back to Portfolio</a>
    </nav>

    <!-- Header -->
    <header class="header">
      <h1 class="title">TYPING MASTER</h1>
      <p class="subtitle">Test your typing speed and accuracy</p>
    </header>

    <!-- Test Configuration -->
    <div class="config-panel">
      <div class="config-group">
        <label for="testDuration">Duration:</label>
        <select id="testDuration" class="config-select">
          <option value="15">15 seconds</option>
          <option value="30" selected>30 seconds</option>
          <option value="60">1 minute</option>
          <option value="120">2 minutes</option>
          <option value="300">5 minutes</option>
        </select>
      </div>
      
      <div class="config-group">
        <label for="textType">Text Type:</label>
        <select id="textType" class="config-select">
          <option value="common">Common Words</option>
          <option value="quotes" selected>Famous Quotes</option>
          <option value="programming">Programming</option>
          <option value="numbers">Numbers</option>
          <option value="punctuation">Punctuation</option>
        </select>
      </div>
      
      <div class="config-group">
        <label for="difficulty">Difficulty:</label>
        <select id="difficulty" class="config-select">
          <option value="easy">Easy</option>
          <option value="medium" selected>Medium</option>
          <option value="hard">Hard</option>
        </select>
      </div>
    </div>

    <!-- Live Statistics -->
    <div class="stats-panel">
      <div class="stat-item">
        <div class="stat-value" id="wpmValue">0</div>
        <div class="stat-label">WPM</div>
      </div>
      <div class="stat-item">
        <div class="stat-value" id="accuracyValue">100%</div>
        <div class="stat-label">Accuracy</div>
      </div>
      <div class="stat-item">
        <div class="stat-value" id="timeValue">30</div>
        <div class="stat-label">Time Left</div>
      </div>
      <div class="stat-item">
        <div class="stat-value" id="progressValue">0%</div>
        <div class="stat-label">Progress</div>
      </div>
    </div>

    <!-- Test Area -->
    <main class="test-container">
      <div class="text-display" id="textDisplay">
        <div class="text-content" id="textContent">
          Click "Start Test" to begin your typing challenge!
        </div>
      </div>
      
      <div class="input-area">
        <textarea 
          id="typingInput" 
          class="typing-input" 
          placeholder="Start typing here..."
          disabled
          spellcheck="false"
          autocomplete="off"
          autocorrect="off"
          autocapitalize="off"
        ></textarea>
      </div>
      
      <div class="progress-bar">
        <div class="progress-fill" id="progressFill"></div>
      </div>
    </main>

    <!-- Control Buttons -->
    <div class="controls">
      <button class="btn btn-primary" id="startBtn">Start Test</button>
      <button class="btn btn-secondary" id="resetBtn" disabled>Reset</button>
      <button class="btn btn-secondary" id="pauseBtn" disabled>Pause</button>
    </div>

    <!-- Results Modal -->
    <div class="modal" id="resultsModal">
      <div class="modal-content">
        <div class="modal-header">
          <h2>🎉 Test Complete!</h2>
          <button class="close-btn" id="closeResultsBtn">&times;</button>
        </div>
        <div class="modal-body">
          <div class="results-summary">
            <div class="result-main">
              <div class="result-wpm">
                <span class="result-value" id="finalWPM">0</span>
                <span class="result-unit">WPM</span>
              </div>
              <div class="result-accuracy">
                <span class="result-value" id="finalAccuracy">100%</span>
                <span class="result-unit">Accuracy</span>
              </div>
            </div>
            
            <div class="results-details">
              <div class="detail-row">
                <span class="detail-label">Raw WPM:</span>
                <span class="detail-value" id="rawWPM">0</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Characters Typed:</span>
                <span class="detail-value" id="charactersTyped">0</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Correct Characters:</span>
                <span class="detail-value" id="correctChars">0</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Incorrect Characters:</span>
                <span class="detail-value" id="incorrectChars">0</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Words Completed:</span>
                <span class="detail-value" id="wordsCompleted">0</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Test Duration:</span>
                <span class="detail-value" id="testDurationResult">30s</span>
              </div>
            </div>
          </div>
          
          <!-- Performance Chart -->
          <div class="chart-container">
            <h3>Performance Over Time</h3>
            <canvas id="performanceChart" width="400" height="200"></canvas>
          </div>
          
          <!-- Mistake Analysis -->
          <div class="mistake-analysis" id="mistakeAnalysis">
            <h3>Common Mistakes</h3>
            <div class="mistake-list" id="mistakeList">
              <!-- Mistakes will be populated by JavaScript -->
            </div>
          </div>
          
          <div class="results-actions">
            <button class="btn btn-primary" id="retryBtn">Try Again</button>
            <button class="btn btn-secondary" id="shareResultsBtn">Share Results</button>
            <button class="btn btn-secondary" id="saveResultsBtn">Save Results</button>
          </div>
        </div>
      </div>
    </div>

    <!-- Statistics Modal -->
    <div class="modal" id="statsModal">
      <div class="modal-content">
        <div class="modal-header">
          <h2>📊 Your Statistics</h2>
          <button class="close-btn" id="closeStatsBtn">&times;</button>
        </div>
        <div class="modal-body">
          <div class="stats-overview">
            <div class="overview-stat">
              <div class="overview-value" id="avgWPM">0</div>
              <div class="overview-label">Average WPM</div>
            </div>
            <div class="overview-stat">
              <div class="overview-value" id="bestWPM">0</div>
              <div class="overview-label">Best WPM</div>
            </div>
            <div class="overview-stat">
              <div class="overview-value" id="avgAccuracy">100%</div>
              <div class="overview-label">Average Accuracy</div>
            </div>
            <div class="overview-stat">
              <div class="overview-value" id="testsCompleted">0</div>
              <div class="overview-label">Tests Completed</div>
            </div>
          </div>
          
          <div class="progress-tracking">
            <h3>Progress Over Time</h3>
            <canvas id="progressChart" width="400" height="200"></canvas>
          </div>
          
          <div class="achievements">
            <h3>Achievements</h3>
            <div class="achievement-grid" id="achievementGrid">
              <!-- Achievements will be populated by JavaScript -->
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Settings Modal -->
    <div class="modal" id="settingsModal">
      <div class="modal-content">
        <div class="modal-header">
          <h2>⚙️ Settings</h2>
          <button class="close-btn" id="closeSettingsBtn">&times;</button>
        </div>
        <div class="modal-body">
          <div class="setting">
            <div class="setting-text">
              <div class="setting-title">Sound Effects</div>
              <div class="setting-description">Play sounds for keystrokes and errors</div>
            </div>
            <div class="setting-switch">
              <input type="checkbox" id="soundEffects" class="switch-input" checked>
              <label for="soundEffects" class="switch-label"></label>
            </div>
          </div>
          
          <div class="setting">
            <div class="setting-text">
              <div class="setting-title">Show Live WPM</div>
              <div class="setting-description">Display WPM in real-time during test</div>
            </div>
            <div class="setting-switch">
              <input type="checkbox" id="showLiveWPM" class="switch-input" checked>
              <label for="showLiveWPM" class="switch-label"></label>
            </div>
          </div>
          
          <div class="setting">
            <div class="setting-text">
              <div class="setting-title">Highlight Errors</div>
              <div class="setting-description">Highlight incorrect characters in red</div>
            </div>
            <div class="setting-switch">
              <input type="checkbox" id="highlightErrors" class="switch-input" checked>
              <label for="highlightErrors" class="switch-label"></label>
            </div>
          </div>
          
          <div class="setting">
            <div class="setting-text">
              <div class="setting-title">Dark Theme</div>
              <div class="setting-description">Switch to dark mode</div>
            </div>
            <div class="setting-switch">
              <input type="checkbox" id="darkTheme" class="switch-input">
              <label for="darkTheme" class="switch-label"></label>
            </div>
          </div>
          
          <div class="setting">
            <div class="setting-text">
              <div class="setting-title">Font Size</div>
              <div class="setting-description">Adjust text display size</div>
            </div>
            <div class="setting-control">
              <select id="fontSize" class="config-select">
                <option value="small">Small</option>
                <option value="medium" selected>Medium</option>
                <option value="large">Large</option>
                <option value="extra-large">Extra Large</option>
              </select>
            </div>
          </div>
          
          <div class="setting">
            <div class="setting-text">
              <div class="setting-title">Caret Style</div>
              <div class="setting-description">Choose cursor appearance</div>
            </div>
            <div class="setting-control">
              <select id="caretStyle" class="config-select">
                <option value="line" selected>Line</option>
                <option value="block">Block</option>
                <option value="underline">Underline</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
      <button class="icon-btn" id="statsBtn" aria-label="Statistics" title="View statistics">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
          <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
        </svg>
      </button>
      <button class="icon-btn" id="settingsBtn" aria-label="Settings" title="Settings">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
          <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.82,11.69,4.82,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
        </svg>
      </button>
    </div>

    <!-- Toast Notifications -->
    <div class="toast" id="toast"></div>
  </div>

  <script src="text-data.js"></script>
  <script src="main.js"></script>
</body>
</html>
