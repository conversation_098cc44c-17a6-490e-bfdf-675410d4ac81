# Multi-stage build for production optimization
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY src/ ./src/

# Build TypeScript
RUN npm run build

# Production stage
FROM node:18-alpine AS production

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S sudoku -u 1001

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install only production dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy built application from builder stage
COPY --from=builder /app/dist ./dist

# Copy public assets if they exist
COPY --chown=sudoku:nodejs public/ ./public/ 2>/dev/null || true

# Create necessary directories
RUN mkdir -p logs data && \
    chown -R sudoku:nodejs /app

# Switch to non-root user
USER sudoku

# Expose port
EXPOSE 3002

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node dist/health-check.js || exit 1

# Start the application
CMD ["node", "dist/server.js"]

# Labels for better maintainability
LABEL maintainer="Zayden Sharp <<EMAIL>>"
LABEL version="2.0.0"
LABEL description="Enterprise Sudoku Solver API"
LABEL org.opencontainers.image.source="https://github.com/ZaydenJS/PortFolio-2025"
