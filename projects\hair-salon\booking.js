/**
 * Booking System Functionality
 * Professional appointment booking with form validation and user experience
 */

document.addEventListener('DOMContentLoaded', function() {
    initBookingForm();
    initDateValidation();
    initServiceCalculator();
    initFormValidation();
    
    console.log('Booking system loaded successfully');
});

/**
 * Initialize booking form functionality
 */
function initBookingForm() {
    const form = document.getElementById('bookingForm');
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (validateForm()) {
            submitBookingRequest();
        }
    });
}

/**
 * Initialize date validation
 */
function initDateValidation() {
    const preferredDate = document.getElementById('preferredDate');
    const alternativeDate = document.getElementById('alternativeDate');
    
    // Set minimum date to tomorrow
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const minDate = tomorrow.toISOString().split('T')[0];
    
    preferredDate.min = minDate;
    alternativeDate.min = minDate;
    
    // Disable Sundays and Mondays (salon closed)
    preferredDate.addEventListener('change', function() {
        validateSelectedDate(this);
    });
    
    alternativeDate.addEventListener('change', function() {
        validateSelectedDate(this);
    });
}

/**
 * Validate selected date (no Sundays or Mondays)
 */
function validateSelectedDate(dateInput) {
    const selectedDate = new Date(dateInput.value);
    const dayOfWeek = selectedDate.getDay();
    
    if (dayOfWeek === 0 || dayOfWeek === 1) { // Sunday = 0, Monday = 1
        alert('We are closed on Sundays and Mondays. Please select Tuesday through Saturday.');
        dateInput.value = '';
        return false;
    }
    return true;
}

/**
 * Initialize service price calculator
 */
function initServiceCalculator() {
    const serviceSelect = document.getElementById('service');
    const additionalServices = document.querySelectorAll('input[name="additionalServices"]');
    
    serviceSelect.addEventListener('change', updatePriceEstimate);
    additionalServices.forEach(service => {
        service.addEventListener('change', updatePriceEstimate);
    });
    
    // Create price display element
    const priceDisplay = document.createElement('div');
    priceDisplay.className = 'price-estimate';
    priceDisplay.innerHTML = '<h4>Estimated Total: <span id="totalPrice">$0</span></h4>';
    
    const serviceSection = document.querySelector('.form-section:nth-child(2)');
    serviceSection.appendChild(priceDisplay);
}

/**
 * Update price estimate based on selected services
 */
function updatePriceEstimate() {
    const serviceSelect = document.getElementById('service');
    const additionalServices = document.querySelectorAll('input[name="additionalServices"]:checked');
    
    let total = 0;
    
    // Base service prices
    const servicePrices = {
        'blonde-specialist': 180,
        'color-correction': 220,
        'hair-coloring': 120,
        'precision-cut': 60,
        'deep-conditioning': 40,
        'special-occasion': 80,
        'consultation': 0
    };
    
    // Additional service prices
    const additionalPrices = {
        'cut': 60,
        'treatment': 40,
        'styling': 30
    };
    
    // Calculate base service
    if (serviceSelect.value && servicePrices[serviceSelect.value] !== undefined) {
        total += servicePrices[serviceSelect.value];
    }
    
    // Calculate additional services
    additionalServices.forEach(service => {
        if (additionalPrices[service.value]) {
            total += additionalPrices[service.value];
        }
    });
    
    // Update display
    const totalPriceElement = document.getElementById('totalPrice');
    if (totalPriceElement) {
        totalPriceElement.textContent = total > 0 ? `$${total}+` : '$0';
    }
}

/**
 * Initialize form validation
 */
function initFormValidation() {
    const inputs = document.querySelectorAll('input[required], select[required]');
    
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });
        
        input.addEventListener('input', function() {
            clearFieldError(this);
        });
    });
}

/**
 * Validate individual form field
 */
function validateField(field) {
    const value = field.value.trim();
    let isValid = true;
    let errorMessage = '';
    
    // Required field validation
    if (field.hasAttribute('required') && !value) {
        isValid = false;
        errorMessage = 'This field is required';
    }
    
    // Email validation
    if (field.type === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            isValid = false;
            errorMessage = 'Please enter a valid email address';
        }
    }
    
    // Phone validation
    if (field.type === 'tel' && value) {
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        if (!phoneRegex.test(value.replace(/[\s\-\(\)]/g, ''))) {
            isValid = false;
            errorMessage = 'Please enter a valid phone number';
        }
    }
    
    // Display validation result
    if (!isValid) {
        showFieldError(field, errorMessage);
    } else {
        clearFieldError(field);
    }
    
    return isValid;
}

/**
 * Show field error
 */
function showFieldError(field, message) {
    clearFieldError(field);
    
    field.classList.add('error');
    
    const errorElement = document.createElement('span');
    errorElement.className = 'error-message';
    errorElement.textContent = message;
    
    field.parentNode.appendChild(errorElement);
}

/**
 * Clear field error
 */
function clearFieldError(field) {
    field.classList.remove('error');
    
    const existingError = field.parentNode.querySelector('.error-message');
    if (existingError) {
        existingError.remove();
    }
}

/**
 * Validate entire form
 */
function validateForm() {
    const requiredFields = document.querySelectorAll('input[required], select[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!validateField(field)) {
            isValid = false;
        }
    });
    
    // Validate at least one date is selected
    const preferredDate = document.getElementById('preferredDate');
    const alternativeDate = document.getElementById('alternativeDate');
    
    if (!preferredDate.value && !alternativeDate.value) {
        showFieldError(preferredDate, 'Please select at least one preferred date');
        isValid = false;
    }
    
    return isValid;
}

/**
 * Submit booking request
 */
function submitBookingRequest() {
    const form = document.getElementById('bookingForm');
    const formData = new FormData(form);
    
    // Show loading state
    const submitBtn = document.querySelector('.submit-btn');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'Submitting...';
    submitBtn.disabled = true;
    
    // Simulate form submission (in real app, this would send to server)
    setTimeout(() => {
        showBookingConfirmation(formData);
        
        // Reset button
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    }, 2000);
}

/**
 * Show booking confirmation
 */
function showBookingConfirmation(formData) {
    const firstName = formData.get('firstName');
    const service = formData.get('service');
    const preferredDate = formData.get('preferredDate');
    const preferredTime = formData.get('preferredTime');
    
    // Create confirmation modal
    const modal = document.createElement('div');
    modal.className = 'booking-confirmation-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="confirmation-header">
                <div class="success-icon">✅</div>
                <h3>Booking Request Submitted!</h3>
            </div>
            <div class="confirmation-body">
                <p>Thank you, <strong>${firstName}</strong>! Your appointment request has been received.</p>
                
                <div class="booking-summary">
                    <h4>Appointment Details:</h4>
                    <div class="summary-item">
                        <strong>Service:</strong> ${getServiceName(service)}
                    </div>
                    <div class="summary-item">
                        <strong>Preferred Date:</strong> ${formatDate(preferredDate)}
                    </div>
                    <div class="summary-item">
                        <strong>Preferred Time:</strong> ${formatTime(preferredTime)}
                    </div>
                </div>
                
                <div class="next-steps">
                    <h4>What's Next:</h4>
                    <ul>
                        <li>We'll contact you within 24 hours to confirm your appointment</li>
                        <li>You'll receive a confirmation email with all details</li>
                        <li>If you need to make changes, please call us at (555) 123-4567</li>
                    </ul>
                </div>
                
                <div class="confirmation-actions">
                    <button class="btn-primary" onclick="closeConfirmation()">Close</button>
                    <button class="btn-secondary" onclick="window.location.href='index.html'">Return Home</button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Animate modal in
    setTimeout(() => {
        modal.classList.add('show');
    }, 10);
    
    // Clear form
    document.getElementById('bookingForm').reset();
    updatePriceEstimate();
}

/**
 * Close confirmation modal
 */
function closeConfirmation() {
    const modal = document.querySelector('.booking-confirmation-modal');
    if (modal) {
        document.body.removeChild(modal);
    }
}

/**
 * Helper functions
 */
function getServiceName(serviceValue) {
    const serviceNames = {
        'blonde-specialist': 'Blonde Specialist',
        'color-correction': 'Color Correction',
        'hair-coloring': 'Hair Coloring',
        'precision-cut': 'Precision Cut',
        'deep-conditioning': 'Deep Conditioning',
        'special-occasion': 'Special Occasion Styling',
        'consultation': 'Consultation'
    };
    return serviceNames[serviceValue] || serviceValue;
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
    });
}

function formatTime(timeString) {
    const [hour] = timeString.split(':');
    const hourNum = parseInt(hour);
    const ampm = hourNum >= 12 ? 'PM' : 'AM';
    const displayHour = hourNum > 12 ? hourNum - 12 : hourNum;
    return `${displayHour}:00 ${ampm}`;
}

/**
 * Add booking page styles
 */
function addBookingStyles() {
    const style = document.createElement('style');
    style.textContent = `
        /* Booking Hero */
        .booking-hero {
            background: linear-gradient(135deg, var(--light-pink) 0%, var(--cream) 100%);
            padding: 120px 0 80px;
            text-align: center;
        }
        
        .booking-hero-content h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
        }
        
        /* Booking Form Section */
        .booking-form-section {
            padding: 80px 0;
            background: var(--white);
        }
        
        .booking-layout {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 4rem;
            align-items: start;
        }
        
        .booking-form-container {
            background: var(--light-pink);
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 20px 40px var(--shadow-hover);
        }
        
        .form-subtitle {
            color: var(--text-light);
            margin-bottom: 2rem;
        }
        
        .form-section {
            margin-bottom: 2.5rem;
            padding-bottom: 2rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .form-section:last-child {
            border-bottom: none;
        }
        
        .form-section h3 {
            color: var(--dark-pink);
            margin-bottom: 1.5rem;
            font-size: 1.3rem;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-dark);
            font-weight: 500;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.8rem;
            border: 2px solid rgba(255, 255, 255, 0.5);
            border-radius: 8px;
            background: var(--white);
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--dark-pink);
        }
        
        .form-group input.error,
        .form-group select.error {
            border-color: #ef4444;
        }
        
        .error-message {
            color: #ef4444;
            font-size: 0.8rem;
            margin-top: 0.3rem;
            display: block;
        }
        
        .checkbox-group {
            display: flex;
            flex-direction: column;
            gap: 0.8rem;
        }
        
        .checkbox-label {
            display: flex;
            align-items: center;
            gap: 0.8rem;
            cursor: pointer;
            font-weight: normal !important;
        }
        
        .checkbox-label input[type="checkbox"] {
            width: auto;
            margin: 0;
        }
        
        .price-estimate {
            background: var(--white);
            padding: 1.5rem;
            border-radius: 10px;
            margin-top: 1rem;
            text-align: center;
        }
        
        .price-estimate h4 {
            color: var(--dark-pink);
            margin: 0;
        }
        
        .form-submit {
            text-align: center;
            margin-top: 2rem;
        }
        
        .submit-btn {
            width: 100%;
            padding: 1.2rem;
            font-size: 1.1rem;
            margin-bottom: 1rem;
        }
        
        .form-note {
            color: var(--text-light);
            font-size: 0.9rem;
            margin: 0;
        }
        
        /* Booking Info */
        .booking-info {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }
        
        .info-card {
            background: var(--white);
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px var(--shadow);
        }
        
        .info-card h3 {
            color: var(--dark-pink);
            margin-bottom: 1rem;
        }
        
        .info-list {
            list-style: none;
            padding: 0;
        }
        
        .info-list li {
            padding: 0.5rem 0;
            color: var(--text-light);
            border-bottom: 1px solid var(--light-pink);
        }
        
        .info-list li:before {
            content: "✓ ";
            color: var(--dark-pink);
            font-weight: bold;
            margin-right: 0.5rem;
        }
        
        .info-list li:last-child {
            border-bottom: none;
        }
        
        .contact-details {
            display: flex;
            flex-direction: column;
            gap: 0.8rem;
        }
        
        .contact-item {
            color: var(--text-light);
        }
        
        /* Confirmation Modal */
        .booking-confirmation-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .booking-confirmation-modal.show {
            opacity: 1;
        }
        
        .booking-confirmation-modal .modal-content {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            max-width: 600px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            transform: translateY(50px);
            transition: transform 0.3s ease;
        }
        
        .booking-confirmation-modal.show .modal-content {
            transform: translateY(0);
        }
        
        .confirmation-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .success-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .confirmation-header h3 {
            color: var(--dark-pink);
            margin: 0;
        }
        
        .booking-summary {
            background: var(--light-pink);
            padding: 1.5rem;
            border-radius: 10px;
            margin: 1.5rem 0;
        }
        
        .summary-item {
            margin-bottom: 0.8rem;
            color: var(--text-dark);
        }
        
        .next-steps {
            margin: 1.5rem 0;
        }
        
        .next-steps h4 {
            color: var(--dark-pink);
            margin-bottom: 1rem;
        }
        
        .next-steps ul {
            list-style: none;
            padding: 0;
        }
        
        .next-steps li {
            padding: 0.5rem 0;
            color: var(--text-light);
        }
        
        .next-steps li:before {
            content: "→ ";
            color: var(--dark-pink);
            font-weight: bold;
            margin-right: 0.5rem;
        }
        
        .confirmation-actions {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .booking-layout {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .confirmation-actions {
                flex-direction: column;
            }
        }
    `;
    
    document.head.appendChild(style);
}

// Initialize booking styles
addBookingStyles();
