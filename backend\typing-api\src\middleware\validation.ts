/**
 * Request validation middleware using Joi
 */

import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';
import { ValidationError } from './errorHandler';

/**
 * Validation middleware factory
 */
export const validate = (schema: {
  body?: Joi.ObjectSchema;
  query?: Joi.ObjectSchema;
  params?: Joi.ObjectSchema;
}) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const errors: any[] = [];

    // Validate request body
    if (schema.body) {
      const { error } = schema.body.validate(req.body);
      if (error) {
        errors.push({
          location: 'body',
          details: error.details
        });
      }
    }

    // Validate query parameters
    if (schema.query) {
      const { error } = schema.query.validate(req.query);
      if (error) {
        errors.push({
          location: 'query',
          details: error.details
        });
      }
    }

    // Validate route parameters
    if (schema.params) {
      const { error } = schema.params.validate(req.params);
      if (error) {
        errors.push({
          location: 'params',
          details: error.details
        });
      }
    }

    if (errors.length > 0) {
      throw new ValidationError('Request validation failed', errors);
    }

    next();
  };
};

/**
 * Common validation schemas
 */
export const schemas = {
  // Typing test submission
  submitTest: {
    body: Joi.object({
      textId: Joi.number().integer().positive().required(),
      startTime: Joi.string().isoDate().required(),
      endTime: Joi.string().isoDate().required(),
      keystrokes: Joi.array().items(
        Joi.object({
          key: Joi.string().required(),
          timestamp: Joi.number().positive().required(),
          position: Joi.number().integer().min(0).required(),
          isCorrect: Joi.boolean().required(),
          timeSinceLastKey: Joi.number().min(0).required()
        })
      ).min(1).required(),
      errors: Joi.array().items(
        Joi.object({
          position: Joi.number().integer().min(0).required(),
          expected: Joi.string().required(),
          actual: Joi.string().required(),
          timestamp: Joi.number().positive().required(),
          corrected: Joi.boolean().required(),
          correctionTime: Joi.number().positive().optional()
        })
      ).required(),
      metadata: Joi.object({
        browserInfo: Joi.string().required(),
        deviceType: Joi.string().valid('desktop', 'tablet', 'mobile').required(),
        keyboardLayout: Joi.string().optional(),
        language: Joi.string().required(),
        timezone: Joi.string().required(),
        sessionId: Joi.string().required(),
        ipAddress: Joi.string().ip().optional()
      }).required(),
      username: Joi.string().min(2).max(50).required()
    })
  },

  // User registration
  registerUser: {
    body: Joi.object({
      username: Joi.string().alphanum().min(3).max(30).required(),
      email: Joi.string().email().required(),
      password: Joi.string().min(8).max(128).required(),
      profile: Joi.object({
        displayName: Joi.string().min(1).max(100).required(),
        country: Joi.string().length(2).optional(),
        timezone: Joi.string().required(),
        keyboardLayout: Joi.string().required(),
        experienceLevel: Joi.string().valid('beginner', 'intermediate', 'advanced', 'expert', 'master').required()
      }).required()
    })
  },

  // User login
  loginUser: {
    body: Joi.object({
      username: Joi.string().required(),
      password: Joi.string().required()
    })
  },

  // Get leaderboard
  getLeaderboard: {
    query: Joi.object({
      category: Joi.string().valid('quotes', 'programming', 'technical', 'literature', 'news', 'poetry', 'lyrics', 'custom').optional(),
      difficulty: Joi.string().valid('easy', 'medium', 'hard', 'expert', 'insane').optional(),
      timeframe: Joi.string().valid('daily', 'weekly', 'monthly', 'all-time').default('all-time'),
      limit: Joi.number().integer().min(1).max(100).default(50),
      offset: Joi.number().integer().min(0).default(0)
    })
  },

  // Get analytics
  getAnalytics: {
    query: Joi.object({
      startDate: Joi.string().isoDate().optional(),
      endDate: Joi.string().isoDate().optional(),
      granularity: Joi.string().valid('hour', 'day', 'week', 'month').default('day'),
      category: Joi.string().valid('quotes', 'programming', 'technical', 'literature', 'news', 'poetry', 'lyrics', 'custom').optional(),
      difficulty: Joi.string().valid('easy', 'medium', 'hard', 'expert', 'insane').optional()
    })
  },

  // Get user statistics
  getUserStats: {
    params: Joi.object({
      userId: Joi.string().uuid().required()
    }),
    query: Joi.object({
      timeframe: Joi.string().valid('daily', 'weekly', 'monthly', 'all-time').default('all-time'),
      includeHistory: Joi.boolean().default(false)
    })
  },

  // Create text sample
  createText: {
    body: Joi.object({
      text: Joi.string().min(10).max(10000).required(),
      difficulty: Joi.string().valid('easy', 'medium', 'hard', 'expert', 'insane').required(),
      category: Joi.string().valid('quotes', 'programming', 'technical', 'literature', 'news', 'poetry', 'lyrics', 'custom').required(),
      language: Joi.string().default('en'),
      metadata: Joi.object({
        source: Joi.string().optional(),
        author: Joi.string().optional(),
        genre: Joi.string().optional(),
        tags: Joi.array().items(Joi.string()).default([])
      }).optional()
    })
  },

  // Update user preferences
  updatePreferences: {
    body: Joi.object({
      theme: Joi.string().valid('light', 'dark', 'auto').optional(),
      soundEnabled: Joi.boolean().optional(),
      showLiveWpm: Joi.boolean().optional(),
      showLiveAccuracy: Joi.boolean().optional(),
      highlightErrors: Joi.boolean().optional(),
      stopOnError: Joi.boolean().optional(),
      fontSize: Joi.number().min(10).max(24).optional(),
      fontFamily: Joi.string().optional(),
      caretStyle: Joi.string().valid('line', 'block', 'underline').optional(),
      keyboardSounds: Joi.boolean().optional(),
      notifications: Joi.object({
        achievements: Joi.boolean().optional(),
        milestones: Joi.boolean().optional(),
        reminders: Joi.boolean().optional(),
        leaderboard: Joi.boolean().optional(),
        email: Joi.boolean().optional()
      }).optional()
    })
  },

  // Pagination parameters
  pagination: {
    query: Joi.object({
      page: Joi.number().integer().min(1).default(1),
      limit: Joi.number().integer().min(1).max(100).default(20),
      sortBy: Joi.string().optional(),
      sortOrder: Joi.string().valid('asc', 'desc').default('desc')
    })
  },

  // UUID parameter
  uuidParam: {
    params: Joi.object({
      id: Joi.string().uuid().required()
    })
  },

  // Numeric ID parameter
  numericIdParam: {
    params: Joi.object({
      id: Joi.number().integer().positive().required()
    })
  }
};

/**
 * Validation middleware for common use cases
 */
export const validationMiddleware = {
  submitTest: validate(schemas.submitTest),
  registerUser: validate(schemas.registerUser),
  loginUser: validate(schemas.loginUser),
  getLeaderboard: validate(schemas.getLeaderboard),
  getAnalytics: validate(schemas.getAnalytics),
  getUserStats: validate(schemas.getUserStats),
  createText: validate(schemas.createText),
  updatePreferences: validate(schemas.updatePreferences),
  pagination: validate(schemas.pagination),
  uuidParam: validate(schemas.uuidParam),
  numericIdParam: validate(schemas.numericIdParam)
};
