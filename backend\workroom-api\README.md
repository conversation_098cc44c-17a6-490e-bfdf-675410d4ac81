# 🏢 WorkRoom API - Enterprise Collaboration Platform

A professional-grade real-time collaborative workspace built with Node.js, Express, and Socket.IO. Features secure room management, real-time chat, collaborative task management, interactive whiteboard, and synchronized focus timers.

## 🚀 Features

### 🔐 **Secure Room Management**
- Password-protected and public rooms
- JWT-based authentication with bcrypt password hashing
- Room capacity limits and automatic cleanup
- Enterprise-grade security middleware (Helmet.js, CORS, rate limiting)

### 💬 **Real-Time Communication**
- Instant messaging with WebSocket technology
- Typing indicators and user presence
- Message history and persistence
- System notifications for room events

### ✅ **Collaborative Task Management**
- Shared to-do lists with priority levels
- Task assignments and progress tracking
- Real-time updates across all team members
- Task creation, editing, and deletion

### 🎨 **Interactive Whiteboard**
- Collaborative drawing and brainstorming
- Real-time synchronization of drawing elements
- Multi-user drawing with conflict resolution
- Clear and reset functionality

### ⏱️ **Focus Timer (Pomodoro)**
- Synchronized team focus sessions
- Customizable timer durations
- Real-time countdown with team notifications
- Productivity tracking and session management

### 👥 **User Management**
- Anonymous user support with avatar generation
- User status tracking (online, away, busy, offline)
- Member lists and presence indicators
- Automatic connection management

## 🏗️ Technical Architecture

### **Backend Technologies**
- **Node.js & Express.js**: RESTful API with comprehensive middleware
- **Socket.IO**: Real-time bidirectional communication
- **JWT & bcrypt**: Secure authentication and password hashing
- **Express Validator**: Input validation and sanitization
- **Helmet.js**: Security headers and protection
- **Rate Limiting**: API protection and abuse prevention

### **Advanced Features**
- **Connection Management**: Automatic user cleanup and room persistence
- **Event-Driven Architecture**: Real-time synchronization with conflict resolution
- **Memory Optimization**: Efficient data structures with automatic cleanup
- **Scalable Design**: Room-based isolation and efficient broadcasting

### **Security Implementation**
- Content Security Policy (CSP)
- Cross-Origin Resource Sharing (CORS)
- Rate limiting (200 requests per 15 minutes)
- Input validation and XSS prevention
- Password hashing with bcrypt
- JWT token authentication

## 📡 API Endpoints

### **REST API**
```
GET    /api/info                 - API information and statistics
GET    /api/rooms                - List all public rooms
POST   /api/rooms                - Create new room
POST   /api/rooms/:id/join       - Join existing room
GET    /api/rooms/:id            - Get room details
```

### **WebSocket Events**
```
authenticate          - User authentication with JWT
send_message          - Real-time chat messaging
create_task           - Create new task
update_task           - Update existing task
delete_task           - Delete task
whiteboard_update     - Collaborative drawing updates
start_focus_timer     - Start team focus session
stop_focus_timer      - Stop focus session
update_status         - Update user status
typing_start/stop     - Typing indicators
```

## 🛠️ Installation & Setup

### **Prerequisites**
- Node.js 16.0.0 or higher
- npm or yarn package manager

### **Installation**
```bash
# Navigate to project directory
cd backend/workroom-api

# Install dependencies
npm install

# Start the server
npm start

# Run in development mode with auto-reload
npm run dev

# Run comprehensive test suite
npm test
```

### **Server Configuration**
- **Port**: 3004 (configurable via PORT environment variable)
- **JWT Secret**: Configurable via JWT_SECRET environment variable
- **CORS**: Configured for cross-origin requests
- **Rate Limiting**: 200 requests per 15 minutes per IP

## 🧪 Testing

### **Comprehensive Test Suite**
The API includes 18 comprehensive tests covering:

- ✅ REST API endpoint functionality
- ✅ Authentication and authorization
- ✅ Input validation and sanitization
- ✅ Error handling and edge cases
- ✅ Security headers and CORS
- ✅ Rate limiting and abuse prevention
- ✅ WebSocket connectivity
- ✅ XSS prevention and security

```bash
# Run all tests
npm test

# Expected output: 18 tests with detailed coverage report
```

## 🎮 Usage Examples

### **Creating a Room**
```javascript
const response = await fetch('http://localhost:3004/api/rooms', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    name: 'My Team Room',
    username: 'John Doe',
    password: 'optional-password'
  })
});
```

### **WebSocket Connection**
```javascript
const socket = io('http://localhost:3004');

// Authenticate
socket.emit('authenticate', { token: 'your-jwt-token' });

// Send message
socket.emit('send_message', { content: 'Hello team!' });

// Create task
socket.emit('create_task', {
  title: 'Complete project',
  description: 'Finish the collaboration features',
  priority: 'high'
});
```

## 🌐 Demo & Documentation

### **Interactive Demo**
- **Demo Page**: `http://localhost:3004/demo.html`
- **Live Application**: `http://localhost:3004/public/index.html`
- **API Documentation**: Built-in interactive API explorer

### **Key Demo Features**
1. **Room Creation**: Create password-protected or public rooms
2. **Real-Time Chat**: Instant messaging with typing indicators
3. **Task Management**: Collaborative to-do lists with priorities
4. **Whiteboard**: Interactive drawing and brainstorming
5. **Focus Timer**: Synchronized Pomodoro sessions
6. **User Presence**: Live member status and activity

## 📊 Performance & Scalability

### **Optimizations**
- **Memory Management**: Automatic cleanup of inactive rooms and users
- **Message Limiting**: Chat history limited to last 100 messages
- **Connection Pooling**: Efficient WebSocket connection management
- **Data Compression**: Gzip compression for HTTP responses

### **Scalability Considerations**
- **Room Isolation**: Each room operates independently
- **Efficient Broadcasting**: Targeted message delivery
- **Connection Tracking**: Real-time user presence management
- **Resource Cleanup**: Automatic garbage collection

## 🔧 Configuration Options

### **Environment Variables**
```bash
PORT=3004                           # Server port
JWT_SECRET=your-secret-key          # JWT signing secret
NODE_ENV=production                 # Environment mode
```

### **Room Settings**
- **Max Members**: 50 users per room (configurable)
- **Message History**: 100 messages (configurable)
- **Timer Duration**: 25 minutes default (customizable)
- **Connection Timeout**: Automatic cleanup after disconnect

## 🚀 Production Deployment

### **Recommended Setup**
1. **Process Manager**: Use PM2 for process management
2. **Reverse Proxy**: Nginx for load balancing and SSL
3. **Database**: Redis for session storage and persistence
4. **Monitoring**: Application performance monitoring
5. **Logging**: Structured logging with Winston

### **Security Checklist**
- ✅ HTTPS/WSS in production
- ✅ Environment variable configuration
- ✅ Rate limiting and DDoS protection
- ✅ Input validation and sanitization
- ✅ Security headers and CSP
- ✅ Regular dependency updates

## 🤝 Contributing

This is a portfolio project showcasing enterprise-grade full-stack development skills. The codebase demonstrates:

- **Professional Architecture**: Scalable, maintainable code structure
- **Security Best Practices**: Comprehensive security implementation
- **Real-Time Features**: Advanced WebSocket programming
- **Testing Excellence**: Comprehensive test coverage
- **Documentation Quality**: Professional documentation standards

## 📄 License

MIT License - Built for portfolio demonstration purposes.

---

**🎯 Perfect for demonstrating advanced full-stack development skills to senior developers and technical interviewers!**
