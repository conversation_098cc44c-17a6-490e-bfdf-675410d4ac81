/**
 * Vista3D - 3D Product Configurator
 * Advanced three.js application with GLTF loading and material customization
 */

class Vista3DApp {
  constructor() {
    // Three.js core objects
    this.scene = null;
    this.camera = null;
    this.renderer = null;
    this.controls = null;
    this.loader = null;
    this.dracoLoader = null;
    
    // Application state
    this.currentModel = null;
    this.currentModelName = 'chair';
    this.currentTheme = 'light';
    this.isLoading = false;
    this.animationId = null;
    this.autoRotate = false;
    this.wireframeMode = false;
    
    // Performance tracking
    this.frameCount = 0;
    this.lastTime = performance.now();
    this.fps = 60;
    
    // Configuration
    this.config = {
      camera: {
        fov: 45,
        near: 0.1,
        far: 1000,
        position: { x: 5, y: 5, z: 5 }
      },
      lighting: {
        intensity: 1.0,
        environment: 'studio'
      },
      materials: {
        currentColor: '#8B4513',
        currentMaterial: 'fabric'
      }
    };
    
    // Model definitions
    this.models = {
      chair: {
        name: 'Office Chair',
        path: './assets/models/chair.glb',
        scale: 1,
        position: { x: 0, y: 0, z: 0 },
        materials: ['seat', 'back', 'base', 'wheels']
      },
      lamp: {
        name: '<PERSON><PERSON>',
        path: './assets/models/lamp.glb',
        scale: 1,
        position: { x: 0, y: 0, z: 0 },
        materials: ['shade', 'arm', 'base']
      },
      table: {
        name: 'Coffee Table',
        path: './assets/models/table.glb',
        scale: 1,
        position: { x: 0, y: 0, z: 0 },
        materials: ['top', 'legs', 'frame']
      }
    };
    
    // Environment maps
    this.environments = {
      studio: './assets/environments/studio.hdr',
      outdoor: './assets/environments/outdoor.hdr',
      indoor: './assets/environments/indoor.hdr',
      neutral: null
    };
  }

  async init() {
    try {
      console.log('Initializing Vista3D...');
      this.updateLoadingProgress(10, 'Setting up 3D engine...');
      
      // Setup theme
      this.setupTheme();
      this.updateLoadingProgress(20, 'Configuring theme...');
      
      // Initialize Three.js
      await this.initThreeJS();
      this.updateLoadingProgress(40, 'Loading 3D engine...');
      
      // Setup event listeners
      this.setupEventListeners();
      this.updateLoadingProgress(60, 'Setting up controls...');
      
      // Load initial model
      await this.loadModel(this.currentModelName);
      this.updateLoadingProgress(80, 'Loading 3D model...');
      
      // Setup UI
      this.setupUI();
      this.updateLoadingProgress(90, 'Finalizing setup...');
      
      // Start render loop
      this.startRenderLoop();
      this.updateLoadingProgress(100, 'Ready!');
      
      // Hide loading screen
      setTimeout(() => {
        this.hideLoadingScreen();
      }, 500);
      
      console.log('Vista3D initialized successfully');
      
    } catch (error) {
      console.error('Failed to initialize Vista3D:', error);
      this.showNotification('Failed to initialize 3D engine', 'error');
      this.hideLoadingScreen();
    }
  }

  async initThreeJS() {
    const canvas = document.getElementById('viewport');
    if (!canvas) throw new Error('Viewport canvas not found');

    // Scene
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x1a1a1a);

    // Camera
    const aspect = canvas.clientWidth / canvas.clientHeight;
    this.camera = new THREE.PerspectiveCamera(
      this.config.camera.fov,
      aspect,
      this.config.camera.near,
      this.config.camera.far
    );
    
    this.camera.position.set(
      this.config.camera.position.x,
      this.config.camera.position.y,
      this.config.camera.position.z
    );

    // Renderer
    this.renderer = new THREE.WebGLRenderer({
      canvas: canvas,
      antialias: true,
      alpha: true,
      preserveDrawingBuffer: true // For screenshots
    });
    
    this.renderer.setSize(canvas.clientWidth, canvas.clientHeight);
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    this.renderer.shadowMap.enabled = true;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
    this.renderer.toneMappingExposure = 1;

    // Controls
    this.controls = new THREE.OrbitControls(this.camera, canvas);
    this.controls.enableDamping = true;
    this.controls.dampingFactor = 0.05;
    this.controls.minDistance = 2;
    this.controls.maxDistance = 20;
    this.controls.maxPolarAngle = Math.PI / 2;

    // Lighting
    this.setupLighting();

    // Loaders
    this.setupLoaders();

    // Handle resize
    this.handleResize();
  }

  setupLighting() {
    // Ambient light
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.4);
    this.scene.add(ambientLight);

    // Main directional light
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(10, 10, 5);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    directionalLight.shadow.camera.near = 0.5;
    directionalLight.shadow.camera.far = 50;
    directionalLight.shadow.camera.left = -10;
    directionalLight.shadow.camera.right = 10;
    directionalLight.shadow.camera.top = 10;
    directionalLight.shadow.camera.bottom = -10;
    this.scene.add(directionalLight);

    // Fill light
    const fillLight = new THREE.DirectionalLight(0xffffff, 0.3);
    fillLight.position.set(-5, 5, -5);
    this.scene.add(fillLight);

    // Ground plane for shadows
    const groundGeometry = new THREE.PlaneGeometry(20, 20);
    const groundMaterial = new THREE.ShadowMaterial({ opacity: 0.3 });
    const ground = new THREE.Mesh(groundGeometry, groundMaterial);
    ground.rotation.x = -Math.PI / 2;
    ground.position.y = -2;
    ground.receiveShadow = true;
    this.scene.add(ground);
  }

  setupLoaders() {
    // Draco loader for compressed models
    this.dracoLoader = new THREE.DRACOLoader();
    this.dracoLoader.setDecoderPath('https://www.gstatic.com/draco/versioned/decoders/1.5.6/');

    // GLTF loader
    this.loader = new THREE.GLTFLoader();
    this.loader.setDRACOLoader(this.dracoLoader);
  }

  async loadModel(modelName) {
    if (!this.models[modelName]) {
      throw new Error(`Model ${modelName} not found`);
    }

    this.showModelLoading();
    
    try {
      // Remove current model
      if (this.currentModel) {
        this.scene.remove(this.currentModel);
        this.currentModel = null;
      }

      const modelConfig = this.models[modelName];
      
      // For demo purposes, create a simple geometric model since we don't have actual GLTF files
      this.currentModel = this.createDemoModel(modelName);
      
      // Apply transformations
      this.currentModel.scale.setScalar(modelConfig.scale);
      this.currentModel.position.set(
        modelConfig.position.x,
        modelConfig.position.y,
        modelConfig.position.z
      );

      // Enable shadows
      this.currentModel.traverse((child) => {
        if (child.isMesh) {
          child.castShadow = true;
          child.receiveShadow = true;
        }
      });

      this.scene.add(this.currentModel);
      this.currentModelName = modelName;
      
      // Update material groups UI
      this.updateMaterialGroups(modelConfig.materials);
      
      // Update triangle count
      this.updateTriangleCount();
      
      console.log(`Model ${modelName} loaded successfully`);
      
    } catch (error) {
      console.error(`Failed to load model ${modelName}:`, error);
      this.showNotification(`Failed to load ${modelName}`, 'error');
    } finally {
      this.hideModelLoading();
    }
  }

  createDemoModel(modelName) {
    const group = new THREE.Group();
    
    switch (modelName) {
      case 'chair':
        return this.createChairModel();
      case 'lamp':
        return this.createLampModel();
      case 'table':
        return this.createTableModel();
      default:
        return this.createChairModel();
    }
  }

  createChairModel() {
    const group = new THREE.Group();
    
    // Seat
    const seatGeometry = new THREE.BoxGeometry(2, 0.2, 2);
    const seatMaterial = new THREE.MeshLambertMaterial({ color: this.config.materials.currentColor });
    const seat = new THREE.Mesh(seatGeometry, seatMaterial);
    seat.position.y = 1;
    seat.userData = { materialGroup: 'seat' };
    group.add(seat);
    
    // Backrest
    const backGeometry = new THREE.BoxGeometry(2, 2, 0.2);
    const backMaterial = new THREE.MeshLambertMaterial({ color: this.config.materials.currentColor });
    const back = new THREE.Mesh(backGeometry, backMaterial);
    back.position.set(0, 2, -0.9);
    back.userData = { materialGroup: 'back' };
    group.add(back);
    
    // Base
    const baseGeometry = new THREE.CylinderGeometry(0.8, 0.8, 0.2, 8);
    const baseMaterial = new THREE.MeshLambertMaterial({ color: '#333333' });
    const base = new THREE.Mesh(baseGeometry, baseMaterial);
    base.position.y = 0.1;
    base.userData = { materialGroup: 'base' };
    group.add(base);
    
    // Legs
    for (let i = 0; i < 5; i++) {
      const angle = (i / 5) * Math.PI * 2;
      const legGeometry = new THREE.CylinderGeometry(0.05, 0.05, 0.8, 8);
      const legMaterial = new THREE.MeshLambertMaterial({ color: '#333333' });
      const leg = new THREE.Mesh(legGeometry, legMaterial);
      leg.position.set(Math.cos(angle) * 0.6, -0.4, Math.sin(angle) * 0.6);
      leg.userData = { materialGroup: 'base' };
      group.add(leg);
      
      // Wheels
      const wheelGeometry = new THREE.SphereGeometry(0.1, 8, 8);
      const wheelMaterial = new THREE.MeshLambertMaterial({ color: '#666666' });
      const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
      wheel.position.set(Math.cos(angle) * 0.6, -0.8, Math.sin(angle) * 0.6);
      wheel.userData = { materialGroup: 'wheels' };
      group.add(wheel);
    }
    
    return group;
  }

  createLampModel() {
    const group = new THREE.Group();
    
    // Base
    const baseGeometry = new THREE.CylinderGeometry(0.8, 1, 0.3, 16);
    const baseMaterial = new THREE.MeshLambertMaterial({ color: '#333333' });
    const base = new THREE.Mesh(baseGeometry, baseMaterial);
    base.position.y = 0.15;
    base.userData = { materialGroup: 'base' };
    group.add(base);
    
    // Arm
    const armGeometry = new THREE.CylinderGeometry(0.05, 0.05, 3, 8);
    const armMaterial = new THREE.MeshLambertMaterial({ color: '#666666' });
    const arm = new THREE.Mesh(armGeometry, armMaterial);
    arm.position.set(0, 1.8, 0);
    arm.rotation.z = Math.PI / 6;
    arm.userData = { materialGroup: 'arm' };
    group.add(arm);
    
    // Shade
    const shadeGeometry = new THREE.ConeGeometry(1, 1.5, 16, 1, true);
    const shadeMaterial = new THREE.MeshLambertMaterial({ 
      color: this.config.materials.currentColor,
      side: THREE.DoubleSide 
    });
    const shade = new THREE.Mesh(shadeGeometry, shadeMaterial);
    shade.position.set(1.5, 3, 0);
    shade.userData = { materialGroup: 'shade' };
    group.add(shade);
    
    return group;
  }

  createTableModel() {
    const group = new THREE.Group();
    
    // Top
    const topGeometry = new THREE.BoxGeometry(4, 0.2, 2);
    const topMaterial = new THREE.MeshLambertMaterial({ color: this.config.materials.currentColor });
    const top = new THREE.Mesh(topGeometry, topMaterial);
    top.position.y = 1;
    top.userData = { materialGroup: 'top' };
    group.add(top);
    
    // Legs
    const legPositions = [
      [-1.8, 0, -0.8],
      [1.8, 0, -0.8],
      [-1.8, 0, 0.8],
      [1.8, 0, 0.8]
    ];
    
    legPositions.forEach(pos => {
      const legGeometry = new THREE.BoxGeometry(0.2, 2, 0.2);
      const legMaterial = new THREE.MeshLambertMaterial({ color: '#8B4513' });
      const leg = new THREE.Mesh(legGeometry, legMaterial);
      leg.position.set(pos[0], pos[1], pos[2]);
      leg.userData = { materialGroup: 'legs' };
      group.add(leg);
    });
    
    return group;
  }

  setupTheme() {
    const savedTheme = localStorage.getItem('vista3d-theme');
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    this.currentTheme = savedTheme || (systemPrefersDark ? 'dark' : 'light');

    this.setTheme(this.currentTheme);
  }

  setTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('vista3d-theme', theme);
    this.currentTheme = theme;

    const themeIcon = document.querySelector('.theme-icon');
    if (themeIcon) {
      themeIcon.textContent = theme === 'dark' ? '☀️' : '🌙';
    }

    // Update scene background
    if (this.scene) {
      const bgColor = theme === 'dark' ? 0x0f0f0f : 0x1a1a1a;
      this.scene.background = new THREE.Color(bgColor);
    }
  }

  setupEventListeners() {
    // Theme toggle
    const themeToggle = document.getElementById('themeToggle');
    themeToggle?.addEventListener('click', () => {
      const newTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
      this.setTheme(newTheme);
    });

    // Fullscreen toggle
    const fullscreenBtn = document.getElementById('fullscreenBtn');
    fullscreenBtn?.addEventListener('click', () => {
      this.toggleFullscreen();
    });

    // Navigation
    this.setupNavigation();

    // Viewport controls
    this.setupViewportControls();

    // Configuration panel
    this.setupConfigurationPanel();

    // Window resize
    window.addEventListener('resize', () => {
      this.handleResize();
    });
  }

  setupNavigation() {
    // Mobile menu toggle
    const navToggle = document.getElementById('navToggle');
    const navMenu = document.getElementById('navMenu');

    navToggle?.addEventListener('click', () => {
      navMenu?.classList.toggle('active');
    });

    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        const href = link.getAttribute('href');
        if (href.startsWith('#')) {
          e.preventDefault();
          const target = document.querySelector(href);
          if (target) {
            target.scrollIntoView({ behavior: 'smooth' });

            // Update active nav link
            navLinks.forEach(l => l.classList.remove('active'));
            link.classList.add('active');

            // Close mobile menu
            navMenu?.classList.remove('active');
          }
        }
      });
    });
  }

  setupViewportControls() {
    // Reset camera
    const resetCameraBtn = document.getElementById('resetCameraBtn');
    resetCameraBtn?.addEventListener('click', () => {
      this.resetCamera();
    });

    // Toggle wireframe
    const wireframeBtn = document.getElementById('wireframeBtn');
    wireframeBtn?.addEventListener('click', () => {
      this.toggleWireframe();
    });

    // Take screenshot
    const screenshotBtn = document.getElementById('screenshotBtn');
    screenshotBtn?.addEventListener('click', () => {
      this.takeScreenshot();
    });

    // Export PNG
    const exportBtn = document.getElementById('exportBtn');
    exportBtn?.addEventListener('click', () => {
      this.exportPNG();
    });
  }

  setupConfigurationPanel() {
    // Panel toggle
    const panelToggle = document.getElementById('panelToggle');
    const panelContent = document.getElementById('panelContent');

    panelToggle?.addEventListener('click', () => {
      panelContent?.classList.toggle('collapsed');
    });

    // Model selection
    const modelButtons = document.querySelectorAll('.model-btn');
    modelButtons.forEach(btn => {
      btn.addEventListener('click', () => {
        const modelName = btn.dataset.model;
        if (modelName && modelName !== this.currentModelName) {
          // Update active button
          modelButtons.forEach(b => b.classList.remove('active'));
          btn.classList.add('active');

          // Load model
          this.loadModel(modelName);
        }
      });
    });

    // Color palette
    const colorButtons = document.querySelectorAll('.color-btn');
    colorButtons.forEach(btn => {
      btn.addEventListener('click', () => {
        const color = btn.dataset.color;
        if (color) {
          // Update active button
          colorButtons.forEach(b => b.classList.remove('active'));
          btn.classList.add('active');

          // Apply color
          this.applyColor(color);
        }
      });
    });

    // Environment selection
    const environmentSelect = document.getElementById('environmentSelect');
    environmentSelect?.addEventListener('change', (e) => {
      this.setEnvironment(e.target.value);
    });

    // Lighting intensity
    const lightingIntensity = document.getElementById('lightingIntensity');
    const lightingValue = document.getElementById('lightingValue');

    lightingIntensity?.addEventListener('input', (e) => {
      const intensity = parseFloat(e.target.value);
      this.setLightingIntensity(intensity);
      if (lightingValue) {
        lightingValue.textContent = intensity.toFixed(1);
      }
    });

    // Animation controls
    const playAnimationBtn = document.getElementById('playAnimationBtn');
    playAnimationBtn?.addEventListener('click', () => {
      this.playAnimation();
    });

    const autoRotateBtn = document.getElementById('autoRotateBtn');
    autoRotateBtn?.addEventListener('click', () => {
      this.toggleAutoRotate();
    });

    // Export controls
    const exportResolution = document.getElementById('exportResolution');
    const customResolution = document.getElementById('customResolution');

    exportResolution?.addEventListener('change', (e) => {
      if (e.target.value === 'custom') {
        customResolution.style.display = 'block';
      } else {
        customResolution.style.display = 'none';
      }
    });

    const exportPngBtn = document.getElementById('exportPngBtn');
    exportPngBtn?.addEventListener('click', () => {
      this.exportHighResPNG();
    });
  }

  // Action Methods
  resetCamera() {
    if (this.camera && this.controls) {
      this.camera.position.set(
        this.config.camera.position.x,
        this.config.camera.position.y,
        this.config.camera.position.z
      );
      this.controls.reset();
      this.showNotification('Camera reset', 'success');
    }
  }

  toggleWireframe() {
    this.wireframeMode = !this.wireframeMode;

    if (this.currentModel) {
      this.currentModel.traverse((child) => {
        if (child.isMesh && child.material) {
          child.material.wireframe = this.wireframeMode;
        }
      });
    }

    this.showNotification(
      `Wireframe ${this.wireframeMode ? 'enabled' : 'disabled'}`,
      'success'
    );
  }

  takeScreenshot() {
    if (this.renderer) {
      const canvas = this.renderer.domElement;
      const link = document.createElement('a');
      link.download = `vista3d-screenshot-${Date.now()}.png`;
      link.href = canvas.toDataURL('image/png');
      link.click();

      this.showNotification('Screenshot saved', 'success');
    }
  }

  exportPNG() {
    this.takeScreenshot();
  }

  exportHighResPNG() {
    const exportResolution = document.getElementById('exportResolution');
    const customWidth = document.getElementById('customWidth');
    const customHeight = document.getElementById('customHeight');

    let width = 1920;
    let height = 1080;

    if (exportResolution) {
      const resolution = exportResolution.value;
      switch (resolution) {
        case '2560x1440':
          width = 2560;
          height = 1440;
          break;
        case '3840x2160':
          width = 3840;
          height = 2160;
          break;
        case 'custom':
          width = parseInt(customWidth?.value) || 1920;
          height = parseInt(customHeight?.value) || 1080;
          break;
        default:
          width = 1920;
          height = 1080;
      }
    }

    this.renderHighResolution(width, height);
  }

  renderHighResolution(width, height) {
    if (!this.renderer || !this.scene || !this.camera) return;

    // Store original size
    const originalSize = this.renderer.getSize(new THREE.Vector2());
    const originalAspect = this.camera.aspect;

    // Set high resolution
    this.renderer.setSize(width, height, false);
    this.camera.aspect = width / height;
    this.camera.updateProjectionMatrix();

    // Render
    this.renderer.render(this.scene, this.camera);

    // Export
    const canvas = this.renderer.domElement;
    const link = document.createElement('a');
    link.download = `vista3d-export-${width}x${height}-${Date.now()}.png`;
    link.href = canvas.toDataURL('image/png');
    link.click();

    // Restore original size
    this.renderer.setSize(originalSize.x, originalSize.y, false);
    this.camera.aspect = originalAspect;
    this.camera.updateProjectionMatrix();

    this.showNotification(`High-res PNG exported (${width}x${height})`, 'success');
  }

  applyColor(color) {
    this.config.materials.currentColor = color;

    if (this.currentModel) {
      this.currentModel.traverse((child) => {
        if (child.isMesh && child.material && child.userData.materialGroup) {
          // Apply color to specific material groups
          const group = child.userData.materialGroup;
          if (['seat', 'back', 'shade', 'top'].includes(group)) {
            child.material.color.setHex(color.replace('#', '0x'));
          }
        }
      });
    }

    this.showNotification('Color applied', 'success');
  }

  setEnvironment(environment) {
    this.config.lighting.environment = environment;

    // Update scene background based on environment
    if (this.scene) {
      let bgColor;
      switch (environment) {
        case 'outdoor':
          bgColor = 0x87CEEB; // Sky blue
          break;
        case 'indoor':
          bgColor = 0xF5F5DC; // Beige
          break;
        case 'neutral':
          bgColor = 0x808080; // Gray
          break;
        default: // studio
          bgColor = this.currentTheme === 'dark' ? 0x0f0f0f : 0x1a1a1a;
      }
      this.scene.background = new THREE.Color(bgColor);
    }

    this.showNotification(`Environment: ${environment}`, 'success');
  }

  setLightingIntensity(intensity) {
    this.config.lighting.intensity = intensity;

    // Update all lights in the scene
    this.scene.traverse((child) => {
      if (child.isLight && child.type !== 'AmbientLight') {
        child.intensity = intensity;
      }
    });

    this.showNotification(`Lighting: ${intensity.toFixed(1)}`, 'success');
  }

  playAnimation() {
    if (this.currentModel) {
      // Simple bounce animation
      const startY = this.currentModel.position.y;
      const duration = 1000;
      const startTime = performance.now();

      const animate = (currentTime) => {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // Bounce easing
        const bounce = Math.sin(progress * Math.PI * 4) * Math.exp(-progress * 3);
        this.currentModel.position.y = startY + bounce * 0.5;

        if (progress < 1) {
          requestAnimationFrame(animate);
        } else {
          this.currentModel.position.y = startY;
        }
      };

      requestAnimationFrame(animate);
      this.showNotification('Animation played', 'success');
    }
  }

  toggleAutoRotate() {
    this.autoRotate = !this.autoRotate;

    const autoRotateBtn = document.getElementById('autoRotateBtn');
    if (autoRotateBtn) {
      autoRotateBtn.style.backgroundColor = this.autoRotate ?
        'var(--color-primary)' : 'var(--color-gray-100)';
      autoRotateBtn.style.color = this.autoRotate ?
        'white' : 'var(--color-gray-700)';
    }

    this.showNotification(
      `Auto rotate ${this.autoRotate ? 'enabled' : 'disabled'}`,
      'success'
    );
  }

  toggleMaterialVisibility(materialGroup, visible) {
    if (this.currentModel) {
      this.currentModel.traverse((child) => {
        if (child.isMesh && child.userData.materialGroup === materialGroup) {
          child.visible = visible;
        }
      });
    }

    this.showNotification(
      `${materialGroup} ${visible ? 'shown' : 'hidden'}`,
      'success'
    );
  }

  toggleFullscreen() {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen().catch(err => {
        console.error('Error attempting to enable fullscreen:', err);
      });
    } else {
      document.exitFullscreen();
    }
  }

  handleResize() {
    if (!this.renderer || !this.camera) return;

    const canvas = this.renderer.domElement;
    const width = canvas.clientWidth;
    const height = canvas.clientHeight;

    this.camera.aspect = width / height;
    this.camera.updateProjectionMatrix();

    this.renderer.setSize(width, height, false);
  }

  // UI Helper Methods
  updateLoadingProgress(percentage, text) {
    const progressFill = document.getElementById('progressFill');
    const progressText = document.getElementById('progressText');
    const loadingText = document.getElementById('loadingText');

    if (progressFill) {
      progressFill.style.width = `${percentage}%`;
    }

    if (progressText) {
      progressText.textContent = `${percentage}%`;
    }

    if (loadingText && text) {
      loadingText.textContent = text;
    }
  }

  hideLoadingScreen() {
    const loadingScreen = document.getElementById('loadingScreen');
    if (loadingScreen) {
      loadingScreen.style.opacity = '0';
      setTimeout(() => {
        loadingScreen.style.display = 'none';
      }, 300);
    }
  }

  showModelLoading() {
    const modelLoading = document.getElementById('modelLoading');
    if (modelLoading) {
      modelLoading.style.display = 'block';
    }
  }

  hideModelLoading() {
    const modelLoading = document.getElementById('modelLoading');
    if (modelLoading) {
      modelLoading.style.display = 'none';
    }
  }

  showNotification(message, type = 'info') {
    const container = document.getElementById('notificationContainer');
    if (!container) return;

    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
      <div class="notification-content">
        <span class="notification-message">${message}</span>
        <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
      </div>
    `;

    container.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, 3000);
  }

  setupUI() {
    // Populate gallery
    this.populateGallery();

    // Update material groups for current model
    const modelConfig = this.models[this.currentModelName];
    if (modelConfig) {
      this.updateMaterialGroups(modelConfig.materials);
    }
  }

  populateGallery() {
    const galleryGrid = document.getElementById('galleryGrid');
    if (!galleryGrid) return;

    const galleryItems = Object.entries(this.models).map(([key, model]) => `
      <div class="gallery-item" data-model="${key}">
        <div class="gallery-image">🎯</div>
        <div class="gallery-info">
          <h3 class="gallery-title">${model.name}</h3>
          <p class="gallery-description">Interactive 3D model with customizable materials</p>
          <div class="gallery-stats">
            <span>Materials: ${model.materials.length}</span>
            <span>Optimized</span>
          </div>
        </div>
      </div>
    `).join('');

    galleryGrid.innerHTML = galleryItems;

    // Add click handlers
    const galleryItemElements = galleryGrid.querySelectorAll('.gallery-item');
    galleryItemElements.forEach(item => {
      item.addEventListener('click', () => {
        const modelName = item.dataset.model;
        if (modelName) {
          // Scroll to configurator
          document.getElementById('configurator')?.scrollIntoView({ behavior: 'smooth' });

          // Load model after scroll
          setTimeout(() => {
            this.loadModel(modelName);

            // Update model selector
            const modelBtn = document.querySelector(`[data-model="${modelName}"]`);
            if (modelBtn) {
              document.querySelectorAll('.model-btn').forEach(b => b.classList.remove('active'));
              modelBtn.classList.add('active');
            }
          }, 500);
        }
      });
    });
  }

  updateMaterialGroups(materials) {
    const materialGroups = document.getElementById('materialGroups');
    if (!materialGroups) return;

    const groupsHTML = materials.map(material => `
      <div class="material-group">
        <label class="material-label">
          <input type="checkbox" class="material-checkbox" data-material="${material}" checked>
          <span class="material-name">${material.charAt(0).toUpperCase() + material.slice(1)}</span>
        </label>
      </div>
    `).join('');

    materialGroups.innerHTML = groupsHTML;

    // Add event listeners
    const checkboxes = materialGroups.querySelectorAll('.material-checkbox');
    checkboxes.forEach(checkbox => {
      checkbox.addEventListener('change', (e) => {
        const material = e.target.dataset.material;
        const visible = e.target.checked;
        this.toggleMaterialVisibility(material, visible);
      });
    });
  }

  startRenderLoop() {
    const animate = () => {
      this.animationId = requestAnimationFrame(animate);

      // Update controls
      if (this.controls) {
        this.controls.update();
      }

      // Auto rotate
      if (this.autoRotate && this.currentModel) {
        this.currentModel.rotation.y += 0.01;
      }

      // Update performance stats
      this.updatePerformanceStats();

      // Render
      if (this.renderer && this.scene && this.camera) {
        this.renderer.render(this.scene, this.camera);
      }
    };

    animate();
  }

  updatePerformanceStats() {
    this.frameCount++;
    const currentTime = performance.now();

    if (currentTime >= this.lastTime + 1000) {
      this.fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastTime));
      this.frameCount = 0;
      this.lastTime = currentTime;

      // Update FPS display
      const fpsCounter = document.getElementById('fpsCounter');
      if (fpsCounter) {
        fpsCounter.textContent = this.fps;
      }
    }
  }

  updateTriangleCount() {
    let triangleCount = 0;

    if (this.currentModel) {
      this.currentModel.traverse((child) => {
        if (child.isMesh && child.geometry) {
          const geometry = child.geometry;
          if (geometry.index) {
            triangleCount += geometry.index.count / 3;
          } else {
            triangleCount += geometry.attributes.position.count / 3;
          }
        }
      });
    }

    const triangleCountElement = document.getElementById('triangleCount');
    if (triangleCountElement) {
      triangleCountElement.textContent = Math.round(triangleCount).toLocaleString();
    }
  }

  // Cleanup
  destroy() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
    }

    if (this.renderer) {
      this.renderer.dispose();
    }

    if (this.dracoLoader) {
      this.dracoLoader.dispose();
    }
  }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
  const app = new Vista3DApp();
  app.init().catch(error => {
    console.error('Failed to initialize Vista3D:', error);
  });

  // Store app instance globally for debugging
  window.vista3d = app;
});

// Handle page unload
window.addEventListener('beforeunload', () => {
  if (window.vista3d) {
    window.vista3d.destroy();
  }
});
