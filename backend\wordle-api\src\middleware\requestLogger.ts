/**
 * Request logging middleware for comprehensive API monitoring
 */

import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '@/utils/logger';

// Extend Request interface to include requestId
declare global {
  namespace Express {
    interface Request {
      requestId: string;
      startTime: number;
    }
  }
}

/**
 * Request logging middleware
 */
export const requestLogger = (req: Request, res: Response, next: NextFunction): void => {
  // Generate unique request ID
  req.requestId = uuidv4();
  req.startTime = Date.now();

  // Add request ID to response headers
  res.setHeader('X-Request-ID', req.requestId);

  // Log incoming request
  const requestInfo = {
    requestId: req.requestId,
    method: req.method,
    url: req.originalUrl,
    userAgent: req.get('User-Agent'),
    ip: req.ip || req.connection.remoteAddress,
    contentLength: req.get('Content-Length'),
    referer: req.get('Referer')
  };

  // Sanitize sensitive data from body
  let sanitizedBody = {};
  if (req.body && typeof req.body === 'object') {
    sanitizedBody = sanitizeRequestBody(req.body);
  }

  logger.api('request-start', req.requestId, undefined, {
    ...requestInfo,
    body: Object.keys(sanitizedBody).length > 0 ? sanitizedBody : undefined
  });

  // Override res.json to log response
  const originalJson = res.json;
  res.json = function(body: any) {
    const duration = Date.now() - req.startTime;
    
    // Log response
    logger.api('request-end', req.requestId, undefined, {
      statusCode: res.statusCode,
      duration,
      contentLength: JSON.stringify(body).length,
      success: res.statusCode < 400
    });

    return originalJson.call(this, body);
  };

  // Override res.send to log response
  const originalSend = res.send;
  res.send = function(body: any) {
    const duration = Date.now() - req.startTime;
    
    // Log response
    logger.api('request-end', req.requestId, undefined, {
      statusCode: res.statusCode,
      duration,
      contentLength: typeof body === 'string' ? body.length : JSON.stringify(body).length,
      success: res.statusCode < 400
    });

    return originalSend.call(this, body);
  };

  next();
};

/**
 * Sanitize request body to remove sensitive information
 */
function sanitizeRequestBody(body: any): any {
  const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth'];
  const sanitized = { ...body };

  for (const field of sensitiveFields) {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]';
    }
  }

  // Recursively sanitize nested objects
  for (const key in sanitized) {
    if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
      sanitized[key] = sanitizeRequestBody(sanitized[key]);
    }
  }

  return sanitized;
}
