<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Code Showcase - Portfolio</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
      color: #ffffff;
      min-height: 100vh;
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      padding: 20px;
    }

    .header {
      text-align: center;
      margin-bottom: 40px;
    }

    .header h1 {
      font-size: 2.5rem;
      margin-bottom: 10px;
      background: linear-gradient(45deg, #64b5f6, #42a5f5);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .header p {
      font-size: 1.1rem;
      opacity: 0.9;
    }

    .back-button {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      background: rgba(255, 255, 255, 0.1);
      color: white;
      text-decoration: none;
      padding: 12px 20px;
      border-radius: 8px;
      margin-bottom: 30px;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
    }

    .back-button:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: translateY(-2px);
    }

    .showcase-layout {
      display: grid;
      grid-template-columns: 300px 1fr;
      gap: 30px;
      height: calc(100vh - 200px);
    }

    .file-explorer {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 15px;
      padding: 20px;
      backdrop-filter: blur(10px);
      overflow-y: auto;
    }

    .file-explorer h3 {
      margin-bottom: 20px;
      font-size: 1.2rem;
      color: #64b5f6;
    }

    .file-list {
      list-style: none;
    }

    .file-item {
      padding: 12px 16px;
      margin-bottom: 8px;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .file-item:hover {
      background: rgba(255, 255, 255, 0.15);
      transform: translateX(5px);
    }

    .file-item.active {
      background: linear-gradient(45deg, #64b5f6, #42a5f5);
      color: white;
    }

    .file-icon {
      width: 16px;
      height: 16px;
      border-radius: 3px;
    }

    .file-icon.html { background: #e34c26; }
    .file-icon.css { background: #1572b6; }
    .file-icon.js { background: #f7df1e; color: #000; }
    .file-icon.json { background: #000000; }
    .file-icon.md { background: #083fa1; }
    .file-icon.default { background: #6c757d; }

    .code-viewer {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 15px;
      padding: 20px;
      backdrop-filter: blur(10px);
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }

    .code-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }

    .code-title {
      font-size: 1.3rem;
      color: #64b5f6;
    }

    .copy-button {
      background: rgba(100, 181, 246, 0.2);
      color: #64b5f6;
      border: 1px solid #64b5f6;
      padding: 8px 16px;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .copy-button:hover {
      background: #64b5f6;
      color: white;
    }

    .code-content {
      flex: 1;
      overflow-y: auto;
      background: #1a1a1a;
      border-radius: 10px;
      padding: 20px;
      font-family: 'Courier New', monospace;
      font-size: 14px;
      line-height: 1.6;
    }

    .line-numbers {
      color: #666;
      user-select: none;
      margin-right: 20px;
      display: inline-block;
      width: 40px;
      text-align: right;
    }

    .code-line {
      display: block;
      white-space: pre-wrap;
      word-break: break-all;
    }

    .welcome-message {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      text-align: center;
      opacity: 0.7;
    }

    @media (max-width: 768px) {
      .showcase-layout {
        grid-template-columns: 1fr;
        height: auto;
      }
      
      .file-explorer {
        height: 300px;
      }
      
      .code-viewer {
        height: 500px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <a href="javascript:history.back()" class="back-button">
      ← Back to Portfolio
    </a>

    <div class="header">
      <h1 id="projectTitle">Code Showcase</h1>
      <p id="projectDescription">Explore the source code and file structure</p>
    </div>

    <div class="showcase-layout">
      <div class="file-explorer">
        <h3>📁 Project Files</h3>
        <ul class="file-list" id="fileList">
          <!-- Files will be populated by JavaScript -->
        </ul>
      </div>

      <div class="code-viewer">
        <div class="code-header">
          <h3 class="code-title" id="currentFileName">Select a file to view</h3>
          <button class="copy-button" id="copyButton" onclick="copyCode()" style="display: none;">
            📋 Copy Code
          </button>
        </div>
        <div class="code-content" id="codeContent">
          <div class="welcome-message">
            <div>
              <h3>👈 Select a file from the explorer</h3>
              <p>Click on any file to view its source code</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Project configurations
    const projectConfigs = {
      'project-gallery': {
        title: 'GitHub Projects Gallery',
        description: 'Interactive gallery showcasing professional website projects',
        files: [
          { name: 'index.html', type: 'html' },
          { name: 'styles.css', type: 'css' },
          { name: 'main.js', type: 'js' },
          { name: 'gallery-data.js', type: 'js' }
        ]
      },
      'collabo-canvas': {
        title: 'CollaboCanvas - Real-time Whiteboard',
        description: 'WebRTC peer-to-peer collaborative whiteboard application',
        files: [
          { name: 'index.html', type: 'html' },
          { name: 'styles.css', type: 'css' },
          { name: 'main.js', type: 'js' },
          { name: 'README.md', type: 'md' }
        ]
      },
      'workroom': {
        title: 'CollabSpace - Enterprise Workspace',
        description: 'Real-time chat, whiteboard, task management, and focus timer',
        files: [
          { name: 'index.html', type: 'html' },
          { name: 'package.json', type: 'json' },
          { name: 'tsconfig.json', type: 'json' },
          { name: 'vite.config.ts', type: 'js' },
          { name: 'README.md', type: 'md' }
        ]
      },
      'wordle-game': {
        title: 'Wordle Game Clone',
        description: 'Full-stack word game with advanced validation algorithms',
        files: [
          { name: 'index.html', type: 'html' },
          { name: 'styles.css', type: 'css' },
          { name: 'main.js', type: 'js' },
          { name: 'words.js', type: 'js' }
        ]
      },
      'typing-test': {
        title: 'Typing Speed Test',
        description: 'Real-time WPM calculation and performance analytics',
        files: [
          { name: 'index.html', type: 'html' },
          { name: 'styles.css', type: 'css' },
          { name: 'main.js', type: 'js' },
          { name: 'text-data.js', type: 'js' }
        ]
      },
      'sudoku-solver': {
        title: 'Sudoku Solver',
        description: 'Advanced backtracking algorithms with interactive interface',
        files: [
          { name: 'index.html', type: 'html' },
          { name: 'styles.css', type: 'css' },
          { name: 'main.js', type: 'js' },
          { name: 'sudoku-solver.js', type: 'js' }
        ]
      }
    };

    let currentProject = '';
    let currentFile = '';

    // Initialize the showcase
    function init() {
      const urlParams = new URLSearchParams(window.location.search);
      currentProject = urlParams.get('project');
      
      if (!currentProject || !projectConfigs[currentProject]) {
        document.getElementById('projectTitle').textContent = 'Project Not Found';
        document.getElementById('projectDescription').textContent = 'The requested project could not be found.';
        return;
      }

      const config = projectConfigs[currentProject];
      document.getElementById('projectTitle').textContent = config.title;
      document.getElementById('projectDescription').textContent = config.description;
      
      loadFileList(config.files);
    }

    // Load file list in explorer
    function loadFileList(files) {
      const fileList = document.getElementById('fileList');
      fileList.innerHTML = '';

      files.forEach(file => {
        const li = document.createElement('li');
        li.className = 'file-item';
        li.onclick = () => loadFile(file.name, file.type);
        
        li.innerHTML = `
          <div class="file-icon ${file.type}"></div>
          <span>${file.name}</span>
        `;
        
        fileList.appendChild(li);
      });
    }

    // Load and display file content
    async function loadFile(fileName, fileType) {
      try {
        // Update active file in explorer
        document.querySelectorAll('.file-item').forEach(item => {
          item.classList.remove('active');
        });
        event.target.closest('.file-item').classList.add('active');

        // Update header
        document.getElementById('currentFileName').textContent = fileName;
        document.getElementById('copyButton').style.display = 'block';

        // Load file content
        const response = await fetch(`./projects/${currentProject}/${fileName}`);
        if (!response.ok) throw new Error('File not found');
        
        const content = await response.text();
        displayCode(content, fileType);
        currentFile = content;

      } catch (error) {
        document.getElementById('codeContent').innerHTML = `
          <div class="welcome-message">
            <div>
              <h3>❌ Error loading file</h3>
              <p>${error.message}</p>
            </div>
          </div>
        `;
      }
    }

    // Display code with syntax highlighting
    function displayCode(content, fileType) {
      const codeContent = document.getElementById('codeContent');
      const lines = content.split('\n');
      
      let html = '';
      lines.forEach((line, index) => {
        const lineNumber = (index + 1).toString().padStart(3, ' ');
        html += `<div class="code-line"><span class="line-numbers">${lineNumber}</span>${escapeHtml(line)}</div>`;
      });
      
      codeContent.innerHTML = html;
    }

    // Copy code to clipboard
    function copyCode() {
      if (currentFile) {
        navigator.clipboard.writeText(currentFile).then(() => {
          const button = document.getElementById('copyButton');
          const originalText = button.textContent;
          button.textContent = '✅ Copied!';
          setTimeout(() => {
            button.textContent = originalText;
          }, 2000);
        });
      }
    }

    // Escape HTML characters
    function escapeHtml(text) {
      const div = document.createElement('div');
      div.textContent = text;
      return div.innerHTML;
    }

    // Initialize when page loads
    document.addEventListener('DOMContentLoaded', init);
  </script>
</body>
</html>
