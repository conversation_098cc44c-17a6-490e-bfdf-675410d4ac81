/**
 * User management routes
 */

import { Router, Request, Response } from 'express';
import { asyncHand<PERSON> } from '@/middleware/errorHandler';
import { logger } from '@/utils/logger';

const router = Router();

/**
 * Get user profile
 */
router.get('/:username', asyncHandler(async (req: Request, res: Response) => {
  const { username } = req.params;

  try {
    // Mock user data
    const user = {
      username,
      displayName: `${username.charAt(0).toUpperCase()}${username.slice(1)}`,
      joinDate: '2024-01-15',
      lastActive: new Date().toISOString(),
      statistics: {
        totalTests: 156,
        averageWpm: 67.3,
        bestWpm: 89.2,
        averageAccuracy: 94.8,
        totalTime: 8400000, // milliseconds
        improvementRate: 2.1
      }
    };

    res.json({
      success: true,
      data: user
    });

  } catch (error) {
    logger.error('Failed to get user profile:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get user profile',
      requestId: (req as any).requestId
    });
  }
}));

export default router;
