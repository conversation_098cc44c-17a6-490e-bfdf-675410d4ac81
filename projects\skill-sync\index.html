<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="SkillSync - AI-powered interview preparation tool with flash cards and practice sessions">
  <meta name="keywords" content="interview prep, AI, flash cards, job interview, skills assessment">
  <title>SkillSync - AI Interview Preparation</title>
  <link rel="icon" type="image/svg+xml" href="../../assets/favicon.svg">
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <header class="header">
    <nav class="nav">
      <div class="nav-container">
        <a href="../../" class="logo">
          <span class="logo-icon">🎯</span>
          <span class="logo-text">SkillSync</span>
        </a>
        <div class="nav-controls">
          <button class="theme-toggle" aria-label="Toggle theme">
            <span class="theme-icon">🌙</span>
          </button>
          <a href="../../" class="back-link" aria-label="Back to portfolio">
            <span class="back-icon">←</span>
          </a>
        </div>
      </div>
    </nav>
  </header>

  <main class="main">
    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-container">
        <h1 class="hero-title">AI Interview Preparation</h1>
        <p class="hero-description">
          Master your next interview with AI-powered practice sessions and personalized flash cards
        </p>
        <div class="hero-actions">
          <button class="btn btn-primary" id="startPractice">Start Practice Session</button>
          <button class="btn btn-secondary" id="viewCards">View Flash Cards</button>
        </div>
      </div>
    </section>

    <!-- Practice Session -->
    <section class="practice-session" id="practiceSession" style="display: none;">
      <div class="practice-container">
        <div class="practice-header">
          <h2>Practice Session</h2>
          <div class="practice-controls">
            <button class="btn btn-small" id="pauseSession">Pause</button>
            <button class="btn btn-small btn-danger" id="endSession">End Session</button>
          </div>
        </div>
        
        <div class="question-card">
          <div class="question-header">
            <span class="question-number">Question 1 of 10</span>
            <span class="question-category">Technical</span>
          </div>
          <div class="question-content">
            <h3 id="currentQuestion">Loading question...</h3>
          </div>
          <div class="question-actions">
            <button class="btn btn-outline" id="showHint">Show Hint</button>
            <button class="btn btn-primary" id="showAnswer">Show Answer</button>
          </div>
        </div>

        <div class="answer-section" id="answerSection" style="display: none;">
          <div class="answer-content">
            <h4>Suggested Answer:</h4>
            <div id="answerText" class="answer-text"></div>
          </div>
          <div class="answer-actions">
            <button class="btn btn-success" id="correctAnswer">Got it right!</button>
            <button class="btn btn-warning" id="partialAnswer">Partially correct</button>
            <button class="btn btn-danger" id="wrongAnswer">Need to review</button>
          </div>
        </div>

        <div class="progress-bar">
          <div class="progress-fill" id="progressFill"></div>
        </div>
      </div>
    </section>

    <!-- Flash Cards Section -->
    <section class="flash-cards" id="flashCards">
      <div class="cards-container">
        <div class="cards-header">
          <h2>Flash Cards</h2>
          <div class="cards-controls">
            <button class="btn btn-primary" id="addCard">Add New Card</button>
            <select id="categoryFilter" class="category-filter">
              <option value="all">All Categories</option>
              <option value="technical">Technical</option>
              <option value="behavioral">Behavioral</option>
              <option value="system-design">System Design</option>
              <option value="leadership">Leadership</option>
            </select>
          </div>
        </div>

        <div class="cards-grid" id="cardsGrid">
          <!-- Flash cards will be dynamically inserted here -->
        </div>

        <div class="cards-stats">
          <div class="stat">
            <span class="stat-number" id="totalCards">0</span>
            <span class="stat-label">Total Cards</span>
          </div>
          <div class="stat">
            <span class="stat-number" id="masteredCards">0</span>
            <span class="stat-label">Mastered</span>
          </div>
          <div class="stat">
            <span class="stat-number" id="reviewCards">0</span>
            <span class="stat-label">Need Review</span>
          </div>
        </div>
      </div>
    </section>

    <!-- AI Integration Section -->
    <section class="ai-integration">
      <div class="ai-container">
        <h2>AI-Powered Features</h2>
        <div class="ai-features">
          <div class="feature-card">
            <div class="feature-icon">🤖</div>
            <h3>Smart Question Generation</h3>
            <p>AI generates personalized interview questions based on your target role and experience level.</p>
            <button class="btn btn-outline" id="generateQuestions">Generate Questions</button>
          </div>
          
          <div class="feature-card">
            <div class="feature-icon">📊</div>
            <h3>Performance Analysis</h3>
            <p>Get detailed insights into your strengths and areas for improvement.</p>
            <button class="btn btn-outline" id="viewAnalysis">View Analysis</button>
          </div>
          
          <div class="feature-card">
            <div class="feature-icon">💡</div>
            <h3>Personalized Tips</h3>
            <p>Receive AI-generated tips and strategies tailored to your interview performance.</p>
            <button class="btn btn-outline" id="getTips">Get Tips</button>
          </div>
        </div>
      </div>
    </section>
  </main>

  <!-- Modal for adding new cards -->
  <div class="modal" id="addCardModal" style="display: none;">
    <div class="modal-content">
      <div class="modal-header">
        <h3>Add New Flash Card</h3>
        <button class="modal-close" id="closeModal">&times;</button>
      </div>
      <form class="modal-form" id="addCardForm">
        <div class="form-group">
          <label for="cardQuestion">Question:</label>
          <textarea id="cardQuestion" required placeholder="Enter your interview question..."></textarea>
        </div>
        <div class="form-group">
          <label for="cardAnswer">Answer:</label>
          <textarea id="cardAnswer" required placeholder="Enter the ideal answer..."></textarea>
        </div>
        <div class="form-group">
          <label for="cardCategory">Category:</label>
          <select id="cardCategory" required>
            <option value="">Select category</option>
            <option value="technical">Technical</option>
            <option value="behavioral">Behavioral</option>
            <option value="system-design">System Design</option>
            <option value="leadership">Leadership</option>
          </select>
        </div>
        <div class="form-group">
          <label for="cardDifficulty">Difficulty:</label>
          <select id="cardDifficulty" required>
            <option value="">Select difficulty</option>
            <option value="easy">Easy</option>
            <option value="medium">Medium</option>
            <option value="hard">Hard</option>
          </select>
        </div>
        <div class="form-actions">
          <button type="button" class="btn btn-secondary" id="cancelAdd">Cancel</button>
          <button type="submit" class="btn btn-primary">Add Card</button>
        </div>
      </form>
    </div>
  </div>

  <!-- Loading overlay -->
  <div class="loading-overlay" id="loadingOverlay" style="display: none;">
    <div class="loading-spinner"></div>
    <p>Generating AI content...</p>
  </div>

  <footer class="footer">
    <div class="footer-container">
      <p>&copy; 2025 SkillSync. Part of Portfolio Eight.</p>
      <p>
        <a href="../../">← Back to Portfolio</a> |
        <a href="https://github.com/yourusername/portfolio-eight/tree/main/projects/skill-sync">View Source</a>
      </p>
    </div>
  </footer>

  <script src="main.js" type="module"></script>
</body>
</html>
