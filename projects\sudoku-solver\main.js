/**
 * Sudoku Solver & Game - Main Game Logic
 * Complete implementation with all features
 */

class SudokuGame {
  constructor() {
    this.solver = new SudokuSolver();
    this.selectedCell = null;
    this.selectedNumber = null;
    this.gameState = 'playing'; // 'playing', 'won', 'lost'
    this.startTime = null;
    this.elapsedTime = 0;
    this.timerInterval = null;
    this.mistakes = 0;
    this.maxMistakes = 3;
    this.hintsUsed = 0;
    this.maxHints = 3;
    this.difficulty = 'easy';
    this.settings = {
      showMistakes: true,
      autoCheck: true,
      highlightRelated: true,
      darkTheme: false
    };
    
    this.init();
  }

  init() {
    this.loadSettings();
    this.createGrid();
    this.createNumberPad();
    this.setupEventListeners();
    this.applyTheme();
    this.newGame();
  }

  createGrid() {
    const grid = document.getElementById('sudokuGrid');
    grid.innerHTML = '';
    
    for (let row = 0; row < 9; row++) {
      for (let col = 0; col < 9; col++) {
        const cell = document.createElement('div');
        cell.className = 'sudoku-cell';
        cell.setAttribute('data-row', row);
        cell.setAttribute('data-col', col);
        cell.setAttribute('tabindex', '0');
        cell.addEventListener('click', () => this.selectCell(row, col));
        cell.addEventListener('keydown', (e) => this.handleCellKeydown(e, row, col));
        grid.appendChild(cell);
      }
    }
  }

  createNumberPad() {
    const numberPad = document.getElementById('numberPad');
    const buttons = numberPad.querySelectorAll('.number-btn');
    
    buttons.forEach(btn => {
      btn.addEventListener('click', () => {
        const number = parseInt(btn.getAttribute('data-number'));
        this.selectNumber(number);
      });
    });
  }

  setupEventListeners() {
    // Game controls
    document.getElementById('newGameBtn').addEventListener('click', () => this.newGame());
    document.getElementById('solveBtn').addEventListener('click', () => this.solvePuzzle());
    document.getElementById('hintBtn').addEventListener('click', () => this.getHint());
    document.getElementById('checkBtn').addEventListener('click', () => this.checkSolution());
    document.getElementById('clearBtn').addEventListener('click', () => this.clearGrid());
    
    // Difficulty selector
    document.getElementById('difficultySelect').addEventListener('change', (e) => {
      this.difficulty = e.target.value;
      this.updateDifficultyBadge();
    });
    
    // Modal controls
    document.getElementById('helpBtn').addEventListener('click', () => this.showModal('helpModal'));
    document.getElementById('settingsBtn').addEventListener('click', () => this.showModal('settingsModal'));
    
    // Close modal buttons
    document.querySelectorAll('.close-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const modalId = e.target.getAttribute('data-modal');
        this.hideModal(modalId);
      });
    });
    
    // Modal backdrop clicks
    document.querySelectorAll('.modal').forEach(modal => {
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          this.hideModal(modal.id);
        }
      });
    });
    
    // Victory modal buttons
    document.getElementById('playAgainBtn').addEventListener('click', () => {
      this.hideModal('victoryModal');
      this.newGame();
    });
    
    document.getElementById('shareScoreBtn').addEventListener('click', () => this.shareScore());
    
    // Game over modal buttons
    document.getElementById('tryAgainBtn').addEventListener('click', () => {
      this.hideModal('gameOverModal');
      this.newGame();
    });
    
    document.getElementById('showSolutionBtn').addEventListener('click', () => {
      this.hideModal('gameOverModal');
      this.showSolution();
    });
    
    // Settings toggles
    document.getElementById('showMistakes').addEventListener('change', (e) => {
      this.settings.showMistakes = e.target.checked;
      this.saveSettings();
    });
    
    document.getElementById('autoCheck').addEventListener('change', (e) => {
      this.settings.autoCheck = e.target.checked;
      this.saveSettings();
    });
    
    document.getElementById('highlightRelated').addEventListener('change', (e) => {
      this.settings.highlightRelated = e.target.checked;
      this.saveSettings();
      this.updateCellHighlights();
    });
    
    document.getElementById('darkTheme').addEventListener('change', (e) => {
      this.settings.darkTheme = e.target.checked;
      this.saveSettings();
      this.applyTheme();
    });
    
    // Keyboard controls
    document.addEventListener('keydown', (e) => this.handleGlobalKeydown(e));
  }

  newGame() {
    this.gameState = 'playing';
    this.mistakes = 0;
    this.hintsUsed = 0;
    this.selectedCell = null;
    this.selectedNumber = null;
    
    // Generate new puzzle
    const { puzzle, solution } = this.solver.generatePuzzle(this.difficulty);
    this.solver.loadGrid(puzzle);
    
    this.updateGrid();
    this.updateStatus();
    this.updateDifficultyBadge();
    this.startTimer();
    this.showMessage('New game started! Good luck!', 'success');
  }

  selectCell(row, col) {
    if (this.gameState !== 'playing') return;
    
    // Clear previous selection
    document.querySelectorAll('.sudoku-cell').forEach(cell => {
      cell.classList.remove('selected', 'highlighted');
    });
    
    this.selectedCell = { row, col };
    const cell = this.getCell(row, col);
    cell.classList.add('selected');
    
    if (this.settings.highlightRelated) {
      this.highlightRelatedCells(row, col);
    }
    
    this.updateNumberPadState();
  }

  selectNumber(number) {
    if (this.gameState !== 'playing') return;
    
    // Clear previous number selection
    document.querySelectorAll('.number-btn').forEach(btn => {
      btn.classList.remove('selected');
    });
    
    this.selectedNumber = number;
    
    if (number === 0) {
      // Erase mode
      if (this.selectedCell) {
        this.clearCell(this.selectedCell.row, this.selectedCell.col);
      }
    } else {
      // Number selection
      const btn = document.querySelector(`[data-number="${number}"]`);
      if (btn) btn.classList.add('selected');
      
      if (this.selectedCell) {
        this.placeNumber(this.selectedCell.row, this.selectedCell.col, number);
      }
    }
  }

  placeNumber(row, col, number) {
    if (this.solver.isOriginalCell(row, col)) {
      this.showMessage('Cannot modify given numbers!', 'error');
      return;
    }
    
    const isValid = this.solver.isValid(this.solver.getGrid(), row, col, number);
    
    if (this.settings.autoCheck && !isValid) {
      this.mistakes++;
      this.showMessage('Invalid move!', 'error');
      
      if (this.settings.showMistakes) {
        const cell = this.getCell(row, col);
        cell.classList.add('error');
        setTimeout(() => cell.classList.remove('error'), 1000);
      }
      
      if (this.mistakes >= this.maxMistakes) {
        this.gameOver();
        return;
      }
    }
    
    this.solver.setCell(row, col, number);
    this.updateGrid();
    this.updateStatus();
    
    if (this.solver.isComplete()) {
      this.gameWon();
    }
  }

  clearCell(row, col) {
    if (this.solver.isOriginalCell(row, col)) {
      this.showMessage('Cannot modify given numbers!', 'error');
      return;
    }
    
    this.solver.clearCell(row, col);
    this.updateGrid();
    this.updateStatus();
  }

  getHint() {
    if (this.gameState !== 'playing') return;
    
    if (this.hintsUsed >= this.maxHints) {
      this.showMessage('No more hints available!', 'warning');
      return;
    }
    
    const hint = this.solver.getHint();
    if (!hint) {
      this.showMessage('No hints available!', 'warning');
      return;
    }
    
    this.hintsUsed++;
    this.solver.setCell(hint.row, hint.col, hint.number);
    
    const cell = this.getCell(hint.row, hint.col);
    cell.classList.add('hint');
    setTimeout(() => cell.classList.remove('hint'), 2000);
    
    this.updateGrid();
    this.updateStatus();
    this.showMessage(`Hint: ${hint.technique}`, 'success');
    
    if (this.solver.isComplete()) {
      this.gameWon();
    }
  }

  solvePuzzle() {
    if (this.gameState !== 'playing') return;
    
    const grid = this.solver.getGrid();
    if (this.solver.solveAdvanced(grid)) {
      this.updateGrid();
      this.showMessage('Puzzle solved!', 'success');
      this.gameState = 'solved';
      this.stopTimer();
    } else {
      this.showMessage('Unable to solve puzzle!', 'error');
    }
  }

  checkSolution() {
    if (this.gameState !== 'playing') return;
    
    const grid = this.solver.getGrid();
    let errors = 0;
    
    for (let row = 0; row < 9; row++) {
      for (let col = 0; col < 9; col++) {
        if (grid[row][col] !== 0) {
          const num = grid[row][col];
          grid[row][col] = 0; // Temporarily remove
          
          if (!this.solver.isValid(grid, row, col, num)) {
            errors++;
            if (this.settings.showMistakes) {
              const cell = this.getCell(row, col);
              cell.classList.add('error');
              setTimeout(() => cell.classList.remove('error'), 2000);
            }
          }
          
          grid[row][col] = num; // Restore
        }
      }
    }
    
    if (errors === 0) {
      this.showMessage('No errors found!', 'success');
    } else {
      this.showMessage(`${errors} error${errors > 1 ? 's' : ''} found!`, 'error');
    }
  }

  clearGrid() {
    if (this.gameState !== 'playing') return;
    
    if (confirm('Are you sure you want to clear all your entries?')) {
      const grid = this.solver.getGrid();
      for (let row = 0; row < 9; row++) {
        for (let col = 0; col < 9; col++) {
          if (!this.solver.isOriginalCell(row, col)) {
            this.solver.clearCell(row, col);
          }
        }
      }
      
      this.updateGrid();
      this.updateStatus();
      this.showMessage('Grid cleared!', 'info');
    }
  }

  showSolution() {
    const solution = this.solver.getSolution();
    this.solver.loadGrid(solution);
    this.updateGrid();
    this.gameState = 'solved';
    this.stopTimer();
    this.showMessage('Solution revealed!', 'info');
  }

  gameWon() {
    this.gameState = 'won';
    this.stopTimer();
    
    // Update victory modal
    document.getElementById('finalTime').textContent = this.formatTime(this.elapsedTime);
    document.getElementById('finalDifficulty').textContent = this.difficulty.charAt(0).toUpperCase() + this.difficulty.slice(1);
    document.getElementById('finalMistakes').textContent = this.mistakes;
    document.getElementById('finalHints').textContent = this.hintsUsed;
    
    setTimeout(() => {
      this.showModal('victoryModal');
    }, 1000);
    
    this.showMessage('Congratulations! Puzzle solved!', 'success');
  }

  gameOver() {
    this.gameState = 'lost';
    this.stopTimer();
    
    setTimeout(() => {
      this.showModal('gameOverModal');
    }, 1000);
    
    this.showMessage('Game Over! Too many mistakes.', 'error');
  }

  updateGrid() {
    const grid = this.solver.getGrid();
    
    for (let row = 0; row < 9; row++) {
      for (let col = 0; col < 9; col++) {
        const cell = this.getCell(row, col);
        const value = grid[row][col];
        
        cell.textContent = value === 0 ? '' : value;
        cell.classList.remove('given', 'user');
        
        if (value !== 0) {
          if (this.solver.isOriginalCell(row, col)) {
            cell.classList.add('given');
          } else {
            cell.classList.add('user');
          }
        }
      }
    }
  }

  updateStatus() {
    document.getElementById('mistakeCount').textContent = `${this.mistakes}/${this.maxMistakes}`;
    document.getElementById('hintCount').textContent = this.maxHints - this.hintsUsed;
    
    const filledCells = this.solver.getGrid().flat().filter(cell => cell !== 0).length;
    document.getElementById('progressCount').textContent = `${filledCells}/81`;
    
    // Update hint button state
    const hintBtn = document.getElementById('hintBtn');
    hintBtn.disabled = this.hintsUsed >= this.maxHints || this.gameState !== 'playing';
  }

  updateDifficultyBadge() {
    const badge = document.getElementById('difficultyBadge');
    badge.textContent = this.difficulty.charAt(0).toUpperCase() + this.difficulty.slice(1);
    
    const colors = {
      easy: '#4caf50',
      medium: '#ff9800',
      hard: '#f44336',
      expert: '#9c27b0'
    };
    
    badge.style.backgroundColor = colors[this.difficulty];
  }

  updateNumberPadState() {
    if (!this.selectedCell) return;
    
    const grid = this.solver.getGrid();
    const { row, col } = this.selectedCell;
    
    document.querySelectorAll('.number-btn').forEach(btn => {
      const number = parseInt(btn.getAttribute('data-number'));
      
      if (number === 0) return; // Skip erase button
      
      const isValid = this.solver.isValid(grid, row, col, number);
      btn.disabled = !isValid && this.settings.autoCheck;
    });
  }

  highlightRelatedCells(row, col) {
    // Highlight row
    for (let c = 0; c < 9; c++) {
      if (c !== col) {
        this.getCell(row, c).classList.add('highlighted');
      }
    }
    
    // Highlight column
    for (let r = 0; r < 9; r++) {
      if (r !== row) {
        this.getCell(r, col).classList.add('highlighted');
      }
    }
    
    // Highlight 3x3 box
    const boxRow = Math.floor(row / 3) * 3;
    const boxCol = Math.floor(col / 3) * 3;
    
    for (let r = boxRow; r < boxRow + 3; r++) {
      for (let c = boxCol; c < boxCol + 3; c++) {
        if (r !== row || c !== col) {
          this.getCell(r, c).classList.add('highlighted');
        }
      }
    }
  }

  updateCellHighlights() {
    document.querySelectorAll('.sudoku-cell').forEach(cell => {
      cell.classList.remove('highlighted');
    });
    
    if (this.selectedCell && this.settings.highlightRelated) {
      this.highlightRelatedCells(this.selectedCell.row, this.selectedCell.col);
    }
  }

  handleCellKeydown(e, row, col) {
    e.preventDefault();
    
    const key = e.key;
    
    if (key >= '1' && key <= '9') {
      this.selectNumber(parseInt(key));
    } else if (key === '0' || key === 'Delete' || key === 'Backspace') {
      this.selectNumber(0);
    } else if (key === 'ArrowUp' && row > 0) {
      this.selectCell(row - 1, col);
    } else if (key === 'ArrowDown' && row < 8) {
      this.selectCell(row + 1, col);
    } else if (key === 'ArrowLeft' && col > 0) {
      this.selectCell(row, col - 1);
    } else if (key === 'ArrowRight' && col < 8) {
      this.selectCell(row, col + 1);
    }
  }

  handleGlobalKeydown(e) {
    if (e.target.tagName === 'INPUT' || e.target.tagName === 'SELECT') return;
    
    const key = e.key;
    
    if (key === 'Escape') {
      // Close any open modal
      document.querySelectorAll('.modal.show').forEach(modal => {
        this.hideModal(modal.id);
      });
    } else if (key === 'h' || key === 'H') {
      this.getHint();
    } else if (key === 'n' || key === 'N') {
      this.newGame();
    } else if (key === 'c' || key === 'C') {
      this.checkSolution();
    }
  }

  startTimer() {
    this.startTime = Date.now();
    this.elapsedTime = 0;
    
    this.timerInterval = setInterval(() => {
      this.elapsedTime = Date.now() - this.startTime;
      document.getElementById('timer').textContent = this.formatTime(this.elapsedTime);
    }, 1000);
  }

  stopTimer() {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
      this.timerInterval = null;
    }
  }

  formatTime(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  getCell(row, col) {
    return document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
  }

  showMessage(message, type = 'info') {
    const messageElement = document.getElementById('gameMessage');
    messageElement.textContent = message;
    messageElement.className = `message show ${type}`;
    
    setTimeout(() => {
      messageElement.classList.remove('show');
    }, 3000);
  }

  showToast(message) {
    const toast = document.getElementById('toast');
    toast.textContent = message;
    toast.classList.add('show');
    
    setTimeout(() => {
      toast.classList.remove('show');
    }, 2000);
  }

  showModal(modalId) {
    const modal = document.getElementById(modalId);
    modal.classList.add('show');
    
    if (modalId === 'settingsModal') {
      this.updateSettingsDisplay();
    }
  }

  hideModal(modalId) {
    const modal = document.getElementById(modalId);
    modal.classList.remove('show');
  }

  updateSettingsDisplay() {
    document.getElementById('showMistakes').checked = this.settings.showMistakes;
    document.getElementById('autoCheck').checked = this.settings.autoCheck;
    document.getElementById('highlightRelated').checked = this.settings.highlightRelated;
    document.getElementById('darkTheme').checked = this.settings.darkTheme;
  }

  shareScore() {
    const time = this.formatTime(this.elapsedTime);
    const shareText = `🧩 Sudoku Solved! 🧩\n\n⏱️ Time: ${time}\n🎯 Difficulty: ${this.difficulty.charAt(0).toUpperCase() + this.difficulty.slice(1)}\n❌ Mistakes: ${this.mistakes}\n💡 Hints: ${this.hintsUsed}\n\nPlay at: ${window.location.href}`;
    
    if (navigator.share) {
      navigator.share({
        title: 'Sudoku Game',
        text: shareText
      });
    } else {
      navigator.clipboard.writeText(shareText).then(() => {
        this.showToast('Score copied to clipboard!');
      }).catch(() => {
        this.showToast('Unable to copy score');
      });
    }
  }

  applyTheme() {
    document.body.setAttribute('data-theme', this.settings.darkTheme ? 'dark' : 'light');
  }

  saveSettings() {
    localStorage.setItem('sudoku-settings', JSON.stringify(this.settings));
  }

  loadSettings() {
    const saved = localStorage.getItem('sudoku-settings');
    if (saved) {
      this.settings = { ...this.settings, ...JSON.parse(saved) };
    }
  }
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', () => {
  new SudokuGame();
});
