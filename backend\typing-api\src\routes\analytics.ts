/**
 * Analytics routes for comprehensive typing performance insights
 */

import { Router, Request, Response } from 'express';
import { async<PERSON>and<PERSON> } from '@/middleware/errorHandler';
import { validationMiddleware } from '@/middleware/validation';
import { logger } from '@/utils/logger';

const router = Router();

/**
 * Get global analytics
 */
router.get('/', validationMiddleware.getAnalytics, asyncHandler(async (req: Request, res: Response) => {
  const { startDate, endDate, granularity = 'day', category, difficulty } = req.query;

  try {
    // Mock analytics data - in production this would query the database
    const analytics = {
      totalTests: 15420,
      totalUsers: 2847,
      averageWpm: 52.3,
      averageAccuracy: 91.7,
      totalCharacters: 8947362,
      totalTime: 2847293, // milliseconds
      popularCategories: [
        { category: 'quotes', testsCount: 5420, averageWpm: 54.2, averageAccuracy: 92.1, popularityScore: 85 },
        { category: 'programming', testsCount: 3210, averageWpm: 48.7, averageAccuracy: 89.3, popularityScore: 72 },
        { category: 'literature', testsCount: 2890, averageWpm: 56.1, averageAccuracy: 93.8, popularityScore: 68 }
      ],
      difficultyDistribution: [
        { difficulty: 'easy', testsCount: 6420, averageWpm: 58.3, averageAccuracy: 95.2, completionRate: 98.5 },
        { difficulty: 'medium', testsCount: 5890, averageWpm: 51.7, averageAccuracy: 91.4, completionRate: 94.2 },
        { difficulty: 'hard', testsCount: 2310, averageWpm: 45.2, averageAccuracy: 87.9, completionRate: 87.3 }
      ],
      hourlyActivity: generateHourlyActivity(),
      dailyActivity: generateDailyActivity(),
      trends: {
        wpmTrend: generateTrendData('wpm'),
        accuracyTrend: generateTrendData('accuracy'),
        userGrowth: generateTrendData('users'),
        testVolume: generateTrendData('tests')
      }
    };

    res.json({
      success: true,
      data: analytics,
      filters: {
        startDate,
        endDate,
        granularity,
        category,
        difficulty
      }
    });

  } catch (error) {
    logger.error('Failed to get analytics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get analytics',
      requestId: (req as any).requestId
    });
  }
}));

/**
 * Get performance trends
 */
router.get('/trends', asyncHandler(async (req: Request, res: Response) => {
  const { metric = 'wpm', period = '30d', category, difficulty } = req.query;

  try {
    const trends = {
      metric,
      period,
      data: generateTrendData(metric as string),
      summary: {
        current: 52.3,
        previous: 49.8,
        change: 5.02,
        trend: 'increasing'
      }
    };

    res.json({
      success: true,
      data: trends
    });

  } catch (error) {
    logger.error('Failed to get trends:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get trends',
      requestId: (req as any).requestId
    });
  }
}));

/**
 * Get category performance breakdown
 */
router.get('/categories', asyncHandler(async (req: Request, res: Response) => {
  try {
    const categories = [
      {
        category: 'quotes',
        testsCount: 5420,
        averageWpm: 54.2,
        averageAccuracy: 92.1,
        popularityScore: 85,
        topPerformers: [
          { username: 'speedtyper', wpm: 89.3, accuracy: 97.2 },
          { username: 'keyboardmaster', wpm: 87.1, accuracy: 96.8 }
        ]
      },
      {
        category: 'programming',
        testsCount: 3210,
        averageWpm: 48.7,
        averageAccuracy: 89.3,
        popularityScore: 72,
        topPerformers: [
          { username: 'codetyper', wpm: 76.4, accuracy: 94.5 },
          { username: 'devspeed', wpm: 74.2, accuracy: 93.8 }
        ]
      }
    ];

    res.json({
      success: true,
      data: categories
    });

  } catch (error) {
    logger.error('Failed to get category analytics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get category analytics',
      requestId: (req as any).requestId
    });
  }
}));

/**
 * Generate mock hourly activity data
 */
function generateHourlyActivity() {
  const hours = [];
  for (let i = 0; i < 24; i++) {
    // Simulate realistic activity patterns (higher during day, lower at night)
    const baseActivity = Math.sin((i - 6) * Math.PI / 12) * 0.5 + 0.5;
    const testsCount = Math.floor(baseActivity * 800 + Math.random() * 200);
    const activeUsers = Math.floor(testsCount * 0.3 + Math.random() * 50);
    
    hours.push({
      hour: i,
      testsCount,
      averageWpm: 50 + Math.random() * 10,
      activeUsers
    });
  }
  return hours;
}

/**
 * Generate mock daily activity data
 */
function generateDailyActivity() {
  const days = [];
  const today = new Date();
  
  for (let i = 29; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);
    
    // Simulate weekly patterns (higher on weekdays)
    const dayOfWeek = date.getDay();
    const weekdayMultiplier = dayOfWeek === 0 || dayOfWeek === 6 ? 0.7 : 1.0;
    
    const testsCount = Math.floor((400 + Math.random() * 200) * weekdayMultiplier);
    const uniqueUsers = Math.floor(testsCount * 0.4 + Math.random() * 50);
    
    days.push({
      date: date.toISOString().split('T')[0],
      testsCount,
      uniqueUsers,
      averageWpm: 50 + Math.random() * 8,
      averageAccuracy: 90 + Math.random() * 5
    });
  }
  
  return days;
}

/**
 * Generate mock trend data
 */
function generateTrendData(metric: string) {
  const data = [];
  const periods = 30;
  
  let baseValue;
  switch (metric) {
    case 'wpm':
      baseValue = 50;
      break;
    case 'accuracy':
      baseValue = 90;
      break;
    case 'users':
      baseValue = 100;
      break;
    case 'tests':
      baseValue = 500;
      break;
    default:
      baseValue = 50;
  }
  
  for (let i = periods - 1; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    
    // Simulate gradual improvement over time
    const trend = (periods - i) * 0.1;
    const noise = (Math.random() - 0.5) * 2;
    const value = baseValue + trend + noise;
    
    const previousValue = i === periods - 1 ? value : data[data.length - 1].value;
    const change = ((value - previousValue) / previousValue) * 100;
    
    data.push({
      period: date.toISOString().split('T')[0],
      value: Math.round(value * 100) / 100,
      change: Math.round(change * 100) / 100
    });
  }
  
  return data;
}

export default router;
