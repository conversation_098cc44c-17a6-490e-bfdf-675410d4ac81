const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(express.json());

console.log('🔧 Starting test server...');

// Simple test route
app.post('/api/game/new', (req, res) => {
  console.log('📝 POST /api/game/new called');
  res.json({
    success: true,
    message: 'Test server working!',
    gameId: 'test123'
  });
});

app.get('/api/test', (req, res) => {
  console.log('📝 GET /api/test called');
  res.json({ message: 'Server is running!' });
});

app.listen(PORT, () => {
  console.log(`🎯 Test server running on port ${PORT}`);
  console.log(`🔗 Test URL: http://localhost:${PORT}/api/test`);
});
