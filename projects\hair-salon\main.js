/**
 * A Elements Hair Salon - Interactive Features
 * Premium hair salon website with smooth animations and interactions
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all interactive features
    initSmoothScrolling();
    initNavbarEffects();
    initAnimations();
    initBookingSystem();
    initImageFallbacks();
    
    console.log('A Elements Hair Salon website loaded successfully');
});

/**
 * Smooth scrolling for navigation links
 */
function initSmoothScrolling() {
    const navLinks = document.querySelectorAll('.nav-menu a[href^="#"]');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const offsetTop = targetSection.offsetTop - 80; // Account for fixed navbar
                
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
                
                // Update active nav link
                updateActiveNavLink(targetId);
            }
        });
    });
}

/**
 * Update active navigation link
 */
function updateActiveNavLink(targetId) {
    const navLinks = document.querySelectorAll('.nav-menu a');
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === targetId) {
            link.classList.add('active');
        }
    });
}

/**
 * Navbar scroll effects
 */
function initNavbarEffects() {
    const navbar = document.querySelector('.navbar');
    let lastScrollTop = 0;
    
    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        // Add/remove scrolled class for styling
        if (scrollTop > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
        
        // Hide/show navbar on scroll
        if (scrollTop > lastScrollTop && scrollTop > 100) {
            navbar.style.transform = 'translateY(-100%)';
        } else {
            navbar.style.transform = 'translateY(0)';
        }
        
        lastScrollTop = scrollTop;
    });
}

/**
 * Intersection Observer for animations
 */
function initAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
                
                // Stagger animations for grid items
                if (entry.target.classList.contains('service-card') || 
                    entry.target.classList.contains('step') ||
                    entry.target.classList.contains('contact-item')) {
                    staggerAnimation(entry.target);
                }
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    const animateElements = document.querySelectorAll(`
        .hero-text, .hero-image, .about-content, .service-card, 
        .step, .contact-item, .faq-item, .specialty-content,
        .sustainability-hero, .visit-content
    `);
    
    animateElements.forEach(el => {
        observer.observe(el);
    });
}

/**
 * Stagger animations for grid items
 */
function staggerAnimation(element) {
    const siblings = element.parentElement.children;
    const index = Array.from(siblings).indexOf(element);
    
    setTimeout(() => {
        element.style.opacity = '1';
        element.style.transform = 'translateY(0)';
    }, index * 100);
}

/**
 * Booking system interactions
 */
function initBookingSystem() {
    const bookingButtons = document.querySelectorAll('.book-btn, .btn-primary');
    
    bookingButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            if (this.textContent.includes('Book')) {
                e.preventDefault();
                window.location.href = 'booking.html';
            } else if (this.textContent.includes('Call')) {
                // Allow phone calls to proceed normally
                return;
            }
        });
    });
    
    // Service card interactions
    const serviceCards = document.querySelectorAll('.service-card');
    serviceCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

/**
 * Show booking modal (placeholder)
 */
function showBookingModal() {
    // Create modal overlay
    const modal = document.createElement('div');
    modal.className = 'booking-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>Book Your Appointment</h3>
                <button class="close-modal">&times;</button>
            </div>
            <div class="modal-body">
                <p>Ready to transform your hair? Contact us to schedule your appointment:</p>
                <div class="contact-options">
                    <a href="tel:+15551234567" class="contact-option">
                        <span class="icon">📞</span>
                        <span>Call (*************</span>
                    </a>
                    <a href="mailto:<EMAIL>" class="contact-option">
                        <span class="icon">✉️</span>
                        <span>Email Us</span>
                    </a>
                </div>
                <div class="booking-info">
                    <h4>What to Expect:</h4>
                    <ul>
                        <li>Complimentary consultation</li>
                        <li>Professional hair analysis</li>
                        <li>Personalized treatment plan</li>
                        <li>Premium sustainable products</li>
                    </ul>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Close modal functionality
    const closeBtn = modal.querySelector('.close-modal');
    closeBtn.addEventListener('click', () => {
        document.body.removeChild(modal);
    });
    
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            document.body.removeChild(modal);
        }
    });
    
    // Animate modal in
    setTimeout(() => {
        modal.classList.add('show');
    }, 10);
}

/**
 * Image fallbacks for missing photos
 */
function initImageFallbacks() {
    const images = document.querySelectorAll('img');
    
    images.forEach(img => {
        img.addEventListener('error', function() {
            // Create placeholder based on image type
            const placeholder = createImagePlaceholder(this.alt, this.className);
            this.src = placeholder;
        });
    });
}

/**
 * Create placeholder image
 */
function createImagePlaceholder(altText, className) {
    const canvas = document.createElement('canvas');
    canvas.width = 800;
    canvas.height = 600;
    const ctx = canvas.getContext('2d');
    
    // Create gradient background
    const gradient = ctx.createLinearGradient(0, 0, 800, 600);
    gradient.addColorStop(0, '#f4c2c2');
    gradient.addColorStop(1, '#ec4899');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, 800, 600);
    
    // Add text
    ctx.fillStyle = 'white';
    ctx.font = 'bold 32px Inter';
    ctx.textAlign = 'center';
    ctx.fillText('A Elements Hair', 400, 280);
    
    ctx.font = '20px Inter';
    ctx.fillText(altText || 'Professional Hair Salon', 400, 320);
    
    // Add decorative elements
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
    ctx.lineWidth = 3;
    for (let i = 0; i < 3; i++) {
        ctx.beginPath();
        ctx.arc(200 + i * 200, 150, 30, 0, Math.PI * 2);
        ctx.stroke();
    }
    
    return canvas.toDataURL();
}

/**
 * Add CSS for modal and animations
 */
function addDynamicStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .booking-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .booking-modal.show {
            opacity: 1;
        }
        
        .modal-content {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            transform: translateY(50px);
            transition: transform 0.3s ease;
        }
        
        .booking-modal.show .modal-content {
            transform: translateY(0);
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #f4c2c2;
        }
        
        .close-modal {
            background: none;
            border: none;
            font-size: 2rem;
            cursor: pointer;
            color: #ec4899;
        }
        
        .contact-options {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin: 1.5rem 0;
        }
        
        .contact-option {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: #fdf2f8;
            border-radius: 10px;
            text-decoration: none;
            color: #1f2937;
            transition: background 0.3s ease;
        }
        
        .contact-option:hover {
            background: #f4c2c2;
        }
        
        .booking-info {
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid #f4c2c2;
        }
        
        .booking-info h4 {
            color: #ec4899;
            margin-bottom: 1rem;
        }
        
        .booking-info ul {
            list-style: none;
            padding: 0;
        }
        
        .booking-info li {
            padding: 0.5rem 0;
            color: #6b7280;
        }
        
        .booking-info li:before {
            content: "✨ ";
            margin-right: 0.5rem;
        }
        
        /* Animation styles */
        .hero-text, .hero-image, .about-content, .service-card, 
        .step, .contact-item, .faq-item, .specialty-content,
        .sustainability-hero, .visit-content {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }
        
        .animate-in {
            opacity: 1 !important;
            transform: translateY(0) !important;
        }
        
        .navbar {
            transition: transform 0.3s ease;
        }
        
        .navbar.scrolled {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: 0 2px 30px rgba(0, 0, 0, 0.15);
        }
    `;
    
    document.head.appendChild(style);
}

// Initialize dynamic styles
addDynamicStyles();
