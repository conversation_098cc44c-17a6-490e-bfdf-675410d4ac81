# Prometheus Alerting Rules for Sudoku API
groups:
  - name: sudoku_api_alerts
    rules:
      # High error rate
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} errors per second"

      # High response time
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }} seconds"

      # High memory usage
      - alert: HighMemoryUsage
        expr: process_resident_memory_bytes / 1024 / 1024 > 500
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is {{ $value }} MB"

      # API down
      - alert: APIDown
        expr: up{job="sudoku-api"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Sudoku API is down"
          description: "The Sudoku API has been down for more than 1 minute"

      # High CPU usage
      - alert: HighCPUUsage
        expr: rate(process_cpu_seconds_total[5m]) * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is {{ $value }}%"

      # Low success rate for puzzle solving
      - alert: LowSolveSuccessRate
        expr: rate(sudoku_operations_total{operation="solve",result="success"}[10m]) / rate(sudoku_operations_total{operation="solve"}[10m]) < 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Low puzzle solve success rate"
          description: "Puzzle solve success rate is {{ $value | humanizePercentage }}"

      # High puzzle generation time
      - alert: HighGenerationTime
        expr: histogram_quantile(0.95, rate(sudoku_operation_duration_seconds_bucket{operation="generate"}[5m])) > 5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High puzzle generation time"
          description: "95th percentile generation time is {{ $value }} seconds"
