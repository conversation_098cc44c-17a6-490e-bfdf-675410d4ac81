/**
 * Comprehensive error handling middleware for collaborative workspace API
 * Custom error classes and global error handling with proper HTTP status codes
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '@/utils/logger';

/**
 * Base error class for all application errors
 */
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;
  public code?: string;
  public details?: any;

  constructor(message: string, statusCode: number = 500, code?: string, details?: any) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;
    this.code = code;
    this.details = details;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Authentication error
 */
export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication failed', details?: any) {
    super(message, 401, 'AUTHENTICATION_ERROR', details);
  }
}

/**
 * Authorization error
 */
export class AuthorizationError extends AppError {
  constructor(message: string = 'Access denied', details?: any) {
    super(message, 403, 'AUTHORIZATION_ERROR', details);
  }
}

/**
 * Validation error
 */
export class ValidationError extends AppError {
  constructor(message: string = 'Validation failed', details?: any) {
    super(message, 400, 'VALIDATION_ERROR', details);
  }
}

/**
 * Not found error
 */
export class NotFoundError extends AppError {
  constructor(resource: string = 'Resource', details?: any) {
    super(`${resource} not found`, 404, 'NOT_FOUND_ERROR', details);
  }
}

/**
 * Conflict error
 */
export class ConflictError extends AppError {
  constructor(message: string = 'Resource conflict', details?: any) {
    super(message, 409, 'CONFLICT_ERROR', details);
  }
}

/**
 * Rate limit error
 */
export class RateLimitError extends AppError {
  constructor(message: string = 'Rate limit exceeded', details?: any) {
    super(message, 429, 'RATE_LIMIT_ERROR', details);
  }
}

/**
 * Workspace-specific errors
 */
export class WorkspaceError extends AppError {
  constructor(message: string, statusCode: number = 400, details?: any) {
    super(message, statusCode, 'WORKSPACE_ERROR', details);
  }
}

/**
 * Room-specific errors
 */
export class RoomError extends AppError {
  constructor(message: string, statusCode: number = 400, details?: any) {
    super(message, statusCode, 'ROOM_ERROR', details);
  }
}

/**
 * Chat-specific errors
 */
export class ChatError extends AppError {
  constructor(message: string, statusCode: number = 400, details?: any) {
    super(message, statusCode, 'CHAT_ERROR', details);
  }
}

/**
 * Whiteboard-specific errors
 */
export class WhiteboardError extends AppError {
  constructor(message: string, statusCode: number = 400, details?: any) {
    super(message, statusCode, 'WHITEBOARD_ERROR', details);
  }
}

/**
 * Task-specific errors
 */
export class TaskError extends AppError {
  constructor(message: string, statusCode: number = 400, details?: any) {
    super(message, statusCode, 'TASK_ERROR', details);
  }
}

/**
 * File-specific errors
 */
export class FileError extends AppError {
  constructor(message: string, statusCode: number = 400, details?: any) {
    super(message, statusCode, 'FILE_ERROR', details);
  }
}

/**
 * Database error
 */
export class DatabaseError extends AppError {
  constructor(message: string = 'Database operation failed', details?: any) {
    super(message, 500, 'DATABASE_ERROR', details);
  }
}

/**
 * Cache error
 */
export class CacheError extends AppError {
  constructor(message: string = 'Cache operation failed', details?: any) {
    super(message, 500, 'CACHE_ERROR', details);
  }
}

/**
 * Real-time error
 */
export class RealTimeError extends AppError {
  constructor(message: string = 'Real-time operation failed', details?: any) {
    super(message, 500, 'REALTIME_ERROR', details);
  }
}

/**
 * External service error
 */
export class ExternalServiceError extends AppError {
  constructor(service: string, message: string = 'External service error', details?: any) {
    super(`${service}: ${message}`, 502, 'EXTERNAL_SERVICE_ERROR', details);
  }
}

/**
 * Async handler wrapper to catch Promise rejections
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Global error handling middleware
 */
export const errorHandler = (
  error: Error | AppError,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let statusCode = 500;
  let message = 'Internal server error';
  let code = 'INTERNAL_ERROR';
  let details: any = null;

  // Handle known application errors
  if (error instanceof AppError) {
    statusCode = error.statusCode;
    message = error.message;
    code = error.code || 'APP_ERROR';
    details = error.details;
  }
  // Handle validation errors from express-validator
  else if (error.name === 'ValidationError') {
    statusCode = 400;
    message = 'Validation failed';
    code = 'VALIDATION_ERROR';
    details = error.message;
  }
  // Handle JWT errors
  else if (error.name === 'JsonWebTokenError') {
    statusCode = 401;
    message = 'Invalid token';
    code = 'INVALID_TOKEN';
  }
  else if (error.name === 'TokenExpiredError') {
    statusCode = 401;
    message = 'Token expired';
    code = 'TOKEN_EXPIRED';
  }
  // Handle multer errors (file upload)
  else if (error.name === 'MulterError') {
    statusCode = 400;
    message = 'File upload error';
    code = 'FILE_UPLOAD_ERROR';
    details = error.message;
  }
  // Handle database errors
  else if (error.message.includes('SQLITE_')) {
    statusCode = 500;
    message = 'Database error';
    code = 'DATABASE_ERROR';
    details = process.env.NODE_ENV === 'development' ? error.message : null;
  }
  // Handle syntax errors
  else if (error instanceof SyntaxError && 'body' in error) {
    statusCode = 400;
    message = 'Invalid JSON in request body';
    code = 'INVALID_JSON';
  }
  // Handle other known errors
  else if (error.name === 'CastError') {
    statusCode = 400;
    message = 'Invalid ID format';
    code = 'INVALID_ID';
  }

  // Log error details
  const errorInfo = {
    requestId: req.requestId,
    method: req.method,
    url: req.originalUrl,
    statusCode,
    code,
    message,
    details,
    userId: req.userId,
    workspaceId: req.workspaceId,
    roomId: req.roomId,
    userAgent: req.get('User-Agent'),
    ip: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
    timestamp: new Date().toISOString(),
    stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
  };

  // Log based on severity
  if (statusCode >= 500) {
    logger.error('Server error occurred', errorInfo);
  } else if (statusCode >= 400) {
    logger.warn('Client error occurred', errorInfo);
  }

  // Send error response
  const errorResponse: any = {
    success: false,
    error: {
      code,
      message,
      statusCode,
      timestamp: new Date().toISOString(),
      requestId: req.requestId
    }
  };

  // Include details in development mode or for validation errors
  if (details && (process.env.NODE_ENV === 'development' || statusCode === 400)) {
    errorResponse.error.details = details;
  }

  // Include stack trace in development mode
  if (process.env.NODE_ENV === 'development' && error.stack) {
    errorResponse.error.stack = error.stack;
  }

  res.status(statusCode).json(errorResponse);
};

/**
 * 404 handler for unmatched routes
 */
export const notFoundHandler = (req: Request, res: Response, next: NextFunction): void => {
  const error = new NotFoundError('Endpoint', {
    path: req.originalUrl,
    method: req.method
  });
  next(error);
};

/**
 * Error handler for unhandled promise rejections
 */
export const unhandledRejectionHandler = (reason: any, promise: Promise<any>): void => {
  logger.error('Unhandled Promise Rejection', {
    reason: reason?.message || reason,
    stack: reason?.stack,
    promise: promise.toString()
  });
  
  // In production, we might want to restart the process
  if (process.env.NODE_ENV === 'production') {
    process.exit(1);
  }
};

/**
 * Error handler for uncaught exceptions
 */
export const uncaughtExceptionHandler = (error: Error): void => {
  logger.error('Uncaught Exception', {
    message: error.message,
    stack: error.stack,
    name: error.name
  });
  
  // Always exit on uncaught exceptions
  process.exit(1);
};

/**
 * Setup global error handlers
 */
export const setupGlobalErrorHandlers = (): void => {
  process.on('unhandledRejection', unhandledRejectionHandler);
  process.on('uncaughtException', uncaughtExceptionHandler);
};

/**
 * Validation error formatter for express-validator
 */
export const formatValidationErrors = (errors: any[]): any => {
  const formatted: any = {};
  
  errors.forEach((error) => {
    const field = error.param || error.path || 'unknown';
    if (!formatted[field]) {
      formatted[field] = [];
    }
    formatted[field].push({
      message: error.msg,
      value: error.value,
      location: error.location
    });
  });
  
  return formatted;
};
