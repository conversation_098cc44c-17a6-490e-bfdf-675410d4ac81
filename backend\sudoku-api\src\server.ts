/**
 * Enterprise Sudoku Solver API Server
 * Production-ready Express server with comprehensive middleware and monitoring
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { v4 as uuidv4 } from 'uuid';

import { getConfig, validateConfig } from '@/config';
import { log } from '@/utils/logger';
import { ApiResponse } from '@/types';

// Import routes
import sudokuRoutes from '@/routes/sudoku';
import healthRoutes from '@/routes/health';
import metricsRoutes from '@/routes/metrics';
import docsRoutes from '@/routes/docs';

// Import middleware
import { errorHandler } from '@/middleware/errorHandler';
import { requestLogger } from '@/middleware/requestLogger';
import { metricsMiddleware } from '@/middleware/metrics';
import { validationMiddleware } from '@/middleware/validation';

/**
 * Application class for better organization and testability
 */
export class SudokuApiServer {
  private app: express.Application;
  private config = getConfig();

  constructor() {
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  /**
   * Setup middleware stack
   */
  private setupMiddleware(): void {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
      hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true,
      },
    }));

    // CORS configuration
    this.app.use(cors({
      origin: this.config.cors.origin,
      credentials: this.config.cors.credentials,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Request-ID'],
    }));

    // Compression
    this.app.use(compression());

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Request ID middleware
    this.app.use((req, res, next) => {
      req.id = req.headers['x-request-id'] as string || uuidv4();
      res.setHeader('X-Request-ID', req.id);
      next();
    });

    // Rate limiting
    if (this.config.nodeEnv === 'production') {
      const limiter = rateLimit({
        windowMs: this.config.rateLimit.windowMs,
        max: this.config.rateLimit.maxRequests,
        message: {
          success: false,
          error: 'Too many requests, please try again later',
          timestamp: new Date().toISOString(),
          requestId: '',
        },
        standardHeaders: true,
        legacyHeaders: false,
        skip: (req) => {
          // Skip rate limiting for health checks
          return req.path === '/health' || req.path === '/metrics';
        },
      });
      this.app.use('/api', limiter);
    }

    // Logging middleware
    this.app.use(requestLogger);

    // Metrics middleware
    this.app.use(metricsMiddleware);

    // Validation middleware
    this.app.use(validationMiddleware);

    // Static files
    this.app.use('/public', express.static('public'));
  }

  /**
   * Setup API routes
   */
  private setupRoutes(): void {
    // Health check (before API prefix)
    this.app.use('/health', healthRoutes);
    this.app.use('/metrics', metricsRoutes);

    // API routes
    this.app.use('/api/v2/sudoku', sudokuRoutes);
    this.app.use('/api/v2/docs', docsRoutes);

    // Legacy API support (v1)
    this.app.use('/api', sudokuRoutes);

    // Root endpoint
    this.app.get('/', (req, res) => {
      const response: ApiResponse = {
        success: true,
        data: {
          name: 'Sudoku Solver API',
          version: '2.0.0',
          description: 'Enterprise-grade Sudoku API with advanced algorithms and monitoring',
          documentation: '/api/v2/docs',
          health: '/health',
          metrics: '/metrics',
          endpoints: {
            solve: 'POST /api/v2/sudoku/solve',
            generate: 'POST /api/v2/sudoku/generate',
            validate: 'POST /api/v2/sudoku/validate',
            hint: 'POST /api/v2/sudoku/hint',
            analyze: 'POST /api/v2/sudoku/analyze',
          },
        },
        timestamp: new Date().toISOString(),
        requestId: req.id,
      };
      res.json(response);
    });

    // 404 handler
    this.app.use('*', (req, res) => {
      const response: ApiResponse = {
        success: false,
        error: `Endpoint not found: ${req.method} ${req.originalUrl}`,
        timestamp: new Date().toISOString(),
        requestId: req.id,
      };
      res.status(404).json(response);
    });
  }

  /**
   * Setup error handling
   */
  private setupErrorHandling(): void {
    this.app.use(errorHandler);

    // Graceful shutdown handlers
    process.on('SIGTERM', this.gracefulShutdown.bind(this));
    process.on('SIGINT', this.gracefulShutdown.bind(this));

    // Unhandled promise rejection handler
    process.on('unhandledRejection', (reason, promise) => {
      log.error('Unhandled Promise Rejection', reason, { promise });
    });

    // Uncaught exception handler
    process.on('uncaughtException', (error) => {
      log.error('Uncaught Exception', error);
      process.exit(1);
    });
  }

  /**
   * Start the server
   */
  public async start(): Promise<void> {
    try {
      // Validate configuration
      validateConfig();

      // Start server
      const server = this.app.listen(this.config.port, () => {
        log.info('Server started successfully', {
          port: this.config.port,
          nodeEnv: this.config.nodeEnv,
          version: '2.0.0',
        });

        // Log startup information
        log.info('API Endpoints:', {
          health: `/health`,
          metrics: `/metrics`,
          docs: `/api/v2/docs`,
          solve: `POST /api/v2/sudoku/solve`,
          generate: `POST /api/v2/sudoku/generate`,
        });
      });

      // Store server reference for graceful shutdown
      this.server = server;

      // Handle server errors
      server.on('error', (error: NodeJS.ErrnoException) => {
        if (error.code === 'EADDRINUSE') {
          log.error(`Port ${this.config.port} is already in use`);
        } else {
          log.error('Server error', error);
        }
        process.exit(1);
      });

    } catch (error) {
      log.error('Failed to start server', error);
      process.exit(1);
    }
  }

  /**
   * Graceful shutdown
   */
  private async gracefulShutdown(signal: string): Promise<void> {
    log.info(`Received ${signal}, starting graceful shutdown`);

    if (this.server) {
      this.server.close((error) => {
        if (error) {
          log.error('Error during server shutdown', error);
          process.exit(1);
        } else {
          log.info('Server closed successfully');
          process.exit(0);
        }
      });

      // Force shutdown after 30 seconds
      setTimeout(() => {
        log.error('Forced shutdown after timeout');
        process.exit(1);
      }, 30000);
    } else {
      process.exit(0);
    }
  }

  /**
   * Get Express app instance (for testing)
   */
  public getApp(): express.Application {
    return this.app;
  }

  private server?: any;
}

// Extend Express Request interface
declare global {
  namespace Express {
    interface Request {
      id: string;
    }
  }
}

// Start server if this file is run directly
if (require.main === module) {
  const server = new SudokuApiServer();
  server.start().catch((error) => {
    console.error('Failed to start server:', error);
    process.exit(1);
  });
}

export default SudokuApiServer;
