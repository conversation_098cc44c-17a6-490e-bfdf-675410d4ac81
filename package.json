{"name": "portfolio-six", "version": "1.0.0", "description": "Front-end developer portfolio showcasing 6 projects with vanilla web technologies", "type": "module", "scripts": {"lint": "eslint projects/**/*.js main.js --ignore-pattern node_modules", "format": "prettier --write projects/**/*.{js,css,html} *.{md,html,js}", "serve": "npx serve .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "validate": "npm run lint && npm run test:ci"}, "keywords": ["portfolio", "vanilla-javascript", "html5", "css3", "pwa", "webrtc", "frontend"], "author": "Your Name", "license": "MIT", "engines": {"node": ">=20.0.0"}, "devDependencies": {"eslint": "^8.57.0", "eslint-plugin-compat": "^4.2.0", "prettier": "^3.2.5", "serve": "^14.2.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0", "babel-jest": "^29.7.0", "jsdom": "^23.0.0"}, "browserslist": ["defaults", "not IE 11"]}