/**
 * FinTrack Main Application
 * Coordinates all modules and handles PWA functionality
 */

import db from './js/database.js';
import charts from './js/charts.js';
import transactionManager from './js/transactions.js';
import goalManager from './js/goals.js';
import exportImportManager from './js/export.js';

class FinTrackApp {
  constructor() {
    this.isOnline = navigator.onLine;
    this.currentTheme = 'light';
    this.syncInProgress = false;
    this.updateTimeout = null;
    
    // Make modules globally available
    window.finTrackDB = db;
    window.finTrackCharts = charts;
    window.transactionManager = transactionManager;
    window.goalManager = goalManager;
    window.exportImportManager = exportImportManager;
    window.finTrackApp = this;
  }

  async init() {
    try {
      console.log('Initializing FinTrack...');
      
      // Initialize database first
      await db.init();
      
      // Setup PWA functionality
      await this.setupPWA();
      
      // Setup theme
      this.setupTheme();
      
      // Setup event listeners
      this.setupEventListeners();
      
      // Initialize modules
      await this.initializeModules();
      
      // Update dashboard
      await this.updateDashboard();
      
      // Setup offline/online detection
      this.setupNetworkDetection();
      
      // Handle URL parameters for shortcuts
      this.handleURLParameters();
      
      console.log('FinTrack initialized successfully');
      
    } catch (error) {
      console.error('Failed to initialize FinTrack:', error);
      this.showNotification('Failed to initialize application', 'error');
    }
  }

  async setupPWA() {
    // Register service worker
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register('./sw.js');
        console.log('Service Worker registered:', registration);
        
        // Listen for service worker updates
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing;
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              this.showUpdateAvailable();
            }
          });
        });
        
      } catch (error) {
        console.error('Service Worker registration failed:', error);
      }
    }

    // Handle app install prompt
    window.addEventListener('beforeinstallprompt', (e) => {
      e.preventDefault();
      this.deferredPrompt = e;
      this.showInstallPrompt();
    });

    // Handle app installed
    window.addEventListener('appinstalled', () => {
      console.log('FinTrack installed as PWA');
      this.showNotification('FinTrack installed successfully!', 'success');
    });
  }

  setupTheme() {
    const savedTheme = localStorage.getItem('fintrack-theme');
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    this.currentTheme = savedTheme || (systemPrefersDark ? 'dark' : 'light');
    
    this.setTheme(this.currentTheme);
    
    // Listen for system theme changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
      if (!localStorage.getItem('fintrack-theme')) {
        this.setTheme(e.matches ? 'dark' : 'light');
      }
    });
  }

  setTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('fintrack-theme', theme);
    this.currentTheme = theme;
    
    // Update theme toggle icon
    const themeIcon = document.querySelector('.theme-icon');
    if (themeIcon) {
      themeIcon.textContent = theme === 'dark' ? '☀️' : '🌙';
    }
    
    // Update charts theme
    if (window.finTrackCharts) {
      charts.updateChartsTheme(theme === 'dark');
    }
  }

  setupEventListeners() {
    // Theme toggle
    document.querySelector('.theme-toggle')?.addEventListener('click', () => {
      const newTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
      this.setTheme(newTheme);
    });

    // Sync button
    document.querySelector('.sync-btn')?.addEventListener('click', () => {
      this.performSync();
    });

    // Quick action buttons
    document.getElementById('addIncomeBtn')?.addEventListener('click', () => {
      transactionManager.showAddIncomeModal();
    });

    document.getElementById('addExpenseBtn')?.addEventListener('click', () => {
      transactionManager.showAddExpenseModal();
    });

    document.getElementById('addTransferBtn')?.addEventListener('click', () => {
      transactionManager.showAddTransferModal();
    });

    document.getElementById('addGoalBtn')?.addEventListener('click', () => {
      goalManager.showGoalModal();
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      this.handleKeyboardShortcuts(e);
    });

    // Window resize for charts
    window.addEventListener('resize', () => {
      if (window.finTrackCharts) {
        charts.handleResize();
      }
    });

    // Handle service worker messages
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('message', (event) => {
        this.handleServiceWorkerMessage(event);
      });
    }
  }

  async initializeModules() {
    // Initialize all modules
    await Promise.all([
      charts.initCharts(),
      transactionManager.init(),
      goalManager.init(),
      exportImportManager.init()
    ]);
  }

  async updateDashboard() {
    try {
      const summary = await db.getTransactionSummary('month');

      // Update stat cards
      this.updateStatCard('currentBalance', summary.netIncome, 'balance');
      this.updateStatCard('monthlyIncome', summary.totalIncome, 'income');
      this.updateStatCard('monthlyExpenses', summary.totalExpenses, 'expenses');
      this.updateStatCard('savingsRate', summary.savingsRate, 'savings', '%');

      // Update change indicators (mock data for now)
      this.updateChangeIndicator('balanceChange', 15.5, 'positive');
      this.updateChangeIndicator('incomeChange', 8.2, 'positive');
      this.updateChangeIndicator('expensesChange', -3.1, 'negative');

    } catch (error) {
      console.error('Failed to update dashboard:', error);
    }
  }

  // Debounced chart update to prevent infinite loops
  debouncedUpdateCharts() {
    if (this.updateTimeout) {
      clearTimeout(this.updateTimeout);
    }

    this.updateTimeout = setTimeout(async () => {
      if (window.finTrackCharts && window.finTrackCharts.updateCharts) {
        await window.finTrackCharts.updateCharts();
      }
    }, 500); // Wait 500ms before updating
  }

  updateStatCard(elementId, value, type, suffix = '') {
    const element = document.getElementById(elementId);
    if (!element) return;

    let formattedValue;
    if (type === 'savings') {
      formattedValue = `${Math.round(value)}${suffix}`;
    } else {
      formattedValue = `$${Math.abs(value).toFixed(2)}${suffix}`;
    }

    element.textContent = formattedValue;
  }

  updateChangeIndicator(elementId, changePercent, direction) {
    const element = document.getElementById(elementId);
    if (!element) return;

    const sign = direction === 'positive' ? '+' : '';
    element.textContent = `${sign}${changePercent}% vs last month`;
    element.className = `stat-change ${direction}`;
  }

  setupNetworkDetection() {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.hideOfflineIndicator();
      this.performSync();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
      this.showOfflineIndicator();
    });

    // Initial check
    if (!this.isOnline) {
      this.showOfflineIndicator();
    }
  }

  showOfflineIndicator() {
    const indicator = document.getElementById('offlineIndicator');
    if (indicator) {
      indicator.style.display = 'flex';
    }
  }

  hideOfflineIndicator() {
    const indicator = document.getElementById('offlineIndicator');
    if (indicator) {
      indicator.style.display = 'none';
    }
  }

  async performSync() {
    if (this.syncInProgress || !this.isOnline) return;

    this.syncInProgress = true;
    const syncBtn = document.querySelector('.sync-btn');
    
    if (syncBtn) {
      syncBtn.classList.add('syncing');
    }

    try {
      // Register background sync if supported
      if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
        const registration = await navigator.serviceWorker.ready;
        await registration.sync.register('sync-transactions');
        await registration.sync.register('sync-goals');
      }

      // Simulate sync delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      this.showNotification('Data synced successfully', 'success');
      
    } catch (error) {
      console.error('Sync failed:', error);
      this.showNotification('Sync failed', 'error');
    } finally {
      this.syncInProgress = false;
      if (syncBtn) {
        syncBtn.classList.remove('syncing');
      }
    }
  }

  handleURLParameters() {
    const urlParams = new URLSearchParams(window.location.search);
    const action = urlParams.get('action');
    const view = urlParams.get('view');

    // Handle shortcut actions
    switch (action) {
      case 'add-expense':
        setTimeout(() => transactionManager.showAddExpenseModal(), 500);
        break;
      case 'add-income':
        setTimeout(() => transactionManager.showAddIncomeModal(), 500);
        break;
    }

    // Handle view parameters
    switch (view) {
      case 'reports':
        // Scroll to charts section
        setTimeout(() => {
          document.querySelector('.charts-section')?.scrollIntoView({ behavior: 'smooth' });
        }, 500);
        break;
    }
  }

  handleKeyboardShortcuts(e) {
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 'i':
          e.preventDefault();
          transactionManager.showAddIncomeModal();
          break;
        case 'e':
          e.preventDefault();
          transactionManager.showAddExpenseModal();
          break;
        case 'g':
          e.preventDefault();
          goalManager.showGoalModal();
          break;
        case 's':
          e.preventDefault();
          this.performSync();
          break;
      }
    }

    // Escape key to close modals
    if (e.key === 'Escape') {
      const modals = document.querySelectorAll('.modal[style*="flex"]');
      modals.forEach(modal => {
        modal.style.display = 'none';
      });
    }
  }

  handleServiceWorkerMessage(event) {
    const { type, data } = event.data;

    switch (type) {
      case 'SYNC_COMPLETE':
        this.showNotification(`${data.type} synced: ${data.count} items`, 'success');
        break;
      case 'UPDATE_AVAILABLE':
        this.showUpdateAvailable();
        break;
    }
  }

  showUpdateAvailable() {
    const notification = document.createElement('div');
    notification.className = 'update-notification';
    notification.innerHTML = `
      <div class="update-content">
        <span>🔄 App update available!</span>
        <button class="btn btn-small" onclick="window.location.reload()">Update Now</button>
      </div>
    `;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: var(--color-info);
      color: white;
      padding: 12px 24px;
      border-radius: 8px;
      z-index: 4000;
      display: flex;
      align-items: center;
      gap: 16px;
    `;
    
    document.body.appendChild(notification);
  }

  showInstallPrompt() {
    if (!this.deferredPrompt) return;

    const notification = document.createElement('div');
    notification.className = 'install-notification';
    notification.innerHTML = `
      <div class="install-content">
        <span>📱 Install FinTrack for quick access!</span>
        <button class="btn btn-small install-btn">Install</button>
        <button class="btn btn-small btn-secondary dismiss-btn">Dismiss</button>
      </div>
    `;
    notification.style.cssText = `
      position: fixed;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: var(--color-primary);
      color: white;
      padding: 16px 24px;
      border-radius: 12px;
      z-index: 4000;
      display: flex;
      align-items: center;
      gap: 16px;
      box-shadow: var(--shadow-lg);
    `;
    
    document.body.appendChild(notification);

    // Handle install button
    notification.querySelector('.install-btn').addEventListener('click', async () => {
      this.deferredPrompt.prompt();
      const { outcome } = await this.deferredPrompt.userChoice;
      
      if (outcome === 'accepted') {
        console.log('User accepted the install prompt');
      }
      
      this.deferredPrompt = null;
      notification.remove();
    });

    // Handle dismiss button
    notification.querySelector('.dismiss-btn').addEventListener('click', () => {
      notification.remove();
    });

    // Auto-dismiss after 10 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove();
      }
    }, 10000);
  }

  showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 24px;
      border-radius: 8px;
      color: white;
      font-weight: 500;
      z-index: 4000;
      animation: slideIn 0.3s ease-out;
      background-color: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
      notification.style.animation = 'slideOut 0.3s ease-in';
      setTimeout(() => notification.remove(), 300);
    }, 3000);
  }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  const app = new FinTrackApp();
  app.init();
});

// Add notification animations
const notificationStyles = document.createElement('style');
notificationStyles.textContent = `
  @keyframes slideIn {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  @keyframes slideOut {
    from {
      transform: translateX(0);
      opacity: 1;
    }
    to {
      transform: translateX(100%);
      opacity: 0;
    }
  }
  
  .update-content,
  .install-content {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  .update-content .btn,
  .install-content .btn {
    white-space: nowrap;
  }
`;
document.head.appendChild(notificationStyles);
