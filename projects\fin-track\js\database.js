/**
 * FinTrack Database Module
 * Handles IndexedDB operations for offline data storage
 */

class FinTrackDB {
  constructor() {
    this.dbName = 'FinTrackDB';
    this.version = 1;
    this.db = null;
  }

  async init() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);

      request.onerror = () => {
        console.error('Database failed to open');
        reject(request.error);
      };

      request.onsuccess = () => {
        this.db = request.result;
        console.log('Database opened successfully');
        resolve(this.db);
      };

      request.onupgradeneeded = (event) => {
        this.db = event.target.result;
        console.log('Database upgrade needed');

        // Create transactions store
        if (!this.db.objectStoreNames.contains('transactions')) {
          const transactionStore = this.db.createObjectStore('transactions', {
            keyPath: 'id',
            autoIncrement: true
          });

          // Create indexes for efficient querying
          transactionStore.createIndex('date', 'date', { unique: false });
          transactionStore.createIndex('category', 'category', { unique: false });
          transactionStore.createIndex('type', 'type', { unique: false });
          transactionStore.createIndex('account', 'account', { unique: false });
          transactionStore.createIndex('synced', 'synced', { unique: false });
        }

        // Create goals store
        if (!this.db.objectStoreNames.contains('goals')) {
          const goalStore = this.db.createObjectStore('goals', {
            keyPath: 'id',
            autoIncrement: true
          });

          goalStore.createIndex('category', 'category', { unique: false });
          goalStore.createIndex('deadline', 'deadline', { unique: false });
          goalStore.createIndex('active', 'active', { unique: false });
          goalStore.createIndex('synced', 'synced', { unique: false });
        }

        // Create accounts store
        if (!this.db.objectStoreNames.contains('accounts')) {
          const accountStore = this.db.createObjectStore('accounts', {
            keyPath: 'id',
            autoIncrement: true
          });

          accountStore.createIndex('name', 'name', { unique: true });
          accountStore.createIndex('type', 'type', { unique: false });
        }

        // Create categories store
        if (!this.db.objectStoreNames.contains('categories')) {
          const categoryStore = this.db.createObjectStore('categories', {
            keyPath: 'id',
            autoIncrement: true
          });

          categoryStore.createIndex('name', 'name', { unique: true });
          categoryStore.createIndex('type', 'type', { unique: false });
        }

        // Create settings store
        if (!this.db.objectStoreNames.contains('settings')) {
          const settingsStore = this.db.createObjectStore('settings', {
            keyPath: 'key'
          });
        }

        console.log('Database setup complete');
      };
    });
  }

  // Transaction operations
  async addTransaction(transaction) {
    const tx = this.db.transaction(['transactions'], 'readwrite');
    const store = tx.objectStore('transactions');
    
    const transactionData = {
      ...transaction,
      id: transaction.id || Date.now() + Math.random(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      synced: false
    };

    return new Promise((resolve, reject) => {
      const request = store.add(transactionData);
      
      request.onsuccess = () => {
        console.log('Transaction added:', transactionData.id);
        resolve(transactionData);
      };
      
      request.onerror = () => {
        console.error('Failed to add transaction:', request.error);
        reject(request.error);
      };
    });
  }

  async getTransactions(filters = {}) {
    const tx = this.db.transaction(['transactions'], 'readonly');
    const store = tx.objectStore('transactions');
    
    return new Promise((resolve, reject) => {
      const request = store.getAll();
      
      request.onsuccess = () => {
        let transactions = request.result;
        
        // Apply filters
        if (filters.category && filters.category !== 'all') {
          transactions = transactions.filter(t => t.category === filters.category);
        }
        
        if (filters.type && filters.type !== 'all') {
          transactions = transactions.filter(t => t.type === filters.type);
        }
        
        if (filters.account && filters.account !== 'all') {
          transactions = transactions.filter(t => t.account === filters.account);
        }
        
        if (filters.dateFrom) {
          transactions = transactions.filter(t => new Date(t.date) >= new Date(filters.dateFrom));
        }
        
        if (filters.dateTo) {
          transactions = transactions.filter(t => new Date(t.date) <= new Date(filters.dateTo));
        }
        
        // Sort by date (newest first)
        transactions.sort((a, b) => new Date(b.date) - new Date(a.date));
        
        resolve(transactions);
      };
      
      request.onerror = () => {
        console.error('Failed to get transactions:', request.error);
        reject(request.error);
      };
    });
  }

  async updateTransaction(id, updates) {
    const tx = this.db.transaction(['transactions'], 'readwrite');
    const store = tx.objectStore('transactions');
    
    return new Promise((resolve, reject) => {
      const getRequest = store.get(id);
      
      getRequest.onsuccess = () => {
        const transaction = getRequest.result;
        if (!transaction) {
          reject(new Error('Transaction not found'));
          return;
        }
        
        const updatedTransaction = {
          ...transaction,
          ...updates,
          updatedAt: new Date().toISOString(),
          synced: false
        };
        
        const putRequest = store.put(updatedTransaction);
        
        putRequest.onsuccess = () => {
          console.log('Transaction updated:', id);
          resolve(updatedTransaction);
        };
        
        putRequest.onerror = () => {
          console.error('Failed to update transaction:', putRequest.error);
          reject(putRequest.error);
        };
      };
      
      getRequest.onerror = () => {
        console.error('Failed to get transaction for update:', getRequest.error);
        reject(getRequest.error);
      };
    });
  }

  async deleteTransaction(id) {
    const tx = this.db.transaction(['transactions'], 'readwrite');
    const store = tx.objectStore('transactions');
    
    return new Promise((resolve, reject) => {
      const request = store.delete(id);
      
      request.onsuccess = () => {
        console.log('Transaction deleted:', id);
        resolve(true);
      };
      
      request.onerror = () => {
        console.error('Failed to delete transaction:', request.error);
        reject(request.error);
      };
    });
  }

  // Goal operations
  async addGoal(goal) {
    const tx = this.db.transaction(['goals'], 'readwrite');
    const store = tx.objectStore('goals');
    
    const goalData = {
      ...goal,
      id: goal.id || Date.now() + Math.random(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      active: true,
      synced: false
    };

    return new Promise((resolve, reject) => {
      const request = store.add(goalData);
      
      request.onsuccess = () => {
        console.log('Goal added:', goalData.id);
        resolve(goalData);
      };
      
      request.onerror = () => {
        console.error('Failed to add goal:', request.error);
        reject(request.error);
      };
    });
  }

  async getGoals(activeOnly = true) {
    const tx = this.db.transaction(['goals'], 'readonly');
    const store = tx.objectStore('goals');
    
    return new Promise((resolve, reject) => {
      const request = store.getAll();
      
      request.onsuccess = () => {
        let goals = request.result;
        
        if (activeOnly) {
          goals = goals.filter(g => g.active);
        }
        
        // Sort by deadline (nearest first)
        goals.sort((a, b) => {
          if (!a.deadline && !b.deadline) return 0;
          if (!a.deadline) return 1;
          if (!b.deadline) return -1;
          return new Date(a.deadline) - new Date(b.deadline);
        });
        
        resolve(goals);
      };
      
      request.onerror = () => {
        console.error('Failed to get goals:', request.error);
        reject(request.error);
      };
    });
  }

  async updateGoal(id, updates) {
    const tx = this.db.transaction(['goals'], 'readwrite');
    const store = tx.objectStore('goals');
    
    return new Promise((resolve, reject) => {
      const getRequest = store.get(id);
      
      getRequest.onsuccess = () => {
        const goal = getRequest.result;
        if (!goal) {
          reject(new Error('Goal not found'));
          return;
        }
        
        const updatedGoal = {
          ...goal,
          ...updates,
          updatedAt: new Date().toISOString(),
          synced: false
        };
        
        const putRequest = store.put(updatedGoal);
        
        putRequest.onsuccess = () => {
          console.log('Goal updated:', id);
          resolve(updatedGoal);
        };
        
        putRequest.onerror = () => {
          console.error('Failed to update goal:', putRequest.error);
          reject(putRequest.error);
        };
      };
      
      getRequest.onerror = () => {
        console.error('Failed to get goal for update:', getRequest.error);
        reject(getRequest.error);
      };
    });
  }

  async deleteGoal(id) {
    const tx = this.db.transaction(['goals'], 'readwrite');
    const store = tx.objectStore('goals');
    
    return new Promise((resolve, reject) => {
      const request = store.delete(id);
      
      request.onsuccess = () => {
        console.log('Goal deleted:', id);
        resolve(true);
      };
      
      request.onerror = () => {
        console.error('Failed to delete goal:', request.error);
        reject(request.error);
      };
    });
  }

  // Analytics and reporting
  async getTransactionSummary(period = 'month') {
    const transactions = await this.getTransactions();
    const now = new Date();
    let startDate;

    switch (period) {
      case 'today':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        startDate = new Date(0);
    }

    const filteredTransactions = transactions.filter(t => 
      new Date(t.date) >= startDate
    );

    const summary = {
      totalIncome: 0,
      totalExpenses: 0,
      totalTransfers: 0,
      transactionCount: filteredTransactions.length,
      categoryBreakdown: {},
      accountBreakdown: {}
    };

    filteredTransactions.forEach(transaction => {
      const amount = parseFloat(transaction.amount);
      
      switch (transaction.type) {
        case 'income':
          summary.totalIncome += amount;
          break;
        case 'expense':
          summary.totalExpenses += amount;
          break;
        case 'transfer':
          summary.totalTransfers += amount;
          break;
      }

      // Category breakdown
      if (!summary.categoryBreakdown[transaction.category]) {
        summary.categoryBreakdown[transaction.category] = 0;
      }
      summary.categoryBreakdown[transaction.category] += amount;

      // Account breakdown
      if (!summary.accountBreakdown[transaction.account]) {
        summary.accountBreakdown[transaction.account] = 0;
      }
      summary.accountBreakdown[transaction.account] += amount;
    });

    summary.netIncome = summary.totalIncome - summary.totalExpenses;
    summary.savingsRate = summary.totalIncome > 0 
      ? (summary.netIncome / summary.totalIncome) * 100 
      : 0;

    return summary;
  }

  // Settings operations
  async getSetting(key, defaultValue = null) {
    const tx = this.db.transaction(['settings'], 'readonly');
    const store = tx.objectStore('settings');
    
    return new Promise((resolve, reject) => {
      const request = store.get(key);
      
      request.onsuccess = () => {
        const result = request.result;
        resolve(result ? result.value : defaultValue);
      };
      
      request.onerror = () => {
        console.error('Failed to get setting:', request.error);
        reject(request.error);
      };
    });
  }

  async setSetting(key, value) {
    const tx = this.db.transaction(['settings'], 'readwrite');
    const store = tx.objectStore('settings');
    
    return new Promise((resolve, reject) => {
      const request = store.put({ key, value });
      
      request.onsuccess = () => {
        console.log('Setting saved:', key);
        resolve(true);
      };
      
      request.onerror = () => {
        console.error('Failed to save setting:', request.error);
        reject(request.error);
      };
    });
  }

  // Utility methods
  async clearAllData() {
    const stores = ['transactions', 'goals', 'accounts', 'categories', 'settings'];
    const tx = this.db.transaction(stores, 'readwrite');
    
    const promises = stores.map(storeName => {
      return new Promise((resolve, reject) => {
        const store = tx.objectStore(storeName);
        const request = store.clear();
        
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });
    });

    return Promise.all(promises);
  }

  async exportData() {
    const [transactions, goals] = await Promise.all([
      this.getTransactions(),
      this.getGoals(false)
    ]);

    return {
      transactions,
      goals,
      exportDate: new Date().toISOString(),
      version: this.version
    };
  }

  async importData(data) {
    // Clear existing data
    await this.clearAllData();

    // Import transactions
    if (data.transactions) {
      for (const transaction of data.transactions) {
        await this.addTransaction(transaction);
      }
    }

    // Import goals
    if (data.goals) {
      for (const goal of data.goals) {
        await this.addGoal(goal);
      }
    }

    console.log('Data import completed');
  }
}

// Create and export singleton instance
const db = new FinTrackDB();

export default db;
