/**
 * Typing Speed Test - Main Game Logic
 * Real-time typing test with comprehensive analytics
 */

class TypingTest {
  constructor() {
    this.currentText = '';
    this.userInput = '';
    this.currentIndex = 0;
    this.startTime = null;
    this.endTime = null;
    this.isActive = false;
    this.isPaused = false;
    this.duration = 30; // seconds
    this.timeLeft = 30;
    this.timerInterval = null;
    this.updateInterval = null;
    
    // Statistics
    this.correctChars = 0;
    this.incorrectChars = 0;
    this.totalChars = 0;
    this.wordsCompleted = 0;
    this.mistakes = new Map(); // character -> count
    this.wpmHistory = [];
    this.accuracyHistory = [];
    
    // Settings
    this.settings = {
      soundEffects: true,
      showLiveWPM: true,
      highlightErrors: true,
      darkTheme: false,
      fontSize: 'medium',
      caretStyle: 'line'
    };
    
    // Configuration
    this.testDuration = 30;
    this.textType = 'quotes';
    this.difficulty = 'medium';
    
    this.init();
  }

  init() {
    this.loadSettings();
    this.setupEventListeners();
    this.applySettings();
    this.generateNewText();
    this.updateDisplay();
  }

  setupEventListeners() {
    // Control buttons
    document.getElementById('startBtn').addEventListener('click', () => this.startTest());
    document.getElementById('resetBtn').addEventListener('click', () => this.resetTest());
    document.getElementById('pauseBtn').addEventListener('click', () => this.togglePause());
    
    // Configuration
    document.getElementById('testDuration').addEventListener('change', (e) => {
      this.testDuration = parseInt(e.target.value);
      this.duration = this.testDuration;
      this.timeLeft = this.testDuration;
      this.updateDisplay();
    });
    
    document.getElementById('textType').addEventListener('change', (e) => {
      this.textType = e.target.value;
      this.generateNewText();
    });
    
    document.getElementById('difficulty').addEventListener('change', (e) => {
      this.difficulty = e.target.value;
      this.generateNewText();
    });
    
    // Typing input
    const typingInput = document.getElementById('typingInput');
    typingInput.addEventListener('input', (e) => this.handleInput(e));
    typingInput.addEventListener('keydown', (e) => this.handleKeydown(e));
    typingInput.addEventListener('paste', (e) => e.preventDefault());
    
    // Modal controls
    document.getElementById('statsBtn').addEventListener('click', () => this.showModal('statsModal'));
    document.getElementById('settingsBtn').addEventListener('click', () => this.showModal('settingsModal'));
    
    // Close modal buttons
    document.querySelectorAll('.close-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const modalId = e.target.closest('.modal').id;
        this.hideModal(modalId);
      });
    });
    
    // Modal backdrop clicks
    document.querySelectorAll('.modal').forEach(modal => {
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          this.hideModal(modal.id);
        }
      });
    });
    
    // Results modal buttons
    document.getElementById('retryBtn').addEventListener('click', () => {
      this.hideModal('resultsModal');
      this.resetTest();
      this.startTest();
    });
    
    document.getElementById('shareResultsBtn').addEventListener('click', () => this.shareResults());
    document.getElementById('saveResultsBtn').addEventListener('click', () => this.saveResults());
    
    // Settings
    this.setupSettingsListeners();
    
    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => this.handleGlobalKeydown(e));
    
    // Prevent context menu on typing area
    document.getElementById('textDisplay').addEventListener('contextmenu', (e) => e.preventDefault());
  }

  setupSettingsListeners() {
    document.getElementById('soundEffects').addEventListener('change', (e) => {
      this.settings.soundEffects = e.target.checked;
      this.saveSettings();
    });
    
    document.getElementById('showLiveWPM').addEventListener('change', (e) => {
      this.settings.showLiveWPM = e.target.checked;
      this.saveSettings();
    });
    
    document.getElementById('highlightErrors').addEventListener('change', (e) => {
      this.settings.highlightErrors = e.target.checked;
      this.saveSettings();
      this.updateTextDisplay();
    });
    
    document.getElementById('darkTheme').addEventListener('change', (e) => {
      this.settings.darkTheme = e.target.checked;
      this.saveSettings();
      this.applyTheme();
    });
    
    document.getElementById('fontSize').addEventListener('change', (e) => {
      this.settings.fontSize = e.target.value;
      this.saveSettings();
      this.applyFontSize();
    });
    
    document.getElementById('caretStyle').addEventListener('change', (e) => {
      this.settings.caretStyle = e.target.value;
      this.saveSettings();
      this.applyCaret();
    });
  }

  generateNewText() {
    this.currentText = generateText(this.textType, this.difficulty, 300);
    this.resetStats();
    this.updateTextDisplay();
  }

  startTest() {
    if (this.isActive && !this.isPaused) return;
    
    if (this.isPaused) {
      this.resumeTest();
      return;
    }
    
    this.isActive = true;
    this.isPaused = false;
    this.startTime = Date.now();
    this.timeLeft = this.duration;
    
    // Enable input
    const typingInput = document.getElementById('typingInput');
    typingInput.disabled = false;
    typingInput.focus();
    typingInput.value = '';
    
    // Update buttons
    document.getElementById('startBtn').disabled = true;
    document.getElementById('resetBtn').disabled = false;
    document.getElementById('pauseBtn').disabled = false;
    document.getElementById('pauseBtn').textContent = 'Pause';
    
    // Start timers
    this.startTimer();
    this.startUpdateLoop();
    
    this.showToast('Test started! Start typing...');
  }

  pauseTest() {
    if (!this.isActive || this.isPaused) return;
    
    this.isPaused = true;
    this.stopTimer();
    this.stopUpdateLoop();
    
    // Disable input
    document.getElementById('typingInput').disabled = true;
    document.getElementById('pauseBtn').textContent = 'Resume';
    
    this.showToast('Test paused');
  }

  resumeTest() {
    if (!this.isActive || !this.isPaused) return;
    
    this.isPaused = false;
    
    // Enable input
    const typingInput = document.getElementById('typingInput');
    typingInput.disabled = false;
    typingInput.focus();
    
    document.getElementById('pauseBtn').textContent = 'Pause';
    
    // Resume timers
    this.startTimer();
    this.startUpdateLoop();
    
    this.showToast('Test resumed');
  }

  togglePause() {
    if (this.isPaused) {
      this.resumeTest();
    } else {
      this.pauseTest();
    }
  }

  resetTest() {
    this.isActive = false;
    this.isPaused = false;
    this.currentIndex = 0;
    this.userInput = '';
    this.timeLeft = this.duration;
    
    this.stopTimer();
    this.stopUpdateLoop();
    this.resetStats();
    
    // Reset input
    const typingInput = document.getElementById('typingInput');
    typingInput.value = '';
    typingInput.disabled = true;
    
    // Reset buttons
    document.getElementById('startBtn').disabled = false;
    document.getElementById('resetBtn').disabled = true;
    document.getElementById('pauseBtn').disabled = true;
    document.getElementById('pauseBtn').textContent = 'Pause';
    
    this.generateNewText();
    this.updateDisplay();
    
    this.showToast('Test reset');
  }

  endTest() {
    this.isActive = false;
    this.isPaused = false;
    this.endTime = Date.now();
    
    this.stopTimer();
    this.stopUpdateLoop();
    
    // Disable input
    document.getElementById('typingInput').disabled = true;
    
    // Reset buttons
    document.getElementById('startBtn').disabled = false;
    document.getElementById('resetBtn').disabled = false;
    document.getElementById('pauseBtn').disabled = true;
    
    this.calculateFinalStats();
    this.saveTestResult();
    this.showResults();
  }

  handleInput(e) {
    if (!this.isActive || this.isPaused) return;
    
    this.userInput = e.target.value;
    this.processInput();
    this.updateTextDisplay();
    this.updateStats();
  }

  handleKeydown(e) {
    if (!this.isActive || this.isPaused) return;
    
    // Play sound effect
    if (this.settings.soundEffects) {
      this.playKeystrokeSound();
    }
    
    // Handle special keys
    if (e.key === 'Tab') {
      e.preventDefault();
      return;
    }
  }

  handleGlobalKeydown(e) {
    // Prevent shortcuts when typing
    if (e.target.id === 'typingInput') return;
    
    switch (e.key) {
      case 'Escape':
        // Close any open modal
        document.querySelectorAll('.modal.show').forEach(modal => {
          this.hideModal(modal.id);
        });
        break;
      case ' ':
        if (e.ctrlKey) {
          e.preventDefault();
          if (!this.isActive) {
            this.startTest();
          } else {
            this.togglePause();
          }
        }
        break;
      case 'r':
        if (e.ctrlKey) {
          e.preventDefault();
          this.resetTest();
        }
        break;
    }
  }

  processInput() {
    this.currentIndex = this.userInput.length;
    this.totalChars = this.currentIndex;
    
    // Count correct and incorrect characters
    this.correctChars = 0;
    this.incorrectChars = 0;
    
    for (let i = 0; i < this.currentIndex; i++) {
      const expectedChar = this.currentText[i];
      const typedChar = this.userInput[i];
      
      if (typedChar === expectedChar) {
        this.correctChars++;
      } else {
        this.incorrectChars++;
        
        // Track mistakes
        if (this.mistakes.has(expectedChar)) {
          this.mistakes.set(expectedChar, this.mistakes.get(expectedChar) + 1);
        } else {
          this.mistakes.set(expectedChar, 1);
        }
        
        // Play error sound
        if (this.settings.soundEffects) {
          this.playErrorSound();
        }
      }
    }
    
    // Count completed words
    this.wordsCompleted = this.userInput.trim().split(/\s+/).length - 1;
    if (this.currentIndex > 0 && this.currentText[this.currentIndex - 1] === ' ') {
      this.wordsCompleted++;
    }
    
    // Check if test is complete
    if (this.currentIndex >= this.currentText.length) {
      this.endTest();
    }
  }

  updateTextDisplay() {
    const textContent = document.getElementById('textContent');
    let html = '';
    
    for (let i = 0; i < this.currentText.length; i++) {
      const char = this.currentText[i];
      let className = 'char';
      
      if (i < this.currentIndex) {
        // Already typed
        if (this.userInput[i] === char) {
          className += ' correct';
        } else if (this.settings.highlightErrors) {
          className += ' incorrect';
        }
      } else if (i === this.currentIndex) {
        // Current character
        className += ' current';
      } else {
        // Not yet typed
        className += ' pending';
      }
      
      html += `<span class="${className}">${char === ' ' ? '&nbsp;' : char}</span>`;
    }
    
    textContent.innerHTML = html;
    
    // Scroll to current position
    const currentChar = textContent.querySelector('.current');
    if (currentChar) {
      currentChar.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }

  updateStats() {
    if (!this.isActive || this.isPaused) return;
    
    const elapsedTime = (Date.now() - this.startTime) / 1000 / 60; // minutes
    const wpm = elapsedTime > 0 ? Math.round((this.correctChars / 5) / elapsedTime) : 0;
    const accuracy = this.totalChars > 0 ? Math.round((this.correctChars / this.totalChars) * 100) : 100;
    const progress = Math.round((this.currentIndex / this.currentText.length) * 100);
    
    // Update display
    if (this.settings.showLiveWPM) {
      document.getElementById('wpmValue').textContent = wpm;
    }
    document.getElementById('accuracyValue').textContent = accuracy + '%';
    document.getElementById('progressValue').textContent = progress + '%';
    
    // Update progress bar
    document.getElementById('progressFill').style.width = progress + '%';
    
    // Store history for charts
    this.wpmHistory.push({ time: elapsedTime * 60, wpm });
    this.accuracyHistory.push({ time: elapsedTime * 60, accuracy });
  }

  updateDisplay() {
    document.getElementById('timeValue').textContent = this.timeLeft;
    document.getElementById('wpmValue').textContent = '0';
    document.getElementById('accuracyValue').textContent = '100%';
    document.getElementById('progressValue').textContent = '0%';
    document.getElementById('progressFill').style.width = '0%';
  }

  startTimer() {
    this.timerInterval = setInterval(() => {
      this.timeLeft--;
      document.getElementById('timeValue').textContent = this.timeLeft;
      
      if (this.timeLeft <= 0) {
        this.endTest();
      }
    }, 1000);
  }

  stopTimer() {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
      this.timerInterval = null;
    }
  }

  startUpdateLoop() {
    this.updateInterval = setInterval(() => {
      this.updateStats();
    }, 100);
  }

  stopUpdateLoop() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
  }

  resetStats() {
    this.correctChars = 0;
    this.incorrectChars = 0;
    this.totalChars = 0;
    this.wordsCompleted = 0;
    this.mistakes.clear();
    this.wpmHistory = [];
    this.accuracyHistory = [];
    this.currentIndex = 0;
    this.userInput = '';
  }

  calculateFinalStats() {
    const testDuration = this.duration; // seconds
    const elapsedTime = testDuration / 60; // minutes
    
    this.finalStats = {
      wpm: Math.round((this.correctChars / 5) / elapsedTime),
      rawWPM: Math.round((this.totalChars / 5) / elapsedTime),
      accuracy: this.totalChars > 0 ? Math.round((this.correctChars / this.totalChars) * 100) : 100,
      correctChars: this.correctChars,
      incorrectChars: this.incorrectChars,
      totalChars: this.totalChars,
      wordsCompleted: this.wordsCompleted,
      testDuration: testDuration,
      mistakes: Array.from(this.mistakes.entries()).sort((a, b) => b[1] - a[1])
    };
  }

  showResults() {
    // Update results modal
    document.getElementById('finalWPM').textContent = this.finalStats.wpm;
    document.getElementById('finalAccuracy').textContent = this.finalStats.accuracy + '%';
    document.getElementById('rawWPM').textContent = this.finalStats.rawWPM;
    document.getElementById('charactersTyped').textContent = this.finalStats.totalChars;
    document.getElementById('correctChars').textContent = this.finalStats.correctChars;
    document.getElementById('incorrectChars').textContent = this.finalStats.incorrectChars;
    document.getElementById('wordsCompleted').textContent = this.finalStats.wordsCompleted;
    document.getElementById('testDurationResult').textContent = this.finalStats.testDuration + 's';
    
    // Show mistake analysis
    this.showMistakeAnalysis();
    
    // Draw performance chart
    this.drawPerformanceChart();
    
    // Show modal
    this.showModal('resultsModal');
  }

  showMistakeAnalysis() {
    const mistakeList = document.getElementById('mistakeList');
    mistakeList.innerHTML = '';
    
    if (this.finalStats.mistakes.length === 0) {
      mistakeList.innerHTML = '<p>No mistakes! Perfect typing!</p>';
      return;
    }
    
    this.finalStats.mistakes.slice(0, 10).forEach(([char, count]) => {
      const mistakeItem = document.createElement('div');
      mistakeItem.className = 'mistake-item';
      mistakeItem.innerHTML = `
        <div class="mistake-char">'${char === ' ' ? 'SPACE' : char}'</div>
        <div class="mistake-count">${count} mistake${count > 1 ? 's' : ''}</div>
      `;
      mistakeList.appendChild(mistakeItem);
    });
  }

  drawPerformanceChart() {
    const canvas = document.getElementById('performanceChart');
    const ctx = canvas.getContext('2d');
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    if (this.wpmHistory.length < 2) return;
    
    // Set up chart dimensions
    const padding = 40;
    const chartWidth = canvas.width - 2 * padding;
    const chartHeight = canvas.height - 2 * padding;
    
    // Find data ranges
    const maxWPM = Math.max(...this.wpmHistory.map(d => d.wpm));
    const maxTime = Math.max(...this.wpmHistory.map(d => d.time));
    
    // Draw axes
    ctx.strokeStyle = '#666';
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.moveTo(padding, padding);
    ctx.lineTo(padding, canvas.height - padding);
    ctx.lineTo(canvas.width - padding, canvas.height - padding);
    ctx.stroke();
    
    // Draw WPM line
    ctx.strokeStyle = '#1976d2';
    ctx.lineWidth = 2;
    ctx.beginPath();
    
    this.wpmHistory.forEach((point, index) => {
      const x = padding + (point.time / maxTime) * chartWidth;
      const y = canvas.height - padding - (point.wpm / maxWPM) * chartHeight;
      
      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });
    
    ctx.stroke();
    
    // Add labels
    ctx.fillStyle = '#666';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('Time', canvas.width / 2, canvas.height - 10);
    
    ctx.save();
    ctx.translate(15, canvas.height / 2);
    ctx.rotate(-Math.PI / 2);
    ctx.fillText('WPM', 0, 0);
    ctx.restore();
  }

  shareResults() {
    const shareText = `🚀 Typing Test Results 🚀\n\n⚡ WPM: ${this.finalStats.wpm}\n🎯 Accuracy: ${this.finalStats.accuracy}%\n📝 Characters: ${this.finalStats.totalChars}\n⏱️ Duration: ${this.finalStats.testDuration}s\n\nTest your typing speed: ${window.location.href}`;
    
    if (navigator.share) {
      navigator.share({
        title: 'Typing Speed Test Results',
        text: shareText
      });
    } else {
      navigator.clipboard.writeText(shareText).then(() => {
        this.showToast('Results copied to clipboard!');
      }).catch(() => {
        this.showToast('Unable to copy results');
      });
    }
  }

  saveResults() {
    const results = {
      ...this.finalStats,
      timestamp: new Date().toISOString(),
      textType: this.textType,
      difficulty: this.difficulty
    };
    
    // Save to localStorage
    const savedResults = JSON.parse(localStorage.getItem('typing-test-results') || '[]');
    savedResults.push(results);
    
    // Keep only last 100 results
    if (savedResults.length > 100) {
      savedResults.splice(0, savedResults.length - 100);
    }
    
    localStorage.setItem('typing-test-results', JSON.stringify(savedResults));
    this.showToast('Results saved!');
  }

  playKeystrokeSound() {
    // Create a simple beep sound
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    oscillator.frequency.value = 800;
    oscillator.type = 'sine';
    
    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
    
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.1);
  }

  playErrorSound() {
    // Create an error sound
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    oscillator.frequency.value = 300;
    oscillator.type = 'sawtooth';
    
    gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
    
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.2);
  }

  showModal(modalId) {
    const modal = document.getElementById(modalId);
    modal.classList.add('show');
    
    if (modalId === 'settingsModal') {
      this.updateSettingsDisplay();
    } else if (modalId === 'statsModal') {
      this.updateStatsDisplay();
    }
  }

  hideModal(modalId) {
    const modal = document.getElementById(modalId);
    modal.classList.remove('show');
  }

  updateSettingsDisplay() {
    document.getElementById('soundEffects').checked = this.settings.soundEffects;
    document.getElementById('showLiveWPM').checked = this.settings.showLiveWPM;
    document.getElementById('highlightErrors').checked = this.settings.highlightErrors;
    document.getElementById('darkTheme').checked = this.settings.darkTheme;
    document.getElementById('fontSize').value = this.settings.fontSize;
    document.getElementById('caretStyle').value = this.settings.caretStyle;
  }

  updateStatsDisplay() {
    const savedResults = JSON.parse(localStorage.getItem('typing-test-results') || '[]');
    
    if (savedResults.length === 0) {
      document.getElementById('avgWPM').textContent = '0';
      document.getElementById('bestWPM').textContent = '0';
      document.getElementById('avgAccuracy').textContent = '100%';
      document.getElementById('testsCompleted').textContent = '0';
      return;
    }
    
    const avgWPM = Math.round(savedResults.reduce((sum, r) => sum + r.wpm, 0) / savedResults.length);
    const bestWPM = Math.max(...savedResults.map(r => r.wpm));
    const avgAccuracy = Math.round(savedResults.reduce((sum, r) => sum + r.accuracy, 0) / savedResults.length);
    
    document.getElementById('avgWPM').textContent = avgWPM;
    document.getElementById('bestWPM').textContent = bestWPM;
    document.getElementById('avgAccuracy').textContent = avgAccuracy + '%';
    document.getElementById('testsCompleted').textContent = savedResults.length;
    
    this.drawProgressChart(savedResults);
    this.showAchievements(savedResults);
  }

  drawProgressChart(results) {
    const canvas = document.getElementById('progressChart');
    const ctx = canvas.getContext('2d');
    
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    if (results.length < 2) return;
    
    const padding = 40;
    const chartWidth = canvas.width - 2 * padding;
    const chartHeight = canvas.height - 2 * padding;
    
    const maxWPM = Math.max(...results.map(r => r.wpm));
    const minWPM = Math.min(...results.map(r => r.wpm));
    const wpmRange = maxWPM - minWPM || 1;
    
    // Draw axes
    ctx.strokeStyle = '#666';
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.moveTo(padding, padding);
    ctx.lineTo(padding, canvas.height - padding);
    ctx.lineTo(canvas.width - padding, canvas.height - padding);
    ctx.stroke();
    
    // Draw WPM progression
    ctx.strokeStyle = '#1976d2';
    ctx.lineWidth = 2;
    ctx.beginPath();
    
    results.forEach((result, index) => {
      const x = padding + (index / (results.length - 1)) * chartWidth;
      const y = canvas.height - padding - ((result.wpm - minWPM) / wpmRange) * chartHeight;
      
      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });
    
    ctx.stroke();
  }

  showAchievements(results) {
    const achievementGrid = document.getElementById('achievementGrid');
    achievementGrid.innerHTML = '';
    
    const achievements = [
      { id: 'first-test', title: 'First Steps', description: 'Complete your first test', condition: () => results.length >= 1 },
      { id: 'speed-demon', title: 'Speed Demon', description: 'Reach 60 WPM', condition: () => results.some(r => r.wpm >= 60) },
      { id: 'accuracy-master', title: 'Accuracy Master', description: 'Achieve 95% accuracy', condition: () => results.some(r => r.accuracy >= 95) },
      { id: 'consistent', title: 'Consistent Performer', description: 'Complete 10 tests', condition: () => results.length >= 10 },
      { id: 'perfectionist', title: 'Perfectionist', description: 'Achieve 100% accuracy', condition: () => results.some(r => r.accuracy === 100) },
      { id: 'lightning-fast', title: 'Lightning Fast', description: 'Reach 80 WPM', condition: () => results.some(r => r.wpm >= 80) }
    ];
    
    achievements.forEach(achievement => {
      const isUnlocked = achievement.condition();
      const achievementEl = document.createElement('div');
      achievementEl.className = `achievement ${isUnlocked ? 'unlocked' : ''}`;
      achievementEl.innerHTML = `
        <div class="achievement-icon">${isUnlocked ? '🏆' : '🔒'}</div>
        <div class="achievement-title">${achievement.title}</div>
        <div class="achievement-description">${achievement.description}</div>
      `;
      achievementGrid.appendChild(achievementEl);
    });
  }

  showToast(message) {
    const toast = document.getElementById('toast');
    toast.textContent = message;
    toast.classList.add('show');
    
    setTimeout(() => {
      toast.classList.remove('show');
    }, 3000);
  }

  applySettings() {
    this.applyTheme();
    this.applyFontSize();
    this.applyCaret();
  }

  applyTheme() {
    document.body.setAttribute('data-theme', this.settings.darkTheme ? 'dark' : 'light');
  }

  applyFontSize() {
    const textContent = document.getElementById('textContent');
    textContent.className = `text-content ${this.settings.fontSize}`;
  }

  applyCaret() {
    // This would be implemented with more complex cursor styling
    // For now, we'll use CSS classes
    document.documentElement.setAttribute('data-caret', this.settings.caretStyle);
  }

  saveSettings() {
    localStorage.setItem('typing-test-settings', JSON.stringify(this.settings));
  }

  loadSettings() {
    const saved = localStorage.getItem('typing-test-settings');
    if (saved) {
      this.settings = { ...this.settings, ...JSON.parse(saved) };
    }
  }
}

// Initialize the typing test when the page loads
document.addEventListener('DOMContentLoaded', () => {
  new TypingTest();
});
