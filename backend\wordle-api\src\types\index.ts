/**
 * Comprehensive TypeScript type definitions for Enterprise Wordle Game API
 * Advanced word game system with multiplayer, statistics, and real-time features
 */

// Core game interfaces
export interface WordleGame {
  id: string;
  playerId?: string;
  playerName: string;
  targetWord: string;
  guesses: GameGuess[];
  gameState: GameState;
  maxGuesses: number;
  startTime: Date;
  endTime?: Date;
  duration?: number; // milliseconds
  difficulty: DifficultyLevel;
  gameMode: GameMode;
  hints: GameHint[];
  score: number;
  streak: number;
  metadata: GameMetadata;
  createdAt: Date;
  updatedAt: Date;
}

export interface GameGuess {
  word: string;
  feedback: LetterFeedback[];
  timestamp: Date;
  guessNumber: number;
  timeToGuess: number; // milliseconds since previous guess
  hintsUsed: number;
  isCorrect: boolean;
}

export interface LetterFeedback {
  letter: string;
  position: number;
  status: LetterStatus;
  isRevealed: boolean;
}

export interface GameHint {
  type: HintType;
  content: string;
  usedAt: Date;
  guessNumber: number;
  cost: number; // points deducted
}

export interface GameMetadata {
  browserInfo: string;
  deviceType: 'desktop' | 'tablet' | 'mobile';
  language: string;
  timezone: string;
  sessionId: string;
  ipAddress?: string;
  referrer?: string;
}

// Player and user interfaces
export interface Player {
  id: string;
  username: string;
  email?: string;
  passwordHash?: string;
  profile: PlayerProfile;
  statistics: PlayerStatistics;
  preferences: PlayerPreferences;
  achievements: Achievement[];
  friends: string[]; // player IDs
  blockedPlayers: string[]; // player IDs
  createdAt: Date;
  updatedAt: Date;
  lastActiveAt: Date;
}

export interface PlayerProfile {
  displayName: string;
  avatar?: string;
  bio?: string;
  country?: string;
  timezone: string;
  experienceLevel: ExperienceLevel;
  favoriteCategory: WordCategory;
  customization: ProfileCustomization;
}

export interface ProfileCustomization {
  theme: 'light' | 'dark' | 'auto' | 'custom';
  colorScheme: ColorScheme;
  animations: boolean;
  soundEffects: boolean;
  hapticFeedback: boolean;
}

export interface ColorScheme {
  correct: string;
  present: string;
  absent: string;
  background: string;
  text: string;
}

export interface PlayerStatistics {
  totalGames: number;
  gamesWon: number;
  gamesLost: number;
  winRate: number;
  currentStreak: number;
  maxStreak: number;
  averageGuesses: number;
  bestTime: number; // milliseconds
  totalPlayTime: number; // milliseconds
  averageTime: number; // milliseconds
  guessDistribution: GuessDistribution;
  categoryStats: CategoryStatistics[];
  difficultyStats: DifficultyStatistics[];
  dailyStats: DailyStatistics[];
  achievements: AchievementProgress[];
  ranking: PlayerRanking;
  socialStats: SocialStatistics;
}

export interface GuessDistribution {
  1: number;
  2: number;
  3: number;
  4: number;
  5: number;
  6: number;
  failed: number;
}

export interface CategoryStatistics {
  category: WordCategory;
  gamesPlayed: number;
  winRate: number;
  averageGuesses: number;
  bestStreak: number;
}

export interface DifficultyStatistics {
  difficulty: DifficultyLevel;
  gamesPlayed: number;
  winRate: number;
  averageGuesses: number;
  averageTime: number;
}

export interface DailyStatistics {
  date: string;
  gamesPlayed: number;
  gamesWon: number;
  averageGuesses: number;
  totalTime: number;
  streak: number;
}

export interface PlayerRanking {
  globalRank: number;
  categoryRanks: { [category: string]: number };
  percentile: number;
  rating: number; // ELO-style rating
  tier: PlayerTier;
}

export interface SocialStatistics {
  friendsCount: number;
  gamesWithFriends: number;
  multiplayerWins: number;
  multiplayerGames: number;
  challengesSent: number;
  challengesReceived: number;
  challengesWon: number;
}

// Word and dictionary interfaces
export interface WordEntry {
  word: string;
  category: WordCategory;
  difficulty: DifficultyLevel;
  frequency: number; // usage frequency score
  length: number;
  commonality: number; // how common the word is (1-100)
  etymology?: string;
  definition?: string;
  pronunciation?: string;
  partOfSpeech?: string;
  examples?: string[];
  synonyms?: string[];
  antonyms?: string[];
  tags: string[];
  isOffensive: boolean;
  isArchaic: boolean;
  isProperNoun: boolean;
  addedAt: Date;
  lastUsed?: Date;
  usageCount: number;
  successRate: number; // percentage of players who guess it correctly
}

export interface DailyWord {
  date: string;
  word: string;
  category: WordCategory;
  difficulty: DifficultyLevel;
  hint?: string;
  funFact?: string;
  etymology?: string;
  totalAttempts: number;
  successfulAttempts: number;
  averageGuesses: number;
  guessDistribution: GuessDistribution;
}

// Multiplayer and real-time interfaces
export interface MultiplayerRoom {
  id: string;
  name: string;
  hostId: string;
  players: RoomPlayer[];
  maxPlayers: number;
  gameMode: GameMode;
  difficulty: DifficultyLevel;
  category: WordCategory;
  isPrivate: boolean;
  password?: string;
  status: RoomStatus;
  currentGame?: MultiplayerGame;
  settings: RoomSettings;
  createdAt: Date;
  startedAt?: Date;
  endedAt?: Date;
}

export interface RoomPlayer {
  playerId: string;
  username: string;
  isReady: boolean;
  isHost: boolean;
  joinedAt: Date;
  status: PlayerStatus;
  currentGuesses: number;
  isFinished: boolean;
  finalResult?: GameResult;
}

export interface MultiplayerGame {
  id: string;
  roomId: string;
  targetWord: string;
  startTime: Date;
  endTime?: Date;
  playerGames: { [playerId: string]: WordleGame };
  leaderboard: MultiplayerLeaderboard[];
  status: GameStatus;
}

export interface MultiplayerLeaderboard {
  playerId: string;
  username: string;
  guesses: number;
  timeToComplete: number;
  score: number;
  rank: number;
  isWinner: boolean;
}

export interface RoomSettings {
  timeLimit?: number; // seconds per guess
  hintsEnabled: boolean;
  spectatingAllowed: boolean;
  chatEnabled: boolean;
  autoStart: boolean;
  roundsToPlay: number;
}

// Achievement and gamification interfaces
export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: AchievementCategory;
  difficulty: AchievementDifficulty;
  requirements: AchievementRequirement[];
  reward: AchievementReward;
  isSecret: boolean;
  rarity: AchievementRarity;
  createdAt: Date;
}

export interface AchievementProgress {
  achievementId: string;
  progress: number; // 0-100
  isUnlocked: boolean;
  unlockedAt?: Date;
  currentValue: number;
  targetValue: number;
}

export interface AchievementRequirement {
  type: 'games_won' | 'streak' | 'time_limit' | 'category_mastery' | 'difficulty_mastery' | 'social' | 'special';
  value: number;
  operator: 'gte' | 'lte' | 'eq';
  context?: any;
}

export interface AchievementReward {
  type: 'badge' | 'title' | 'theme' | 'avatar' | 'points';
  value: string | number;
  description: string;
}

// Leaderboard and competition interfaces
export interface LeaderboardEntry {
  rank: number;
  playerId: string;
  username: string;
  score: number;
  gamesWon: number;
  winRate: number;
  currentStreak: number;
  maxStreak: number;
  averageGuesses: number;
  totalGames: number;
  rating: number;
  tier: PlayerTier;
  country?: string;
  lastGameAt: Date;
}

export interface Tournament {
  id: string;
  name: string;
  description: string;
  startDate: Date;
  endDate: Date;
  status: TournamentStatus;
  participants: TournamentParticipant[];
  maxParticipants: number;
  entryFee: number;
  prizePool: TournamentPrize[];
  rules: TournamentRules;
  brackets?: TournamentBracket[];
  currentRound: number;
  totalRounds: number;
  createdBy: string;
  createdAt: Date;
}

export interface TournamentParticipant {
  playerId: string;
  username: string;
  registeredAt: Date;
  currentRound: number;
  isEliminated: boolean;
  totalScore: number;
  gamesPlayed: number;
  gamesWon: number;
}

export interface TournamentRules {
  gameMode: GameMode;
  difficulty: DifficultyLevel;
  category: WordCategory;
  timeLimit: number;
  eliminationStyle: 'single' | 'double' | 'round-robin';
  tiebreaker: 'time' | 'guesses' | 'score';
}

// API request/response interfaces
export interface StartGameRequest {
  playerName: string;
  difficulty?: DifficultyLevel;
  gameMode?: GameMode;
  category?: WordCategory;
  customWord?: string; // for practice mode
}

export interface StartGameResponse {
  success: boolean;
  data: {
    gameId: string;
    maxGuesses: number;
    difficulty: DifficultyLevel;
    gameMode: GameMode;
    category: WordCategory;
    hintsAvailable: number;
    timeLimit?: number;
  };
  message: string;
}

export interface MakeGuessRequest {
  gameId: string;
  word: string;
  timestamp: string;
}

export interface MakeGuessResponse {
  success: boolean;
  data: {
    guess: GameGuess;
    gameState: GameState;
    isGameOver: boolean;
    isWinner: boolean;
    targetWord?: string; // only revealed when game is over
    statistics?: GameStatistics;
    achievements?: Achievement[];
    nextDailyWord?: Date;
  };
  message: string;
}

export interface GameStatistics {
  totalGuesses: number;
  timeElapsed: number;
  averageTimePerGuess: number;
  hintsUsed: number;
  score: number;
  efficiency: number; // score based on guesses and time
  letterAccuracy: number;
  positionAccuracy: number;
}

// Enum types
export type GameState = 'waiting' | 'playing' | 'won' | 'lost' | 'abandoned';
export type GameMode = 'daily' | 'practice' | 'challenge' | 'multiplayer' | 'tournament' | 'custom';
export type DifficultyLevel = 'easy' | 'medium' | 'hard' | 'expert' | 'nightmare';
export type WordCategory = 'common' | 'animals' | 'food' | 'science' | 'technology' | 'sports' | 'music' | 'movies' | 'books' | 'geography' | 'history' | 'custom';
export type LetterStatus = 'correct' | 'present' | 'absent' | 'unknown';
export type HintType = 'letter' | 'position' | 'category' | 'definition' | 'rhyme' | 'length';
export type ExperienceLevel = 'beginner' | 'intermediate' | 'advanced' | 'expert' | 'master';
export type PlayerTier = 'bronze' | 'silver' | 'gold' | 'platinum' | 'diamond' | 'master' | 'grandmaster';
export type AchievementCategory = 'gameplay' | 'social' | 'streak' | 'speed' | 'accuracy' | 'exploration' | 'special';
export type AchievementDifficulty = 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';
export type AchievementRarity = 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary' | 'mythic';
export type RoomStatus = 'waiting' | 'starting' | 'playing' | 'finished' | 'cancelled';
export type PlayerStatus = 'waiting' | 'ready' | 'playing' | 'finished' | 'disconnected';
export type GameStatus = 'waiting' | 'active' | 'finished' | 'cancelled';
export type TournamentStatus = 'upcoming' | 'registration' | 'active' | 'finished' | 'cancelled';

// Real-time interfaces
export interface RealTimeGameUpdate {
  type: 'guess_made' | 'game_finished' | 'player_joined' | 'player_left' | 'hint_used';
  gameId: string;
  playerId: string;
  data: any;
  timestamp: Date;
}

export interface ChatMessage {
  id: string;
  roomId: string;
  playerId: string;
  username: string;
  message: string;
  timestamp: Date;
  type: 'text' | 'system' | 'emoji' | 'sticker';
}

// Configuration interfaces
export interface GameConfig {
  maxGuesses: number;
  wordLength: number;
  hintsPerGame: number;
  timeLimit?: number;
  categories: WordCategory[];
  difficulties: DifficultyLevel[];
  scoring: ScoringConfig;
}

export interface ScoringConfig {
  baseScore: number;
  guessMultiplier: number;
  timeBonus: number;
  streakBonus: number;
  difficultyMultiplier: { [key in DifficultyLevel]: number };
  hintPenalty: number;
}

// Database interfaces
export interface DatabaseConfig {
  type: 'sqlite' | 'postgresql' | 'mysql';
  host?: string;
  port?: number;
  database: string;
  username?: string;
  password?: string;
  ssl?: boolean;
  pool?: {
    min: number;
    max: number;
  };
}

// Error interfaces
export interface GameError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
  gameId?: string;
  playerId?: string;
}

export interface ValidationError {
  field: string;
  message: string;
  value?: any;
  code: string;
}

// Analytics interfaces
export interface GameAnalytics {
  totalGames: number;
  totalPlayers: number;
  averageGuesses: number;
  winRate: number;
  popularWords: PopularWord[];
  difficultyDistribution: DifficultyDistribution[];
  categoryDistribution: CategoryDistribution[];
  hourlyActivity: HourlyActivity[];
  playerRetention: RetentionMetrics;
  performanceMetrics: PerformanceMetrics;
}

export interface PopularWord {
  word: string;
  timesUsed: number;
  averageGuesses: number;
  successRate: number;
  difficulty: DifficultyLevel;
  category: WordCategory;
}

export interface DifficultyDistribution {
  difficulty: DifficultyLevel;
  gamesPlayed: number;
  winRate: number;
  averageGuesses: number;
  averageTime: number;
}

export interface CategoryDistribution {
  category: WordCategory;
  gamesPlayed: number;
  popularity: number;
  winRate: number;
  averageGuesses: number;
}

export interface HourlyActivity {
  hour: number;
  gamesPlayed: number;
  uniquePlayers: number;
  averageSessionLength: number;
}

export interface RetentionMetrics {
  day1: number;
  day7: number;
  day30: number;
  averageSessionsPerUser: number;
  averageSessionLength: number;
}

export interface PerformanceMetrics {
  averageResponseTime: number;
  errorRate: number;
  uptime: number;
  concurrentUsers: number;
  peakConcurrentUsers: number;
}

// Game result interface
export interface GameResult {
  gameId: string;
  playerId: string;
  isWinner: boolean;
  guesses: number;
  timeElapsed: number;
  score: number;
  targetWord: string;
  finalGuess?: string;
  hintsUsed: number;
  achievements: string[];
  newPersonalBests: string[];
}
