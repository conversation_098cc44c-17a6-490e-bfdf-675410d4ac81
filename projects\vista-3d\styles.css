/**
 * Vista3D - 3D Product Configurator Styles
 * Modern, responsive design with 3D-focused UI
 */

/* CSS Custom Properties */
:root {
  /* Colors - Tech/3D Theme */
  --color-primary: #3b82f6;
  --color-primary-dark: #2563eb;
  --color-primary-light: #93c5fd;
  --color-secondary: #8b5cf6;
  --color-accent: #06b6d4;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  
  /* Neutral Colors */
  --color-white: #ffffff;
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  
  /* 3D Specific Colors */
  --color-viewport-bg: #1a1a1a;
  --color-panel-bg: rgba(255, 255, 255, 0.95);
  --color-control-bg: rgba(0, 0, 0, 0.7);
  --color-control-hover: rgba(0, 0, 0, 0.9);
  
  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  /* Spacing */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  --spacing-24: 6rem;
  
  /* Layout */
  --max-width-sm: 640px;
  --max-width-md: 768px;
  --max-width-lg: 1024px;
  --max-width-xl: 1280px;
  --max-width-2xl: 1536px;
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  
  /* Z-index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-loading: 9999;
}

/* Dark Theme */
[data-theme="dark"] {
  --color-white: #111827;
  --color-gray-50: #1f2937;
  --color-gray-100: #374151;
  --color-gray-200: #4b5563;
  --color-gray-300: #6b7280;
  --color-gray-400: #9ca3af;
  --color-gray-500: #d1d5db;
  --color-gray-600: #e5e7eb;
  --color-gray-700: #f3f4f6;
  --color-gray-800: #f9fafb;
  --color-gray-900: #ffffff;
  
  --color-panel-bg: rgba(31, 41, 55, 0.95);
  --color-viewport-bg: #0f0f0f;
}

/* Reset and Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--color-gray-900);
  background-color: var(--color-white);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-gray-900);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
  margin-bottom: var(--spacing-4);
  color: var(--color-gray-700);
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--color-primary-dark);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-6);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  border: 1px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  color: var(--color-white);
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
  color: var(--color-white);
}

.btn-secondary {
  color: var(--color-gray-700);
  background-color: var(--color-white);
  border-color: var(--color-gray-300);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--color-gray-50);
  border-color: var(--color-gray-400);
  color: var(--color-gray-800);
}

/* Loading Screen */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-loading);
  color: white;
}

.loading-content {
  text-align: center;
  max-width: 400px;
  padding: var(--spacing-8);
}

.loading-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-8);
}

.logo-icon {
  font-size: var(--font-size-4xl);
}

.logo-text {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
}

.loading-spinner {
  margin-bottom: var(--spacing-6);
}

.spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-6);
  opacity: 0.9;
}

.loading-progress {
  width: 100%;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin-bottom: var(--spacing-3);
}

.progress-fill {
  height: 100%;
  background-color: white;
  border-radius: var(--radius-full);
  transition: width var(--transition-normal);
  width: 0%;
}

.progress-text {
  font-size: var(--font-size-sm);
  opacity: 0.8;
}

/* Header */
.header {
  position: sticky;
  top: 0;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--color-gray-200);
  z-index: var(--z-sticky);
}

.navbar {
  padding: var(--spacing-4) 0;
}

.nav-container {
  max-width: var(--max-width-2xl);
  margin: 0 auto;
  padding: 0 var(--spacing-4);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nav-brand {
  display: flex;
  align-items: center;
}

.brand-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  text-decoration: none;
}

.brand-icon {
  font-size: var(--font-size-2xl);
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-8);
  list-style: none;
}

.nav-link {
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
  padding: var(--spacing-2) 0;
  position: relative;
  transition: color var(--transition-fast);
}

.nav-link:hover,
.nav-link.active {
  color: var(--color-primary);
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--color-primary);
  border-radius: var(--radius-full);
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.theme-toggle,
.fullscreen-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: var(--radius-lg);
  background-color: var(--color-gray-100);
  color: var(--color-gray-700);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.theme-toggle:hover,
.fullscreen-btn:hover {
  background-color: var(--color-gray-200);
  color: var(--color-gray-900);
}

.nav-toggle {
  display: none;
  flex-direction: column;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  background: none;
  cursor: pointer;
}

.hamburger {
  width: 20px;
  height: 2px;
  background-color: var(--color-gray-700);
  transition: all var(--transition-fast);
  position: relative;
}

.hamburger::before,
.hamburger::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 2px;
  background-color: var(--color-gray-700);
  transition: all var(--transition-fast);
}

.hamburger::before {
  top: -6px;
}

.hamburger::after {
  bottom: -6px;
}

/* Main Content */
.main {
  min-height: calc(100vh - 80px);
}

/* Configurator Section */
.configurator {
  padding: 0;
  background-color: var(--color-gray-50);
  min-height: calc(100vh - 80px);
}

.configurator-container {
  display: grid;
  grid-template-columns: 1fr 400px;
  height: calc(100vh - 80px);
  max-width: var(--max-width-2xl);
  margin: 0 auto;
}

/* Viewport */
.viewport-container {
  position: relative;
  background-color: var(--color-viewport-bg);
  overflow: hidden;
}

.viewport {
  width: 100%;
  height: 100%;
  display: block;
  cursor: grab;
}

.viewport:active {
  cursor: grabbing;
}

.viewport-controls {
  position: absolute;
  top: var(--spacing-4);
  left: var(--spacing-4);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.control-btn {
  width: 48px;
  height: 48px;
  background-color: var(--color-control-bg);
  border: none;
  border-radius: var(--radius-lg);
  color: white;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
  backdrop-filter: blur(10px);
}

.control-btn:hover {
  background-color: var(--color-control-hover);
  transform: translateY(-2px);
}

.performance-stats {
  position: absolute;
  top: var(--spacing-4);
  right: var(--spacing-4);
  background-color: var(--color-control-bg);
  color: white;
  padding: var(--spacing-3);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  backdrop-filter: blur(10px);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-1);
}

.stat-item:last-child {
  margin-bottom: 0;
}

.model-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;
  display: none;
}

.model-loading .spinner {
  width: 40px;
  height: 40px;
  margin-bottom: var(--spacing-4);
}

/* Configuration Panel */
.config-panel {
  background-color: var(--color-panel-bg);
  backdrop-filter: blur(10px);
  border-left: 1px solid var(--color-gray-200);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--color-gray-200);
}

.panel-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  margin: 0;
}

.panel-toggle {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  font-size: var(--font-size-lg);
  cursor: pointer;
  color: var(--color-gray-600);
  transition: color var(--transition-fast);
}

.panel-toggle:hover {
  color: var(--color-gray-900);
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-6);
}

.config-section {
  margin-bottom: var(--spacing-8);
}

.config-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-4);
  color: var(--color-gray-900);
}

/* Model Selector */
.model-selector {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-3);
}

.model-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  border: 2px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  background-color: var(--color-white);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-align: left;
}

.model-btn:hover {
  border-color: var(--color-primary-light);
  background-color: var(--color-gray-50);
}

.model-btn.active {
  border-color: var(--color-primary);
  background-color: var(--color-primary-light);
  color: var(--color-primary-dark);
}

.model-thumb {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-gray-100);
  border-radius: var(--radius-md);
  font-size: var(--font-size-2xl);
}

.model-btn span {
  font-weight: var(--font-weight-medium);
}

/* Color Palette */
.color-palette {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-2);
}

.color-btn {
  width: 40px;
  height: 40px;
  border: 3px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.color-btn:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-md);
}

.color-btn.active {
  border-color: var(--color-primary);
  transform: scale(1.1);
  box-shadow: var(--shadow-lg);
}

/* Control Groups */
.control-group {
  margin-bottom: var(--spacing-4);
}

.control-group label {
  display: block;
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
  margin-bottom: var(--spacing-2);
}

.control-select {
  width: 100%;
  padding: var(--spacing-3);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-lg);
  background-color: var(--color-white);
  font-size: var(--font-size-sm);
  transition: border-color var(--transition-fast);
}

.control-select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.control-slider {
  width: 100%;
  margin-bottom: var(--spacing-2);
}

.slider-value {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  font-weight: var(--font-weight-medium);
}

.resolution-inputs {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.resolution-inputs input {
  flex: 1;
  padding: var(--spacing-2);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
}

.resolution-inputs span {
  color: var(--color-gray-500);
  font-weight: var(--font-weight-medium);
}

/* Animation Controls */
.animation-controls {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.animation-controls .control-btn {
  width: 100%;
  height: auto;
  padding: var(--spacing-3);
  background-color: var(--color-gray-100);
  color: var(--color-gray-700);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  justify-content: flex-start;
  gap: var(--spacing-2);
}

.animation-controls .control-btn:hover {
  background-color: var(--color-gray-200);
  transform: none;
}

/* Export Controls */
.export-controls {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

/* Gallery Section */
.gallery {
  padding: var(--spacing-20) 0;
  background-color: var(--color-white);
}

.gallery-container {
  max-width: var(--max-width-xl);
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.section-title {
  text-align: center;
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
}

.section-description {
  text-align: center;
  color: var(--color-gray-600);
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-16);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-6);
}

.gallery-item {
  background-color: var(--color-white);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
  cursor: pointer;
}

.gallery-item:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-4px);
}

.gallery-image {
  width: 100%;
  height: 200px;
  background-color: var(--color-gray-100);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-4xl);
  color: var(--color-gray-400);
}

.gallery-info {
  padding: var(--spacing-6);
}

.gallery-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-2);
}

.gallery-description {
  color: var(--color-gray-600);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-4);
}

.gallery-stats {
  display: flex;
  justify-content: space-between;
  font-size: var(--font-size-xs);
  color: var(--color-gray-500);
}

/* About Section */
.about {
  padding: var(--spacing-20) 0;
  background-color: var(--color-gray-50);
}

.about-container {
  max-width: var(--max-width-xl);
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.about-content {
  margin-bottom: var(--spacing-16);
}

.about-text {
  font-size: var(--font-size-lg);
  color: var(--color-gray-600);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-12);
  text-align: center;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-8);
  margin-bottom: var(--spacing-16);
}

.feature-item {
  text-align: center;
  padding: var(--spacing-6);
  background-color: var(--color-white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  transition: transform var(--transition-normal);
}

.feature-item:hover {
  transform: translateY(-4px);
}

.feature-icon {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-4);
}

.feature-item h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-3);
}

.feature-item p {
  color: var(--color-gray-600);
  line-height: var(--line-height-relaxed);
  margin-bottom: 0;
}

.tech-stack {
  text-align: center;
}

.tech-stack h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-6);
}

.tech-items {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--spacing-3);
}

.tech-item {
  padding: var(--spacing-2) var(--spacing-4);
  background-color: var(--color-primary);
  color: var(--color-white);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

/* Help Section */
.help {
  padding: var(--spacing-20) 0;
  background-color: var(--color-white);
}

.help-container {
  max-width: var(--max-width-lg);
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.help-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-8);
  margin-top: var(--spacing-12);
}

.help-section h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-4);
  color: var(--color-gray-900);
}

.help-section ul {
  list-style: none;
  padding: 0;
}

.help-section li {
  padding: var(--spacing-3) 0;
  border-bottom: 1px solid var(--color-gray-200);
  color: var(--color-gray-700);
  line-height: var(--line-height-relaxed);
}

.help-section li:last-child {
  border-bottom: none;
}

.help-section strong {
  color: var(--color-gray-900);
  font-weight: var(--font-weight-semibold);
}

/* Footer */
.footer {
  background-color: var(--color-gray-900);
  color: var(--color-gray-300);
  padding: var(--spacing-16) 0 var(--spacing-8);
}

.footer-container {
  max-width: var(--max-width-xl);
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-8);
  margin-bottom: var(--spacing-12);
}

.footer-section h4 {
  color: var(--color-white);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-4);
}

.footer-section p {
  color: var(--color-gray-400);
  line-height: var(--line-height-relaxed);
  margin-bottom: 0;
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: var(--spacing-2);
}

.footer-section ul li a {
  color: var(--color-gray-400);
  transition: color var(--transition-fast);
}

.footer-section ul li a:hover {
  color: var(--color-white);
}

.footer-bottom {
  border-top: 1px solid var(--color-gray-800);
  padding-top: var(--spacing-8);
  text-align: center;
}

.footer-bottom p {
  color: var(--color-gray-500);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-2);
}

.footer-bottom a {
  color: var(--color-gray-400);
  transition: color var(--transition-fast);
}

.footer-bottom a:hover {
  color: var(--color-white);
}

/* Notifications */
.notification-container {
  position: fixed;
  top: var(--spacing-4);
  right: var(--spacing-4);
  z-index: var(--z-tooltip);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.notification {
  background-color: var(--color-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  padding: var(--spacing-4);
  max-width: 400px;
  animation: slideInRight 0.3s ease-out;
  border-left: 4px solid var(--color-primary);
}

.notification.success {
  border-left-color: var(--color-success);
}

.notification.error {
  border-left-color: var(--color-error);
}

.notification.warning {
  border-left-color: var(--color-warning);
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .configurator-container {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr auto;
  }

  .config-panel {
    border-left: none;
    border-top: 1px solid var(--color-gray-200);
    max-height: 50vh;
  }

  .viewport-container {
    min-height: 50vh;
  }
}

@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }

  .nav-toggle {
    display: flex;
  }

  .configurator-container {
    height: auto;
    min-height: calc(100vh - 80px);
  }

  .viewport-container {
    min-height: 60vh;
  }

  .config-panel {
    max-height: none;
  }

  .panel-content {
    max-height: 40vh;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .help-content {
    grid-template-columns: 1fr;
  }

  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .color-palette {
    grid-template-columns: repeat(6, 1fr);
  }

  .model-selector {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .viewport-controls {
    flex-direction: row;
    bottom: var(--spacing-4);
    top: auto;
    left: 50%;
    transform: translateX(-50%);
  }

  .control-btn {
    width: 40px;
    height: 40px;
    font-size: var(--font-size-base);
  }

  .performance-stats {
    position: relative;
    top: auto;
    right: auto;
    margin: var(--spacing-4);
    display: inline-block;
  }

  .notification-container {
    left: var(--spacing-2);
    right: var(--spacing-2);
  }

  .notification {
    max-width: none;
  }
}

/* Utility Classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.hidden {
  display: none !important;
}

.visible {
  display: block !important;
}

/* Custom Scrollbar */
.panel-content::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track {
  background: var(--color-gray-100);
  border-radius: var(--radius-full);
}

.panel-content::-webkit-scrollbar-thumb {
  background: var(--color-gray-400);
  border-radius: var(--radius-full);
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-500);
}
