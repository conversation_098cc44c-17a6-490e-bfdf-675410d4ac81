/**
 * Enterprise logging utility with <PERSON>
 * Comprehensive logging for collaborative workspace events, performance, and system monitoring
 */

import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import path from 'path';

// Define log levels
const logLevels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4
};

// Define log colors
const logColors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white'
};

winston.addColors(logColors);

// Create logs directory if it doesn't exist
const logsDir = process.env.LOG_DIR || './logs';

// Define log format
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf((info) => {
    const { timestamp, level, message, ...meta } = info;
    const metaString = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
    return `${timestamp} [${level}]: ${message} ${metaString}`;
  })
);

// Define file format (without colors)
const fileFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Create transports
const transports: winston.transport[] = [];

// Console transport
if (process.env.NODE_ENV !== 'test') {
  transports.push(
    new winston.transports.Console({
      level: process.env.LOG_LEVEL || 'info',
      format: logFormat
    })
  );
}

// File transports (only if LOG_FILE is enabled)
if (process.env.LOG_FILE === 'true') {
  // General log file with rotation
  transports.push(
    new DailyRotateFile({
      filename: path.join(logsDir, 'application-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '14d',
      level: 'info',
      format: fileFormat
    })
  );

  // Error log file
  transports.push(
    new DailyRotateFile({
      filename: path.join(logsDir, 'error-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '30d',
      level: 'error',
      format: fileFormat
    })
  );

  // Workspace events log file
  transports.push(
    new DailyRotateFile({
      filename: path.join(logsDir, 'workspace-events-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '50m',
      maxFiles: '7d',
      level: 'info',
      format: fileFormat,
      // Only log workspace-related events
      filter: (info) => ['workspace', 'room', 'chat', 'whiteboard', 'task'].includes(info.category)
    })
  );
}

// Create logger instance
const logger = winston.createLogger({
  levels: logLevels,
  level: process.env.LOG_LEVEL || 'info',
  transports,
  exitOnError: false,
  // Handle uncaught exceptions and rejections
  exceptionHandlers: process.env.LOG_FILE === 'true' ? [
    new DailyRotateFile({
      filename: path.join(logsDir, 'exceptions-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '30d',
      format: fileFormat
    })
  ] : [],
  rejectionHandlers: process.env.LOG_FILE === 'true' ? [
    new DailyRotateFile({
      filename: path.join(logsDir, 'rejections-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '30d',
      format: fileFormat
    })
  ] : []
});

/**
 * Enhanced logger with specialized methods for collaborative workspace
 */
export const enhancedLogger = {
  // Standard logging methods
  error: (message: string, meta?: any) => logger.error(message, meta),
  warn: (message: string, meta?: any) => logger.warn(message, meta),
  info: (message: string, meta?: any) => logger.info(message, meta),
  http: (message: string, meta?: any) => logger.http(message, meta),
  debug: (message: string, meta?: any) => logger.debug(message, meta),

  // Workspace-specific logging
  workspace: (event: string, workspaceId: string, userId?: string, meta?: any) => {
    logger.info(`Workspace: ${event}`, {
      category: 'workspace',
      event,
      workspaceId,
      userId,
      timestamp: new Date().toISOString(),
      ...meta
    });
  },

  // Room-specific logging
  room: (event: string, roomId: string, userId?: string, meta?: any) => {
    logger.info(`Room: ${event}`, {
      category: 'room',
      event,
      roomId,
      userId,
      timestamp: new Date().toISOString(),
      ...meta
    });
  },

  // Chat logging
  chat: (event: string, roomId: string, userId?: string, meta?: any) => {
    logger.info(`Chat: ${event}`, {
      category: 'chat',
      event,
      roomId,
      userId,
      timestamp: new Date().toISOString(),
      ...meta
    });
  },

  // Whiteboard logging
  whiteboard: (event: string, whiteboardId: string, userId?: string, meta?: any) => {
    logger.info(`Whiteboard: ${event}`, {
      category: 'whiteboard',
      event,
      whiteboardId,
      userId,
      timestamp: new Date().toISOString(),
      ...meta
    });
  },

  // Task logging
  task: (event: string, taskId: string, userId?: string, meta?: any) => {
    logger.info(`Task: ${event}`, {
      category: 'task',
      event,
      taskId,
      userId,
      timestamp: new Date().toISOString(),
      ...meta
    });
  },

  // File logging
  file: (event: string, fileId: string, userId?: string, meta?: any) => {
    logger.info(`File: ${event}`, {
      category: 'file',
      event,
      fileId,
      userId,
      timestamp: new Date().toISOString(),
      ...meta
    });
  },

  // Meeting logging
  meeting: (event: string, meetingId: string, userId?: string, meta?: any) => {
    logger.info(`Meeting: ${event}`, {
      category: 'meeting',
      event,
      meetingId,
      userId,
      timestamp: new Date().toISOString(),
      ...meta
    });
  },

  // Performance logging
  performance: (operation: string, duration: number, meta?: any) => {
    logger.info(`Performance: ${operation}`, {
      category: 'performance',
      operation,
      duration,
      timestamp: new Date().toISOString(),
      ...meta
    });
  },

  // Database logging
  database: (operation: string, table: string, duration?: number, meta?: any) => {
    logger.debug(`Database: ${operation} on ${table}`, {
      category: 'database',
      operation,
      table,
      duration,
      timestamp: new Date().toISOString(),
      ...meta
    });
  },

  // Cache logging
  cache: (operation: string, key: string, hit?: boolean, meta?: any) => {
    logger.debug(`Cache: ${operation} for ${key}`, {
      category: 'cache',
      operation,
      key,
      hit,
      timestamp: new Date().toISOString(),
      ...meta
    });
  },

  // API logging
  api: (event: string, requestId: string, userId?: string, meta?: any) => {
    logger.http(`API: ${event}`, {
      category: 'api',
      event,
      requestId,
      userId,
      timestamp: new Date().toISOString(),
      ...meta
    });
  },

  // Real-time logging
  realtime: (event: string, connectionId: string, userId?: string, meta?: any) => {
    logger.info(`Real-time: ${event}`, {
      category: 'realtime',
      event,
      connectionId,
      userId,
      timestamp: new Date().toISOString(),
      ...meta
    });
  },

  // Security logging
  security: (event: string, userId?: string, ip?: string, meta?: any) => {
    logger.warn(`Security: ${event}`, {
      category: 'security',
      event,
      userId,
      ip,
      timestamp: new Date().toISOString(),
      ...meta
    });
  },

  // Business logic logging
  business: (event: string, entity: string, entityId: string, meta?: any) => {
    logger.info(`Business: ${event}`, {
      category: 'business',
      event,
      entity,
      entityId,
      timestamp: new Date().toISOString(),
      ...meta
    });
  },

  // Notification logging
  notification: (event: string, userId: string, type?: string, meta?: any) => {
    logger.info(`Notification: ${event}`, {
      category: 'notification',
      event,
      userId,
      type,
      timestamp: new Date().toISOString(),
      ...meta
    });
  },

  // Analytics logging
  analytics: (event: string, metric: string, value?: number, meta?: any) => {
    logger.info(`Analytics: ${event}`, {
      category: 'analytics',
      event,
      metric,
      value,
      timestamp: new Date().toISOString(),
      ...meta
    });
  }
};

// Export both the standard logger and enhanced logger
export { logger };
export default enhancedLogger;
