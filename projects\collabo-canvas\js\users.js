/**
 * CollaboCanvas Users Module
 * Handles user management and cursor tracking
 */

class UsersManager {
  constructor() {
    this.users = new Map();
    this.currentUser = null;
    this.cursors = new Map();
    this.isOpen = false;
    
    // User colors for identification
    this.userColors = [
      '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57',
      '#ff9ff3', '#54a0ff', '#5f27cd', '#00d2d3', '#ff9f43',
      '#10ac84', '#ee5a24', '#0abde3', '#3867d6', '#8854d0'
    ];
    this.colorIndex = 0;
  }

  init(currentUserId) {
    this.currentUser = currentUserId;
    this.setupUsersInterface();
    this.setupCursorTracking();
    
    // Add current user
    this.addUser(currentUserId, true);
    
    console.log('Users manager initialized for:', currentUserId);
  }

  setupUsersInterface() {
    const usersToggle = document.getElementById('usersPanelToggle');
    
    if (usersToggle) {
      usersToggle.addEventListener('click', () => {
        this.toggleUsersPanel();
      });
    }
  }

  setupCursorTracking() {
    const cursorsLayer = document.getElementById('cursorsLayer');
    if (!cursorsLayer) {
      console.warn('Cursors layer not found');
      return;
    }
    
    // Setup cursor container
    cursorsLayer.style.position = 'absolute';
    cursorsLayer.style.top = '0';
    cursorsLayer.style.left = '0';
    cursorsLayer.style.width = '100%';
    cursorsLayer.style.height = '100%';
    cursorsLayer.style.pointerEvents = 'none';
    cursorsLayer.style.zIndex = '10';
  }

  toggleUsersPanel() {
    const usersPanel = document.getElementById('usersPanel');
    if (usersPanel) {
      this.isOpen = !this.isOpen;
      
      if (this.isOpen) {
        usersPanel.classList.add('open');
      } else {
        usersPanel.classList.remove('open');
      }
    }
  }

  addUser(userId, isCurrent = false) {
    if (this.users.has(userId)) {
      console.log('User already exists:', userId);
      return;
    }

    const user = {
      id: userId,
      name: this.generateUserName(userId),
      color: this.getNextUserColor(),
      isCurrent: isCurrent,
      isOnline: true,
      joinedAt: Date.now(),
      lastSeen: Date.now(),
      cursor: { x: 0, y: 0, visible: false }
    };

    this.users.set(userId, user);
    this.renderUser(user);
    this.updateUsersCount();
    
    if (!isCurrent) {
      this.createUserCursor(userId, user.color);
    }
    
    console.log('User added:', user);
  }

  removeUser(userId) {
    const user = this.users.get(userId);
    if (!user) return;

    // Remove from users map
    this.users.delete(userId);
    
    // Remove from UI
    this.removeUserFromUI(userId);
    
    // Remove cursor
    this.removeUserCursor(userId);
    
    // Update count
    this.updateUsersCount();
    
    console.log('User removed:', userId);
  }

  renderUser(user) {
    const usersList = document.getElementById('usersList');
    if (!usersList) return;

    const userElement = document.createElement('div');
    userElement.className = 'user-item';
    userElement.dataset.userId = user.id;
    
    const statusClass = user.isOnline ? 'online' : 'offline';
    const currentLabel = user.isCurrent ? ' (You)' : '';
    
    userElement.innerHTML = `
      <div class="user-avatar" style="background-color: ${user.color}">
        ${user.name.charAt(0).toUpperCase()}
      </div>
      <div class="user-info">
        <div class="user-name">${user.name}${currentLabel}</div>
        <div class="user-status ${statusClass}">
          ${user.isOnline ? 'Online' : 'Offline'}
        </div>
      </div>
    `;
    
    usersList.appendChild(userElement);
    
    // Add animation
    userElement.classList.add('slide-in-right');
  }

  removeUserFromUI(userId) {
    const userElement = document.querySelector(`[data-user-id="${userId}"]`);
    if (userElement) {
      userElement.remove();
    }
  }

  updateUsersCount() {
    const usersCount = document.getElementById('usersCount');
    if (usersCount) {
      usersCount.textContent = this.users.size;
    }
  }

  createUserCursor(userId, color) {
    const cursorsLayer = document.getElementById('cursorsLayer');
    if (!cursorsLayer) return;

    const cursorElement = document.createElement('div');
    cursorElement.className = 'user-cursor';
    cursorElement.dataset.userId = userId;
    cursorElement.style.display = 'none';
    
    cursorElement.innerHTML = `
      <div class="cursor-pointer" style="border-left-color: ${color}"></div>
      <div class="cursor-label" style="background-color: ${color}">
        ${this.getUserName(userId)}
      </div>
    `;
    
    cursorsLayer.appendChild(cursorElement);
    this.cursors.set(userId, cursorElement);
  }

  removeUserCursor(userId) {
    const cursorElement = this.cursors.get(userId);
    if (cursorElement) {
      cursorElement.remove();
      this.cursors.delete(userId);
    }
  }

  updateUserCursor(userId, x, y) {
    const cursorElement = this.cursors.get(userId);
    if (!cursorElement) return;

    // Update cursor position
    cursorElement.style.left = x + 'px';
    cursorElement.style.top = y + 'px';
    cursorElement.style.display = 'block';
    
    // Update user data
    const user = this.users.get(userId);
    if (user) {
      user.cursor = { x, y, visible: true };
      user.lastSeen = Date.now();
    }
    
    // Hide cursor after inactivity
    clearTimeout(cursorElement.hideTimeout);
    cursorElement.hideTimeout = setTimeout(() => {
      cursorElement.style.display = 'none';
      if (user) {
        user.cursor.visible = false;
      }
    }, 3000);
  }

  hideUserCursor(userId) {
    const cursorElement = this.cursors.get(userId);
    if (cursorElement) {
      cursorElement.style.display = 'none';
    }
    
    const user = this.users.get(userId);
    if (user) {
      user.cursor.visible = false;
    }
  }

  generateUserName(userId) {
    // Generate a friendly name from user ID
    const adjectives = [
      'Creative', 'Artistic', 'Brilliant', 'Clever', 'Dynamic',
      'Energetic', 'Friendly', 'Gentle', 'Happy', 'Innovative',
      'Joyful', 'Kind', 'Lively', 'Magical', 'Noble',
      'Optimistic', 'Peaceful', 'Quick', 'Radiant', 'Serene'
    ];
    
    const nouns = [
      'Artist', 'Creator', 'Designer', 'Painter', 'Sketcher',
      'Illustrator', 'Doodler', 'Drawer', 'Crafter', 'Maker',
      'Builder', 'Inventor', 'Dreamer', 'Thinker', 'Visionary'
    ];
    
    // Use user ID to generate consistent name
    const hash = this.simpleHash(userId);
    const adjective = adjectives[hash % adjectives.length];
    const noun = nouns[Math.floor(hash / adjectives.length) % nouns.length];
    
    return `${adjective} ${noun}`;
  }

  simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  getNextUserColor() {
    const color = this.userColors[this.colorIndex % this.userColors.length];
    this.colorIndex++;
    return color;
  }

  getUserName(userId) {
    const user = this.users.get(userId);
    return user ? user.name : `User ${userId.slice(-4)}`;
  }

  getUserColor(userId) {
    const user = this.users.get(userId);
    return user ? user.color : '#666666';
  }

  setUserOnlineStatus(userId, isOnline) {
    const user = this.users.get(userId);
    if (!user) return;

    user.isOnline = isOnline;
    user.lastSeen = Date.now();
    
    // Update UI
    const userElement = document.querySelector(`[data-user-id="${userId}"]`);
    if (userElement) {
      const statusElement = userElement.querySelector('.user-status');
      if (statusElement) {
        statusElement.className = `user-status ${isOnline ? 'online' : 'offline'}`;
        statusElement.textContent = isOnline ? 'Online' : 'Offline';
      }
    }
    
    // Hide cursor if user goes offline
    if (!isOnline) {
      this.hideUserCursor(userId);
    }
  }

  // Get user statistics
  getUserStats() {
    const totalUsers = this.users.size;
    const onlineUsers = Array.from(this.users.values()).filter(u => u.isOnline).length;
    const offlineUsers = totalUsers - onlineUsers;
    
    return {
      total: totalUsers,
      online: onlineUsers,
      offline: offlineUsers
    };
  }

  // Get all users data
  getAllUsers() {
    return Array.from(this.users.values());
  }

  // Get online users
  getOnlineUsers() {
    return Array.from(this.users.values()).filter(user => user.isOnline);
  }

  // Check if user exists
  hasUser(userId) {
    return this.users.has(userId);
  }

  // Get user by ID
  getUser(userId) {
    return this.users.get(userId);
  }

  // Update user activity
  updateUserActivity(userId) {
    const user = this.users.get(userId);
    if (user) {
      user.lastSeen = Date.now();
    }
  }

  // Clean up inactive users (for demo purposes)
  cleanupInactiveUsers() {
    const now = Date.now();
    const inactiveThreshold = 5 * 60 * 1000; // 5 minutes
    
    this.users.forEach((user, userId) => {
      if (!user.isCurrent && (now - user.lastSeen) > inactiveThreshold) {
        this.setUserOnlineStatus(userId, false);
      }
    });
  }

  // Export users data
  exportUsersData() {
    const usersData = {
      users: this.getAllUsers(),
      exportDate: new Date().toISOString(),
      currentUser: this.currentUser
    };
    
    return usersData;
  }

  // Handle user interactions
  handleUserClick(userId) {
    const user = this.users.get(userId);
    if (!user) return;
    
    // Could implement user profile modal, private chat, etc.
    console.log('User clicked:', user);
  }

  // Cursor animation effects
  animateUserCursor(userId, effect = 'pulse') {
    const cursorElement = this.cursors.get(userId);
    if (!cursorElement) return;
    
    switch (effect) {
      case 'pulse':
        cursorElement.style.animation = 'pulse 0.5s ease-in-out';
        break;
      case 'bounce':
        cursorElement.style.animation = 'bounce 0.6s ease-in-out';
        break;
      case 'shake':
        cursorElement.style.animation = 'shake 0.5s ease-in-out';
        break;
    }
    
    // Clear animation after completion
    setTimeout(() => {
      cursorElement.style.animation = '';
    }, 1000);
  }

  // Cleanup
  destroy() {
    // Clear all users
    this.users.clear();
    
    // Remove all cursors
    this.cursors.forEach((cursor, userId) => {
      cursor.remove();
    });
    this.cursors.clear();
    
    // Close panel
    const usersPanel = document.getElementById('usersPanel');
    if (usersPanel) {
      usersPanel.classList.remove('open');
    }
    
    // Clear users list
    const usersList = document.getElementById('usersList');
    if (usersList) {
      usersList.innerHTML = '';
    }
    
    // Reset counter
    this.updateUsersCount();
  }
}

// Add cursor animation styles
const cursorStyles = document.createElement('style');
cursorStyles.textContent = `
  @keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
  }
  
  @keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
  }
  
  @keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
  }
  
  .user-cursor {
    transition: all 0.1s ease;
  }
  
  .cursor-label {
    opacity: 0;
    transition: opacity 0.2s ease;
  }
  
  .user-cursor:hover .cursor-label {
    opacity: 1;
  }
`;
document.head.appendChild(cursorStyles);

export default UsersManager;
