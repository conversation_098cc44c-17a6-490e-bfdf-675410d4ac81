{"dashboard": {"id": null, "title": "Sudoku API Dashboard", "tags": ["sudoku", "api", "monitoring"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "Request Rate", "type": "stat", "targets": [{"expr": "rate(http_requests_total[5m])", "legendFormat": "Requests/sec"}], "fieldConfig": {"defaults": {"unit": "reqps", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 50}]}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "Error Rate", "type": "stat", "targets": [{"expr": "rate(http_requests_total{status=~\"5..\"}[5m])", "legendFormat": "Errors/sec"}], "fieldConfig": {"defaults": {"unit": "reqps", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.1}, {"color": "red", "value": 1}]}}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}}, {"id": 3, "title": "Response Time (95th percentile)", "type": "stat", "targets": [{"expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))", "legendFormat": "95th percentile"}], "fieldConfig": {"defaults": {"unit": "s", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.5}, {"color": "red", "value": 1}]}}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}}, {"id": 4, "title": "Memory Usage", "type": "stat", "targets": [{"expr": "process_resident_memory_bytes / 1024 / 1024", "legendFormat": "Memory (MB)"}], "fieldConfig": {"defaults": {"unit": "MB", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 256}, {"color": "red", "value": 512}]}}}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}}, {"id": 5, "title": "HTTP Requests by Status", "type": "timeseries", "targets": [{"expr": "rate(http_requests_total[5m])", "legendFormat": "{{status}} - {{method}}"}], "fieldConfig": {"defaults": {"unit": "reqps", "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 1, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "never", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "scaleDistribution": {"type": "linear"}, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 6, "title": "Response Time Distribution", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))", "legendFormat": "50th percentile"}, {"expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.99, rate(http_request_duration_seconds_bucket[5m]))", "legendFormat": "99th percentile"}], "fieldConfig": {"defaults": {"unit": "s"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 7, "title": "Sudoku Operations", "type": "timeseries", "targets": [{"expr": "rate(sudoku_operations_total[5m])", "legendFormat": "{{operation}} - {{result}}"}], "fieldConfig": {"defaults": {"unit": "ops"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 8, "title": "Sudoku Operation Duration", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.95, rate(sudoku_operation_duration_seconds_bucket[5m]))", "legendFormat": "{{operation}} - 95th percentile"}], "fieldConfig": {"defaults": {"unit": "s"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 9, "title": "Business Metrics", "type": "stat", "targets": [{"expr": "sudoku_puzzles_solved_total", "legendFormat": "Puzzles Solved"}, {"expr": "sudoku_puzzles_generated_total", "legendFormat": "Puzzles Generated"}, {"expr": "sudoku_hints_provided_total", "legendFormat": "Hints Provided"}, {"expr": "sudoku_validation_requests_total", "legendFormat": "Validations"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}}, {"id": 10, "title": "System Resources", "type": "timeseries", "targets": [{"expr": "rate(process_cpu_seconds_total[5m]) * 100", "legendFormat": "CPU Usage %"}, {"expr": "process_resident_memory_bytes / 1024 / 1024", "legendFormat": "Memory Usage (MB)"}], "fieldConfig": {"defaults": {"unit": "percent"}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 32}}], "templating": {"list": []}, "annotations": {"list": []}, "schemaVersion": 30, "version": 1, "links": []}}