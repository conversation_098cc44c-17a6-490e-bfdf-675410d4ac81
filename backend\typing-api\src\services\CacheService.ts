/**
 * Redis cache service for high-performance data caching
 */

import { createClient, RedisClientType } from 'redis';
import { logger } from '@/utils/logger';
import { MetricsCollector } from '@/middleware/metrics';

export class CacheService {
  private client: RedisClientType | null = null;
  private config: any;
  private isHealthy: boolean = false;

  constructor(config: any) {
    this.config = config;
  }

  /**
   * Initialize Redis connection
   */
  async initialize(): Promise<void> {
    try {
      this.client = createClient({
        socket: {
          host: this.config.host,
          port: this.config.port,
        },
        password: this.config.password,
        database: this.config.db
      });

      this.client.on('error', (error) => {
        logger.error('Redis client error:', error);
        this.isHealthy = false;
      });

      this.client.on('connect', () => {
        logger.info('Redis client connected');
        this.isHealthy = true;
      });

      this.client.on('disconnect', () => {
        logger.warn('Redis client disconnected');
        this.isHealthy = false;
      });

      await this.client.connect();
      logger.info('Cache service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize cache service:', error);
      // Don't throw error - allow app to run without cache
      this.isHealthy = false;
    }
  }

  /**
   * Get value from cache
   */
  async get<T>(key: string): Promise<T | null> {
    if (!this.client || !this.isHealthy) {
      MetricsCollector.recordCacheOperation('get', 'error');
      return null;
    }

    const startTime = Date.now();
    try {
      const value = await this.client.get(key);
      const duration = Date.now() - startTime;
      
      if (value) {
        MetricsCollector.recordCacheOperation('get', 'hit');
        logger.cache('get', key, true, duration);
        return JSON.parse(value);
      } else {
        MetricsCollector.recordCacheOperation('get', 'miss');
        logger.cache('get', key, false, duration);
        return null;
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      MetricsCollector.recordCacheOperation('get', 'error');
      logger.cache('get', key, false, duration);
      logger.error('Cache get error:', error);
      return null;
    }
  }

  /**
   * Set value in cache
   */
  async set(key: string, value: any, ttlSeconds?: number): Promise<boolean> {
    if (!this.client || !this.isHealthy) {
      MetricsCollector.recordCacheOperation('set', 'error');
      return false;
    }

    const startTime = Date.now();
    try {
      const serialized = JSON.stringify(value);
      
      if (ttlSeconds) {
        await this.client.setEx(key, ttlSeconds, serialized);
      } else {
        await this.client.set(key, serialized);
      }

      const duration = Date.now() - startTime;
      MetricsCollector.recordCacheOperation('set', 'success');
      logger.cache('set', key, undefined, duration);
      return true;
    } catch (error) {
      const duration = Date.now() - startTime;
      MetricsCollector.recordCacheOperation('set', 'error');
      logger.cache('set', key, undefined, duration);
      logger.error('Cache set error:', error);
      return false;
    }
  }

  /**
   * Delete value from cache
   */
  async delete(key: string): Promise<boolean> {
    if (!this.client || !this.isHealthy) {
      MetricsCollector.recordCacheOperation('delete', 'error');
      return false;
    }

    const startTime = Date.now();
    try {
      const result = await this.client.del(key);
      const duration = Date.now() - startTime;
      
      MetricsCollector.recordCacheOperation('delete', 'success');
      logger.cache('delete', key, undefined, duration);
      return result > 0;
    } catch (error) {
      const duration = Date.now() - startTime;
      MetricsCollector.recordCacheOperation('delete', 'error');
      logger.cache('delete', key, undefined, duration);
      logger.error('Cache delete error:', error);
      return false;
    }
  }

  /**
   * Clear all cache
   */
  async clear(): Promise<boolean> {
    if (!this.client || !this.isHealthy) {
      MetricsCollector.recordCacheOperation('clear', 'error');
      return false;
    }

    try {
      await this.client.flushDb();
      MetricsCollector.recordCacheOperation('clear', 'success');
      logger.cache('clear', 'all');
      return true;
    } catch (error) {
      MetricsCollector.recordCacheOperation('clear', 'error');
      logger.error('Cache clear error:', error);
      return false;
    }
  }

  /**
   * Check if cache is healthy
   */
  isHealthy(): boolean {
    return this.isHealthy;
  }

  /**
   * Close cache connection
   */
  async close(): Promise<void> {
    if (this.client) {
      await this.client.quit();
      this.client = null;
      this.isHealthy = false;
      logger.info('Cache connection closed');
    }
  }
}
