/**
 * Enterprise logging utility using Winston
 */

import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import path from 'path';

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define colors for each level
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

// Add colors to winston
winston.addColors(colors);

// Define log format
const format = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}${
      info.splat !== undefined ? ` ${JSON.stringify(info.splat, null, 2)}` : ''
    }${
      info.stack ? `\n${info.stack}` : ''
    }`
  ),
);

// Define which transports the logger must use
const transports = [];

// Console transport
if (process.env.LOG_CONSOLE !== 'false') {
  transports.push(
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  );
}

// File transports
if (process.env.LOG_FILE === 'true' || process.env.NODE_ENV === 'production') {
  const logDir = process.env.LOG_DIR || './logs';

  // Error log file
  transports.push(
    new DailyRotateFile({
      filename: path.join(logDir, 'error-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      level: 'error',
      handleExceptions: true,
      json: true,
      maxSize: '20m',
      maxFiles: '14d',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    })
  );

  // Combined log file
  transports.push(
    new DailyRotateFile({
      filename: path.join(logDir, 'combined-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      handleExceptions: true,
      json: true,
      maxSize: '20m',
      maxFiles: '14d',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    })
  );

  // HTTP log file
  transports.push(
    new DailyRotateFile({
      filename: path.join(logDir, 'http-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      level: 'http',
      handleExceptions: false,
      json: true,
      maxSize: '20m',
      maxFiles: '7d',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    })
  );
}

// Create the logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  levels,
  format,
  transports,
  exitOnError: false,
  handleExceptions: true,
  handleRejections: true
});

// Create a stream object for Morgan HTTP logging
logger.stream = {
  write: (message: string) => {
    logger.http(message.trim());
  },
};

// Enhanced logging methods
const enhancedLogger = {
  ...logger,
  
  /**
   * Log with structured data
   */
  logWithData: (level: string, message: string, data?: any) => {
    if (data) {
      logger.log(level, message, { data });
    } else {
      logger.log(level, message);
    }
  },

  /**
   * Log performance metrics
   */
  performance: (operation: string, duration: number, metadata?: any) => {
    logger.info(`Performance: ${operation} completed in ${duration}ms`, {
      operation,
      duration,
      ...metadata
    });
  },

  /**
   * Log database operations
   */
  database: (operation: string, table: string, duration?: number, error?: any) => {
    if (error) {
      logger.error(`Database error: ${operation} on ${table}`, {
        operation,
        table,
        error: error.message,
        stack: error.stack,
        duration
      });
    } else {
      logger.debug(`Database: ${operation} on ${table}${duration ? ` (${duration}ms)` : ''}`, {
        operation,
        table,
        duration
      });
    }
  },

  /**
   * Log cache operations
   */
  cache: (operation: string, key: string, hit?: boolean, duration?: number) => {
    logger.debug(`Cache: ${operation} ${key}${hit !== undefined ? ` (${hit ? 'HIT' : 'MISS'})` : ''}${duration ? ` (${duration}ms)` : ''}`, {
      operation,
      key,
      hit,
      duration
    });
  },

  /**
   * Log API requests
   */
  api: (method: string, url: string, statusCode: number, duration: number, userId?: string) => {
    logger.http(`${method} ${url} ${statusCode} ${duration}ms`, {
      method,
      url,
      statusCode,
      duration,
      userId
    });
  },

  /**
   * Log business events
   */
  business: (event: string, userId?: string, metadata?: any) => {
    logger.info(`Business event: ${event}`, {
      event,
      userId,
      ...metadata
    });
  },

  /**
   * Log security events
   */
  security: (event: string, severity: 'low' | 'medium' | 'high' | 'critical', details?: any) => {
    const level = severity === 'critical' || severity === 'high' ? 'error' : 'warn';
    logger.log(level, `Security event: ${event}`, {
      event,
      severity,
      ...details
    });
  },

  /**
   * Log typing test events
   */
  typingTest: (event: string, testId?: string, userId?: string, metadata?: any) => {
    logger.info(`Typing test: ${event}`, {
      event,
      testId,
      userId,
      ...metadata
    });
  },

  /**
   * Log real-time events
   */
  realtime: (event: string, sessionId?: string, userId?: string, metadata?: any) => {
    logger.debug(`Real-time: ${event}`, {
      event,
      sessionId,
      userId,
      ...metadata
    });
  }
};

export { enhancedLogger as logger };
export default enhancedLogger;
