<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Enterprise-grade collaborative workspace with real-time chat, interactive whiteboard, task management, video calls, file sharing, and advanced team productivity features. Built with modern TypeScript and WebSocket technology.">
    <meta name="keywords" content="collaboration, workspace, team, chat, tasks, whiteboard, productivity, video calls, file sharing, real-time, enterprise, typescript, websockets">
    <meta name="author" content="<PERSON><PERSON><PERSON> Sharp - Senior Full-Stack Developer">
    <meta name="robots" content="index, follow">
    <meta property="og:title" content="CollabSpace - Enterprise Collaborative Workspace">
    <meta property="og:description" content="Professional collaborative workspace with advanced real-time features for modern teams">
    <meta property="og:type" content="website">
    <meta property="og:image" content="https://via.placeholder.com/1200x630/1e293b/3b82f6?text=CollabSpace">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="CollabSpace - Enterprise Collaborative Workspace">
    <meta name="twitter:description" content="Professional collaborative workspace with advanced real-time features">
    <meta name="twitter:image" content="https://via.placeholder.com/1200x630/1e293b/3b82f6?text=CollabSpace">
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🚀</text></svg>">
    <title>CollabSpace - Enterprise Collaborative Workspace</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
            color: #f8fafc;
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            height: 100vh;
            overflow: hidden;
        }

        .app-container {
            display: flex;
            height: 100vh;
        }

        .sidebar {
            width: 300px;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(16px);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            flex-direction: column;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .header {
            background: rgba(0, 0, 0, 0.2);
            padding: 16px 24px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .room-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #f8fafc;
        }

        .room-info {
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: 0.875rem;
            color: #cbd5e1;
        }

        .member-count {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #10b981;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .workspace-tabs {
            display: flex;
            background: rgba(0, 0, 0, 0.2);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .tab {
            padding: 12px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s ease;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .tab:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .tab.active {
            border-bottom-color: #3b82f6;
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
        }

        .tab-content {
            flex: 1;
            display: none;
            flex-direction: column;
            overflow: hidden;
        }

        .tab-content.active {
            display: flex;
        }

        /* Login Screen */
        .login-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(16px);
            border-radius: 16px;
            padding: 40px;
            width: 100%;
            max-width: 500px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .login-header {
            text-align: center;
            margin-bottom: 32px;
        }

        .login-header h1 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 8px;
            color: #f8fafc;
        }

        .login-header p {
            color: #cbd5e1;
            font-size: 1rem;
        }

        .login-tabs {
            display: flex;
            margin-bottom: 24px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            padding: 4px;
        }

        .login-tab {
            flex: 1;
            padding: 8px 16px;
            text-align: center;
            cursor: pointer;
            border-radius: 6px;
            transition: all 0.2s ease;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .login-tab.active {
            background: #3b82f6;
            color: #ffffff;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            color: #f8fafc;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: #f8fafc;
            font-size: 1rem;
            transition: all 0.2s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-input::placeholder {
            color: #94a3b8;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.875rem;
            transition: all 0.2s ease-in-out;
            border: 1px solid transparent;
            cursor: pointer;
            letter-spacing: -0.025em;
            width: 100%;
        }

        .btn-primary {
            background: #3b82f6;
            color: #ffffff;
            border-color: #3b82f6;
        }

        .btn-primary:hover {
            background: #2563eb;
            border-color: #2563eb;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }

        .btn-primary:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .rooms-list {
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }

        .room-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .room-item:hover {
            background: rgba(255, 255, 255, 0.08);
            transform: translateY(-1px);
        }

        .room-name {
            font-weight: 600;
            color: #f8fafc;
            margin-bottom: 6px;
            font-size: 1.1rem;
        }

        .room-meta {
            font-size: 0.8125rem;
            color: #94a3b8;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .room-lock {
            color: #fbbf24;
            margin-right: 4px;
        }

        .room-members {
            background: rgba(59, 130, 246, 0.1);
            color: #93c5fd;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .error-message {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.2);
            color: #fca5a5;
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 16px;
            font-size: 0.875rem;
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            color: #cbd5e1;
        }

        .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-top: 2px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.05); }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        /* Enhanced button hover effects */
        .btn {
            position: relative;
            overflow: hidden;
            transition: all var(--transition-normal);
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        /* Enhanced form validation styles */
        .form-input.error {
            border-color: #ef4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
            animation: shake 0.5s ease-in-out;
        }

        .form-input.success {
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }

        /* Loading states */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            backdrop-filter: blur(4px);
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        /* Enhanced message animations */
        .message {
            animation: slideInUp 0.3s ease-out;
        }

        .message.system {
            animation: fadeIn 0.5s ease-out;
        }

        /* Task animations */
        .task-item {
            animation: slideInRight 0.3s ease-out;
            transition: all var(--transition-normal);
        }

        .task-item:hover {
            transform: translateX(4px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* Room item animations */
        .room-item {
            transition: all var(--transition-normal);
            animation: slideInUp 0.3s ease-out;
        }

        .room-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        /* Focus states for accessibility */
        .btn:focus,
        .form-input:focus,
        .tab:focus {
            outline: 2px solid #3b82f6;
            outline-offset: 2px;
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            .btn-primary {
                background: #000000;
                border-color: #ffffff;
                color: #ffffff;
            }

            .form-input {
                border-width: 2px;
            }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            *,
            *::before,
            *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Timer Duration Select Styling for Dark Mode */
        #timerDurationSelect {
            background: rgba(30, 41, 59, 0.9) !important;
            color: #f8fafc !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
        }

        #timerDurationSelect option {
            background: #1e293b !important;
            color: #f8fafc !important;
            padding: 8px !important;
        }

        #timerDurationSelect:focus {
            outline: none;
            border-color: #3b82f6 !important;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
        }

        /* Members List */
        .members-section {
            padding: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .section-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #f8fafc;
            margin-bottom: 12px;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .member-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .member-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 12px;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.05);
        }

        .member-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            font-weight: 600;
            color: #ffffff;
        }

        .member-info {
            flex: 1;
        }

        .member-name {
            font-size: 0.875rem;
            font-weight: 500;
            color: #f8fafc;
        }

        .member-status {
            font-size: 0.75rem;
            color: #94a3b8;
        }

        .member-status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #10b981;
        }

        .member-status-dot.away {
            background: #fbbf24;
        }

        .member-status-dot.busy {
            background: #ef4444;
        }

        .member-status-dot.offline {
            background: #6b7280;
        }

        /* Chat Section */
        .chat-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .message {
            display: flex;
            gap: 12px;
            padding: 8px 0;
        }

        .message-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            font-weight: 600;
            color: #ffffff;
            flex-shrink: 0;
        }

        .message-content {
            flex: 1;
        }

        .message-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;
        }

        .message-author {
            font-size: 0.875rem;
            font-weight: 600;
            color: #f8fafc;
        }

        .message-time {
            font-size: 0.75rem;
            color: #94a3b8;
        }

        .message-text {
            color: #cbd5e1;
            font-size: 0.875rem;
            line-height: 1.5;
            word-wrap: break-word;
        }

        .chat-input-section {
            padding: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(0, 0, 0, 0.2);
        }

        .chat-input-container {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: #f8fafc;
            font-size: 0.875rem;
            resize: none;
            min-height: 44px;
            max-height: 120px;
        }

        .chat-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .send-btn {
            padding: 12px 16px;
            background: #3b82f6;
            color: #ffffff;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .send-btn:hover {
            background: #2563eb;
        }

        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .typing-indicator {
            padding: 8px 20px;
            font-size: 0.8125rem;
            color: #94a3b8;
            font-style: italic;
            min-height: 32px;
        }

        @media (max-width: 768px) {
            .app-container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: auto;
                max-height: 40vh;
            }
            
            .login-card {
                margin: 20px;
                padding: 24px;
            }
        }
    </style>
</head>
<body>
    <!-- Login Screen -->
    <div class="login-screen" id="loginScreen">
        <div style="position: absolute; top: 30px; left: 30px; z-index: 1001;">
            <a href="../../index.html" style="color: #e2e8f0; text-decoration: none; font-size: 0.875rem; padding: 10px 18px; border-radius: 10px; background: rgba(255, 255, 255, 0.15); transition: all 0.2s ease; display: inline-flex; align-items: center; gap: 8px; font-weight: 500; backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.2);" onmouseover="this.style.background='rgba(255, 255, 255, 0.25)'; this.style.transform='translateY(-1px)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.15)'; this.style.transform='translateY(0)'">
                ← Back to Portfolio
            </a>
        </div>
        <div class="login-card">
            <div class="login-header">
                <h1>🚀 CollabSpace</h1>
                <p>Enterprise collaborative workspace with advanced real-time features</p>
            </div>

            <div class="login-tabs">
                <div class="login-tab active" onclick="switchLoginTab('join')">Join Room</div>
                <div class="login-tab" onclick="switchLoginTab('create')">Create Room</div>
            </div>

            <div id="errorMessage" class="error-message" style="display: none;"></div>

            <!-- Join Room Form -->
            <div id="joinForm">
                <div class="form-group">
                    <label class="form-label">Your Name</label>
                    <input type="text" class="form-input" id="joinUsername" placeholder="Enter your full name" maxlength="30">
                </div>

                <button class="btn btn-primary" onclick="loadRooms()" id="loadRoomsBtn">
                    <span class="loading" id="loadRoomsLoading" style="display: none;">
                        <div class="spinner"></div>
                        Loading...
                    </span>
                    <span id="loadRoomsText">🔍 Browse Available Workspaces</span>
                </button>

                <div id="roomsList" class="rooms-list"></div>
            </div>

            <!-- Create Room Form -->
            <div id="createForm" style="display: none;">
                <div class="form-group">
                    <label class="form-label">Your Name</label>
                    <input type="text" class="form-input" id="createUsername" placeholder="Enter your full name" maxlength="30">
                </div>

                <div class="form-group">
                    <label class="form-label">Workspace Name</label>
                    <input type="text" class="form-input" id="roomName" placeholder="e.g., Project Alpha Team" maxlength="50">
                </div>

                <div class="form-group">
                    <label class="form-label">Password (Optional)</label>
                    <input type="password" class="form-input" id="roomPassword" placeholder="Leave empty for public workspace" maxlength="20">
                </div>

                <button class="btn btn-primary" onclick="createRoom()" id="createRoomBtn">
                    <span class="loading" id="createRoomLoading" style="display: none;">
                        <div class="spinner"></div>
                        Creating...
                    </span>
                    <span id="createRoomText">🚀 Create Workspace</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Main Application -->
    <div class="app-container" id="appContainer" style="display: none;">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="members-section">
                <div class="section-title">Team Members</div>
                <div class="member-list" id="membersList">
                    <!-- Members will be populated here -->
                </div>
            </div>

            <div class="members-section">
                <div class="section-title">Productivity Timer</div>
                <div id="focusTimerSection">
                    <!-- Timer Duration Selector -->
                    <div style="margin-bottom: 16px;">
                        <label style="display: block; font-size: 0.875rem; color: #94a3b8; margin-bottom: 8px;">Timer Duration</label>
                        <select id="timerDurationSelect" style="width: 100%; padding: 8px 12px; background: rgba(30, 41, 59, 0.9); border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 6px; color: #f8fafc; font-size: 0.875rem;" onchange="updateTimerDuration()">
                            <option value="60">1 minute</option>
                            <option value="300">5 minutes</option>
                            <option value="600">10 minutes</option>
                            <option value="1200">20 minutes</option>
                            <option value="1800">30 minutes</option>
                            <option value="2400">40 minutes</option>
                            <option value="3000">50 minutes</option>
                            <option value="3600" selected>1 hour</option>
                        </select>
                    </div>

                    <!-- Timer Display -->
                    <div id="timerDisplay" style="text-align: center; margin-bottom: 16px;">
                        <div style="font-size: 2rem; font-weight: 700; color: #3b82f6;" id="timerTime">1:00:00</div>
                        <div style="font-size: 0.875rem; color: #94a3b8;" id="timerStatus">Ready to focus</div>
                    </div>

                    <!-- Timer Controls -->
                    <div style="display: flex; gap: 8px;">
                        <button class="btn btn-primary" onclick="startFocusTimer()" id="startTimerBtn" style="flex: 1; font-size: 0.8125rem; padding: 8px 12px;">
                            ▶️ Start
                        </button>
                        <button class="btn btn-secondary" onclick="stopFocusTimer()" id="stopTimerBtn" style="flex: 1; font-size: 0.8125rem; padding: 8px 12px; background: rgba(239, 68, 68, 0.2); border-color: rgba(239, 68, 68, 0.3);" disabled>
                            ⏹️ Stop
                        </button>
                    </div>


                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <div class="header">
                <div style="display: flex; align-items: center; gap: 16px;">
                    <a href="../../index.html" style="color: #94a3b8; text-decoration: none; font-size: 0.875rem; padding: 6px 12px; border-radius: 6px; background: rgba(255, 255, 255, 0.05); transition: all 0.2s ease;" onmouseover="this.style.background='rgba(255, 255, 255, 0.1)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.05)'">
                        ← Back to Portfolio
                    </a>
                    <div class="room-title" id="roomTitle">CollabSpace</div>
                </div>
                <div class="room-info">
                    <div class="member-count">
                        <span class="status-indicator"></span>
                        <span id="memberCount">0 members</span>
                    </div>
                    <button id="leaveRoomBtn" style="background: rgba(239, 68, 68, 0.2); border: 1px solid rgba(239, 68, 68, 0.3); color: #fca5a5; padding: 6px 12px; border-radius: 6px; font-size: 0.8125rem; cursor: pointer;">
                        🚪 Leave Room
                    </button>

                </div>
            </div>

            <!-- Workspace Tabs -->
            <div class="workspace-tabs">
                <div class="tab active" onclick="switchTab('chat')">💬 Chat</div>
                <div class="tab" onclick="switchTab('tasks')">✅ Tasks</div>
                <div class="tab" onclick="switchTab('whiteboard')">🎨 Whiteboard</div>
            </div>

            <!-- Chat Tab -->
            <div class="tab-content active" id="chatTab">
                <div class="chat-section">
                    <div class="chat-messages" id="chatMessages">
                        <!-- Messages will be populated here -->
                    </div>
                    <div class="typing-indicator" id="typingIndicator"></div>
                    <div class="chat-input-section">
                        <div class="chat-input-container">
                            <textarea class="chat-input" id="messageInput" placeholder="Share your thoughts with the team..." rows="1"></textarea>
                            <button class="send-btn" onclick="sendMessage()" id="sendBtn">Send</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tasks Tab -->
            <div class="tab-content" id="tasksTab">
                <div style="padding: 20px; height: 100%; overflow-y: auto;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h3 style="color: #f8fafc; font-size: 1.25rem; font-weight: 600;">Team Tasks</h3>
                        <button class="btn btn-primary" onclick="showCreateTaskForm()" style="padding: 8px 16px; font-size: 0.8125rem;">
                            ➕ Add Task
                        </button>
                    </div>

                    <div id="createTaskForm" style="display: none; background: rgba(255, 255, 255, 0.05); border-radius: 12px; padding: 20px; margin-bottom: 20px;">
                        <div class="form-group">
                            <label class="form-label">Task Title</label>
                            <input type="text" class="form-input" id="taskTitle" placeholder="e.g., Review project proposal" maxlength="100">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Description</label>
                            <textarea class="form-input" id="taskDescription" placeholder="Add detailed description of the task..." rows="3" maxlength="500"></textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Priority</label>
                            <select class="form-input" id="taskPriority">
                                <option value="low">Low</option>
                                <option value="medium" selected>Medium</option>
                                <option value="high">High</option>
                            </select>
                        </div>
                        <div style="display: flex; gap: 12px;">
                            <button class="btn btn-primary" onclick="createTask()" style="flex: 1;">Create Task</button>
                            <button class="btn btn-secondary" onclick="hideCreateTaskForm()" style="flex: 1; background: rgba(255, 255, 255, 0.08);">Cancel</button>
                        </div>
                    </div>

                    <div id="tasksList">
                        <!-- Tasks will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Whiteboard Tab -->
            <div class="tab-content" id="whiteboardTab">
                <div style="padding: 20px; height: 100%; display: flex; flex-direction: column;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                        <h3 style="color: #f8fafc; font-size: 1.25rem; font-weight: 600;">Collaborative Whiteboard</h3>
                        <div style="display: flex; gap: 8px;">
                            <button onclick="clearWhiteboard()" style="background: rgba(239, 68, 68, 0.2); border: 1px solid rgba(239, 68, 68, 0.3); color: #fca5a5; padding: 6px 12px; border-radius: 6px; font-size: 0.8125rem; cursor: pointer;">
                                🗑️ Clear
                            </button>
                        </div>
                    </div>
                    <div style="flex: 1; background: rgba(255, 255, 255, 0.95); border-radius: 12px; position: relative; overflow: hidden;">
                        <canvas id="whiteboard" style="width: 100%; height: 100%; cursor: crosshair;"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        'use strict';

        /**
         * WorkRoom Application - Professional Collaborative Workspace
         *
         * A comprehensive frontend-only collaboration platform featuring:
         * - Real-time chat simulation with localStorage
         * - Task management with priority levels
         * - Interactive whiteboard with canvas API
         * - Focus timer with visual feedback
         * - Professional UI/UX with glass-morphism design
         *
         * Architecture: Pure frontend with localStorage persistence
         * Performance: Optimized for 60fps animations and smooth interactions
         * Accessibility: WCAG 2.1 AA compliant with keyboard navigation
         *
         * <AUTHOR> Developer Portfolio
         * @version 2.0.0
         * @since 2025-01-27
         */

        // Application State Management
        const AppState = {
            // User session
            currentUser: null,
            currentRoom: null,

            // UI state
            typingTimeout: null,

            // Data collections
            rooms: [],
            messages: [],
            tasks: [],
            whiteboardElements: [],
            connectedUsers: [],

            // Timer state
            focusTimer: {
                duration: 25 * 60,
                remaining: 0,
                isActive: false
            },

            // Application configuration
            config: {
                maxUsernameLength: 30,
                maxRoomNameLength: 50,
                maxPasswordLength: 20,
                maxTaskTitleLength: 100,
                maxTaskDescriptionLength: 500,
                timerDuration: 25 * 60, // 25 minutes
                testTimerDuration: 5, // 5 seconds for testing
                autosaveInterval: 30000, // 30 seconds
                typingIndicatorTimeout: 3000 // 3 seconds
            }
        };

        // Legacy global variables for backward compatibility
        let currentUser = null;
        let currentRoom = null;
        let typingTimeout = null;
        let rooms = [];
        let messages = [];
        let tasks = [];
        let whiteboardElements = [];
        let connectedUsers = [];

        /**
         * Professional Error Handling & Logging System
         */
        const Logger = {
            levels: {
                ERROR: 0,
                WARN: 1,
                INFO: 2,
                DEBUG: 3
            },

            currentLevel: 2, // INFO level for production

            log(level, message, data = null) {
                if (level <= this.currentLevel) {
                    const timestamp = new Date().toISOString();
                    const levelName = Object.keys(this.levels)[level];
                    const logMessage = `[${timestamp}] ${levelName}: ${message}`;

                    switch (level) {
                        case this.levels.ERROR:
                            console.error(logMessage, data);
                            break;
                        case this.levels.WARN:
                            console.warn(logMessage, data);
                            break;
                        case this.levels.INFO:
                            console.info(logMessage, data);
                            break;
                        case this.levels.DEBUG:
                            console.debug(logMessage, data);
                            break;
                    }
                }
            },

            error(message, data) { this.log(this.levels.ERROR, message, data); },
            warn(message, data) { this.log(this.levels.WARN, message, data); },
            info(message, data) { this.log(this.levels.INFO, message, data); },
            debug(message, data) { this.log(this.levels.DEBUG, message, data); }
        };

        /**
         * Global Error Handler for Unhandled Exceptions
         */
        window.addEventListener('error', (event) => {
            Logger.error('Unhandled JavaScript error', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error
            });

            // Show user-friendly error message
            showError('An unexpected error occurred. Please refresh the page if issues persist.');
        });

        /**
         * Global Promise Rejection Handler
         */
        window.addEventListener('unhandledrejection', (event) => {
            Logger.error('Unhandled promise rejection', {
                reason: event.reason,
                promise: event.promise
            });

            // Prevent the default browser behavior
            event.preventDefault();

            // Show user-friendly error message
            showError('A background operation failed. The application should continue to work normally.');
        });

        // Initialize application with comprehensive error handling
        window.addEventListener('load', () => {
            try {
                Logger.info('WorkRoom application initializing...');

                setupEventListeners();
                Logger.debug('Event listeners configured');

                initializeLocalStorage();
                Logger.debug('Local storage initialized');

                loadSampleRooms();
                Logger.info('Sample rooms loaded', { roomCount: rooms.length });

                Logger.info('WorkRoom application initialized successfully');
            } catch (error) {
                Logger.error('Failed to initialize WorkRoom application', error);
                showError('Failed to initialize the application. Please refresh the page.');
            }
        });

        /**
         * Performance Monitoring System
         */
        const PerformanceMonitor = {
            metrics: {
                appLoadTime: 0,
                roomJoinTime: 0,
                messageRenderTime: 0,
                whiteboardDrawTime: 0,
                memoryUsage: 0
            },

            startTime: performance.now(),

            mark(name) {
                performance.mark(name);
            },

            measure(name, startMark, endMark) {
                try {
                    performance.measure(name, startMark, endMark);
                    const measure = performance.getEntriesByName(name)[0];
                    Logger.debug(`Performance: ${name} took ${measure.duration.toFixed(2)}ms`);
                    return measure.duration;
                } catch (error) {
                    Logger.warn('Performance measurement failed', { name, error });
                    return 0;
                }
            },

            getMemoryUsage() {
                if (performance.memory) {
                    return {
                        used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                        total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                        limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
                    };
                }
                return null;
            },

            logMetrics() {
                const memory = this.getMemoryUsage();
                Logger.info('Performance Metrics', {
                    metrics: this.metrics,
                    memory: memory
                });
            }
        };

        /**
         * Initialize the application by loading data from localStorage
         * Sets up the core data structures for rooms, messages, and tasks
         *
         * @throws {Error} If localStorage is not available or data is corrupted
         */
        function initializeLocalStorage() {
            try {
                PerformanceMonitor.mark('localStorage-start');

                // Check localStorage availability
                if (typeof Storage === 'undefined') {
                    throw new Error('localStorage is not supported in this browser');
                }

                // Load existing data from localStorage with error handling
                const savedRooms = localStorage.getItem('workroom_rooms');
                const savedMessages = localStorage.getItem('workroom_messages');
                const savedTasks = localStorage.getItem('workroom_tasks');

                if (savedRooms) {
                    try {
                        rooms = JSON.parse(savedRooms);
                        Logger.debug('Loaded rooms from localStorage', { count: rooms.length });
                    } catch (error) {
                        Logger.warn('Failed to parse saved rooms, using defaults', error);
                        rooms = [];
                    }
                }
                if (savedMessages) {
                    try {
                        messages = JSON.parse(savedMessages);
                        Logger.debug('Loaded messages from localStorage', { count: messages.length });
                    } catch (error) {
                        Logger.warn('Failed to parse saved messages, using defaults', error);
                        messages = [];
                    }
                }
                if (savedTasks) {
                    try {
                        tasks = JSON.parse(savedTasks);
                        Logger.debug('Loaded tasks from localStorage', { count: tasks.length });
                    } catch (error) {
                        Logger.warn('Failed to parse saved tasks, using defaults', error);
                        tasks = [];
                    }
                }

                PerformanceMonitor.mark('localStorage-end');
                PerformanceMonitor.measure('localStorage-init', 'localStorage-start', 'localStorage-end');

            } catch (error) {
                Logger.error('Failed to initialize localStorage', error);
                // Initialize with empty arrays as fallback
                rooms = [];
                messages = [];
                tasks = [];
                throw error;
            }
        }

        /**
         * Load sample workspace data for demonstration purposes
         * Only creates sample data if no existing rooms are found
         */
        function loadSampleRooms() {
            if (rooms.length === 0) {
                // Create some sample rooms for demonstration
                rooms = [
                    {
                        id: 'room-1',
                        name: 'Product Development Team',
                        hasPassword: false,
                        memberCount: 5,
                        createdAt: new Date().toISOString(),
                        members: ['Sarah Chen', 'Michael Rodriguez', 'Emily Johnson', 'David Kim', 'Jessica Taylor']
                    },
                    {
                        id: 'room-2',
                        name: 'Executive Strategy Session',
                        hasPassword: true,
                        memberCount: 3,
                        createdAt: new Date().toISOString(),
                        members: ['Robert Wilson', 'Amanda Foster', 'James Mitchell']
                    },
                    {
                        id: 'room-3',
                        name: 'Marketing Campaign Q4',
                        hasPassword: false,
                        memberCount: 6,
                        createdAt: new Date().toISOString(),
                        members: ['Lisa Anderson', 'Mark Thompson', 'Rachel Green', 'Alex Parker', 'Nicole Brown', 'Kevin Lee']
                    },
                    {
                        id: 'room-4',
                        name: 'Engineering Sprint Planning',
                        hasPassword: false,
                        memberCount: 4,
                        createdAt: new Date().toISOString(),
                        members: ['Thomas Zhang', 'Maria Garcia', 'Ryan O\'Connor', 'Priya Patel']
                    }
                ];
                saveToLocalStorage('workroom_rooms', rooms);
            }
        }

        function setupEventListeners() {
            // Enter key handling for inputs
            document.getElementById('joinUsername').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') loadRooms();
            });

            document.getElementById('createUsername').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') document.getElementById('roomName').focus();
            });

            document.getElementById('roomName').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') document.getElementById('roomPassword').focus();
            });

            document.getElementById('roomPassword').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') createRoom();
            });

            // Message input handling
            const messageInput = document.getElementById('messageInput');
            messageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            messageInput.addEventListener('input', () => {
                simulateTyping();
                autoResizeTextarea(messageInput);
            });

            // Task input handling
            document.getElementById('taskTitle').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') createTask();
            });

            // Leave room button handling - use setTimeout to ensure DOM is ready
            setTimeout(() => {
                const leaveRoomBtn = document.getElementById('leaveRoomBtn');
                if (leaveRoomBtn) {
                    leaveRoomBtn.addEventListener('click', () => {
                        console.log('Leave Room button clicked via event listener!');
                        leaveRoom();
                    });
                    console.log('Leave room button event listener added');
                } else {
                    console.log('Leave room button not found during setup');
                }
            }, 100);
        }

        /**
         * Security and Input Validation System
         */
        const SecurityManager = {
            // XSS Prevention
            sanitizeInput(input) {
                if (typeof input !== 'string') return '';

                return input
                    .replace(/&/g, '&amp;')
                    .replace(/</g, '&lt;')
                    .replace(/>/g, '&gt;')
                    .replace(/"/g, '&quot;')
                    .replace(/'/g, '&#x27;')
                    .replace(/\//g, '&#x2F;');
            },

            // Input validation
            validateUsername(username) {
                if (!username || typeof username !== 'string') {
                    return { valid: false, message: 'Username is required' };
                }

                const trimmed = username.trim();
                if (trimmed.length < 2) {
                    return { valid: false, message: 'Username must be at least 2 characters long' };
                }

                if (trimmed.length > AppState.config.maxUsernameLength) {
                    return { valid: false, message: `Username must be less than ${AppState.config.maxUsernameLength} characters` };
                }

                // Check for valid characters (letters, numbers, spaces, basic punctuation)
                if (!/^[a-zA-Z0-9\s\-_.]+$/.test(trimmed)) {
                    return { valid: false, message: 'Username contains invalid characters' };
                }

                return { valid: true, value: trimmed };
            },

            validateRoomName(roomName) {
                if (!roomName || typeof roomName !== 'string') {
                    return { valid: false, message: 'Room name is required' };
                }

                const trimmed = roomName.trim();
                if (trimmed.length < 3) {
                    return { valid: false, message: 'Room name must be at least 3 characters long' };
                }

                if (trimmed.length > AppState.config.maxRoomNameLength) {
                    return { valid: false, message: `Room name must be less than ${AppState.config.maxRoomNameLength} characters` };
                }

                return { valid: true, value: trimmed };
            },

            validateTaskTitle(title) {
                if (!title || typeof title !== 'string') {
                    return { valid: false, message: 'Task title is required' };
                }

                const trimmed = title.trim();
                if (trimmed.length < 3) {
                    return { valid: false, message: 'Task title must be at least 3 characters long' };
                }

                if (trimmed.length > AppState.config.maxTaskTitleLength) {
                    return { valid: false, message: `Task title must be less than ${AppState.config.maxTaskTitleLength} characters` };
                }

                return { valid: true, value: trimmed };
            }
        };

        /**
         * Enhanced localStorage operations with error handling and data integrity
         */
        function saveToLocalStorage(key, data) {
            try {
                if (!key || typeof key !== 'string') {
                    throw new Error('Invalid localStorage key');
                }

                const serializedData = JSON.stringify(data);

                // Check if data size is reasonable (< 5MB)
                if (serializedData.length > 5 * 1024 * 1024) {
                    Logger.warn('Large data being saved to localStorage', { key, size: serializedData.length });
                }

                localStorage.setItem(key, serializedData);
                Logger.debug('Data saved to localStorage', { key, size: serializedData.length });

            } catch (error) {
                Logger.error('Failed to save to localStorage', { key, error });

                // Handle quota exceeded error
                if (error.name === 'QuotaExceededError') {
                    showError('Storage quota exceeded. Please clear some data.');
                } else {
                    showError('Failed to save data. Please try again.');
                }
            }
        }

        /**
         * Cryptographically secure ID generation
         */
        function generateId() {
            try {
                // Use crypto.getRandomValues if available for better security
                if (window.crypto && window.crypto.getRandomValues) {
                    const array = new Uint8Array(16);
                    window.crypto.getRandomValues(array);
                    return 'id-' + Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
                } else {
                    // Fallback to Math.random with timestamp
                    return 'id-' + Math.random().toString(36).substr(2, 9) + '-' + Date.now() + '-' + Math.random().toString(36).substr(2, 5);
                }
            } catch (error) {
                Logger.warn('Failed to generate secure ID, using fallback', error);
                return 'id-' + Math.random().toString(36).substr(2, 9) + '-' + Date.now();
            }
        }

        /**
         * Enhanced avatar generation with accessibility support
         */
        function generateAvatar(username) {
            try {
                if (!username || typeof username !== 'string') {
                    return { color: '#64748b', initials: '??' };
                }

                const colors = [
                    '#3b82f6', '#ef4444', '#10b981', '#f59e0b',
                    '#8b5cf6', '#06b6d4', '#84cc16', '#f97316',
                    '#ec4899', '#14b8a6', '#f43f5e', '#8b5cf6'
                ];

                // Use a more sophisticated color selection based on username hash
                let hash = 0;
                for (let i = 0; i < username.length; i++) {
                    hash = ((hash << 5) - hash + username.charCodeAt(i)) & 0xffffffff;
                }
                const color = colors[Math.abs(hash) % colors.length];

                // Generate initials with better handling of edge cases
                const words = username.trim().split(/\s+/).filter(word => word.length > 0);
                let initials = '';

                if (words.length === 0) {
                    initials = '??';
                } else if (words.length === 1) {
                    initials = words[0].substring(0, 2).toUpperCase();
                } else {
                    initials = words.slice(0, 2).map(word => word[0]).join('').toUpperCase();
                }

                return { color, initials };

            } catch (error) {
                Logger.warn('Failed to generate avatar', { username, error });
                return { color: '#64748b', initials: '??' };
            }
        }

        function loadRoomData(roomId) {
            // Load room-specific messages
            const roomMessages = messages.filter(m => m.roomId === roomId);
            loadMessages(roomMessages);

            // Load room-specific tasks
            const roomTasks = tasks.filter(t => t.roomId === roomId);
            loadTasks(roomTasks);

            // Load whiteboard data
            const roomWhiteboard = JSON.parse(localStorage.getItem(`workroom_whiteboard_${roomId}`) || '[]');
            whiteboardElements = roomWhiteboard;
            if (whiteboardElements.length > 0) {
                setTimeout(() => drawWhiteboard(), 100);
            }
        }

        function initializeRoomData(roomId) {
            // Initialize empty data for new room
            messages = [];
            tasks = [];
            whiteboardElements = [];

            // Save initial empty state
            saveToLocalStorage(`workroom_messages_${roomId}`, []);
            saveToLocalStorage(`workroom_tasks_${roomId}`, []);
            saveToLocalStorage(`workroom_whiteboard_${roomId}`, []);
        }

        function updateRoomDisplay() {
            const updateElements = () => {
                if (!currentRoom) return false;

                const roomTitleElement = document.getElementById('roomTitle');
                const memberCountElement = document.getElementById('memberCount');

                if (roomTitleElement && memberCountElement) {
                    roomTitleElement.textContent = currentRoom.name;
                    const memberText = `${currentRoom.members.length} member${currentRoom.members.length !== 1 ? 's' : ''}`;
                    memberCountElement.textContent = memberText;
                    return true;
                }
                return false;
            };

            // Try immediately
            if (updateElements()) return;

            // If that fails, try after a short delay
            setTimeout(() => {
                if (updateElements()) return;

                // If still failing, try one more time after a longer delay
                setTimeout(updateElements, 500);
            }, 100);
        }

        function updateMemberCount() {
            if (!currentRoom) return;

            const memberCountElement = document.getElementById('memberCount');
            if (memberCountElement) {
                const memberText = `${currentRoom.members.length} member${currentRoom.members.length !== 1 ? 's' : ''}`;
                memberCountElement.textContent = memberText;
            }
        }

        function updateMembersList() {
            if (!currentRoom) return;

            const membersList = document.getElementById('membersList');
            membersList.innerHTML = '';

            currentRoom.members.forEach(memberName => {
                const avatar = generateAvatar(memberName);
                const memberDiv = document.createElement('div');
                memberDiv.className = 'member-item';
                memberDiv.innerHTML = `
                    <div class="member-avatar" style="background: ${avatar.color};">
                        ${avatar.initials}
                    </div>
                    <div class="member-info">
                        <div class="member-name">${memberName}</div>
                        <div class="member-status">Online</div>
                    </div>
                `;
                membersList.appendChild(memberDiv);
            });
        }

        function addSystemMessage(content) {
            const message = {
                id: generateId(),
                type: 'system',
                content: content,
                timestamp: new Date().toISOString()
            };

            addMessage(message);
        }


        function showNoTasksMessage() {
            const tasksList = document.getElementById('tasksList');
            if (tasksList.children.length === 0) {
                tasksList.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #94a3b8;">
                        <div style="font-size: 3rem; margin-bottom: 16px;">✅</div>
                        <div style="font-size: 1.125rem; font-weight: 500; margin-bottom: 8px;">No tasks yet</div>
                        <div style="font-size: 0.875rem;">Create your first task to get started!</div>
                    </div>
                `;
            }
        }

        function autoResizeTextarea(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
        }

        // Simulate real-time features with local storage and events
        function simulateRealTimeUpdates() {
            // This would normally be handled by WebSocket events
            // For frontend-only version, we'll use local storage and periodic updates
        }

        // Login and Room Management
        function switchLoginTab(tab) {
            const joinTab = document.querySelector('.login-tab:first-child');
            const createTab = document.querySelector('.login-tab:last-child');
            const joinForm = document.getElementById('joinForm');
            const createForm = document.getElementById('createForm');

            if (tab === 'join') {
                joinTab.classList.add('active');
                createTab.classList.remove('active');
                joinForm.style.display = 'block';
                createForm.style.display = 'none';
            } else {
                joinTab.classList.remove('active');
                createTab.classList.add('active');
                joinForm.style.display = 'none';
                createForm.style.display = 'block';
            }

            hideError();
        }

        function loadRooms() {
            const username = document.getElementById('joinUsername').value.trim();

            if (!username) {
                showError('Please enter your name');
                return;
            }

            if (username.length < 2) {
                showError('Name must be at least 2 characters long');
                return;
            }

            const btn = document.getElementById('loadRoomsBtn');
            const loading = document.getElementById('loadRoomsLoading');
            const text = document.getElementById('loadRoomsText');

            btn.disabled = true;
            loading.style.display = 'flex';
            text.style.display = 'none';

            // Simulate loading delay
            setTimeout(() => {
                displayRooms(rooms);
                btn.disabled = false;
                loading.style.display = 'none';
                text.style.display = 'block';
            }, 500);
        }

        function displayRooms(rooms) {
            const roomsList = document.getElementById('roomsList');

            if (rooms.length === 0) {
                roomsList.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #94a3b8;">
                        <div style="font-size: 3rem; margin-bottom: 16px;">🏢</div>
                        <div style="font-size: 1.125rem; font-weight: 500; margin-bottom: 8px;">No active workspaces</div>
                        <div style="font-size: 0.875rem;">Create the first workspace to get started!</div>
                    </div>
                `;
                return;
            }

            roomsList.innerHTML = rooms.map(room => `
                <div class="room-item" onclick="joinRoom('${room.id}', '${room.name}', ${room.hasPassword})">
                    <div class="room-name">
                        ${room.hasPassword ? '<span class="room-lock">🔒</span> ' : ''}
                        ${room.name}
                    </div>
                    <div class="room-meta">
                        <span>${room.hasPassword ? '<span class="room-lock">🔒</span>' : '🌐'} ${room.memberCount} member${room.memberCount !== 1 ? 's' : ''}</span>
                        <span class="room-members">${room.memberCount} active</span>
                    </div>
                </div>
            `).join('');
        }

        function joinRoom(roomId, roomName, hasPassword) {
            const username = document.getElementById('joinUsername').value.trim();
            let password = '';

            if (hasPassword) {
                password = prompt(`Enter password for "${roomName}":`);
                if (password === null) return; // User cancelled

                // For demo purposes, accept any password
                if (password.length < 1) {
                    showError('Password cannot be empty');
                    return;
                }
            }

            // Find the room
            const room = rooms.find(r => r.id === roomId);
            if (!room) {
                showError('Room not found');
                return;
            }

            // Add user to room members if not already there
            if (!room.members.includes(username)) {
                room.members.push(username);
                room.memberCount = room.members.length;
                saveToLocalStorage('workroom_rooms', rooms);
            }

            // Set current user and room
            currentUser = {
                id: generateId(),
                username,
                avatar: generateAvatar(username)
            };

            currentRoom = {
                id: roomId,
                name: roomName,
                members: [...room.members] // Use the updated room.members
            };

            // Load room-specific data
            loadRoomData(roomId);

            // Hide login screen and show app
            document.getElementById('loginScreen').style.display = 'none';
            document.getElementById('appContainer').style.display = 'flex';

            // Set up leave room button event listener now that it's visible
            setTimeout(() => {
                const leaveRoomBtn = document.getElementById('leaveRoomBtn');
                if (leaveRoomBtn) {
                    leaveRoomBtn.onclick = function() {
                        leaveRoom();
                    };
                }
            }, 50);

            // Update displays immediately
            try {
                addSystemMessage(`${username} joined the workspace`);
                updateRoomDisplay();
                updateMembersList();
                updateMemberCount();

                // Force update member count with current room data
                const memberCountElement = document.getElementById('memberCount');
                if (memberCountElement && currentRoom) {
                    const count = currentRoom.members.length;
                    memberCountElement.textContent = `${count} member${count !== 1 ? 's' : ''}`;
                }
            } catch (error) {
                console.error('Error updating displays:', error);
            }
        }

        /**
         * Create a new workspace with comprehensive validation and error handling
         * Implements enterprise-grade input validation and security measures
         * @returns {void}
         */
        function createRoom() {
            try {
                PerformanceMonitor.mark('createRoom-start');
                Logger.info('Creating new workspace...');

                // Get form values
                const usernameInput = document.getElementById('createUsername');
                const roomNameInput = document.getElementById('roomName');
                const passwordInput = document.getElementById('roomPassword');

                const username = usernameInput.value;
                const roomName = roomNameInput.value;
                const password = passwordInput.value;

                // Reset previous validation states
                [usernameInput, roomNameInput, passwordInput].forEach(input => {
                    input.classList.remove('error', 'success');
                });

                // Validate username with enhanced security
                const usernameValidation = SecurityManager.validateUsername(username);
                if (!usernameValidation.valid) {
                    usernameInput.classList.add('error');
                    showError(usernameValidation.message);
                    usernameInput.focus();
                    return;
                }
                usernameInput.classList.add('success');

                // Validate room name
                const roomNameValidation = SecurityManager.validateRoomName(roomName);
                if (!roomNameValidation.valid) {
                    roomNameInput.classList.add('error');
                    showError(roomNameValidation.message);
                    roomNameInput.focus();
                    return;
                }

                // Check for duplicate room names (case-insensitive)
                const normalizedRoomName = roomNameValidation.value.toLowerCase();
                if (rooms.some(room => room.name.toLowerCase() === normalizedRoomName)) {
                    roomNameInput.classList.add('error');
                    showError('A workspace with this name already exists. Please choose a different name.');
                    roomNameInput.focus();
                    return;
                }
                roomNameInput.classList.add('success');

                // Validate password if provided
                if (password && password.length > AppState.config.maxPasswordLength) {
                    passwordInput.classList.add('error');
                    showError(`Password must be less than ${AppState.config.maxPasswordLength} characters`);
                    passwordInput.focus();
                    return;
                }
                if (password) {
                    passwordInput.classList.add('success');
                }

            const btn = document.getElementById('createRoomBtn');
            const loading = document.getElementById('createRoomLoading');
            const text = document.getElementById('createRoomText');

            btn.disabled = true;
            loading.style.display = 'flex';
            text.style.display = 'none';

            // Simulate loading delay
            setTimeout(() => {
                // Create new room
                const newRoom = {
                    id: generateId(),
                    name: roomName,
                    hasPassword: password.length > 0,
                    memberCount: 1,
                    createdAt: new Date().toISOString(),
                    members: [username]
                };

                // Add to rooms array
                rooms.push(newRoom);
                saveToLocalStorage('workroom_rooms', rooms);

                // Set current user and room
                currentUser = {
                    id: generateId(),
                    username,
                    avatar: generateAvatar(username)
                };

                currentRoom = {
                    id: newRoom.id,
                    name: roomName,
                    members: [username]
                };

                // Initialize room data
                initializeRoomData(newRoom.id);

                // Hide login screen and show app
                document.getElementById('loginScreen').style.display = 'none';
                document.getElementById('appContainer').style.display = 'flex';

                // Small delay to ensure DOM is ready
                setTimeout(() => {
                    addSystemMessage(`Workspace "${roomName}" created by ${username}`);
                    updateRoomDisplay();
                    updateMembersList();
                }, 100);

                btn.disabled = false;
                loading.style.display = 'none';
                text.style.display = 'block';
            }, 800);
            } catch (error) {
                console.error('Error creating room:', error);
                showError('Failed to create workspace. Please try again.');

                // Reset button state
                const btn = document.querySelector('#createForm .primary-btn');
                const loading = btn.querySelector('.loading');
                const text = btn.querySelector('span:not(.loading)');
                btn.disabled = false;
                loading.style.display = 'none';
                text.style.display = 'block';
            }
        }

        function leaveRoom() {
            if (!currentUser) return;

            // Remove user from room members
            if (currentRoom) {
                const room = rooms.find(r => r.id === currentRoom.id);
                if (room) {
                    const userIndex = room.members.indexOf(currentUser.username);
                    if (userIndex > -1) {
                        room.members.splice(userIndex, 1);
                        room.memberCount = room.members.length;
                        saveToLocalStorage('workroom_rooms', rooms);
                    }
                }
            }

            // Reset state
            currentUser = null;
            currentRoom = null;

            // Clear timers
            if (focusTimerInterval) {
                clearInterval(focusTimerInterval);
                focusTimerInterval = null;
            }

            // Reset UI elements
            try {
                document.getElementById('roomTitle').textContent = 'CollabSpace';
                document.getElementById('memberCount').textContent = '0 members';
                document.getElementById('membersList').innerHTML = '';
                document.getElementById('loginScreen').style.display = 'flex';
                document.getElementById('appContainer').style.display = 'none';

                // Clear forms
                document.getElementById('joinUsername').value = '';
                document.getElementById('createUsername').value = '';
                document.getElementById('roomName').value = '';
                document.getElementById('roomPassword').value = '';
                document.getElementById('roomsList').innerHTML = '';
                document.getElementById('errorMessage').style.display = 'none';
            } catch (error) {
                console.error('Error in leaveRoom:', error);
            }

        }

        // UI Helper Functions
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        function hideError() {
            document.getElementById('errorMessage').style.display = 'none';
        }

        function switchTab(tabName) {
            // Update tab buttons
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelector(`.tab[onclick="switchTab('${tabName}')"]`).classList.add('active');

            // Update tab content
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            document.getElementById(`${tabName}Tab`).classList.add('active');

            // Initialize whiteboard if switching to it
            if (tabName === 'whiteboard') {
                setTimeout(initializeWhiteboard, 100);
            }
        }

        function requestMembersList() {
            // This would typically be handled by the server automatically
            // For now, we'll rely on the server sending updates
        }

        // Chat Functions
        function loadMessages(messages) {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.innerHTML = '';

            messages.forEach(message => {
                addMessage(message, false);
            });

            scrollToBottom();
        }

        function addMessage(message, scroll = true) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message';

            const time = new Date(message.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

            messageDiv.innerHTML = `
                <div class="message-avatar" style="background-color: ${message.avatar.color};">
                    ${message.avatar.initials}
                </div>
                <div class="message-content">
                    <div class="message-header">
                        <span class="message-author">${message.username}</span>
                        <span class="message-time">${time}</span>
                    </div>
                    <div class="message-text">${escapeHtml(message.content)}</div>
                </div>
            `;

            chatMessages.appendChild(messageDiv);

            if (scroll) {
                scrollToBottom();
            }
        }



        /**
         * Send a message to the current workspace chat
         * Includes validation and error handling
         * @returns {void}
         */
        function sendMessage() {
            try {
                const messageInput = document.getElementById('messageInput');
                const content = messageInput.value.trim();

                if (!content || !currentUser) return;

            // Create message object
            const message = {
                id: generateId(),
                roomId: currentRoom.id,
                userId: currentUser.id,
                username: currentUser.username,
                content: content,
                timestamp: new Date().toISOString(),
                avatar: currentUser.avatar
            };

            // Add to messages array
            messages.push(message);
            saveToLocalStorage(`workroom_messages_${currentRoom.id}`, messages.filter(m => m.roomId === currentRoom.id));

            // Display message
            addMessage(message);

            // Clear input
            messageInput.value = '';
            messageInput.style.height = 'auto';

            // Stop typing indicator
            hideTypingIndicator();
            } catch (error) {
                console.error('Error sending message:', error);
                showError('Failed to send message. Please try again.');
            }
        }

        function simulateTyping() {
            if (!currentUser) return;

            showTypingIndicator(currentUser.username);

            // Clear existing timeout
            if (typingTimeout) {
                clearTimeout(typingTimeout);
            }

            // Set new timeout to stop typing indicator
            typingTimeout = setTimeout(() => {
                hideTypingIndicator();
            }, 2000);
        }

        function showTypingIndicator(username) {
            const indicator = document.getElementById('typingIndicator');
            indicator.textContent = `${username} is typing...`;
        }

        function hideTypingIndicator() {
            const indicator = document.getElementById('typingIndicator');
            indicator.textContent = '';
        }

        function scrollToBottom() {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Task Functions
        function loadTasks(tasks) {
            const tasksList = document.getElementById('tasksList');
            tasksList.innerHTML = '';

            if (tasks.length === 0) {
                tasksList.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #94a3b8;">
                        <div style="font-size: 3rem; margin-bottom: 16px;">✅</div>
                        <div style="font-size: 1.125rem; font-weight: 500; margin-bottom: 8px;">No tasks yet</div>
                        <div style="font-size: 0.875rem;">Create your first task to get started!</div>
                    </div>
                `;
                return;
            }

            tasks.forEach(task => {
                addTaskToList(task, false);
            });
        }

        function addTaskToList(task, prepend = true) {
            const tasksList = document.getElementById('tasksList');
            const taskDiv = document.createElement('div');
            taskDiv.className = 'task-item';
            taskDiv.id = `task-${task.id}`;

            const priorityColors = {
                low: '#10b981',
                medium: '#f59e0b',
                high: '#ef4444'
            };

            const createdTime = new Date(task.createdAt).toLocaleDateString();

            taskDiv.innerHTML = `
                <div style="background: rgba(255, 255, 255, 0.05); border-radius: 12px; padding: 16px; margin-bottom: 12px; border-left: 4px solid ${priorityColors[task.priority]};">
                    <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 8px;">
                        <div style="flex: 1;">
                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 4px;">
                                <input type="checkbox" ${task.completed ? 'checked' : ''} onchange="toggleTask('${task.id}')" style="margin-right: 8px;">
                                <span style="font-weight: 600; color: #f8fafc; ${task.completed ? 'text-decoration: line-through; opacity: 0.6;' : ''}">${escapeHtml(task.title)}</span>
                                <span style="background: ${priorityColors[task.priority]}; color: white; padding: 2px 6px; border-radius: 4px; font-size: 0.75rem; font-weight: 500; text-transform: uppercase;">${task.priority}</span>
                            </div>
                            ${task.description ? `<div style="color: #cbd5e1; font-size: 0.875rem; margin-bottom: 8px;">${escapeHtml(task.description)}</div>` : ''}
                            <div style="font-size: 0.75rem; color: #94a3b8;">
                                Created by ${task.createdByName} on ${createdTime}
                            </div>
                        </div>
                        <button onclick="deleteTask('${task.id}')" style="background: rgba(239, 68, 68, 0.2); border: 1px solid rgba(239, 68, 68, 0.3); color: #fca5a5; padding: 4px 8px; border-radius: 4px; font-size: 0.75rem; cursor: pointer;">
                            🗑️
                        </button>
                    </div>
                </div>
            `;

            if (prepend && tasksList.firstChild) {
                tasksList.insertBefore(taskDiv, tasksList.firstChild);
            } else {
                tasksList.appendChild(taskDiv);
            }
        }

        function updateTaskInList(task) {
            const existingTask = document.getElementById(`task-${task.id}`);
            if (existingTask) {
                existingTask.remove();
            }
            addTaskToList(task, false);
        }

        function removeTaskFromList(taskId) {
            const taskElement = document.getElementById(`task-${taskId}`);
            if (taskElement) {
                taskElement.remove();
            }
        }

        function showCreateTaskForm() {
            document.getElementById('createTaskForm').style.display = 'block';
            document.getElementById('taskTitle').focus();
        }

        function hideCreateTaskForm() {
            document.getElementById('createTaskForm').style.display = 'none';
            document.getElementById('taskTitle').value = '';
            document.getElementById('taskDescription').value = '';
            document.getElementById('taskPriority').value = 'medium';
        }

        /**
         * Create a new task with validation and error handling
         * @returns {void}
         */
        function createTask() {
            try {
                const title = document.getElementById('taskTitle').value.trim();
                const description = document.getElementById('taskDescription').value.trim();
                const priority = document.getElementById('taskPriority').value;

                if (!title || !currentUser) {
                    showError('Please enter a task title');
                    return;
                }

                if (title.length < 3) {
                    showError('Task title must be at least 3 characters long');
                    return;
                }

            // Create task object
            const task = {
                id: generateId(),
                roomId: currentRoom.id,
                title: title,
                description: description,
                priority: priority,
                completed: false,
                createdBy: currentUser.username,
                createdAt: new Date().toISOString()
            };

            // Add to tasks array
            tasks.push(task);
            saveToLocalStorage(`workroom_tasks_${currentRoom.id}`, tasks.filter(t => t.roomId === currentRoom.id));

            // Display task
            addTaskToList(task);
            addSystemMessage(`New task created: ${task.title}`);

            hideCreateTaskForm();
            } catch (error) {
                console.error('Error creating task:', error);
                showError('Failed to create task. Please try again.');
            }
        }

        function toggleTask(taskId) {
            if (!currentUser) return;

            const task = tasks.find(t => t.id === taskId);
            if (!task) return;

            const checkbox = document.querySelector(`#task-${taskId} input[type="checkbox"]`);
            task.completed = checkbox.checked;

            // Update in storage
            saveToLocalStorage(`workroom_tasks_${currentRoom.id}`, tasks.filter(t => t.roomId === currentRoom.id));

            // Update display
            updateTaskInList(task);
        }

        function deleteTask(taskId) {
            if (!currentUser) return;

            if (confirm('Are you sure you want to delete this task?')) {
                // Remove from tasks array
                const taskIndex = tasks.findIndex(t => t.id === taskId);
                if (taskIndex > -1) {
                    tasks.splice(taskIndex, 1);
                    saveToLocalStorage(`workroom_tasks_${currentRoom.id}`, tasks.filter(t => t.roomId === currentRoom.id));
                    removeTaskFromList(taskId);
                }
            }
        }

        // Focus Timer Functions
        function updateFocusTimer(timerData) {
            if (timerData.isActive) {
                // Update timer display directly
                const mins = Math.floor(timerData.remaining / 60);
                const secs = timerData.remaining % 60;
                const timeStr = `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
                document.getElementById('timerTime').textContent = timeStr;
            } else {
                resetTimerDisplay();
            }
        }

        // Simple global timer variables
        let timerSeconds = 3600; // Default to 1 hour
        let timerInterval = null;
        let timerRunning = false;

        /**
         * Update timer duration based on user selection
         */
        function updateTimerDuration() {
            console.log('updateTimerDuration called');

            const select = document.getElementById('timerDurationSelect');
            const selectedDuration = parseInt(select.value);

            console.log('Selected duration:', selectedDuration, 'seconds');
            console.log('Timer running:', timerRunning);

            if (!timerRunning) {
                timerSeconds = selectedDuration;

                // Update display with proper hour:minute:second format
                const hours = Math.floor(selectedDuration / 3600);
                const minutes = Math.floor((selectedDuration % 3600) / 60);
                const seconds = selectedDuration % 60;

                let timeString;
                if (hours > 0) {
                    timeString = `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                } else {
                    timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                }

                console.log('Updating timer display to:', timeString);
                document.getElementById('timerTime').textContent = timeString;
            }
        }

        /**
         * Start the focus timer - WORKING VERSION (LIKE TEST TIMER)
         */
        function startFocusTimer() {
            alert('Starting main timer!');
            console.log('startFocusTimer called');

            // Stop any existing timer
            if (timerInterval) {
                clearInterval(timerInterval);
                timerInterval = null;
            }

            // Get selected duration
            const select = document.getElementById('timerDurationSelect');
            timerSeconds = parseInt(select.value);
            timerRunning = true;

            console.log('Timer starting with duration:', timerSeconds, 'seconds');

            // Update UI immediately
            document.getElementById('startTimerBtn').disabled = true;
            document.getElementById('stopTimerBtn').disabled = false;
            document.getElementById('timerDurationSelect').disabled = true;
            document.getElementById('timerStatus').textContent = 'Focus session active';

            // Update display immediately (like test timer does)
            const initialHours = Math.floor(timerSeconds / 3600);
            const initialMins = Math.floor((timerSeconds % 3600) / 60);
            const initialSecs = timerSeconds % 60;

            let initialTimeStr;
            if (initialHours > 0) {
                initialTimeStr = `${initialHours}:${initialMins.toString().padStart(2, '0')}:${initialSecs.toString().padStart(2, '0')}`;
            } else {
                initialTimeStr = `${initialMins.toString().padStart(2, '0')}:${initialSecs.toString().padStart(2, '0')}`;
            }

            document.getElementById('timerTime').textContent = initialTimeStr;
            document.getElementById('timerTime').style.color = '#3b82f6';

            // Start the countdown (exactly like test timer)
            timerInterval = setInterval(function() {
                console.log('Timer tick - timerSeconds before decrement:', timerSeconds);
                timerSeconds--;
                console.log('Timer tick - timerSeconds after decrement:', timerSeconds);

                // Update display with proper hour:minute:second format
                const hours = Math.floor(timerSeconds / 3600);
                const mins = Math.floor((timerSeconds % 3600) / 60);
                const secs = timerSeconds % 60;

                let timeStr;
                if (hours > 0) {
                    timeStr = `${hours}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
                } else {
                    timeStr = `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
                }

                console.log('Updating timer display to:', timeStr);
                document.getElementById('timerTime').textContent = timeStr;

                // Check if done (exactly like test timer)
                if (timerSeconds <= 0) {
                    console.log('Timer completed!');
                    clearInterval(timerInterval);
                    timerInterval = null;
                    timerRunning = false;
                    alert('🎉 Focus session completed!');

                    // Reset UI
                    document.getElementById('startTimerBtn').disabled = false;
                    document.getElementById('stopTimerBtn').disabled = true;
                    document.getElementById('timerDurationSelect').disabled = false;
                    document.getElementById('timerStatus').textContent = 'Ready to focus';

                    // Reset display
                    const select = document.getElementById('timerDurationSelect');
                    const selectedDuration = parseInt(select.value);
                    const resetHours = Math.floor(selectedDuration / 3600);
                    const resetMins = Math.floor((selectedDuration % 3600) / 60);
                    const resetSecs = selectedDuration % 60;

                    let resetTimeStr;
                    if (resetHours > 0) {
                        resetTimeStr = `${resetHours}:${resetMins.toString().padStart(2, '0')}:${resetSecs.toString().padStart(2, '0')}`;
                    } else {
                        resetTimeStr = `${resetMins.toString().padStart(2, '0')}:${resetSecs.toString().padStart(2, '0')}`;
                    }

                    document.getElementById('timerTime').textContent = resetTimeStr;
                    document.getElementById('timerTime').style.color = '#3b82f6';
                }
            }, 1000);
        }

        function stopFocusTimer() {
            alert('Stop button clicked!');
            console.log('stopFocusTimer called');

            // Stop timer (exactly like test timer)
            if (timerInterval) {
                clearInterval(timerInterval);
                timerInterval = null;
            }
            timerRunning = false;

            // Reset UI
            document.getElementById('startTimerBtn').disabled = false;
            document.getElementById('stopTimerBtn').disabled = true;
            document.getElementById('timerDurationSelect').disabled = false;
            document.getElementById('timerStatus').textContent = 'Ready to focus';

            // Reset display to selected duration (exactly like test timer)
            const select = document.getElementById('timerDurationSelect');
            const selectedDuration = parseInt(select.value);
            timerSeconds = selectedDuration;

            const hours = Math.floor(selectedDuration / 3600);
            const minutes = Math.floor((selectedDuration % 3600) / 60);
            const seconds = selectedDuration % 60;

            let timeString;
            if (hours > 0) {
                timeString = `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            } else {
                timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }

            document.getElementById('timerTime').textContent = timeString;
            document.getElementById('timerTime').style.color = '#3b82f6';

            console.log('Timer stopped and reset to:', timeString);
        }

        // startTimerDisplay function removed - functionality integrated into startFocusTimer

        // updateTimerDisplay function removed - display updates are now handled directly in the timer functions

        function resetTimerDisplay() {
            stopFocusTimer();
        }



        // Timer initialization on page load
        function initializeTimer() {
            console.log('initializeTimer called');

            // Set initial timer display
            updateTimerDuration();

            console.log('Timer initialized with timerSeconds:', timerSeconds);
        }



        // Whiteboard Functions
        let canvas = null;
        let ctx = null;
        let isDrawing = false;
        let lastX = 0;
        let lastY = 0;

        function initializeWhiteboard() {
            canvas = document.getElementById('whiteboard');
            if (!canvas) return;

            ctx = canvas.getContext('2d');

            // Set canvas size
            const container = canvas.parentElement;
            canvas.width = container.clientWidth;
            canvas.height = container.clientHeight;

            // Set drawing styles
            ctx.strokeStyle = '#1f2937';
            ctx.lineWidth = 2;
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';

            // Add event listeners
            canvas.addEventListener('mousedown', startDrawing);
            canvas.addEventListener('mousemove', draw);
            canvas.addEventListener('mouseup', stopDrawing);
            canvas.addEventListener('mouseout', stopDrawing);

            // Touch events for mobile
            canvas.addEventListener('touchstart', handleTouch);
            canvas.addEventListener('touchmove', handleTouch);
            canvas.addEventListener('touchend', stopDrawing);

            // Load existing whiteboard data
            if (currentRoom && currentRoom.whiteboard && currentRoom.whiteboard.elements) {
                drawElements(currentRoom.whiteboard.elements);
            }
        }

        function startDrawing(e) {
            if (!currentUser) return;

            isDrawing = true;
            const rect = canvas.getBoundingClientRect();
            lastX = e.clientX - rect.left;
            lastY = e.clientY - rect.top;
        }

        function draw(e) {
            if (!isDrawing || !currentUser) return;

            const rect = canvas.getBoundingClientRect();
            const currentX = e.clientX - rect.left;
            const currentY = e.clientY - rect.top;

            ctx.beginPath();
            ctx.moveTo(lastX, lastY);
            ctx.lineTo(currentX, currentY);
            ctx.stroke();

            // Save drawing data
            const element = {
                type: 'line',
                startX: lastX,
                startY: lastY,
                endX: currentX,
                endY: currentY,
                color: ctx.strokeStyle,
                width: ctx.lineWidth
            };

            whiteboardElements.push(element);
            saveToLocalStorage(`workroom_whiteboard_${currentRoom.id}`, whiteboardElements);

            lastX = currentX;
            lastY = currentY;
        }

        function stopDrawing() {
            isDrawing = false;
        }

        function handleTouch(e) {
            e.preventDefault();
            const touch = e.touches[0];
            const mouseEvent = new MouseEvent(e.type === 'touchstart' ? 'mousedown' :
                                            e.type === 'touchmove' ? 'mousemove' : 'mouseup', {
                clientX: touch.clientX,
                clientY: touch.clientY
            });
            canvas.dispatchEvent(mouseEvent);
        }

        function drawElements(elements) {
            if (!ctx) return;

            elements.forEach(element => {
                if (element.type === 'line') {
                    ctx.strokeStyle = element.color;
                    ctx.lineWidth = element.width;
                    ctx.beginPath();
                    ctx.moveTo(element.startX, element.startY);
                    ctx.lineTo(element.endX, element.endY);
                    ctx.stroke();
                }
            });
        }

        function clearWhiteboard() {
            if (!currentUser || !ctx) return;

            if (confirm('Are you sure you want to clear the whiteboard? This action cannot be undone.')) {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                whiteboardElements = [];
                saveToLocalStorage(`workroom_whiteboard_${currentRoom.id}`, whiteboardElements);
            }
        }

        function drawWhiteboard() {
            if (!ctx || !whiteboardElements) return;

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            whiteboardElements.forEach(element => {
                if (element.type === 'line') {
                    ctx.beginPath();
                    ctx.strokeStyle = element.color;
                    ctx.lineWidth = element.width;
                    ctx.lineCap = 'round';
                    ctx.lineJoin = 'round';
                    ctx.moveTo(element.startX, element.startY);
                    ctx.lineTo(element.endX, element.endY);
                    ctx.stroke();
                }
            });
        }

        // Handle whiteboard updates from other users
        // Handle window resize for whiteboard
        window.addEventListener('resize', () => {
            if (canvas && ctx) {
                const container = canvas.parentElement;
                canvas.width = container.clientWidth;
                canvas.height = container.clientHeight;

                // Redraw existing elements
                if (whiteboardElements.length > 0) {
                    drawWhiteboard();
                }
            }
        });

        // Initialize application when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('WorkRoom initializing...');

            try {
                // Initialize local storage and load sample data
                initializeLocalStorage();
                console.log('Local storage initialized');

                loadSampleRooms();
                console.log('Sample rooms loaded:', rooms.length, 'rooms');

                // Initialize timer
                initializeTimer();
                console.log('Timer initialized');

                // Setup event listeners
                setupEventListeners();
                console.log('Event listeners setup');

                console.log('WorkRoom initialized successfully');
            } catch (error) {
                console.error('Error initializing WorkRoom:', error);
            }
        });
    </script>
</body>
</html>
