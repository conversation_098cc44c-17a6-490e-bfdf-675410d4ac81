/**
 * Real-time WebSocket service for live typing sessions
 */

import { Server as SocketIOServer, Socket } from 'socket.io';
import { logger } from '@/utils/logger';
import { MetricsCollector } from '@/middleware/metrics';
import { DatabaseService } from './DatabaseService';
import { CacheService } from './CacheService';
import { RealTimeSession, LiveTypingData } from '@/types';

export class RealTimeService {
  private io: SocketIOServer;
  private databaseService: DatabaseService;
  private cacheService: CacheService;
  private activeSessions: Map<string, RealTimeSession> = new Map();

  constructor(
    io: SocketIOServer,
    databaseService: DatabaseService,
    cacheService: CacheService
  ) {
    this.io = io;
    this.databaseService = databaseService;
    this.cacheService = cacheService;
  }

  /**
   * Initialize real-time service
   */
  initialize(): void {
    this.io.on('connection', (socket: Socket) => {
      this.handleConnection(socket);
    });

    // Clean up inactive sessions every 5 minutes
    setInterval(() => {
      this.cleanupInactiveSessions();
    }, 5 * 60 * 1000);

    logger.info('Real-time service initialized successfully');
  }

  /**
   * Handle new WebSocket connection
   */
  private handleConnection(socket: Socket): void {
    MetricsCollector.incrementWebSocketConnections();
    logger.realtime('connection', socket.id);

    // Handle session join
    socket.on('join-session', (data: { userId?: string; username: string }) => {
      this.handleJoinSession(socket, data);
    });

    // Handle typing data
    socket.on('typing-data', (data: LiveTypingData) => {
      this.handleTypingData(socket, data);
    });

    // Handle test start
    socket.on('test-start', (data: { textId: number; startTime: string }) => {
      this.handleTestStart(socket, data);
    });

    // Handle test end
    socket.on('test-end', (data: { endTime: string }) => {
      this.handleTestEnd(socket, data);
    });

    // Handle disconnect
    socket.on('disconnect', () => {
      this.handleDisconnect(socket);
    });
  }

  /**
   * Handle session join
   */
  private handleJoinSession(socket: Socket, data: { userId?: string; username: string }): void {
    const session: RealTimeSession = {
      sessionId: socket.id,
      userId: data.userId || '',
      username: data.username,
      isActive: true,
      lastActivity: new Date()
    };

    this.activeSessions.set(socket.id, session);
    socket.join('typing-room');

    // Broadcast user joined
    socket.to('typing-room').emit('user-joined', {
      sessionId: socket.id,
      username: data.username
    });

    // Send current active users
    const activeUsers = Array.from(this.activeSessions.values()).map(s => ({
      sessionId: s.sessionId,
      username: s.username,
      isTyping: !!s.currentTest
    }));

    socket.emit('active-users', activeUsers);

    logger.realtime('session-joined', socket.id, data.userId, { username: data.username });
  }

  /**
   * Handle live typing data
   */
  private handleTypingData(socket: Socket, data: LiveTypingData): void {
    const session = this.activeSessions.get(socket.id);
    if (!session) return;

    session.lastActivity = new Date();

    // Update current test data
    if (session.currentTest) {
      session.currentTest.currentPosition = data.position;
      session.currentTest.currentWpm = data.wpm;
      session.currentTest.currentAccuracy = data.accuracy;
      session.currentTest.errors = data.errors;
    }

    // Broadcast typing data to other users in the room
    socket.to('typing-room').emit('user-typing', {
      sessionId: socket.id,
      username: session.username,
      position: data.position,
      wpm: data.wpm,
      accuracy: data.accuracy,
      errors: data.errors
    });

    logger.realtime('typing-data', socket.id, session.userId, {
      position: data.position,
      wpm: data.wpm,
      accuracy: data.accuracy
    });
  }

  /**
   * Handle test start
   */
  private handleTestStart(socket: Socket, data: { textId: number; startTime: string }): void {
    const session = this.activeSessions.get(socket.id);
    if (!session) return;

    session.currentTest = {
      textId: data.textId,
      startTime: new Date(data.startTime),
      currentPosition: 0,
      currentWpm: 0,
      currentAccuracy: 100,
      errors: 0
    };

    session.lastActivity = new Date();

    // Broadcast test start
    socket.to('typing-room').emit('user-test-start', {
      sessionId: socket.id,
      username: session.username,
      textId: data.textId
    });

    logger.realtime('test-start', socket.id, session.userId, { textId: data.textId });
  }

  /**
   * Handle test end
   */
  private handleTestEnd(socket: Socket, data: { endTime: string }): void {
    const session = this.activeSessions.get(socket.id);
    if (!session || !session.currentTest) return;

    const testData = session.currentTest;
    session.currentTest = undefined;
    session.lastActivity = new Date();

    // Broadcast test end
    socket.to('typing-room').emit('user-test-end', {
      sessionId: socket.id,
      username: session.username,
      finalWpm: testData.currentWpm,
      finalAccuracy: testData.currentAccuracy
    });

    logger.realtime('test-end', socket.id, session.userId, {
      wpm: testData.currentWpm,
      accuracy: testData.currentAccuracy
    });
  }

  /**
   * Handle disconnect
   */
  private handleDisconnect(socket: Socket): void {
    const session = this.activeSessions.get(socket.id);
    
    if (session) {
      // Broadcast user left
      socket.to('typing-room').emit('user-left', {
        sessionId: socket.id,
        username: session.username
      });

      this.activeSessions.delete(socket.id);
      logger.realtime('disconnect', socket.id, session.userId, { username: session.username });
    }

    MetricsCollector.decrementWebSocketConnections();
  }

  /**
   * Clean up inactive sessions
   */
  private cleanupInactiveSessions(): void {
    const now = new Date();
    const inactiveThreshold = 30 * 60 * 1000; // 30 minutes

    for (const [sessionId, session] of this.activeSessions.entries()) {
      if (now.getTime() - session.lastActivity.getTime() > inactiveThreshold) {
        this.activeSessions.delete(sessionId);
        logger.realtime('session-cleanup', sessionId, session.userId, { 
          username: session.username,
          inactiveFor: now.getTime() - session.lastActivity.getTime()
        });
      }
    }
  }

  /**
   * Get active sessions count
   */
  getActiveSessionsCount(): number {
    return this.activeSessions.size;
  }

  /**
   * Get active sessions
   */
  getActiveSessions(): RealTimeSession[] {
    return Array.from(this.activeSessions.values());
  }
}
