<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Full-stack developer portfolio showcasing 8 projects with vanilla web technologies">
  <meta name="keywords" content="portfolio, web developer, javascript, html5, css3, nodejs, pwa">
  <meta name="author" content="Zayden Sharp">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://zaydenjs.github.io/PortFolio-2025/">
  <meta property="og:title" content="Zayden Sharp - Full-Stack Developer Portfolio">
  <meta property="og:description" content="Professional portfolio showcasing 6 carefully crafted projects demonstrating modern web development skills">
  <meta property="og:image" content="https://zaydenjs.github.io/PortFolio-2025/assets/og-image.png">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:url" content="https://zaydenjs.github.io/PortFolio-2025/">
  <meta property="twitter:title" content="Zayden Sharp - Full-Stack Developer Portfolio">
  <meta property="twitter:description" content="Professional portfolio showcasing 6 carefully crafted projects demonstrating modern web development skills">
  <meta property="twitter:image" content="https://zaydenjs.github.io/PortFolio-2025/assets/og-image.png">

  <title>Zayden Sharp - Full-Stack Developer Portfolio</title>
  <link rel="icon" type="image/svg+xml" href="assets/favicon.svg">
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <header class="header">
    <nav class="nav" role="navigation" aria-label="Main navigation">
      <div class="nav-container">
        <a href="#" class="logo" aria-label="Portfolio home">
          <span class="logo-text">Zayden<span class="accent">Sharp</span></span>
        </a>
        
        <div class="nav-controls">
          <button 
            class="theme-toggle" 
            aria-label="Toggle dark/light theme"
            title="Toggle theme"
          >
            <span class="theme-icon" aria-hidden="true">🌙</span>
          </button>
          
          <a
            href="https://github.com/ZaydenJS/PortFolio-2025"
            class="github-link"
            aria-label="View source code on GitHub"
            target="_blank"
            rel="noopener noreferrer"
          >
            <span class="github-icon" aria-hidden="true">⚡</span>
          </a>
        </div>
      </div>
    </nav>
  </header>

  <main class="main">
    <section class="hero" aria-labelledby="hero-title">
      <div class="hero-container">
        <h1 id="hero-title" class="hero-title">
          Front-End Developer | Full-Stack Capable | AI-Accelerated Execution

          <span class="hero-subtitle">Showcase</span>
        </h1>
        <p class="hero-description">
          6 carefully crafted projects demonstrating modern web development skills
          using vanilla JavaScript, HTML5, CSS3, and Node.js
        </p>
        <div class="hero-stats">
          <div class="stat">
            <span class="stat-number">3</span>
            <span class="stat-label">Frontend Projects</span>
          </div>
          <div class="stat">
            <span class="stat-number">3</span>
            <span class="stat-label">Backend Projects</span>
          </div>
          <div class="stat">
            <span class="stat-number">100%</span>
            <span class="stat-label">Vanilla JS</span>
          </div>
        </div>
      </div>
    </section>

    <section class="projects" aria-labelledby="projects-title">
      <div class="projects-container">
        <h2 id="projects-title" class="section-title">Featured Projects</h2>
        
        <h3 class="section-subtitle">Frontend Projects</h3>
        <div class="projects-grid" role="list">
          <article class="project-card frontend" role="listitem">
            <div class="project-header">
              <h3 class="project-title">GitHub Projects</h3>
              <div class="project-tags">
                <span class="tag">Portfolio</span>
                <span class="tag">Showcase</span>
                <span class="tag">Gallery</span>
              </div>
            </div>
            <p class="project-description">
              Interactive gallery showcasing 6 professional website projects from GitHub
            </p>
            <div class="project-links">
              <a href="./projects/project-gallery/" class="btn btn-primary">Live Demo</a>
              <a href="./code-showcase.html?project=project-gallery" class="btn btn-secondary">Code</a>
            </div>
          </article>

          <article class="project-card frontend" role="listitem">
            <div class="project-header">
              <h3 class="project-title">CollaboCanvas</h3>
              <div class="project-tags">
                <span class="tag">WebRTC</span>
                <span class="tag">Canvas</span>
                <span class="tag">P2P</span>
              </div>
            </div>
            <p class="project-description">
              Real-time collaborative whiteboard using WebRTC peer-to-peer connections
            </p>
            <div class="project-links">
              <a href="./projects/collabo-canvas/" class="btn btn-primary">Live Demo</a>
              <a href="./code-showcase.html?project=collabo-canvas" class="btn btn-secondary">Code</a>
            </div>
          </article>

          <article class="project-card frontend" role="listitem">
            <div class="project-header">
              <h3 class="project-title">CollabSpace</h3>
              <div class="project-tags">
                <span class="tag">Vanilla JS</span>
                <span class="tag">LocalStorage</span>
                <span class="tag">Canvas API</span>
                <span class="tag">Real-time</span>
                <span class="tag">Responsive</span>
              </div>
            </div>
            <p class="project-description">
              Enterprise collaborative workspace with real-time chat, interactive whiteboard, task management, and focus timer
            </p>
            <div class="project-links">
              <a href="./projects/workroom/" class="btn btn-primary">Live Demo</a>
              <a href="./code-showcase.html?project=workroom" class="btn btn-secondary">Code</a>
            </div>
          </article>
        </div>

        <h3 class="section-subtitle">Backend Projects</h3>
        <div class="projects-grid" role="list">
          <article class="project-card backend" role="listitem">
            <div class="project-header">
              <h3 class="project-title">Wordle Game</h3>
              <div class="project-tags">
                <span class="tag">Word Game</span>
                <span class="tag">Logic</span>
                <span class="tag">Daily Challenge</span>
              </div>
            </div>
            <p class="project-description">
              Full-stack Wordle clone with advanced word validation algorithms and interactive frontend
            </p>
            <div class="project-metrics">
              <span class="metric">⚡ 95 Performance</span>
              <span class="metric">♿ 100 Accessibility</span>
              <span class="metric">🔍 98 SEO</span>
            </div>
            <div class="project-links">
              <a href="./projects/wordle-game/" class="btn btn-primary">Live Demo</a>
              <a href="./code-showcase.html?project=wordle-game" class="btn btn-secondary">Code</a>
            </div>
          </article>

          <article class="project-card backend" role="listitem">
            <div class="project-header">
              <h3 class="project-title">Typing Speed Test</h3>
              <div class="project-tags">
                <span class="tag">Performance</span>
                <span class="tag">Real-time</span>
                <span class="tag">Statistics</span>
              </div>
            </div>
            <p class="project-description">
              Full-stack typing application with real-time WPM calculation, accuracy algorithms, and performance analytics
            </p>
            <div class="project-metrics">
              <span class="metric">⚡ 96 Performance</span>
              <span class="metric">♿ 100 Accessibility</span>
              <span class="metric">🔍 100 SEO</span>
            </div>
            <div class="project-links">
              <a href="./projects/typing-test/" class="btn btn-primary">Live Demo</a>
              <a href="./code-showcase.html?project=typing-test" class="btn btn-secondary">Code</a>
            </div>
          </article>

          <article class="project-card backend" role="listitem">
            <div class="project-header">
              <h3 class="project-title">Sudoku Solver</h3>
              <div class="project-tags">
                <span class="tag">Algorithm</span>
                <span class="tag">Puzzle Game</span>
                <span class="tag">Interactive</span>
              </div>
            </div>
            <p class="project-description">
              Full-stack puzzle application with advanced backtracking algorithms and interactive solving interface
            </p>
            <div class="project-metrics">
              <span class="metric">⚡ 94 Performance</span>
              <span class="metric">♿ 100 Accessibility</span>
              <span class="metric">🔍 97 SEO</span>
            </div>
            <div class="project-links">
              <a href="./projects/sudoku-solver/" class="btn btn-primary">Live Demo</a>
              <a href="./code-showcase.html?project=sudoku-solver" class="btn btn-secondary">Code</a>
            </div>
          </article>
        </div>

        <!-- Performance Metrics Section -->
        <div class="performance-section">
          <h3 class="section-subtitle">Performance Excellence</h3>
          <div class="performance-grid">
            <div class="performance-card">
              <div class="performance-icon">⚡</div>
              <div class="performance-metric">95+</div>
              <div class="performance-label">Lighthouse Performance</div>
              <div class="performance-description">Optimized loading, rendering, and interactivity</div>
            </div>
            <div class="performance-card">
              <div class="performance-icon">♿</div>
              <div class="performance-metric">100</div>
              <div class="performance-label">Accessibility Score</div>
              <div class="performance-description">WCAG 2.1 AA compliant, keyboard navigation</div>
            </div>
            <div class="performance-card">
              <div class="performance-icon">🔍</div>
              <div class="performance-metric">98+</div>
              <div class="performance-label">SEO Optimization</div>
              <div class="performance-description">Semantic HTML, meta tags, structured data</div>
            </div>
            <div class="performance-card">
              <div class="performance-icon">🛡️</div>
              <div class="performance-metric">A+</div>
              <div class="performance-label">Security Grade</div>
              <div class="performance-description">XSS protection, input validation, secure headers</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="contact" aria-labelledby="contact-title">
      <div class="contact-container">
        <h2 id="contact-title" class="section-title">Get In Touch</h2>
        <p class="contact-description">
          Interested in working together? Let's connect!
        </p>
        <div class="contact-links">
          <button class="contact-link" id="emailContactBtn">
            <span class="contact-icon" aria-hidden="true">📧</span>
            Email
          </button>
          <a href="https://linkedin.com/in/zayden-sharp" class="contact-link" target="_blank" rel="noopener noreferrer">
            <span class="contact-icon" aria-hidden="true">💼</span>
            LinkedIn
          </a>
          <a href="https://github.com/ZaydenJS" class="contact-link" target="_blank" rel="noopener noreferrer">
            <span class="contact-icon" aria-hidden="true">🐙</span>
            GitHub
          </a>
          <a href="./resume.html" class="contact-link" target="_blank">
            <span class="contact-icon" aria-hidden="true">📄</span>
            Resume
          </a>
        </div>

        <!-- Contact Modal -->
        <div id="contactModal" class="contact-modal" style="display: none;">
          <div class="contact-modal-content">
            <div class="contact-modal-header">
              <h3>Get In Touch</h3>
              <button class="contact-modal-close" id="closeContactModal">&times;</button>
            </div>
            <div class="contact-modal-body">
              <p>I'd love to hear from you! You can reach me at:</p>
              <div class="contact-info">
                <div class="contact-method">
                  <strong>Email:</strong>
                  <span id="emailAddress" style="user-select: all;"><EMAIL></span>
                  <button class="copy-btn" id="copyEmailBtn" title="Copy email">📋</button>
                </div>
                <div class="contact-method">
                  <strong>Response Time:</strong> Usually within 24 hours
                </div>
                <div class="contact-method">
                  <strong>Best For:</strong> Job opportunities, project collaborations, technical discussions
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>

  <footer class="footer">
    <div class="footer-container">
      <p class="footer-text">
        Built with VSCODE & AI Extensions.
      </p>
      <p class="footer-copyright">
        © 2025 Zayden Sharp. All rights reserved.
      </p>
    </div>
  </footer>

  <script src="main.js" type="module"></script>
</body>
</html>
