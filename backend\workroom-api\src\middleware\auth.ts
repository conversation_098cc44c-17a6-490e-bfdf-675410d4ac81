/**
 * Authentication and authorization middleware for collaborative workspace API
 * JWT-based authentication with role-based access control and session management
 */

import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { AuthenticationError, AuthorizationError } from '@/middleware/errorHandler';
import { logger } from '@/utils/logger';
import { UserService } from '@/services/UserService';
import { WorkspaceService } from '@/services/WorkspaceService';
import { RoomService } from '@/services/RoomService';

// Extend Request interface to include user information
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        username: string;
        email: string;
        role: string;
        workspaces: string[];
        permissions: string[];
        sessionId: string;
      };
      userId?: string;
      userRole?: string;
      workspaceRole?: string;
      roomRole?: string;
    }
  }
}

/**
 * JWT token payload interface
 */
interface TokenPayload {
  userId: string;
  username: string;
  email: string;
  role: string;
  sessionId: string;
  iat: number;
  exp: number;
}

/**
 * Authentication middleware - verifies JWT token
 */
export const authMiddleware = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    // Extract token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new AuthenticationError('No token provided');
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    if (!token) {
      throw new AuthenticationError('Invalid token format');
    }

    // Verify JWT token
    const jwtSecret = process.env.JWT_SECRET || 'workroom-super-secret-key-2024';
    const decoded = jwt.verify(token, jwtSecret) as TokenPayload;

    // Check if token is expired (additional check)
    if (decoded.exp && Date.now() >= decoded.exp * 1000) {
      throw new AuthenticationError('Token expired');
    }

    // TODO: Verify session is still active in database/cache
    // This would require UserService to be available here
    
    // Set user information in request
    req.user = {
      id: decoded.userId,
      username: decoded.username,
      email: decoded.email,
      role: decoded.role,
      workspaces: [], // Will be populated by workspace middleware if needed
      permissions: [], // Will be populated by permission middleware if needed
      sessionId: decoded.sessionId
    };

    req.userId = decoded.userId;
    req.userRole = decoded.role;

    // Log authentication success
    logger.security('User authenticated', decoded.userId, req.ip, {
      requestId: req.requestId,
      username: decoded.username,
      sessionId: decoded.sessionId,
      userAgent: req.get('User-Agent')
    });

    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      logger.security('Invalid token attempt', undefined, req.ip, {
        requestId: req.requestId,
        error: error.message,
        userAgent: req.get('User-Agent')
      });
      next(new AuthenticationError('Invalid token'));
    } else if (error instanceof jwt.TokenExpiredError) {
      logger.security('Expired token attempt', undefined, req.ip, {
        requestId: req.requestId,
        userAgent: req.get('User-Agent')
      });
      next(new AuthenticationError('Token expired'));
    } else {
      next(error);
    }
  }
};

/**
 * Optional authentication middleware - doesn't fail if no token provided
 */
export const optionalAuthMiddleware = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      // If token is provided, validate it
      await authMiddleware(req, res, next);
    } else {
      // No token provided, continue without authentication
      next();
    }
  } catch (error) {
    // If token validation fails, continue without authentication
    next();
  }
};

/**
 * Role-based authorization middleware
 */
export const requireRole = (requiredRoles: string | string[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      return next(new AuthenticationError('Authentication required'));
    }

    const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
    
    if (!roles.includes(req.user.role)) {
      logger.security('Unauthorized role access attempt', req.user.id, req.ip, {
        requestId: req.requestId,
        requiredRoles: roles,
        userRole: req.user.role,
        endpoint: req.originalUrl
      });
      return next(new AuthorizationError(`Required role: ${roles.join(' or ')}`));
    }

    next();
  };
};

/**
 * Workspace membership middleware
 */
export const requireWorkspaceMembership = (workspaceIdParam: string = 'workspaceId') => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        return next(new AuthenticationError('Authentication required'));
      }

      const workspaceId = req.params[workspaceIdParam] || req.body.workspaceId || req.query.workspaceId;
      if (!workspaceId) {
        return next(new AuthorizationError('Workspace ID required'));
      }

      // TODO: Check workspace membership using WorkspaceService
      // For now, we'll assume the user has access
      req.workspaceId = workspaceId;
      
      logger.business('Workspace access granted', 'workspace', workspaceId, {
        userId: req.user.id,
        requestId: req.requestId
      });

      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Room membership middleware
 */
export const requireRoomMembership = (roomIdParam: string = 'roomId') => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        return next(new AuthenticationError('Authentication required'));
      }

      const roomId = req.params[roomIdParam] || req.body.roomId || req.query.roomId;
      if (!roomId) {
        return next(new AuthorizationError('Room ID required'));
      }

      // TODO: Check room membership using RoomService
      // For now, we'll assume the user has access
      req.roomId = roomId;
      
      logger.business('Room access granted', 'room', roomId, {
        userId: req.user.id,
        requestId: req.requestId
      });

      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Permission-based authorization middleware
 */
export const requirePermission = (permission: string, resourceType?: string) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        return next(new AuthenticationError('Authentication required'));
      }

      // TODO: Check user permissions based on resource type and context
      // This would involve checking workspace/room specific permissions
      
      // For now, we'll do basic role-based checks
      const hasPermission = checkBasicPermission(req.user.role, permission);
      
      if (!hasPermission) {
        logger.security('Insufficient permissions', req.user.id, req.ip, {
          requestId: req.requestId,
          requiredPermission: permission,
          resourceType,
          userRole: req.user.role,
          endpoint: req.originalUrl
        });
        return next(new AuthorizationError(`Permission required: ${permission}`));
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Resource ownership middleware
 */
export const requireOwnership = (resourceType: string, ownerIdField: string = 'userId') => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        return next(new AuthenticationError('Authentication required'));
      }

      // Extract resource owner ID from request
      const ownerId = req.params[ownerIdField] || req.body[ownerIdField];
      
      // Check if user is the owner or has admin role
      if (req.user.id !== ownerId && !['admin', 'super_admin'].includes(req.user.role)) {
        logger.security('Unauthorized resource access attempt', req.user.id, req.ip, {
          requestId: req.requestId,
          resourceType,
          resourceOwnerId: ownerId,
          endpoint: req.originalUrl
        });
        return next(new AuthorizationError('Access denied: resource ownership required'));
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Rate limiting by user middleware
 */
export const userRateLimit = (maxRequests: number, windowMs: number) => {
  const userRequests = new Map<string, { count: number; resetTime: number }>();

  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      return next();
    }

    const userId = req.user.id;
    const now = Date.now();
    const userLimit = userRequests.get(userId);

    if (!userLimit || now > userLimit.resetTime) {
      // Reset or initialize user limit
      userRequests.set(userId, {
        count: 1,
        resetTime: now + windowMs
      });
      return next();
    }

    if (userLimit.count >= maxRequests) {
      logger.security('User rate limit exceeded', userId, req.ip, {
        requestId: req.requestId,
        maxRequests,
        windowMs,
        endpoint: req.originalUrl
      });
      return next(new AuthorizationError('Rate limit exceeded'));
    }

    userLimit.count++;
    next();
  };
};

/**
 * Basic permission check based on role
 */
function checkBasicPermission(role: string, permission: string): boolean {
  const rolePermissions: Record<string, string[]> = {
    'super_admin': ['*'], // All permissions
    'admin': ['read', 'write', 'delete', 'invite', 'moderate'],
    'moderator': ['read', 'write', 'moderate'],
    'member': ['read', 'write'],
    'guest': ['read'],
    'viewer': ['read']
  };

  const permissions = rolePermissions[role] || [];
  return permissions.includes('*') || permissions.includes(permission);
}

/**
 * Generate JWT token
 */
export const generateToken = (payload: Omit<TokenPayload, 'iat' | 'exp'>): string => {
  const jwtSecret = process.env.JWT_SECRET || 'workroom-super-secret-key-2024';
  const expiresIn = process.env.JWT_EXPIRES_IN || '24h';
  
  return jwt.sign(payload, jwtSecret, { expiresIn });
};

/**
 * Verify and decode JWT token
 */
export const verifyToken = (token: string): TokenPayload => {
  const jwtSecret = process.env.JWT_SECRET || 'workroom-super-secret-key-2024';
  return jwt.verify(token, jwtSecret) as TokenPayload;
};

/**
 * Refresh token middleware
 */
export const refreshTokenMiddleware = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    if (!req.user) {
      return next(new AuthenticationError('Authentication required'));
    }

    // Check if token is close to expiration (within 1 hour)
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      const decoded = jwt.decode(token) as TokenPayload;
      
      if (decoded.exp && (decoded.exp * 1000 - Date.now()) < 3600000) { // 1 hour
        // Generate new token
        const newToken = generateToken({
          userId: req.user.id,
          username: req.user.username,
          email: req.user.email,
          role: req.user.role,
          sessionId: req.user.sessionId
        });
        
        // Add new token to response headers
        res.setHeader('X-New-Token', newToken);
        
        logger.security('Token refreshed', req.user.id, req.ip, {
          requestId: req.requestId,
          sessionId: req.user.sessionId
        });
      }
    }

    next();
  } catch (error) {
    next(error);
  }
};
