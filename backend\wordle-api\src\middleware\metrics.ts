/**
 * Prometheus metrics collection middleware for comprehensive monitoring
 */

import { Request, Response, NextFunction } from 'express';
import { register, collectDefaultMetrics, Counter, Histogram, Gauge } from 'prom-client';

// Initialize default metrics collection
collectDefaultMetrics({ register });

/**
 * Metrics collector class
 */
export class MetricsCollector {
  // HTTP metrics
  private static httpRequestsTotal = new Counter({
    name: 'http_requests_total',
    help: 'Total number of HTTP requests',
    labelNames: ['method', 'route', 'status_code']
  });

  private static httpRequestDuration = new Histogram({
    name: 'http_request_duration_seconds',
    help: 'Duration of HTTP requests in seconds',
    labelNames: ['method', 'route', 'status_code'],
    buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10]
  });

  // Game-specific metrics
  private static gamesStarted = new Counter({
    name: 'wordle_games_started_total',
    help: 'Total number of Wordle games started',
    labelNames: ['difficulty', 'game_mode', 'category']
  });

  private static gamesCompleted = new Counter({
    name: 'wordle_games_completed_total',
    help: 'Total number of Wordle games completed',
    labelNames: ['difficulty', 'game_mode', 'category', 'result']
  });

  private static gameGuesses = new Histogram({
    name: 'wordle_game_guesses',
    help: 'Number of guesses per game',
    labelNames: ['difficulty', 'game_mode', 'result'],
    buckets: [1, 2, 3, 4, 5, 6, 7]
  });

  private static gameDuration = new Histogram({
    name: 'wordle_game_duration_seconds',
    help: 'Duration of Wordle games in seconds',
    labelNames: ['difficulty', 'game_mode', 'result'],
    buckets: [30, 60, 120, 300, 600, 1200, 1800]
  });

  private static activeGames = new Gauge({
    name: 'wordle_active_games',
    help: 'Number of currently active games'
  });

  private static activePlayers = new Gauge({
    name: 'wordle_active_players',
    help: 'Number of currently active players'
  });

  // Multiplayer metrics
  private static multiplayerRooms = new Gauge({
    name: 'wordle_multiplayer_rooms',
    help: 'Number of active multiplayer rooms'
  });

  private static multiplayerConnections = new Gauge({
    name: 'wordle_multiplayer_connections',
    help: 'Number of active WebSocket connections'
  });

  // Database metrics
  private static databaseQueries = new Counter({
    name: 'database_queries_total',
    help: 'Total number of database queries',
    labelNames: ['operation', 'table', 'status']
  });

  private static databaseQueryDuration = new Histogram({
    name: 'database_query_duration_seconds',
    help: 'Duration of database queries in seconds',
    labelNames: ['operation', 'table'],
    buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 2, 5]
  });

  // Cache metrics
  private static cacheOperations = new Counter({
    name: 'cache_operations_total',
    help: 'Total number of cache operations',
    labelNames: ['operation', 'status']
  });

  private static cacheHitRate = new Gauge({
    name: 'cache_hit_rate',
    help: 'Cache hit rate percentage'
  });

  /**
   * HTTP request metrics middleware
   */
  static middleware = (req: Request, res: Response, next: NextFunction): void => {
    const startTime = Date.now();

    // Override res.end to capture metrics
    const originalEnd = res.end;
    res.end = function(...args: any[]) {
      const duration = (Date.now() - startTime) / 1000;
      const route = req.route?.path || req.path;

      // Record metrics
      MetricsCollector.httpRequestsTotal
        .labels(req.method, route, res.statusCode.toString())
        .inc();

      MetricsCollector.httpRequestDuration
        .labels(req.method, route, res.statusCode.toString())
        .observe(duration);

      return originalEnd.apply(this, args);
    };

    next();
  };

  /**
   * Record game started
   */
  static recordGameStarted(difficulty: string, gameMode: string, category: string): void {
    this.gamesStarted.labels(difficulty, gameMode, category).inc();
    this.activeGames.inc();
  }

  /**
   * Record game completed
   */
  static recordGameCompleted(
    difficulty: string,
    gameMode: string,
    category: string,
    result: 'won' | 'lost',
    guesses: number,
    duration: number
  ): void {
    this.gamesCompleted.labels(difficulty, gameMode, category, result).inc();
    this.gameGuesses.labels(difficulty, gameMode, result).observe(guesses);
    this.gameDuration.labels(difficulty, gameMode, result).observe(duration / 1000);
    this.activeGames.dec();
  }

  /**
   * Update active players count
   */
  static updateActivePlayers(count: number): void {
    this.activePlayers.set(count);
  }

  /**
   * Update multiplayer room count
   */
  static updateMultiplayerRooms(count: number): void {
    this.multiplayerRooms.set(count);
  }

  /**
   * Increment WebSocket connections
   */
  static incrementWebSocketConnections(): void {
    this.multiplayerConnections.inc();
  }

  /**
   * Decrement WebSocket connections
   */
  static decrementWebSocketConnections(): void {
    this.multiplayerConnections.dec();
  }

  /**
   * Record database query
   */
  static recordDatabaseQuery(
    operation: string,
    table: string,
    duration: number,
    success: boolean
  ): void {
    const status = success ? 'success' : 'error';
    this.databaseQueries.labels(operation, table, status).inc();
    this.databaseQueryDuration.labels(operation, table).observe(duration / 1000);
  }

  /**
   * Record cache operation
   */
  static recordCacheOperation(operation: string, success: boolean): void {
    const status = success ? 'hit' : 'miss';
    this.cacheOperations.labels(operation, status).inc();
  }

  /**
   * Update cache hit rate
   */
  static updateCacheHitRate(rate: number): void {
    this.cacheHitRate.set(rate);
  }

  /**
   * Get metrics endpoint handler
   */
  static metricsEndpoint = async (req: Request, res: Response): Promise<void> => {
    try {
      res.set('Content-Type', register.contentType);
      const metrics = await register.metrics();
      res.end(metrics);
    } catch (error) {
      res.status(500).json({ error: 'Failed to collect metrics' });
    }
  };

  /**
   * Clear all metrics (useful for testing)
   */
  static clearMetrics(): void {
    register.clear();
  }
}
