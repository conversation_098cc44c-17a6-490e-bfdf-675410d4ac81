# Portfolio Testing Suite

This directory contains comprehensive tests for the portfolio application, demonstrating professional testing practices and code quality standards.

## 🧪 Testing Framework

- **Jest**: Modern JavaScript testing framework
- **JSDOM**: DOM testing environment for Node.js
- **Babel**: ES6+ transpilation for tests
- **Coverage**: Code coverage reporting and thresholds

## 📁 Test Structure

```
tests/
├── README.md           # This file
├── setup.js           # Global test configuration
├── portfolio.test.js  # Main application tests
└── utils/             # Test utilities (future)
```

## 🚀 Running Tests

### Basic Commands

```bash
# Run all tests
npm test

# Run tests in watch mode (development)
npm run test:watch

# Run tests with coverage report
npm run test:coverage

# Run tests for CI/CD (no watch, with coverage)
npm run test:ci

# Validate code quality (lint + test)
npm run validate
```

### Coverage Thresholds

The project maintains professional testing standards with coverage thresholds:

- **Branches**: 70%
- **Functions**: 70%
- **Lines**: 70%
- **Statements**: 70%

## 🎯 Test Categories

### 1. Theme Management Tests
- System preference detection
- Theme toggle functionality
- LocalStorage persistence
- Icon updates

### 2. Contact Modal Tests
- Modal open/close functionality
- Email copy functionality
- Keyboard navigation
- Accessibility features

### 3. Keyboard Navigation Tests
- Arrow key navigation
- Enter key activation
- Focus management
- Accessibility compliance

### 4. Performance Tests
- Debounce functionality
- Animation performance
- Memory management
- Error handling

### 5. Security Tests
- Input sanitization
- XSS prevention
- Form validation
- Rate limiting

## 🛠️ Test Utilities

### Global Mocks
- `localStorage` and `sessionStorage`
- `matchMedia` for responsive design
- `IntersectionObserver` for scroll animations
- `clipboard` API for copy functionality
- `performance` API for metrics

### Custom Utilities
- `testUtils.createElement()` - Create mock DOM elements
- `testUtils.fireEvent()` - Simulate user interactions
- `testUtils.waitFor()` - Async operation testing
- `testUtils.mockFetch()` - API response mocking

## 📊 Coverage Reports

Coverage reports are generated in the `coverage/` directory:

- **HTML Report**: `coverage/lcov-report/index.html`
- **LCOV Format**: `coverage/lcov.info`
- **Text Summary**: Console output

## 🔧 Configuration

### Jest Configuration (`jest.config.js`)
- Test environment: JSDOM
- Coverage thresholds: 70% minimum
- Module transformation: Babel
- Setup files: Global mocks and utilities

### Babel Configuration (`.babelrc`)
- Preset: `@babel/preset-env`
- Target: Current Node.js version
- ES6+ module support

## 🎨 Best Practices

### Test Organization
- **Describe blocks**: Group related tests
- **Clear naming**: Descriptive test names
- **Setup/Teardown**: Proper test isolation
- **Mocking**: Isolated unit testing

### Code Quality
- **DRY Principle**: Reusable test utilities
- **Assertions**: Clear and specific
- **Error Handling**: Test error scenarios
- **Performance**: Efficient test execution

### Accessibility Testing
- **ARIA attributes**: Screen reader support
- **Keyboard navigation**: Tab order and shortcuts
- **Focus management**: Proper focus handling
- **Color contrast**: Visual accessibility

## 🚀 Future Enhancements

### Planned Test Additions
- **E2E Testing**: Playwright integration
- **Visual Regression**: Screenshot comparisons
- **Performance Testing**: Lighthouse CI
- **API Testing**: Backend endpoint validation

### Integration Testing
- **Cross-browser**: Multiple browser support
- **Mobile Testing**: Responsive design validation
- **Network Conditions**: Offline/slow connection testing
- **User Flows**: Complete user journey testing

## 📈 Continuous Integration

### GitHub Actions Integration
```yaml
- name: Run Tests
  run: npm run test:ci

- name: Upload Coverage
  uses: codecov/codecov-action@v3
  with:
    file: ./coverage/lcov.info
```

### Quality Gates
- **Test Passing**: All tests must pass
- **Coverage Threshold**: Minimum 70% coverage
- **Linting**: ESLint compliance
- **Type Safety**: TypeScript validation (future)

## 🎯 Professional Standards

This testing suite demonstrates:

- **Industry Best Practices**: Modern testing patterns
- **Code Quality**: High coverage and clear tests
- **Maintainability**: Well-organized and documented
- **Scalability**: Easy to extend and modify
- **Professional Standards**: Enterprise-grade testing

## 📚 Resources

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [Testing Library](https://testing-library.com/)
- [JSDOM Documentation](https://github.com/jsdom/jsdom)
- [Accessibility Testing](https://web.dev/accessibility/)

---

**This testing suite showcases professional development practices and commitment to code quality.**
