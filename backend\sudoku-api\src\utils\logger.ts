/**
 * Professional logging system with <PERSON>
 * Provides structured logging with different transports and formats
 */

import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import { appConfig } from '@/config';

// Custom log levels
const customLevels = {
  levels: {
    error: 0,
    warn: 1,
    info: 2,
    http: 3,
    debug: 4,
  },
  colors: {
    error: 'red',
    warn: 'yellow',
    info: 'green',
    http: 'magenta',
    debug: 'blue',
  },
};

// Add colors to winston
winston.addColors(customLevels.colors);

/**
 * Custom format for development
 */
const developmentFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
    return `${timestamp} [${level}]: ${message} ${metaStr}`;
  })
);

/**
 * Custom format for production
 */
const productionFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    const logObject = {
      timestamp,
      level,
      message,
      ...(stack && { stack }),
      ...meta,
    };
    return JSON.stringify(logObject);
  })
);

/**
 * Create transports based on environment
 */
const createTransports = (): winston.transport[] => {
  const transports: winston.transport[] = [];

  // Console transport
  transports.push(
    new winston.transports.Console({
      level: appConfig.logging.level,
      format: appConfig.nodeEnv === 'production' ? productionFormat : developmentFormat,
    })
  );

  // File transports for production
  if (appConfig.nodeEnv === 'production') {
    // Error log file
    transports.push(
      new DailyRotateFile({
        filename: 'logs/error-%DATE%.log',
        datePattern: 'YYYY-MM-DD',
        level: 'error',
        format: productionFormat,
        maxFiles: appConfig.logging.maxFiles,
        maxSize: appConfig.logging.maxSize,
        zippedArchive: true,
      })
    );

    // Combined log file
    transports.push(
      new DailyRotateFile({
        filename: 'logs/combined-%DATE%.log',
        datePattern: 'YYYY-MM-DD',
        format: productionFormat,
        maxFiles: appConfig.logging.maxFiles,
        maxSize: appConfig.logging.maxSize,
        zippedArchive: true,
      })
    );

    // HTTP access log
    transports.push(
      new DailyRotateFile({
        filename: 'logs/access-%DATE%.log',
        datePattern: 'YYYY-MM-DD',
        level: 'http',
        format: productionFormat,
        maxFiles: appConfig.logging.maxFiles,
        maxSize: appConfig.logging.maxSize,
        zippedArchive: true,
      })
    );
  }

  return transports;
};

/**
 * Create the main logger instance
 */
const logger = winston.createLogger({
  levels: customLevels.levels,
  level: appConfig.logging.level,
  format: appConfig.nodeEnv === 'production' ? productionFormat : developmentFormat,
  transports: createTransports(),
  exitOnError: false,
});

/**
 * Enhanced logger with additional methods
 */
export class Logger {
  private winston: winston.Logger;

  constructor() {
    this.winston = logger;
  }

  /**
   * Log error with context
   */
  error(message: string, error?: Error | unknown, context?: Record<string, unknown>): void {
    const meta: Record<string, unknown> = { ...context };

    if (error instanceof Error) {
      meta.error = {
        name: error.name,
        message: error.message,
        stack: error.stack,
      };
    } else if (error) {
      meta.error = error;
    }

    this.winston.error(message, meta);
  }

  /**
   * Log warning with context
   */
  warn(message: string, context?: Record<string, unknown>): void {
    this.winston.warn(message, context);
  }

  /**
   * Log info with context
   */
  info(message: string, context?: Record<string, unknown>): void {
    this.winston.info(message, context);
  }

  /**
   * Log HTTP requests
   */
  http(message: string, context?: Record<string, unknown>): void {
    this.winston.log('http', message, context);
  }

  /**
   * Log debug information
   */
  debug(message: string, context?: Record<string, unknown>): void {
    this.winston.debug(message, context);
  }

  /**
   * Log API request
   */
  logRequest(req: { method: string; url: string; ip?: string }, responseTime?: number): void {
    this.http('API Request', {
      method: req.method,
      url: req.url,
      ip: req.ip,
      responseTime,
    });
  }

  /**
   * Log API response
   */
  logResponse(
    req: { method: string; url: string },
    res: { statusCode: number },
    responseTime: number
  ): void {
    this.http('API Response', {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      responseTime,
    });
  }

  /**
   * Log performance metrics
   */
  logPerformance(operation: string, duration: number, context?: Record<string, unknown>): void {
    this.info('Performance Metric', {
      operation,
      duration,
      ...context,
    });
  }

  /**
   * Log business events
   */
  logBusinessEvent(event: string, context?: Record<string, unknown>): void {
    this.info('Business Event', {
      event,
      ...context,
    });
  }

  /**
   * Log security events
   */
  logSecurity(event: string, context?: Record<string, unknown>): void {
    this.warn('Security Event', {
      event,
      ...context,
    });
  }

  /**
   * Create child logger with context
   */
  child(context: Record<string, unknown>): Logger {
    const childLogger = new Logger();
    childLogger.winston = this.winston.child(context);
    return childLogger;
  }
}

// Create singleton instance
export const log = new Logger();

/**
 * Express middleware for request logging
 */
export const requestLogger = (
  req: { method: string; url: string; ip?: string },
  res: { statusCode: number },
  next: () => void
): void => {
  const start = Date.now();

  // Log request
  log.logRequest(req);

  // Override res.end to log response
  const originalEnd = (res as any).end;
  (res as any).end = function (...args: unknown[]) {
    const responseTime = Date.now() - start;
    log.logResponse(req, res, responseTime);
    originalEnd.apply(this, args);
  };

  next();
};

export default log;
