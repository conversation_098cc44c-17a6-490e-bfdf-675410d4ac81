/**
 * FinTrack Goals Module
 * Handles financial goal management and tracking
 */

class GoalManager {
  constructor() {
    this.goals = [];
    this.categoryIcons = {
      'emergency': '🚨',
      'vacation': '🏖️',
      'house': '🏠',
      'car': '🚗',
      'education': '🎓',
      'retirement': '🏦',
      'other': '🎯'
    };
  }

  async init() {
    this.setupEventListeners();
    await this.loadGoals();
    this.renderGoals();
  }

  setupEventListeners() {
    // Goal form
    document.getElementById('goalForm')?.addEventListener('submit', (e) => {
      this.handleGoalSubmit(e);
    });

    // Cancel goal
    document.getElementById('cancelGoal')?.addEventListener('click', () => {
      this.hideGoalModal();
    });

    // Modal close
    document.getElementById('closeGoalModal')?.addEventListener('click', () => {
      this.hideGoalModal();
    });

    // Add goal button
    document.getElementById('addGoalBtn')?.addEventListener('click', () => {
      this.showGoalModal();
    });
  }

  async loadGoals() {
    try {
      this.goals = await window.finTrackDB.getGoals();
      console.log('Goals loaded:', this.goals.length);
    } catch (error) {
      console.error('Failed to load goals:', error);
      this.showNotification('Failed to load goals', 'error');
    }
  }

  renderGoals() {
    const container = document.getElementById('goalsGrid');
    if (!container) return;

    if (this.goals.length === 0) {
      container.innerHTML = `
        <div class="empty-state">
          <div class="empty-icon">🎯</div>
          <h3>No financial goals set</h3>
          <p>Set your first financial goal to start tracking your progress.</p>
          <button class="btn btn-primary" onclick="window.goalManager.showGoalModal()">
            Set Your First Goal
          </button>
        </div>
      `;
      return;
    }

    container.innerHTML = this.goals.map(goal => this.createGoalHTML(goal)).join('');

    // Add event listeners to goal cards
    container.querySelectorAll('.goal-card').forEach(card => {
      const goalId = parseInt(card.dataset.goalId);
      
      card.addEventListener('click', () => {
        this.showGoalDetails(goalId);
      });

      // Add progress update functionality
      const updateBtn = card.querySelector('.update-progress-btn');
      if (updateBtn) {
        updateBtn.addEventListener('click', (e) => {
          e.stopPropagation();
          this.showUpdateProgressModal(goalId);
        });
      }
    });
  }

  createGoalHTML(goal) {
    const icon = this.categoryIcons[goal.category] || '🎯';
    const progress = goal.target > 0 ? (goal.current / goal.target) * 100 : 0;
    const remaining = goal.target - goal.current;
    const deadline = goal.deadline ? new Date(goal.deadline).toLocaleDateString() : 'No deadline';
    
    // Calculate days remaining
    let daysRemaining = '';
    if (goal.deadline) {
      const today = new Date();
      const deadlineDate = new Date(goal.deadline);
      const diffTime = deadlineDate - today;
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      if (diffDays > 0) {
        daysRemaining = `${diffDays} days left`;
      } else if (diffDays === 0) {
        daysRemaining = 'Due today';
      } else {
        daysRemaining = `${Math.abs(diffDays)} days overdue`;
      }
    }

    // Determine progress status
    let progressStatus = 'in-progress';
    if (progress >= 100) {
      progressStatus = 'completed';
    } else if (goal.deadline && new Date(goal.deadline) < new Date()) {
      progressStatus = 'overdue';
    }

    return `
      <div class="goal-card ${progressStatus}" data-goal-id="${goal.id}">
        <div class="goal-header">
          <div class="goal-info">
            <div class="goal-icon">${icon}</div>
            <div>
              <h3 class="goal-name">${goal.name}</h3>
              <span class="goal-category">${this.formatCategoryName(goal.category)}</span>
            </div>
          </div>
          <div class="goal-actions">
            <button class="btn btn-small update-progress-btn" title="Update Progress">
              💰
            </button>
          </div>
        </div>
        
        <div class="goal-progress">
          <div class="goal-amounts">
            <span class="current-amount">$${goal.current.toFixed(2)}</span>
            <span class="target-amount">$${goal.target.toFixed(2)}</span>
          </div>
          <div class="goal-progress-bar">
            <div class="goal-progress-fill" style="width: ${Math.min(progress, 100)}%"></div>
          </div>
          <div class="goal-progress-text">
            <span class="progress-percentage">${Math.round(progress)}% complete</span>
            <span class="remaining-amount">$${remaining.toFixed(2)} remaining</span>
          </div>
        </div>
        
        <div class="goal-meta">
          <div class="goal-deadline">
            <span class="deadline-label">Target:</span>
            <span class="deadline-date">${deadline}</span>
          </div>
          ${daysRemaining ? `<div class="days-remaining ${progressStatus}">${daysRemaining}</div>` : ''}
        </div>
        
        ${progress >= 100 ? '<div class="goal-completed-badge">🎉 Goal Achieved!</div>' : ''}
      </div>
    `;
  }

  showGoalModal(goalData = null) {
    const modal = document.getElementById('goalModal');
    const form = document.getElementById('goalForm');
    
    if (!modal || !form) return;

    // Reset form
    form.reset();

    if (goalData) {
      // Edit mode
      this.populateGoalForm(goalData);
    } else {
      // Add mode - set default current amount to 0
      document.getElementById('goalCurrent').value = '0';
    }

    modal.style.display = 'flex';
    document.getElementById('goalName').focus();
  }

  hideGoalModal() {
    const modal = document.getElementById('goalModal');
    if (modal) {
      modal.style.display = 'none';
    }
  }

  populateGoalForm(goal) {
    document.getElementById('goalName').value = goal.name;
    document.getElementById('goalTarget').value = goal.target;
    document.getElementById('goalCurrent').value = goal.current;
    document.getElementById('goalDeadline').value = goal.deadline || '';
    document.getElementById('goalCategory').value = goal.category;
  }

  async handleGoalSubmit(e) {
    e.preventDefault();
    
    const goalData = {
      name: document.getElementById('goalName').value,
      target: parseFloat(document.getElementById('goalTarget').value),
      current: parseFloat(document.getElementById('goalCurrent').value) || 0,
      deadline: document.getElementById('goalDeadline').value || null,
      category: document.getElementById('goalCategory').value
    };

    try {
      await window.finTrackDB.addGoal(goalData);
      
      this.hideGoalModal();
      await this.loadGoals();
      this.renderGoals();
      
      this.showNotification('Goal created successfully!', 'success');
      
      // Register background sync if supported
      if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
        navigator.serviceWorker.ready.then(registration => {
          return registration.sync.register('sync-goals');
        });
      }
      
    } catch (error) {
      console.error('Failed to add goal:', error);
      this.showNotification('Failed to create goal', 'error');
    }
  }

  async showGoalDetails(goalId) {
    const goal = this.goals.find(g => g.id === goalId);
    if (!goal) return;

    // For now, just show edit modal
    this.showGoalModal(goal);
  }

  async showUpdateProgressModal(goalId) {
    const goal = this.goals.find(g => g.id === goalId);
    if (!goal) return;

    const newAmount = prompt(
      `Update progress for "${goal.name}"\nCurrent: $${goal.current.toFixed(2)}\nTarget: $${goal.target.toFixed(2)}\n\nEnter new current amount:`,
      goal.current.toFixed(2)
    );

    if (newAmount === null) return; // User cancelled

    const amount = parseFloat(newAmount);
    if (isNaN(amount) || amount < 0) {
      this.showNotification('Please enter a valid amount', 'error');
      return;
    }

    try {
      await window.finTrackDB.updateGoal(goalId, { current: amount });
      
      await this.loadGoals();
      this.renderGoals();
      
      // Check if goal is completed
      if (amount >= goal.target && goal.current < goal.target) {
        this.showGoalCompletedNotification(goal.name);
      }
      
      this.showNotification('Goal progress updated!', 'success');
      
    } catch (error) {
      console.error('Failed to update goal:', error);
      this.showNotification('Failed to update goal progress', 'error');
    }
  }

  async deleteGoal(goalId) {
    if (!confirm('Are you sure you want to delete this goal?')) {
      return;
    }

    try {
      await window.finTrackDB.deleteGoal(goalId);
      
      await this.loadGoals();
      this.renderGoals();
      
      this.showNotification('Goal deleted successfully!', 'success');
      
    } catch (error) {
      console.error('Failed to delete goal:', error);
      this.showNotification('Failed to delete goal', 'error');
    }
  }

  showGoalCompletedNotification(goalName) {
    // Create a special celebration notification
    const notification = document.createElement('div');
    notification.className = 'goal-completed-notification';
    notification.innerHTML = `
      <div class="celebration-content">
        <div class="celebration-icon">🎉</div>
        <div class="celebration-text">
          <h3>Congratulations!</h3>
          <p>You've achieved your goal: <strong>${goalName}</strong></p>
        </div>
      </div>
    `;
    notification.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: linear-gradient(135deg, #10b981, #059669);
      color: white;
      padding: 24px;
      border-radius: 16px;
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
      z-index: 5000;
      animation: celebrationPop 0.5s ease-out;
      text-align: center;
      min-width: 300px;
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
      notification.style.animation = 'celebrationFadeOut 0.3s ease-in';
      setTimeout(() => notification.remove(), 300);
    }, 5000);
    
    // Add click to dismiss
    notification.addEventListener('click', () => {
      notification.remove();
    });
  }

  // Analytics methods
  async getGoalAnalytics() {
    const analytics = {
      totalGoals: this.goals.length,
      completedGoals: this.goals.filter(g => g.current >= g.target).length,
      totalTargetAmount: this.goals.reduce((sum, g) => sum + g.target, 0),
      totalCurrentAmount: this.goals.reduce((sum, g) => sum + g.current, 0),
      averageProgress: 0,
      upcomingDeadlines: [],
      overdueGoals: []
    };

    if (analytics.totalGoals > 0) {
      analytics.averageProgress = (analytics.totalCurrentAmount / analytics.totalTargetAmount) * 100;
    }

    // Find upcoming deadlines (next 30 days)
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);

    this.goals.forEach(goal => {
      if (goal.deadline) {
        const deadline = new Date(goal.deadline);
        const today = new Date();
        
        if (deadline < today && goal.current < goal.target) {
          analytics.overdueGoals.push(goal);
        } else if (deadline <= thirtyDaysFromNow && deadline >= today) {
          analytics.upcomingDeadlines.push(goal);
        }
      }
    });

    return analytics;
  }

  // Utility methods
  formatCategoryName(category) {
    return category.charAt(0).toUpperCase() + category.slice(1).replace('-', ' ');
  }

  showNotification(message, type = 'info') {
    // Simple notification system
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 24px;
      border-radius: 8px;
      color: white;
      font-weight: 500;
      z-index: 4000;
      animation: slideIn 0.3s ease-out;
      background-color: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
      notification.style.animation = 'slideOut 0.3s ease-in';
      setTimeout(() => notification.remove(), 300);
    }, 3000);
  }
}

// Add celebration animations to the page
const celebrationStyles = document.createElement('style');
celebrationStyles.textContent = `
  @keyframes celebrationPop {
    0% {
      transform: translate(-50%, -50%) scale(0.5);
      opacity: 0;
    }
    50% {
      transform: translate(-50%, -50%) scale(1.1);
    }
    100% {
      transform: translate(-50%, -50%) scale(1);
      opacity: 1;
    }
  }
  
  @keyframes celebrationFadeOut {
    from {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }
    to {
      opacity: 0;
      transform: translate(-50%, -50%) scale(0.9);
    }
  }
  
  .goal-completed-badge {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    text-align: center;
    margin-top: 12px;
    animation: pulse 2s infinite;
  }
  
  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
  }
  
  .goal-card.completed {
    border-color: #10b981;
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.05), rgba(5, 150, 105, 0.05));
  }
  
  .goal-card.overdue {
    border-color: #ef4444;
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.05), rgba(220, 38, 38, 0.05));
  }
  
  .days-remaining.overdue {
    color: #ef4444;
    font-weight: 600;
  }
  
  .days-remaining.completed {
    color: #10b981;
    font-weight: 600;
  }
`;
document.head.appendChild(celebrationStyles);

// Create and export singleton instance
const goalManager = new GoalManager();

export default goalManager;
