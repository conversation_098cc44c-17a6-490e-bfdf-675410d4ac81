<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wordle - Professional Recreation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Clear Sans', 'Helvetica Neue', Arial, sans-serif;
            background-color: #ffffff;
            color: #1a1a1b;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            border-bottom: 1px solid #d3d6da;
            padding: 0 16px;
            height: 65px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }



        .title {
            font-size: 36px;
            font-weight: 700;
            letter-spacing: 0.01em;
            text-align: center;
            color: #1a1a1b;
        }

        .game-container {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-grow: 1;
            overflow: hidden;
        }

        .game {
            width: 100%;
            max-width: 500px;
            height: 100%;
            display: flex;
            flex-direction: column;
            padding: 0 16px;
        }

        .board-container {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-grow: 1;
            overflow: hidden;
        }

        .board {
            display: grid;
            grid-template-rows: repeat(6, 1fr);
            grid-gap: 5px;
            padding: 10px;
            box-sizing: border-box;
        }

        .row {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            grid-gap: 5px;
        }

        .tile {
            width: 62px;
            height: 62px;
            border: 2px solid #d3d6da;
            display: inline-flex;
            justify-content: center;
            align-items: center;
            font-size: 2rem;
            line-height: 2rem;
            font-weight: bold;
            vertical-align: middle;
            box-sizing: border-box;
            color: #1a1a1b;
            text-transform: uppercase;
            user-select: none;
            transition: all 0.2s ease-in-out;
        }

        .tile[data-state="empty"] {
            border: 2px solid #d3d6da;
        }

        .tile[data-state="tbd"] {
            background-color: #ffffff;
            border: 2px solid #878a8c;
            color: #1a1a1b;
        }

        .tile[data-state="correct"] {
            background-color: #6aaa64;
            border: 2px solid #6aaa64;
            color: #ffffff;
        }

        .tile[data-state="present"] {
            background-color: #c9b458;
            border: 2px solid #c9b458;
            color: #ffffff;
        }

        .tile[data-state="absent"] {
            background-color: #787c7e;
            border: 2px solid #787c7e;
            color: #ffffff;
        }

        .tile[data-animation="pop"] {
            animation: PopIn 100ms;
        }

        @keyframes PopIn {
            from {
                transform: scale(0.8);
                opacity: 0;
            }
            40% {
                transform: scale(1.1);
                opacity: 1;
            }
            to {
                transform: scale(1);
                opacity: 1;
            }
        }

        .tile[data-animation="flip-in"] {
            animation: FlipIn 250ms ease-in forwards;
        }

        @keyframes FlipIn {
            0% {
                transform: rotateX(0);
            }
            50% {
                transform: rotateX(-90deg);
            }
            100% {
                transform: rotateX(0);
            }
        }

        .keyboard {
            margin: 0 8px;
            user-select: none;
        }

        .keyboard-row {
            display: flex;
            width: 100%;
            margin: 0 auto 8px;
            touch-action: manipulation;
        }

        .keyboard-row button {
            font-family: inherit;
            font-weight: bold;
            border: 0;
            padding: 0;
            margin: 0 6px 0 0;
            height: 58px;
            border-radius: 4px;
            cursor: pointer;
            user-select: none;
            background-color: #d3d6da;
            color: #1a1a1b;
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            text-transform: uppercase;
            -webkit-tap-highlight-color: rgba(0,0,0,0.3);
            font-size: 14px;
            transition: all 0.1s ease;
        }

        .keyboard-row button:hover {
            background-color: #bbbfc4;
        }

        .keyboard-row button[data-state="correct"] {
            background-color: #6aaa64;
            color: #ffffff;
        }

        .keyboard-row button[data-state="present"] {
            background-color: #c9b458;
            color: #ffffff;
        }

        .keyboard-row button[data-state="absent"] {
            background-color: #787c7e;
            color: #ffffff;
        }

        .keyboard-row button.wide-button {
            flex: 1.5;
            font-size: 12px;
        }

        .keyboard-row button:last-of-type {
            margin: 0;
        }

        .game-status {
            text-align: center;
            margin: 20px 0;
            font-size: 18px;
            font-weight: bold;
        }

        .game-controls {
            text-align: center;
            margin: 20px 0;
        }

        .new-game-btn {
            background-color: #6aaa64;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.1s ease;
        }

        .new-game-btn:hover {
            background-color: #5a9a54;
        }

        .api-info {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
            font-size: 14px;
            color: #6c757d;
            text-align: center;
        }

        @media (max-width: 480px) {
            .tile {
                width: 56px;
                height: 56px;
                font-size: 1.8rem;
            }

            .keyboard-row button {
                height: 52px;
                font-size: 12px;
            }

            .title {
                font-size: 28px;
            }


        }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">Wordle</div>
    </div>

    <div class="game-container">
        <div class="game">
            <div class="board-container">
                <div class="board" id="board">
                    <!-- 6 rows of 5 tiles each -->
                    <div class="row">
                        <div class="tile" data-state="empty"></div>
                        <div class="tile" data-state="empty"></div>
                        <div class="tile" data-state="empty"></div>
                        <div class="tile" data-state="empty"></div>
                        <div class="tile" data-state="empty"></div>
                    </div>
                    <div class="row">
                        <div class="tile" data-state="empty"></div>
                        <div class="tile" data-state="empty"></div>
                        <div class="tile" data-state="empty"></div>
                        <div class="tile" data-state="empty"></div>
                        <div class="tile" data-state="empty"></div>
                    </div>
                    <div class="row">
                        <div class="tile" data-state="empty"></div>
                        <div class="tile" data-state="empty"></div>
                        <div class="tile" data-state="empty"></div>
                        <div class="tile" data-state="empty"></div>
                        <div class="tile" data-state="empty"></div>
                    </div>
                    <div class="row">
                        <div class="tile" data-state="empty"></div>
                        <div class="tile" data-state="empty"></div>
                        <div class="tile" data-state="empty"></div>
                        <div class="tile" data-state="empty"></div>
                        <div class="tile" data-state="empty"></div>
                    </div>
                    <div class="row">
                        <div class="tile" data-state="empty"></div>
                        <div class="tile" data-state="empty"></div>
                        <div class="tile" data-state="empty"></div>
                        <div class="tile" data-state="empty"></div>
                        <div class="tile" data-state="empty"></div>
                    </div>
                    <div class="row">
                        <div class="tile" data-state="empty"></div>
                        <div class="tile" data-state="empty"></div>
                        <div class="tile" data-state="empty"></div>
                        <div class="tile" data-state="empty"></div>
                        <div class="tile" data-state="empty"></div>
                    </div>
                </div>
            </div>

            <div class="game-status" id="gameStatus"></div>

            <div class="game-controls">
                <button class="new-game-btn" onclick="startNewGame()">New Game</button>
            </div>

            <div class="api-info">
                Powered by Professional Node.js API • Real-time Backend Integration
            </div>

            <div class="keyboard" id="keyboard">
                <div class="keyboard-row">
                    <button data-key="q">q</button>
                    <button data-key="w">w</button>
                    <button data-key="e">e</button>
                    <button data-key="r">r</button>
                    <button data-key="t">t</button>
                    <button data-key="y">y</button>
                    <button data-key="u">u</button>
                    <button data-key="i">i</button>
                    <button data-key="o">o</button>
                    <button data-key="p">p</button>
                </div>
                <div class="keyboard-row">
                    <button data-key="a">a</button>
                    <button data-key="s">s</button>
                    <button data-key="d">d</button>
                    <button data-key="f">f</button>
                    <button data-key="g">g</button>
                    <button data-key="h">h</button>
                    <button data-key="j">j</button>
                    <button data-key="k">k</button>
                    <button data-key="l">l</button>
                </div>
                <div class="keyboard-row">
                    <button data-key="enter" class="wide-button">enter</button>
                    <button data-key="z">z</button>
                    <button data-key="x">x</button>
                    <button data-key="c">c</button>
                    <button data-key="v">v</button>
                    <button data-key="b">b</button>
                    <button data-key="n">n</button>
                    <button data-key="m">m</button>
                    <button data-key="backspace" class="wide-button">⌫</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Game state
        let currentGameId = null;
        let gameState = null;
        let currentRow = 0;
        let currentTile = 0;
        let currentGuess = '';
        let gameOver = false;

        // Initialize game
        document.addEventListener('DOMContentLoaded', function() {
            initializeKeyboard();
            startNewGame();
        });

        function initializeKeyboard() {
            const keyboard = document.getElementById('keyboard');
            keyboard.addEventListener('click', handleKeyboardClick);
            document.addEventListener('keydown', handleKeyPress);
        }

        function handleKeyboardClick(e) {
            if (e.target.matches('button')) {
                const key = e.target.getAttribute('data-key');
                handleInput(key);
            }
        }

        function handleKeyPress(e) {
            if (gameOver) return;

            // Ignore modifier keys and other special keys
            if (e.ctrlKey || e.metaKey || e.altKey) return;

            const key = e.key.toLowerCase();

            // Filter out modifier keys specifically
            const modifierKeys = ['control', 'shift', 'alt', 'meta', 'capslock', 'tab', 'escape'];
            if (modifierKeys.includes(key)) return;

            if (key === 'enter') {
                handleInput('enter');
            } else if (key === 'backspace') {
                handleInput('backspace');
            } else if (key.match(/^[a-z]$/) && key.length === 1) {
                handleInput(key);
            }
        }

        function handleInput(key) {
            if (gameOver) return;

            if (key === 'enter') {
                if (currentTile === 5) {
                    submitGuess();
                }
            } else if (key === 'backspace') {
                if (currentTile > 0) {
                    currentTile--;
                    currentGuess = currentGuess.slice(0, -1);
                    updateTile(currentRow, currentTile, '');
                }
            } else if (currentTile < 5) {
                currentGuess += key.toUpperCase();
                updateTile(currentRow, currentTile, key.toUpperCase());
                currentTile++;
            }
        }

        function updateTile(row, col, letter) {
            const tile = document.querySelectorAll('.row')[row].children[col];
            tile.textContent = letter;
            tile.setAttribute('data-state', letter ? 'tbd' : 'empty');

            if (letter) {
                tile.setAttribute('data-animation', 'pop');
                setTimeout(() => tile.removeAttribute('data-animation'), 100);
            }
        }

        async function submitGuess() {
            if (!currentGameId || currentGuess.length !== 5) return;

            try {
                const response = await fetch(`/api/game/${currentGameId}/guess`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ word: currentGuess })
                });

                const data = await response.json();

                if (data.success) {
                    gameState = data.gameState;
                    animateGuessResult(data.guess);
                    updateKeyboard(data.guess);

                    setTimeout(() => {
                        if (gameState.gameState === 'won') {
                            showGameStatus('🎉 Congratulations! You found the word!');
                            gameOver = true;
                        } else if (gameState.gameState === 'lost') {
                            showGameStatus(`😞 Game Over! The word was: ${gameState.targetWord || 'UNKNOWN'}`);
                            gameOver = true;
                        } else {
                            const remaining = gameState.maxGuesses - gameState.guesses.length;
                            showGameStatus(`${remaining} guesses remaining`);
                            nextRow();
                        }
                    }, 1500);
                } else {
                    showGameStatus('❌ ' + (data.error || 'Invalid word'));
                    shakeRow();
                }
            } catch (error) {
                showGameStatus('❌ Error: ' + error.message);
                shakeRow();
            }
        }

        function animateGuessResult(guess) {
            const row = document.querySelectorAll('.row')[currentRow];

            for (let i = 0; i < 5; i++) {
                const tile = row.children[i];
                setTimeout(() => {
                    tile.setAttribute('data-animation', 'flip-in');
                    tile.setAttribute('data-state', guess.feedback[i]);
                }, i * 100);
            }
        }

        function updateKeyboard(guess) {
            for (let i = 0; i < guess.word.length; i++) {
                const letter = guess.word[i].toLowerCase();
                const key = document.querySelector(`[data-key="${letter}"]`);
                if (key) {
                    const currentState = key.getAttribute('data-state');
                    const newState = guess.feedback[i];

                    // Only update if new state is better (correct > present > absent)
                    if (!currentState ||
                        (newState === 'correct') ||
                        (newState === 'present' && currentState !== 'correct')) {
                        key.setAttribute('data-state', newState);
                    }
                }
            }
        }

        function shakeRow() {
            const row = document.querySelectorAll('.row')[currentRow];
            row.style.animation = 'shake 0.5s';
            setTimeout(() => row.style.animation = '', 500);
        }

        function nextRow() {
            currentRow++;
            currentTile = 0;
            currentGuess = '';
        }

        function showGameStatus(message) {
            document.getElementById('gameStatus').textContent = message;
        }

        async function startNewGame() {
            try {
                const response = await fetch('/api/game/new', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (data.success) {
                    currentGameId = data.gameId;
                    gameState = data.gameState;
                    resetBoard();
                    resetKeyboard();
                    showGameStatus('Guess the 5-letter word!');
                    gameOver = false;
                } else {
                    showGameStatus('❌ Failed to start new game');
                }
            } catch (error) {
                showGameStatus('❌ Error starting game: ' + error.message);
            }
        }

        function resetBoard() {
            currentRow = 0;
            currentTile = 0;
            currentGuess = '';

            const tiles = document.querySelectorAll('.tile');
            tiles.forEach(tile => {
                tile.textContent = '';
                tile.setAttribute('data-state', 'empty');
                tile.removeAttribute('data-animation');
            });
        }

        function resetKeyboard() {
            const keys = document.querySelectorAll('.keyboard button');
            keys.forEach(key => {
                key.removeAttribute('data-state');
            });
        }

        // Add shake animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
                20%, 40%, 60%, 80% { transform: translateX(5px); }
            }
        `;
        document.head.appendChild(style);


    </script>
</body>
</html>
