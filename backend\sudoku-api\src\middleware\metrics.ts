/**
 * Metrics collection middleware
 * Collects performance and business metrics for monitoring
 */

import { Request, Response, NextFunction } from 'express';
import { register, Counter, Histogram, Gauge } from 'prom-client';
import { log } from '@/utils/logger';

// Prometheus metrics
const httpRequestsTotal = new Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code'],
});

const httpRequestDuration = new Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10],
});

const activeConnections = new Gauge({
  name: 'http_active_connections',
  help: 'Number of active HTTP connections',
});

const sudokuOperationsTotal = new Counter({
  name: 'sudoku_operations_total',
  help: 'Total number of Sudoku operations',
  labelNames: ['operation', 'difficulty', 'success'],
});

const sudokuSolvingDuration = new Histogram({
  name: 'sudoku_solving_duration_seconds',
  help: 'Duration of Sudoku solving operations in seconds',
  labelNames: ['difficulty', 'method'],
  buckets: [0.01, 0.05, 0.1, 0.5, 1, 2, 5, 10, 30],
});

const sudokuGenerationDuration = new Histogram({
  name: 'sudoku_generation_duration_seconds',
  help: 'Duration of Sudoku generation operations in seconds',
  labelNames: ['difficulty'],
  buckets: [0.1, 0.5, 1, 2, 5, 10, 30, 60],
});

const memoryUsage = new Gauge({
  name: 'nodejs_memory_usage_bytes',
  help: 'Node.js memory usage in bytes',
  labelNames: ['type'],
});

const cpuUsage = new Gauge({
  name: 'nodejs_cpu_usage_percent',
  help: 'Node.js CPU usage percentage',
});

// Track active connections
let activeConnectionsCount = 0;

/**
 * Metrics collection middleware
 */
export const metricsMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  const startTime = Date.now();
  const startHrTime = process.hrtime();

  // Increment active connections
  activeConnectionsCount++;
  activeConnections.set(activeConnectionsCount);

  // Get route pattern for better grouping
  const route = getRoutePattern(req);

  // Override res.end to capture metrics
  const originalEnd = res.end;
  res.end = function(chunk?: any, ...args: any[]) {
    const endTime = Date.now();
    const diff = process.hrtime(startHrTime);
    const duration = diff[0] + diff[1] * 1e-9; // Convert to seconds

    // Record metrics
    const labels = {
      method: req.method,
      route,
      status_code: res.statusCode.toString(),
    };

    httpRequestsTotal.inc(labels);
    httpRequestDuration.observe(labels, duration);

    // Decrement active connections
    activeConnectionsCount--;
    activeConnections.set(activeConnectionsCount);

    // Log performance metrics
    if (duration > 1) { // Log slow requests
      log.logPerformance('http_request', duration * 1000, {
        method: req.method,
        route,
        statusCode: res.statusCode,
        slow: true,
      });
    }

    return originalEnd.call(this, chunk, ...args);
  };

  next();
};

/**
 * Get route pattern from request
 */
function getRoutePattern(req: Request): string {
  // Try to get route from Express route
  if (req.route && req.route.path) {
    return req.route.path;
  }

  // Fallback to URL pattern matching
  const path = req.path;
  
  // Common patterns
  if (path.startsWith('/api/v2/sudoku/')) {
    const operation = path.split('/').pop();
    return `/api/v2/sudoku/${operation}`;
  }
  
  if (path.startsWith('/api/sudoku/')) {
    const operation = path.split('/').pop();
    return `/api/sudoku/${operation}`;
  }

  // Static patterns
  if (path === '/health') return '/health';
  if (path === '/metrics') return '/metrics';
  if (path === '/') return '/';
  if (path.startsWith('/api/v2/docs')) return '/api/v2/docs';

  // Default to path
  return path;
}

/**
 * Record Sudoku operation metrics
 */
export const recordSudokuOperation = (
  operation: 'solve' | 'generate' | 'validate' | 'hint' | 'analyze',
  difficulty?: string,
  success: boolean = true,
  duration?: number
): void => {
  const labels = {
    operation,
    difficulty: difficulty || 'unknown',
    success: success.toString(),
  };

  sudokuOperationsTotal.inc(labels);

  if (duration !== undefined) {
    if (operation === 'solve') {
      sudokuSolvingDuration.observe(
        { difficulty: difficulty || 'unknown', method: 'advanced' },
        duration / 1000 // Convert to seconds
      );
    } else if (operation === 'generate') {
      sudokuGenerationDuration.observe(
        { difficulty: difficulty || 'unknown' },
        duration / 1000
      );
    }
  }

  // Log business event
  log.logBusinessEvent('sudoku_operation', {
    operation,
    difficulty,
    success,
    duration,
  });
};

/**
 * Update system metrics
 */
export const updateSystemMetrics = (): void => {
  const memUsage = process.memoryUsage();
  
  memoryUsage.set({ type: 'rss' }, memUsage.rss);
  memoryUsage.set({ type: 'heapTotal' }, memUsage.heapTotal);
  memoryUsage.set({ type: 'heapUsed' }, memUsage.heapUsed);
  memoryUsage.set({ type: 'external' }, memUsage.external);

  // CPU usage (simplified)
  const cpuUsageValue = process.cpuUsage();
  const cpuPercent = (cpuUsageValue.user + cpuUsageValue.system) / 1000000; // Convert to seconds
  cpuUsage.set(cpuPercent);
};

/**
 * Start metrics collection
 */
export const startMetricsCollection = (): void => {
  // Update system metrics every 30 seconds
  setInterval(updateSystemMetrics, 30000);
  
  // Initial update
  updateSystemMetrics();
  
  log.info('Metrics collection started');
};

/**
 * Get all metrics in Prometheus format
 */
export const getMetrics = async (): Promise<string> => {
  return register.metrics();
};

/**
 * Get metrics summary
 */
export const getMetricsSummary = async (): Promise<any> => {
  const metrics = await register.getMetricsAsJSON();
  
  const summary = {
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    cpu: process.cpuUsage(),
    activeConnections: activeConnectionsCount,
    metrics: {},
  };

  // Process metrics
  for (const metric of metrics) {
    summary.metrics[metric.name] = {
      help: metric.help,
      type: metric.type,
      values: metric.values,
    };
  }

  return summary;
};

/**
 * Reset all metrics (useful for testing)
 */
export const resetMetrics = (): void => {
  register.clear();
  log.info('Metrics reset');
};

/**
 * Custom metrics for business logic
 */
export class BusinessMetrics {
  private static puzzlesSolved = 0;
  private static puzzlesGenerated = 0;
  private static hintsProvided = 0;
  private static validationRequests = 0;

  static incrementPuzzlesSolved(): void {
    this.puzzlesSolved++;
  }

  static incrementPuzzlesGenerated(): void {
    this.puzzlesGenerated++;
  }

  static incrementHintsProvided(): void {
    this.hintsProvided++;
  }

  static incrementValidationRequests(): void {
    this.validationRequests++;
  }

  static getStats(): any {
    return {
      puzzlesSolved: this.puzzlesSolved,
      puzzlesGenerated: this.puzzlesGenerated,
      hintsProvided: this.hintsProvided,
      validationRequests: this.validationRequests,
    };
  }

  static reset(): void {
    this.puzzlesSolved = 0;
    this.puzzlesGenerated = 0;
    this.hintsProvided = 0;
    this.validationRequests = 0;
  }
}

export default {
  metricsMiddleware,
  recordSudokuOperation,
  updateSystemMetrics,
  startMetricsCollection,
  getMetrics,
  getMetricsSummary,
  resetMetrics,
  BusinessMetrics,
};
