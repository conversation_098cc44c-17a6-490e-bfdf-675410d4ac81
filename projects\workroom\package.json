{"name": "collabspace-frontend", "version": "2.0.0", "description": "Enterprise-grade collaborative workspace frontend with real-time features", "main": "index.html", "private": true, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/zaydensharp"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/zaydensharp/portfolio-2025.git", "directory": "projects/workroom"}, "keywords": ["collaboration", "workspace", "real-time", "chat", "whiteboard", "tasks", "video-calls", "file-sharing", "enterprise", "typescript", "websockets", "canvas", "productivity"], "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "type-check": "tsc --noEmit", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,css,md}\"", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "analyze": "npm run build && npx vite-bundle-analyzer dist/stats.html", "clean": "rimraf dist node_modules/.vite", "prepare": "husky install", "docker:build": "docker build -t collabspace-frontend .", "docker:run": "docker run -p 3000:80 collabspace-frontend", "serve": "serve -s dist -l 3000", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "socket.io-client": "^4.7.4", "fabric": "^5.3.0", "konva": "^9.2.0", "react-konva": "^18.2.10", "framer-motion": "^10.16.16", "react-router-dom": "^6.20.1", "zustand": "^4.4.7", "react-query": "^3.39.3", "axios": "^1.6.2", "date-fns": "^2.30.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-dropzone": "^14.2.3", "react-color": "^2.19.3", "react-webcam": "^7.2.0", "simple-peer": "^9.11.1", "emoji-picker-react": "^4.5.16", "react-markdown": "^9.0.1", "prismjs": "^1.29.0", "react-syntax-highlighter": "^15.5.0", "react-beautiful-dnd": "^13.1.1", "react-window": "^1.8.8", "react-window-infinite-loader": "^1.0.9", "react-intersection-observer": "^9.5.3", "workbox-window": "^7.0.0", "idb": "^7.1.1", "comlink": "^4.4.1", "uuid": "^9.0.1", "lodash-es": "^4.17.21", "clsx": "^2.0.0", "class-variance-authority": "^0.7.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/fabric": "^5.3.7", "@types/simple-peer": "^9.11.8", "@types/react-color": "^3.0.9", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-window": "^1.8.8", "@types/uuid": "^9.0.7", "@types/lodash-es": "^4.17.12", "@types/prismjs": "^1.26.3", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "@vitejs/plugin-pwa": "^0.17.4", "vite": "^5.0.8", "vite-plugin-eslint": "^1.8.1", "vite-bundle-analyzer": "^0.7.0", "typescript": "^5.2.2", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "prettier": "^3.1.1", "husky": "^8.0.3", "lint-staged": "^15.2.0", "vitest": "^1.0.4", "@vitest/ui": "^1.0.4", "@vitest/coverage-v8": "^1.0.4", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "jsdom": "^23.0.1", "playwright": "^1.40.1", "@playwright/test": "^1.40.1", "rimraf": "^5.0.5", "serve": "^14.2.1", "@storybook/react": "^7.6.6", "@storybook/react-vite": "^7.6.6", "@storybook/addon-essentials": "^7.6.6", "@storybook/addon-interactions": "^7.6.6", "@storybook/addon-links": "^7.6.6", "@storybook/blocks": "^7.6.6", "@storybook/testing-library": "^0.2.2", "storybook": "^7.6.6", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@tailwindcss/aspect-ratio": "^0.4.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"], "*.{css,md}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run type-check && npm run test"}}}