{"env": {"browser": true, "es2022": true, "node": true}, "extends": ["eslint:recommended"], "plugins": ["compat"], "parserOptions": {"ecmaVersion": 2022, "sourceType": "module"}, "rules": {"no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "no-console": "warn", "prefer-const": "error", "no-var": "error", "prefer-arrow-callback": "error", "arrow-spacing": "error", "object-shorthand": "error", "prefer-template": "error", "template-curly-spacing": "error", "quote-props": ["error", "as-needed"], "quotes": ["error", "single", {"avoidEscape": true}], "semi": ["error", "always"], "comma-dangle": ["error", "never"], "indent": ["error", 2], "linebreak-style": ["error", "unix"], "max-len": ["warn", {"code": 100, "ignoreUrls": true}], "compat/compat": "error"}, "settings": {"polyfills": ["Promise", "fetch", "WebSocket", "IntersectionObserver", "ResizeObserver"]}, "overrides": [{"files": ["backend/**/*.js"], "env": {"node": true, "browser": false}, "rules": {"no-console": "off", "compat/compat": "off"}}, {"files": ["projects/**/*.js"], "env": {"browser": true, "node": false}, "globals": {"Chart": "readonly", "THREE": "readonly", "SimplePeer": "readonly"}}]}