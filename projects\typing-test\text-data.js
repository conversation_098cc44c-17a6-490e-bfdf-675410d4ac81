/**
 * Text Data for Typing Speed Test
 * Various categories of text for different typing challenges
 */

const TEXT_DATA = {
  common: {
    easy: [
      "the quick brown fox jumps over the lazy dog",
      "a journey of a thousand miles begins with a single step",
      "to be or not to be that is the question",
      "all that glitters is not gold",
      "better late than never but never late is better",
      "practice makes perfect and perfect practice makes champions",
      "time flies when you are having fun with friends",
      "the early bird catches the worm every morning",
      "actions speak louder than words in every situation",
      "where there is a will there is always a way"
    ],
    medium: [
      "The five boxing wizards jump quickly over the fence while maintaining their balance and composure throughout the entire exercise.",
      "In a hole in the ground there lived a hobbit. Not a nasty, dirty, wet hole filled with the ends of worms and an oozy smell.",
      "It was the best of times, it was the worst of times, it was the age of wisdom, it was the age of foolishness and confusion.",
      "Two roads diverged in a yellow wood, and sorry I could not travel both and be one traveler, long I stood and looked down one.",
      "Call me <PERSON><PERSON><PERSON>. Some years ago never mind how long precisely having little or no money in my purse and nothing particular.",
      "It is a truth universally acknowledged that a single man in possession of a good fortune must be in want of a wife and family.",
      "In the beginning was the Word, and the Word was with God, and the Word was God, and all things were made through Him.",
      "Four score and seven years ago our fathers brought forth on this continent a new nation conceived in liberty and dedicated.",
      "We hold these truths to be self-evident, that all men are created equal, that they are endowed by their Creator with certain.",
      "Ask not what your country can do for you ask what you can do for your country and for the betterment of all mankind."
    ],
    hard: [
      "The quick-witted, brown fox (weighing approximately 15.7 pounds) jumped over the lazy dog's back at 3:45 PM on December 31st, 2023.",
      "\"Hello, World!\" she exclaimed, while debugging the complex algorithm that contained nested loops, conditional statements, and recursive functions.",
      "The CEO's quarterly report showed a 23.8% increase in revenue, totaling $4,567,890.12 across all departments and subsidiaries worldwide.",
      "Dr. Smith's research paper, titled 'Advanced Quantum Mechanics & Its Applications,' was published in the Journal of Theoretical Physics (Vol. 42, Issue #7).",
      "The password requirements include: 8+ characters, uppercase/lowercase letters, numbers (0-9), special symbols (!@#$%^&*), and no dictionary words.",
      "According to the weather forecast, there's a 67% chance of precipitation tomorrow, with temperatures ranging from 18°C to 24°C (64°F to 75°F).",
      "The SQL query 'SELECT * FROM users WHERE age BETWEEN 25 AND 35 AND status = \"active\"' returned 1,247 records from the database.",
      "Einstein's famous equation E=mc² demonstrates the mass-energy equivalence, where 'c' represents the speed of light (299,792,458 m/s).",
      "The JSON object contains nested arrays: {\"users\": [{\"id\": 1, \"name\": \"John\"}, {\"id\": 2, \"name\": \"Jane\"}], \"total\": 2}.",
      "The regular expression /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/ validates email addresses with 99.7% accuracy."
    ]
  },
  
  quotes: {
    easy: [
      "Be yourself everyone else is already taken",
      "Life is what happens when you are busy making other plans",
      "The only way to do great work is to love what you do",
      "Innovation distinguishes between a leader and a follower",
      "Stay hungry stay foolish and never stop learning",
      "The future belongs to those who believe in the beauty of their dreams",
      "Success is not final failure is not fatal it is the courage to continue",
      "The only impossible journey is the one you never begin",
      "Believe you can and you are halfway there to your destination",
      "Do not go where the path may lead go instead where there is no path"
    ],
    medium: [
      "The greatest glory in living lies not in never falling, but in rising every time we fall and learning from our mistakes.",
      "In the end, we will remember not the words of our enemies, but the silence of our friends during difficult times.",
      "The way to get started is to quit talking and begin doing what needs to be done right now without hesitation.",
      "Your time is limited, so don't waste it living someone else's life and following their dreams instead of your own.",
      "If life were predictable it would cease to be life, and be without flavor, excitement, and the joy of discovery.",
      "If you look at what you have in life, you'll always have more than enough to be grateful for and happy.",
      "Life is really simple, but we insist on making it complicated with unnecessary worries and overthinking everything.",
      "The future belongs to those who believe in the beauty of their dreams and work tirelessly to achieve them.",
      "It is during our darkest moments that we must focus to see the light and find hope in seemingly hopeless situations.",
      "Whoever is happy will make others happy too, spreading joy and positivity throughout their community and beyond."
    ],
    hard: [
      "\"I have not failed. I've just found 10,000 ways that won't work,\" said Thomas Edison, demonstrating the power of persistence & determination.",
      "Albert Einstein once said, \"Imagination is more important than knowledge. For knowledge is limited, whereas imagination embraces the entire world.\"",
      "\"The only thing we have to fear is fear itself—nameless, unreasoning, unjustified terror,\" proclaimed FDR during the Great Depression (1933).",
      "Maya Angelou wisely noted: \"I've learned that people will forget what you said & did, but they'll never forget how you made them feel.\"",
      "\"Two things are infinite: the universe and human stupidity; and I'm not sure about the universe!\" - Einstein's humorous observation.",
      "Winston Churchill declared, \"Success is not final, failure is not fatal: it is the courage to continue that counts\" during WWII.",
      "\"The best time to plant a tree was 20 years ago. The second best time is now,\" reflects an ancient Chinese proverb about timing.",
      "Steve Jobs emphasized, \"Your work is going to fill a large part of your life, and the only way to be truly satisfied is to do great work.\"",
      "\"In three words I can sum up everything I've learned about life: it goes on,\" wrote Robert Frost in his collection of poems.",
      "\"The only way to make sense out of change is to plunge into it, move with it, and join the dance,\" advised Alan Watts."
    ]
  },
  
  programming: {
    easy: [
      "function hello world return console log hello world",
      "var name john age twenty five city new york",
      "if true then print success else print failure",
      "for loop from zero to ten increment by one",
      "array list contains numbers one two three four five",
      "class user has properties name email and password",
      "import library from package and use its methods",
      "try catch block handles errors and exceptions gracefully",
      "const variable equals string concatenation of first and last",
      "return statement exits function and provides result value"
    ],
    medium: [
      "const fetchData = async () => { const response = await fetch('/api/users'); return response.json(); };",
      "function calculateSum(numbers) { return numbers.reduce((acc, num) => acc + num, 0); }",
      "const users = data.filter(user => user.age >= 18).map(user => ({ name: user.name, email: user.email }));",
      "if (username && password && email.includes('@')) { createUser({ username, password, email }); }",
      "const debounce = (func, delay) => { let timeoutId; return (...args) => { clearTimeout(timeoutId); timeoutId = setTimeout(() => func.apply(this, args), delay); }; };",
      "try { const result = JSON.parse(jsonString); processData(result); } catch (error) { console.error('Invalid JSON:', error.message); }",
      "const promise = new Promise((resolve, reject) => { setTimeout(() => resolve('Success!'), 1000); });",
      "class Rectangle { constructor(width, height) { this.width = width; this.height = height; } getArea() { return this.width * this.height; } }",
      "const apiUrl = `https://api.example.com/users/${userId}?include=profile&format=json`;",
      "export default function Component({ title, children }) { return <div className=\"container\"><h1>{title}</h1>{children}</div>; }"
    ],
    hard: [
      "const memoize = (fn) => { const cache = new Map(); return (...args) => { const key = JSON.stringify(args); return cache.has(key) ? cache.get(key) : cache.set(key, fn(...args)).get(key); }; };",
      "interface User { id: number; name: string; email: string; createdAt: Date; } type UserWithoutId = Omit<User, 'id'>;",
      "const quickSort = (arr: number[]): number[] => arr.length <= 1 ? arr : [...quickSort(arr.slice(1).filter(x => x <= arr[0])), arr[0], ...quickSort(arr.slice(1).filter(x => x > arr[0]))];",
      "SELECT u.name, COUNT(o.id) as order_count FROM users u LEFT JOIN orders o ON u.id = o.user_id WHERE u.created_at >= '2023-01-01' GROUP BY u.id HAVING COUNT(o.id) > 5;",
      "const useCustomHook = (initialValue) => { const [state, setState] = useState(initialValue); const updateState = useCallback((newValue) => setState(prev => ({ ...prev, ...newValue })), []); return [state, updateState]; };",
      "public class GenericRepository<T> where T : class { private readonly DbContext _context; public async Task<T> GetByIdAsync(int id) => await _context.Set<T>().FindAsync(id); }",
      "const createStore = (reducer, initialState) => { let state = initialState; const listeners = []; return { getState: () => state, dispatch: (action) => { state = reducer(state, action); listeners.forEach(listener => listener()); }, subscribe: (listener) => { listeners.push(listener); return () => listeners.splice(listeners.indexOf(listener), 1); } }; };",
      "def binary_search(arr: List[int], target: int, left: int = 0, right: int = None) -> int: right = len(arr) - 1 if right is None else right; return -1 if left > right else (mid := (left + right) // 2) if arr[mid] == target else binary_search(arr, target, left, mid - 1) if arr[mid] > target else binary_search(arr, target, mid + 1, right);",
      "const throttle = <T extends (...args: any[]) => any>(func: T, limit: number): T => { let inThrottle: boolean; return ((...args: any[]) => { if (!inThrottle) { func.apply(this, args); inThrottle = true; setTimeout(() => inThrottle = false, limit); } }) as T; };",
      "import { Observable, fromEvent, map, filter, debounceTime, distinctUntilChanged, switchMap, catchError } from 'rxjs'; const searchInput$ = fromEvent(document.getElementById('search'), 'input').pipe(map(e => e.target.value), filter(text => text.length > 2), debounceTime(300), distinctUntilChanged(), switchMap(query => fetch(`/api/search?q=${query}`).then(r => r.json())), catchError(err => Observable.of([])));"
    ]
  },
  
  numbers: {
    easy: [
      "1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16 17 18 19 20",
      "100 200 300 400 500 600 700 ************ 1100 1200",
      "2023 2024 2025 2026 2027 2028 2029 2030 2031 2032",
      "10 20 30 40 50 60 70 80 90 100 110 120 130 140 150",
      "5 10 15 20 25 30 35 40 45 50 55 60 65 70 75 80 85",
      "123 234 345 456 567 678 789 890 901 012 123 234 345",
      "1000 2000 3000 4000 5000 6000 7000 8000 9000 10000",
      "12 24 36 48 60 72 84 96 108 120 132 144 156 168 180",
      "7 14 21 28 35 42 49 56 63 70 77 84 91 98 105 112",
      "25 50 75 100 125 150 175 200 225 250 275 300 325 350"
    ],
    medium: [
      "3.14159 2.71828 1.41421 1.73205 2.23607 1.61803 0.57722 0.69314",
      "123.45 678.90 234.56 789.01 345.67 890.12 456.78 901.23",
      "1,234 5,678 9,012 3,456 7,890 1,234 5,678 9,012 3,456",
      "50% 75% 25% 100% 33% 66% 20% 80% 40% 60% 90% 10%",
      "2^8 = 256, 2^10 = 1024, 2^16 = 65536, 2^32 = 4294967296",
      "$19.99 $49.95 $99.00 $149.50 $199.99 $299.95 $399.00",
      "Phone: 555-0123, 555-4567, 555-8901, 555-2345, 555-6789",
      "Dates: 01/15/2024, 03/22/2024, 07/04/2024, 12/25/2024",
      "Time: 09:30 AM, 02:45 PM, 11:15 PM, 06:00 AM, 08:30 PM",
      "Coordinates: (12.34, -56.78), (90.12, 34.56), (-78.90, 12.34)"
    ],
    hard: [
      "Scientific: 6.022×10²³, 9.109×10⁻³¹, 1.602×10⁻¹⁹, 6.626×10⁻³⁴",
      "Binary: 11010110, 10101010, 01010101, 11111111, 00000000, 10011001",
      "Hex: 0xFF00FF, 0x123ABC, 0xDEADBEEF, 0xCAFEBABE, 0x8BADF00D",
      "Complex: (3+4i), (2-7i), (-5+2i), (0+9i), (8-3i), (-1-6i)",
      "Fractions: 22/7, 355/113, 103993/33102, 312689/99532, 833719/265381",
      "IP Addresses: ***********, **********, ************, ***********",
      "Version: v2.1.3-beta.4+build.567, v1.0.0-alpha.1, v3.2.1-rc.2",
      "UUID: 550e8400-e29b-41d4-a716-************, f47ac10b-58cc-4372",
      "Hash: SHA256:a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3",
      "Regex: /^\\d{3}-\\d{2}-\\d{4}$/, /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}/"
    ]
  },
  
  punctuation: {
    easy: [
      "Hello, world! How are you today? I'm fine, thank you.",
      "It's a beautiful day, isn't it? Yes, it really is!",
      "Can you help me? Of course! I'd be happy to help.",
      "What's your name? My name is John. Nice to meet you!",
      "Where are you going? I'm going to the store, then home.",
      "Do you like pizza? Yes, I love it! It's my favorite food.",
      "What time is it? It's 3:30 PM. Thank you for telling me.",
      "Are you ready? Not yet, but I'll be ready in five minutes.",
      "How's the weather? It's sunny and warm. Perfect for walking!",
      "What's for dinner? We're having pasta with tomato sauce."
    ],
    medium: [
      "\"Hello,\" she said, \"how are you doing today?\" \"I'm fine,\" he replied with a smile.",
      "The items needed are: apples, oranges, bananas; milk, bread, cheese; and soap, shampoo, toothpaste.",
      "According to the report (published in 2023), sales increased by 15.7% year-over-year.",
      "She asked, \"Can you pick up the following: eggs, butter, flour, and sugar?\" \"Of course!\" I answered.",
      "The meeting is scheduled for Monday, March 15th, 2024, at 2:00 PM in Conference Room B.",
      "His favorite books include: \"1984\" by Orwell, \"Pride and Prejudice\" by Austen, and \"The Great Gatsby.\"",
      "The recipe calls for: 2 cups flour, 1/2 cup sugar, 3 eggs, and 1 tsp vanilla extract.",
      "\"Are you sure about this?\" she asked nervously. \"Absolutely,\" he confirmed with confidence.",
      "The address is: 123 Main Street, Apt. 4B, New York, NY 10001, USA.",
      "Important dates to remember: January 1st (New Year's), July 4th (Independence Day), and December 25th."
    ],
    hard: [
      "The CEO announced: \"Q4 results exceeded expectations—revenue up 23.8% (vs. 18.2% projected); net income: $4.7M.\"",
      "Email format: <EMAIL>; phone: (*************; website: https://www.example.com/path?param=value&id=123.",
      "Code snippet: if (x > 0 && y < 100) { return \"valid\"; } else { throw new Error(\"Invalid input!\"); }",
      "Mathematical expression: f(x) = 2x² + 3x - 5, where x ∈ [-10, 10] and f'(x) = 4x + 3.",
      "Bibliography entry: Smith, J. (2023). \"Advanced Algorithms & Data Structures\" (3rd ed.). Tech Publishers, pp. 45-67.",
      "SQL query: SELECT u.name, COUNT(*) FROM users u WHERE u.created_at >= '2023-01-01' GROUP BY u.id;",
      "Regular expression: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$/",
      "JSON object: {\"users\": [{\"id\": 1, \"name\": \"John\"}, {\"id\": 2, \"name\": \"Jane\"}], \"total\": 2}",
      "Terminal command: grep -r \"TODO:\" --include=\"*.js\" --exclude-dir=node_modules ./src/ | wc -l",
      "CSS rule: .container { display: flex; justify-content: space-between; align-items: center; padding: 1rem 2rem; }"
    ]
  }
};

// Function to get random text based on type and difficulty
function getRandomText(type, difficulty) {
  const texts = TEXT_DATA[type]?.[difficulty];
  if (!texts || texts.length === 0) {
    return "The quick brown fox jumps over the lazy dog.";
  }
  return texts[Math.floor(Math.random() * texts.length)];
}

// Function to generate text of specific length
function generateText(type, difficulty, targetLength = 200) {
  let text = "";
  const texts = TEXT_DATA[type]?.[difficulty] || TEXT_DATA.common.easy;
  
  while (text.length < targetLength) {
    const randomText = texts[Math.floor(Math.random() * texts.length)];
    text += (text.length > 0 ? " " : "") + randomText;
  }
  
  return text.substring(0, targetLength).trim();
}

// Export for use in main.js
window.TEXT_DATA = TEXT_DATA;
window.getRandomText = getRandomText;
window.generateText = generateText;
