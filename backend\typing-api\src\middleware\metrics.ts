/**
 * Metrics collection middleware for monitoring and observability
 */

import { Request, Response, NextFunction } from 'express';
import { register, Counter, Histogram, Gauge } from 'prom-client';

// Metrics instances
const httpRequestsTotal = new Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code']
});

const httpRequestDuration = new Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10]
});

const activeConnections = new Gauge({
  name: 'active_connections',
  help: 'Number of active connections'
});

const typingTestsTotal = new Counter({
  name: 'typing_tests_total',
  help: 'Total number of typing tests completed',
  labelNames: ['difficulty', 'category']
});

const typingTestDuration = new Histogram({
  name: 'typing_test_duration_seconds',
  help: 'Duration of typing tests in seconds',
  labelNames: ['difficulty', 'category'],
  buckets: [10, 30, 60, 120, 300, 600]
});

const typingWpmHistogram = new Histogram({
  name: 'typing_wpm',
  help: 'Words per minute distribution',
  labelNames: ['difficulty', 'category'],
  buckets: [10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 120, 150]
});

const typingAccuracyHistogram = new Histogram({
  name: 'typing_accuracy',
  help: 'Typing accuracy distribution',
  labelNames: ['difficulty', 'category'],
  buckets: [0.5, 0.6, 0.7, 0.8, 0.85, 0.9, 0.95, 0.98, 0.99, 1.0]
});

const databaseOperations = new Counter({
  name: 'database_operations_total',
  help: 'Total number of database operations',
  labelNames: ['operation', 'table', 'status']
});

const cacheOperations = new Counter({
  name: 'cache_operations_total',
  help: 'Total number of cache operations',
  labelNames: ['operation', 'status']
});

const websocketConnections = new Gauge({
  name: 'websocket_connections',
  help: 'Number of active WebSocket connections'
});

// Register all metrics
register.registerMetric(httpRequestsTotal);
register.registerMetric(httpRequestDuration);
register.registerMetric(activeConnections);
register.registerMetric(typingTestsTotal);
register.registerMetric(typingTestDuration);
register.registerMetric(typingWpmHistogram);
register.registerMetric(typingAccuracyHistogram);
register.registerMetric(databaseOperations);
register.registerMetric(cacheOperations);
register.registerMetric(websocketConnections);

/**
 * Metrics middleware to collect HTTP request metrics
 */
export const metricsMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  const startTime = Date.now();
  
  // Increment active connections
  activeConnections.inc();

  // Override res.end to capture metrics when response is sent
  const originalEnd = res.end;
  res.end = function(...args: any[]) {
    const duration = (Date.now() - startTime) / 1000;
    const route = getRoutePattern(req.route?.path || req.path);
    
    // Record metrics
    httpRequestsTotal.inc({
      method: req.method,
      route,
      status_code: res.statusCode
    });

    httpRequestDuration.observe(
      {
        method: req.method,
        route,
        status_code: res.statusCode
      },
      duration
    );

    // Decrement active connections
    activeConnections.dec();

    // Call original end method
    return originalEnd.apply(this, args);
  };

  next();
};

/**
 * Get route pattern for metrics (normalize dynamic routes)
 */
function getRoutePattern(path: string): string {
  if (!path) return 'unknown';
  
  // Replace common dynamic segments with placeholders
  return path
    .replace(/\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/gi, '/:id') // UUIDs
    .replace(/\/\d+/g, '/:id') // Numeric IDs
    .replace(/\/[a-zA-Z0-9_-]+\.(json|xml|csv)$/g, '/:file') // File extensions
    || 'unknown';
}

/**
 * Metrics collection utilities for business logic
 */
export class MetricsCollector {
  /**
   * Record typing test completion
   */
  static recordTypingTest(
    duration: number,
    wpm: number,
    accuracy: number,
    difficulty: string,
    category: string
  ): void {
    typingTestsTotal.inc({ difficulty, category });
    typingTestDuration.observe({ difficulty, category }, duration / 1000);
    typingWpmHistogram.observe({ difficulty, category }, wpm);
    typingAccuracyHistogram.observe({ difficulty, category }, accuracy);
  }

  /**
   * Record database operation
   */
  static recordDatabaseOperation(
    operation: 'select' | 'insert' | 'update' | 'delete',
    table: string,
    status: 'success' | 'error'
  ): void {
    databaseOperations.inc({ operation, table, status });
  }

  /**
   * Record cache operation
   */
  static recordCacheOperation(
    operation: 'get' | 'set' | 'delete' | 'clear',
    status: 'hit' | 'miss' | 'success' | 'error'
  ): void {
    cacheOperations.inc({ operation, status });
  }

  /**
   * Update WebSocket connection count
   */
  static setWebSocketConnections(count: number): void {
    websocketConnections.set(count);
  }

  /**
   * Increment WebSocket connections
   */
  static incrementWebSocketConnections(): void {
    websocketConnections.inc();
  }

  /**
   * Decrement WebSocket connections
   */
  static decrementWebSocketConnections(): void {
    websocketConnections.dec();
  }
}

/**
 * Get all metrics in Prometheus format
 */
export const getMetrics = async (): Promise<string> => {
  return register.metrics();
};

/**
 * Clear all metrics (useful for testing)
 */
export const clearMetrics = (): void => {
  register.clear();
};
