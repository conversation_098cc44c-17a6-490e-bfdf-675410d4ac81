class SudokuValidator {
  constructor() {
    this.VALID_NUMBERS = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]; // 0 for empty cells
    this.SOLUTION_NUMBERS = [1, 2, 3, 4, 5, 6, 7, 8, 9]; // Only 1-9 for complete solutions
  }

  // Validate puzzle format (9x9 grid with numbers 0-9)
  isValidFormat(puzzle) {
    if (!Array.isArray(puzzle) || puzzle.length !== 9) {
      return false;
    }

    for (let row = 0; row < 9; row++) {
      if (!Array.isArray(puzzle[row]) || puzzle[row].length !== 9) {
        return false;
      }

      for (let col = 0; col < 9; col++) {
        const value = puzzle[row][col];
        if (!Number.isInteger(value) || !this.VALID_NUMBERS.includes(value)) {
          return false;
        }
      }
    }

    return true;
  }

  // Validate that puzzle doesn't contain contradictions
  isValidPuzzle(puzzle) {
    if (!this.isValidFormat(puzzle)) {
      return false;
    }

    // Check for conflicts in current state
    const conflicts = this.findConflicts(puzzle);
    return conflicts.length === 0;
  }

  // Validate complete solution (all cells filled, no conflicts)
  isValidSolution(puzzle) {
    if (!this.isValidFormat(puzzle)) {
      return false;
    }

    // Check if all cells are filled
    for (let row = 0; row < 9; row++) {
      for (let col = 0; col < 9; col++) {
        if (puzzle[row][col] === 0) {
          return false; // Empty cell found
        }
      }
    }

    // Check for conflicts
    const conflicts = this.findConflicts(puzzle);
    return conflicts.length === 0;
  }

  // Find all conflicts in the puzzle
  findConflicts(puzzle) {
    const conflicts = [];

    // Check rows
    for (let row = 0; row < 9; row++) {
      const rowConflicts = this.findRowConflicts(puzzle, row);
      conflicts.push(...rowConflicts);
    }

    // Check columns
    for (let col = 0; col < 9; col++) {
      const colConflicts = this.findColumnConflicts(puzzle, col);
      conflicts.push(...colConflicts);
    }

    // Check 3x3 boxes
    for (let box = 0; box < 9; box++) {
      const boxConflicts = this.findBoxConflicts(puzzle, box);
      conflicts.push(...boxConflicts);
    }

    // Remove duplicates
    return this.removeDuplicateConflicts(conflicts);
  }

  // Find conflicts in a specific row
  findRowConflicts(puzzle, row) {
    const conflicts = [];
    const seen = new Map();

    for (let col = 0; col < 9; col++) {
      const value = puzzle[row][col];
      if (value !== 0) {
        if (seen.has(value)) {
          // Conflict found
          const firstPos = seen.get(value);
          conflicts.push({
            type: 'row',
            value: value,
            positions: [
              { row: row, col: firstPos },
              { row: row, col: col }
            ],
            unit: row
          });
        } else {
          seen.set(value, col);
        }
      }
    }

    return conflicts;
  }

  // Find conflicts in a specific column
  findColumnConflicts(puzzle, col) {
    const conflicts = [];
    const seen = new Map();

    for (let row = 0; row < 9; row++) {
      const value = puzzle[row][col];
      if (value !== 0) {
        if (seen.has(value)) {
          // Conflict found
          const firstPos = seen.get(value);
          conflicts.push({
            type: 'column',
            value: value,
            positions: [
              { row: firstPos, col: col },
              { row: row, col: col }
            ],
            unit: col
          });
        } else {
          seen.set(value, row);
        }
      }
    }

    return conflicts;
  }

  // Find conflicts in a specific 3x3 box
  findBoxConflicts(puzzle, boxIndex) {
    const conflicts = [];
    const seen = new Map();
    
    const startRow = Math.floor(boxIndex / 3) * 3;
    const startCol = (boxIndex % 3) * 3;

    for (let r = startRow; r < startRow + 3; r++) {
      for (let c = startCol; c < startCol + 3; c++) {
        const value = puzzle[r][c];
        if (value !== 0) {
          if (seen.has(value)) {
            // Conflict found
            const firstPos = seen.get(value);
            conflicts.push({
              type: 'box',
              value: value,
              positions: [
                firstPos,
                { row: r, col: c }
              ],
              unit: boxIndex
            });
          } else {
            seen.set(value, { row: r, col: c });
          }
        }
      }
    }

    return conflicts;
  }

  // Remove duplicate conflict reports
  removeDuplicateConflicts(conflicts) {
    const unique = [];
    const seen = new Set();

    for (const conflict of conflicts) {
      const key = this.getConflictKey(conflict);
      if (!seen.has(key)) {
        seen.add(key);
        unique.push(conflict);
      }
    }

    return unique;
  }

  // Generate unique key for conflict deduplication
  getConflictKey(conflict) {
    const positions = conflict.positions
      .map(pos => `${pos.row},${pos.col}`)
      .sort()
      .join('|');
    return `${conflict.type}:${conflict.value}:${positions}`;
  }

  // Get puzzle completeness percentage
  getCompleteness(puzzle) {
    let filledCells = 0;
    
    for (let row = 0; row < 9; row++) {
      for (let col = 0; col < 9; col++) {
        if (puzzle[row][col] !== 0) {
          filledCells++;
        }
      }
    }

    return {
      filledCells: filledCells,
      totalCells: 81,
      percentage: Math.round((filledCells / 81) * 100)
    };
  }

  // Validate specific cell placement
  isValidCellValue(puzzle, row, col, value) {
    if (row < 0 || row >= 9 || col < 0 || col >= 9) {
      return false;
    }

    if (!this.SOLUTION_NUMBERS.includes(value)) {
      return false;
    }

    // Create temporary puzzle with the new value
    const tempPuzzle = puzzle.map(r => [...r]);
    tempPuzzle[row][col] = value;

    // Check for conflicts
    const conflicts = this.findConflicts(tempPuzzle);
    return conflicts.length === 0;
  }

  // Get all possible values for a cell
  getPossibleValues(puzzle, row, col) {
    if (row < 0 || row >= 9 || col < 0 || col >= 9) {
      return [];
    }

    if (puzzle[row][col] !== 0) {
      return []; // Cell is already filled
    }

    const possible = [];
    
    for (let value = 1; value <= 9; value++) {
      if (this.isValidCellValue(puzzle, row, col, value)) {
        possible.push(value);
      }
    }

    return possible;
  }

  // Validate puzzle difficulty claim
  validateDifficulty(puzzle, claimedDifficulty) {
    const analysis = this.analyzeDifficulty(puzzle);
    return analysis.difficulty === claimedDifficulty;
  }

  // Analyze puzzle difficulty based on clue count and solving techniques
  analyzeDifficulty(puzzle) {
    const clueCount = this.getCompleteness(puzzle).filledCells;
    
    // Basic difficulty assessment based on clue count
    let difficulty;
    if (clueCount >= 36) {
      difficulty = 'easy';
    } else if (clueCount >= 28) {
      difficulty = 'medium';
    } else if (clueCount >= 22) {
      difficulty = 'hard';
    } else if (clueCount >= 17) {
      difficulty = 'expert';
    } else {
      difficulty = 'evil';
    }

    return {
      difficulty: difficulty,
      clueCount: clueCount,
      reasoning: `Based on ${clueCount} clues provided`
    };
  }

  // Check if puzzle is solvable
  isSolvable(puzzle) {
    if (!this.isValidPuzzle(puzzle)) {
      return false;
    }

    // Try to solve using basic backtracking
    const grid = puzzle.map(row => [...row]);
    return this.canSolve(grid);
  }

  // Simple solvability check using backtracking
  canSolve(grid) {
    const emptyCell = this.findEmptyCell(grid);
    if (!emptyCell) {
      return true; // Puzzle is complete
    }

    const [row, col] = emptyCell;
    
    for (let num = 1; num <= 9; num++) {
      if (this.isValidCellValue(grid, row, col, num)) {
        grid[row][col] = num;
        
        if (this.canSolve(grid)) {
          return true;
        }
        
        grid[row][col] = 0; // Backtrack
      }
    }
    
    return false;
  }

  // Find first empty cell
  findEmptyCell(grid) {
    for (let row = 0; row < 9; row++) {
      for (let col = 0; col < 9; col++) {
        if (grid[row][col] === 0) {
          return [row, col];
        }
      }
    }
    return null;
  }

  // Comprehensive puzzle validation report
  getValidationReport(puzzle) {
    const report = {
      isValid: true,
      errors: [],
      warnings: [],
      statistics: {}
    };

    // Format validation
    if (!this.isValidFormat(puzzle)) {
      report.isValid = false;
      report.errors.push('Invalid puzzle format. Must be 9x9 grid with numbers 0-9.');
      return report;
    }

    // Conflict detection
    const conflicts = this.findConflicts(puzzle);
    if (conflicts.length > 0) {
      report.isValid = false;
      report.errors.push(`Found ${conflicts.length} conflicts in the puzzle.`);
      report.conflicts = conflicts;
    }

    // Completeness
    const completeness = this.getCompleteness(puzzle);
    report.statistics.completeness = completeness;

    // Solvability
    if (report.isValid) {
      const solvable = this.isSolvable(puzzle);
      if (!solvable) {
        report.isValid = false;
        report.errors.push('Puzzle is not solvable.');
      }
      report.statistics.solvable = solvable;
    }

    // Difficulty analysis
    const difficulty = this.analyzeDifficulty(puzzle);
    report.statistics.difficulty = difficulty;

    return report;
  }
}

module.exports = SudokuValidator;
