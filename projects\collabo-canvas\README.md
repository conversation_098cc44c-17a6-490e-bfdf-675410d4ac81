# 🎨 CollaboCanvas - Real-time Collaborative Whiteboard

[![Live Demo](https://img.shields.io/badge/Live%20Demo-Visit%20Site-brightgreen)](https://yourusername.github.io/portfolio-eight/projects/collabo-canvas/)
[![WebRTC](https://img.shields.io/badge/WebRTC-Enabled-blue)](https://yourusername.github.io/portfolio-eight/projects/collabo-canvas/)
[![Real-time](https://img.shields.io/badge/Real--time-Collaboration-orange)](https://yourusername.github.io/portfolio-eight/projects/collabo-canvas/)

A real-time collaborative whiteboard application built with WebRTC for peer-to-peer communication, featuring drawing tools, chat, and multi-user cursor tracking.

## ✨ Features

### 🎨 **Drawing Tools**
- **Pen Tool**: Freehand drawing with adjustable brush size
- **Eraser**: Remove content with variable eraser size
- **Shape Tools**: Lines, rectangles, and circles
- **Text Tool**: Add formatted text with custom fonts and sizes
- **Color Picker**: Full color palette with preset colors
- **Brush Sizes**: 1-50px adjustable brush sizes

### 🤝 **Real-time Collaboration**
- **WebRTC P2P**: Direct peer-to-peer communication
- **Live Drawing Sync**: See others draw in real-time
- **Multi-user Cursors**: Track other users' mouse positions
- **User Management**: See who's online with colored avatars
- **Room System**: Create or join rooms with unique IDs

### 💬 **Communication**
- **Real-time Chat**: Text messaging with all participants
- **User Identification**: Unique names and colors for each user
- **System Messages**: Join/leave notifications
- **Message History**: Persistent chat during session

### 🛠️ **Advanced Features**
- **Undo/Redo**: Full history management with 50-step memory
- **Canvas Export**: Save drawings as PNG images
- **Session Save**: Export canvas data as JSON
- **Zoom Controls**: Zoom in/out and reset view
- **Responsive Design**: Works on desktop, tablet, and mobile

### 📱 **User Experience**
- **Dark/Light Theme**: Automatic theme detection with manual toggle
- **Keyboard Shortcuts**: Quick access to tools and actions
- **Touch Support**: Full mobile and tablet compatibility
- **Connection Status**: Real-time connection monitoring
- **Loading States**: Smooth loading and error handling

## 🛠️ Technical Implementation

### Core Technologies
- **HTML5 Canvas**: High-performance drawing surface
- **WebRTC**: Peer-to-peer real-time communication
- **Vanilla JavaScript**: ES2022 modules, no frameworks
- **CSS3**: Custom properties, Grid, Flexbox, animations
- **IndexedDB**: Local storage for session data

### WebRTC Architecture
- **Signaling Server**: Mock implementation for demo (WebSocket in production)
- **STUN Servers**: Google STUN servers for NAT traversal
- **Data Channels**: Reliable ordered channels for drawing data
- **Peer Management**: Automatic connection handling and cleanup

### Drawing Engine
- **Canvas API**: Native HTML5 Canvas for optimal performance
- **Event Handling**: Mouse, touch, and keyboard input
- **Shape Preview**: Real-time shape drawing with temporary overlay
- **History System**: Efficient undo/redo with canvas snapshots

### Real-time Synchronization
- **Drawing Events**: Start, continue, and end drawing operations
- **Cursor Tracking**: Throttled position updates (50ms intervals)
- **Chat Messages**: JSON-based message protocol
- **State Management**: Consistent state across all peers

## 🚀 Getting Started

### Prerequisites
- Modern web browser with WebRTC support
- HTTPS connection (required for WebRTC)
- No build tools or dependencies required

### Installation
1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/portfolio-eight.git
   cd portfolio-eight/projects/collabo-canvas
   ```

2. Serve the files using any static server:
   ```bash
   # Using Python
   python -m http.server 8000
   
   # Using Node.js
   npx serve .
   
   # Using PHP
   php -S localhost:8000
   ```

3. Open `https://localhost:8000` in your browser (HTTPS required for WebRTC)

### Usage
1. **Create Room**: Click "Create New Room" to start a new collaboration session
2. **Join Room**: Enter a room ID and click "Join Room" to join existing session
3. **Draw Together**: Use the toolbar to select tools and start drawing
4. **Chat**: Click the chat icon to communicate with other users
5. **Share Room**: Share the room ID with others to invite them

## 🎮 Controls & Shortcuts

### Drawing Tools
- **P** - Pen tool
- **E** - Eraser tool
- **L** - Line tool
- **R** - Rectangle tool
- **C** - Circle tool
- **T** - Text tool

### Actions
- **Ctrl+Z** - Undo
- **Ctrl+Shift+Z** or **Ctrl+Y** - Redo
- **Ctrl+S** - Save canvas
- **Ctrl+E** - Export as image
- **Escape** - Close modals

### Navigation
- **Mouse Wheel** - Zoom in/out (future feature)
- **Space+Drag** - Pan canvas (future feature)

## 🏗️ Architecture

### Module Structure
```
collabo-canvas/
├── index.html          # Main HTML structure
├── styles.css          # Complete CSS styling
├── main.js            # Application coordinator
└── js/
    ├── webrtc.js      # WebRTC communication
    ├── canvas.js      # Drawing engine
    ├── tools.js       # Tool management
    ├── chat.js        # Chat functionality
    └── users.js       # User management
```

### Data Flow
1. **User Input** → Tools Manager → Canvas Manager
2. **Drawing Events** → WebRTC Manager → Remote Peers
3. **Remote Data** → WebRTC Manager → Canvas Manager
4. **Chat Messages** → Chat Manager → WebRTC Manager
5. **Cursor Movement** → Users Manager → WebRTC Manager

### WebRTC Protocol
```javascript
// Drawing data format
{
  type: 'draw',
  tool: 'pen',
  color: '#000000',
  size: 5,
  x: 100,
  y: 150,
  lastX: 95,
  lastY: 145,
  timestamp: 1640995200000
}

// Chat message format
{
  type: 'chat',
  message: 'Hello everyone!',
  author: 'peer_abc123',
  timestamp: 1640995200000
}
```

## 🎨 Customization

### Adding Custom Tools
```javascript
// In tools.js
this.tools.push('customTool');
this.toolConfigs.customTool = { 
  cursor: 'crosshair', 
  icon: '🔧' 
};
```

### Custom Color Palettes
```javascript
// In tools.js
this.userColors = [
  '#your-color-1',
  '#your-color-2',
  // Add more colors
];
```

### WebRTC Configuration
```javascript
// In webrtc.js
this.rtcConfig = {
  iceServers: [
    { urls: 'stun:your-stun-server.com:19302' },
    { 
      urls: 'turn:your-turn-server.com:3478',
      username: 'user',
      credential: 'pass'
    }
  ]
};
```

## 🧪 Testing

### Manual Testing Checklist
- [ ] Create and join rooms
- [ ] Draw with different tools and colors
- [ ] Real-time synchronization between users
- [ ] Chat functionality
- [ ] Cursor tracking
- [ ] Undo/redo operations
- [ ] Canvas export and save
- [ ] Mobile touch support
- [ ] Theme switching
- [ ] Connection handling

### Multi-user Testing
1. Open multiple browser windows/tabs
2. Create room in first window
3. Join room from other windows using room ID
4. Test drawing synchronization
5. Verify chat and cursor tracking

## 🔒 Security & Privacy

- **Peer-to-peer**: No data passes through central servers
- **Local Storage**: All data stored locally in browser
- **No Tracking**: No analytics or tracking scripts
- **Secure Context**: Requires HTTPS for WebRTC functionality
- **Room Privacy**: Rooms are private with unique IDs

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📝 License

This project is part of Portfolio Eight and is licensed under the MIT License.

## 🔗 Links

- [Live Demo](https://yourusername.github.io/portfolio-eight/projects/collabo-canvas/)
- [Source Code](https://github.com/yourusername/portfolio-eight/tree/main/projects/collabo-canvas)
- [Portfolio Home](https://yourusername.github.io/portfolio-eight/)
- [Other Projects](https://yourusername.github.io/portfolio-eight/#projects)

## 🙏 Acknowledgments

- [WebRTC API](https://developer.mozilla.org/en-US/docs/Web/API/WebRTC_API) for real-time communication
- [Canvas API](https://developer.mozilla.org/en-US/docs/Web/API/Canvas_API) for drawing functionality
- [STUN Servers](https://webrtc.github.io/samples/src/content/peerconnection/trickle-ice/) for NAT traversal

---

*Built with vanilla web technologies as part of the Portfolio Eight showcase*
