const express = require('express');
const cors = require('cors');
const path = require('path');
const SudokuSolver = require('./algorithms/solver');
const SudokuGenerator = require('./algorithms/generator');
const SudokuValidator = require('./algorithms/validator');

const app = express();
const PORT = process.env.PORT || 3002;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Initialize algorithm classes
const solver = new SudokuSolver();
const generator = new SudokuGenerator();
const validator = new SudokuValidator();

// API Routes

// Get API information
app.get('/api/info', (req, res) => {
  res.json({
    name: 'Sudoku Solver API',
    version: '1.0.0',
    description: 'Advanced Sudoku API with puzzle generation, solving algorithms, and difficulty level management',
    endpoints: {
      'POST /api/solve': 'Solve a Sudoku puzzle using advanced algorithms',
      'POST /api/generate': 'Generate a new Sudoku puzzle with specified difficulty',
      'POST /api/validate': 'Validate a Sudoku puzzle solution',
      'POST /api/hint': 'Get a hint for the next move',
      'POST /api/analyze': 'Analyze puzzle difficulty and solvability',
      'GET /api/info': 'Get API information'
    },
    algorithms: [
      'Backtracking with constraint propagation',
      'Naked singles elimination',
      'Hidden singles detection',
      'Intersection removal',
      'Advanced logical techniques'
    ],
    features: [
      'Multiple difficulty levels',
      'Step-by-step solving',
      'Puzzle generation',
      'Solution validation',
      'Hint system',
      'Performance metrics'
    ]
  });
});

// Solve Sudoku puzzle
app.post('/api/solve', (req, res) => {
  try {
    const { puzzle, method = 'advanced', stepByStep = false } = req.body;

    if (!puzzle || !Array.isArray(puzzle) || puzzle.length !== 9) {
      return res.status(400).json({
        success: false,
        error: 'Invalid puzzle format. Expected 9x9 array.'
      });
    }

    // Validate puzzle format
    if (!validator.isValidFormat(puzzle)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid puzzle format. Each row must have 9 numbers (0-9).'
      });
    }

    // Check if puzzle is valid
    if (!validator.isValidPuzzle(puzzle)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid puzzle. Contains contradictions.'
      });
    }

    const startTime = Date.now();
    const result = solver.solve(puzzle, { method, stepByStep });
    const endTime = Date.now();

    if (result.solved) {
      res.json({
        success: true,
        originalPuzzle: puzzle,
        solution: result.solution,
        method: method,
        solvingTime: endTime - startTime,
        steps: result.steps || null,
        difficulty: result.difficulty,
        techniques: result.techniques,
        statistics: {
          iterations: result.iterations,
          backtrackCount: result.backtrackCount,
          logicalSteps: result.logicalSteps
        }
      });
    } else {
      res.status(400).json({
        success: false,
        error: 'Puzzle is unsolvable',
        partialSolution: result.partialSolution,
        reason: result.reason
      });
    }
  } catch (error) {
    console.error('Error solving puzzle:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error while solving puzzle'
    });
  }
});

// Generate new Sudoku puzzle
app.post('/api/generate', (req, res) => {
  try {
    const { difficulty = 'medium', seed = null } = req.body;

    const validDifficulties = ['easy', 'medium', 'hard', 'expert', 'evil'];
    if (!validDifficulties.includes(difficulty)) {
      return res.status(400).json({
        success: false,
        error: `Invalid difficulty. Must be one of: ${validDifficulties.join(', ')}`
      });
    }

    const startTime = Date.now();
    const result = generator.generate(difficulty, seed);
    const endTime = Date.now();

    res.json({
      success: true,
      puzzle: result.puzzle,
      solution: result.solution,
      difficulty: difficulty,
      metadata: {
        clues: result.clueCount,
        uniqueSolution: result.uniqueSolution,
        generationTime: endTime - startTime,
        seed: result.seed,
        techniques: result.requiredTechniques
      }
    });
  } catch (error) {
    console.error('Error generating puzzle:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error while generating puzzle'
    });
  }
});

// Validate Sudoku solution
app.post('/api/validate', (req, res) => {
  try {
    const { puzzle } = req.body;

    if (!puzzle || !Array.isArray(puzzle) || puzzle.length !== 9) {
      return res.status(400).json({
        success: false,
        error: 'Invalid puzzle format. Expected 9x9 array.'
      });
    }

    if (!validator.isValidFormat(puzzle)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid puzzle format. Each row must have 9 numbers (1-9).'
      });
    }

    const isValid = validator.isValidSolution(puzzle);
    const conflicts = validator.findConflicts(puzzle);

    res.json({
      success: true,
      isValid: isValid,
      conflicts: conflicts,
      completeness: validator.getCompleteness(puzzle)
    });
  } catch (error) {
    console.error('Error validating puzzle:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error while validating puzzle'
    });
  }
});

// Get hint for next move
app.post('/api/hint', (req, res) => {
  try {
    const { puzzle } = req.body;

    if (!puzzle || !Array.isArray(puzzle) || puzzle.length !== 9) {
      return res.status(400).json({
        success: false,
        error: 'Invalid puzzle format. Expected 9x9 array.'
      });
    }

    if (!validator.isValidFormat(puzzle)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid puzzle format. Each row must have 9 numbers (0-9).'
      });
    }

    const hint = solver.getHint(puzzle);

    if (hint) {
      res.json({
        success: true,
        hint: {
          row: hint.row,
          col: hint.col,
          value: hint.value,
          technique: hint.technique,
          explanation: hint.explanation,
          difficulty: hint.difficulty
        }
      });
    } else {
      res.json({
        success: false,
        message: 'No hints available. Puzzle may be complete or unsolvable.'
      });
    }
  } catch (error) {
    console.error('Error getting hint:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error while getting hint'
    });
  }
});

// Analyze puzzle difficulty and techniques
app.post('/api/analyze', (req, res) => {
  try {
    const { puzzle } = req.body;

    if (!puzzle || !Array.isArray(puzzle) || puzzle.length !== 9) {
      return res.status(400).json({
        success: false,
        error: 'Invalid puzzle format. Expected 9x9 array.'
      });
    }

    if (!validator.isValidFormat(puzzle)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid puzzle format. Each row must have 9 numbers (0-9).'
      });
    }

    const analysis = solver.analyzePuzzle(puzzle);

    res.json({
      success: true,
      analysis: {
        difficulty: analysis.difficulty,
        solvable: analysis.solvable,
        uniqueSolution: analysis.uniqueSolution,
        clueCount: analysis.clueCount,
        requiredTechniques: analysis.techniques,
        estimatedTime: analysis.estimatedTime,
        complexity: analysis.complexity
      }
    });
  } catch (error) {
    console.error('Error analyzing puzzle:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error while analyzing puzzle'
    });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);
  res.status(500).json({
    success: false,
    error: 'Internal server error'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🧩 Sudoku Solver API running on port ${PORT}`);
  console.log(`📊 API Info: http://localhost:${PORT}/api/info`);
  console.log(`🎮 Demo: http://localhost:${PORT}`);
});

module.exports = app;
