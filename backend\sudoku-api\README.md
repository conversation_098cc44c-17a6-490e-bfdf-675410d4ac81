# 🧩 Sudoku Solver API

A professional-grade Sudoku API featuring advanced solving algorithms, intelligent puzzle generation, and comprehensive validation. Built with Node.js and Express, showcasing algorithmic problem-solving and clean backend architecture.

## 🚀 Quick Start

```bash
# Install dependencies
npm install

# Start the server
npm start

# For development with auto-reload
npm run dev
```

The API will be available at `http://localhost:3002`

## 🎯 Features

### 🔧 Advanced Solving Algorithms
- **Backtracking with Constraint Propagation** - Efficient recursive solving
- **Naked Singles** - Cells with only one possible value
- **Hidden Singles** - Numbers that can only go in one cell within a unit
- **Intersection Removal** - Advanced logical deduction techniques
- **Step-by-Step Solving** - Detailed solution paths with explanations

### 🎮 Intelligent Puzzle Generation
- **5 Difficulty Levels** - Easy, Medium, Hard, Expert, Evil
- **Unique Solution Guarantee** - Every puzzle has exactly one solution
- **Seeded Generation** - Reproducible puzzles for testing
- **Difficulty Calibration** - Puzzles require specific solving techniques

### ✅ Comprehensive Validation
- **Format Validation** - Ensures proper 9x9 grid structure
- **Conflict Detection** - Identifies rule violations in real-time
- **Solution Verification** - Validates complete puzzle solutions
- **Completeness Analysis** - Tracks puzzle progress

### 💡 Smart Hint System
- **Next Move Detection** - Finds the next logical step
- **Technique Explanations** - Educational guidance for learning
- **Difficulty Assessment** - Hints matched to puzzle complexity

### 🏢 Enterprise Features
- **TypeScript**: Full type safety with comprehensive interfaces
- **Production Monitoring**: Prometheus metrics and Grafana dashboards
- **Comprehensive Logging**: Structured logging with Winston and daily rotation
- **Docker Support**: Multi-stage builds with security best practices
- **Health Checks**: Kubernetes-ready liveness and readiness probes
- **API Documentation**: Interactive Swagger/OpenAPI documentation
- **Rate Limiting**: Configurable request throttling
- **Security**: Helmet middleware, CORS, input validation
- **Testing**: Comprehensive unit and integration test suites
- **Error Handling**: Detailed error classification and reporting

## 📡 API Endpoints

### POST `/api/solve`
Solve a Sudoku puzzle using advanced algorithms.

**Request Body:**
```json
{
  "puzzle": [[0,0,0,6,0,0,4,0,0], ...],
  "method": "advanced",
  "stepByStep": false
}
```

**Response:**
```json
{
  "success": true,
  "solution": [[5,3,4,6,7,8,9,1,2], ...],
  "solvingTime": 45,
  "techniques": ["NAKED_SINGLE", "HIDDEN_SINGLE"],
  "difficulty": "medium",
  "statistics": {
    "iterations": 127,
    "backtrackCount": 0,
    "logicalSteps": 23
  }
}
```

### POST `/api/generate`
Generate a new Sudoku puzzle with specified difficulty.

**Request Body:**
```json
{
  "difficulty": "medium",
  "seed": 12345
}
```

**Response:**
```json
{
  "success": true,
  "puzzle": [[0,0,0,6,0,0,4,0,0], ...],
  "solution": [[5,3,4,6,7,8,9,1,2], ...],
  "difficulty": "medium",
  "metadata": {
    "clues": 32,
    "uniqueSolution": true,
    "generationTime": 234,
    "requiredTechniques": ["NAKED_SINGLE", "HIDDEN_SINGLE"]
  }
}
```

### POST `/api/validate`
Validate a Sudoku puzzle and detect conflicts.

**Request Body:**
```json
{
  "puzzle": [[5,3,4,6,7,8,9,1,2], ...]
}
```

**Response:**
```json
{
  "success": true,
  "isValid": true,
  "conflicts": [],
  "completeness": {
    "filledCells": 81,
    "totalCells": 81,
    "percentage": 100
  }
}
```

### POST `/api/hint`
Get an intelligent hint for the next move.

**Request Body:**
```json
{
  "puzzle": [[0,0,0,6,0,0,4,0,0], ...]
}
```

**Response:**
```json
{
  "success": true,
  "hint": {
    "row": 0,
    "col": 0,
    "value": 5,
    "technique": "NAKED_SINGLE",
    "explanation": "This cell can only contain 5",
    "difficulty": "easy"
  }
}
```

### POST `/api/analyze`
Analyze puzzle difficulty and required techniques.

**Request Body:**
```json
{
  "puzzle": [[0,0,0,6,0,0,4,0,0], ...]
}
```

**Response:**
```json
{
  "success": true,
  "analysis": {
    "difficulty": "medium",
    "solvable": true,
    "uniqueSolution": true,
    "clueCount": 32,
    "requiredTechniques": ["NAKED_SINGLE", "HIDDEN_SINGLE"],
    "estimatedTime": "5-10 minutes",
    "complexity": 20
  }
}
```

### GET `/api/info`
Get API information and capabilities.

**Response:**
```json
{
  "name": "Sudoku Solver API",
  "version": "1.0.0",
  "description": "Advanced Sudoku API with puzzle generation, solving algorithms, and difficulty level management",
  "endpoints": { ... },
  "algorithms": [ ... ],
  "features": [ ... ]
}
```

## 🏗️ Architecture

### Core Components

1. **SudokuSolver** (`algorithms/solver.js`)
   - Advanced solving algorithms
   - Multiple solving strategies
   - Performance optimization
   - Step-by-step tracking

2. **SudokuGenerator** (`algorithms/generator.js`)
   - Intelligent puzzle creation
   - Difficulty calibration
   - Unique solution guarantee
   - Seeded generation

3. **SudokuValidator** (`algorithms/validator.js`)
   - Comprehensive validation
   - Conflict detection
   - Format verification
   - Solution checking

### Solving Techniques

- **Naked Singles**: Cells with only one possible value
- **Hidden Singles**: Numbers that can only go in one cell within a unit
- **Intersection Removal**: Advanced constraint propagation
- **Backtracking**: Recursive solving with intelligent pruning

### Difficulty Levels

| Level  | Clues | Techniques Required |
|--------|-------|-------------------|
| Easy   | 36-46 | Naked Singles only |
| Medium | 28-35 | + Hidden Singles |
| Hard   | 22-27 | + Intersection Removal |
| Expert | 17-21 | + Advanced techniques |
| Evil   | 17-20 | + Backtracking required |

## 🎮 Interactive Demo

The API includes a complete interactive Sudoku game interface:

- **Professional UI** - Clean, responsive design
- **Real-time Validation** - Instant conflict detection
- **Keyboard Navigation** - Arrow keys and number input
- **Hint System** - Intelligent move suggestions
- **Multiple Difficulties** - All 5 difficulty levels
- **Progress Tracking** - Completion percentage and statistics

Access the demo at: `http://localhost:3002/public/index.html`

## 🧪 Testing

```bash
# Run basic tests
npm test

# Test specific endpoints
curl -X POST http://localhost:3002/api/solve \
  -H "Content-Type: application/json" \
  -d '{"puzzle": [[0,0,0,6,0,0,4,0,0], ...]}'
```

## 🔧 Configuration

### Environment Variables

- `PORT` - Server port (default: 3002)
- `NODE_ENV` - Environment mode (development/production)

### CORS Configuration

The API is configured to accept requests from any origin for development. In production, configure CORS to restrict access to your frontend domain.

## 📈 Performance

- **Solving Speed**: Most puzzles solved in <100ms
- **Generation Time**: New puzzles created in <500ms
- **Memory Usage**: Efficient algorithms with minimal memory footprint
- **Scalability**: Stateless design supports concurrent requests

## 🚀 Deployment

### Production Setup

1. Set environment variables
2. Configure CORS for your domain
3. Use process manager (PM2, Forever)
4. Set up reverse proxy (Nginx)
5. Enable HTTPS

### Docker Deployment

```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3002
CMD ["npm", "start"]
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Implement your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

## 🎯 Technical Highlights

This project demonstrates:

- **Advanced Algorithm Implementation** - Multiple solving strategies
- **Clean Architecture** - Separation of concerns and modularity
- **Professional API Design** - RESTful endpoints with comprehensive error handling
- **Performance Optimization** - Efficient algorithms and data structures
- **Complete Documentation** - Comprehensive API documentation and examples
- **Production Readiness** - Error handling, validation, and security considerations

Perfect for showcasing backend development skills, algorithmic thinking, and professional software engineering practices.
