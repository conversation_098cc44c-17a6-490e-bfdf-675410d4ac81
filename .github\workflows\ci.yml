name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  lint-and-test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [20.x]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'

    - name: Install root dependencies
      run: npm ci

    - name: Install backend dependencies
      run: |
        cd backend/wordsmith-api && npm install
        cd ../mastermind-api && npm install

    - name: Lint JavaScript files
      run: npm run lint

    - name: Check code formatting
      run: npm run format -- --check

    - name: Test backend services
      run: |
        # Test Wordsmith API
        cd backend/wordsmith-api
        timeout 10s npm start &
        sleep 5
        curl -f http://localhost:3000/start || exit 1
        pkill -f "node server.js" || true
        
        # Test Mastermind API  
        cd ../mastermind-api
        timeout 10s npm start &
        sleep 5
        curl -f http://localhost:3001 || exit 1
        pkill -f "node ws-server.js" || true

  lighthouse:
    runs-on: ubuntu-latest
    needs: lint-and-test
    if: github.event_name == 'pull_request'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Start local server
      run: |
        npm run serve &
        sleep 5

    - name: Run Lighthouse CI
      run: |
        npm install -g @lhci/cli@0.12.x
        lhci autorun --upload.target=temporary-public-storage
      env:
        LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}
