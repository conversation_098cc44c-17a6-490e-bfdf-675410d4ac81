# 🎯 Wordle API - Full-Stack Demo

A complete Node.js API implementation of the popular Wordle game with word validation, game state management, and an interactive frontend demo.

## 🚀 Quick Start (Portfolio Demo)

**Want to see it in action? Follow these simple steps:**

1. **Navigate to the project:** `cd backend/wordle-api`
2. **Install dependencies:** `npm install`
3. **Start the server:** `npm start`
4. **Play the game:** Open `http://localhost:3001` in your browser!

This showcases a **complete full-stack application** with professional backend API and interactive frontend.

## 🚀 Features

- **Complete Game Logic**: Full Wordle implementation with 6-guess limit
- **Word Validation**: 315+ target words and 2000+ valid guesses
- **Daily Words**: Deterministic daily word selection
- **Game State Management**: Track guesses, feedback, and game status
- **RESTful API**: Clean, documented endpoints
- **Demo Client**: Interactive web interface for testing
- **Error Handling**: Comprehensive error responses
- **CORS Support**: Ready for frontend integration

## 📋 API Endpoints

### Create New Game
```http
POST /api/game/new
```

**Response:**
```json
{
  "success": true,
  "gameId": "1703123456789abc123def",
  "gameState": {
    "gameId": "1703123456789abc123def",
    "guesses": [],
    "gameState": "playing",
    "maxGuesses": 6,
    "targetWord": null,
    "startTime": "2024-01-01T12:00:00.000Z",
    "endTime": null
  }
}
```

### Make a Guess
```http
POST /api/game/:gameId/guess
Content-Type: application/json

{
  "word": "CRANE"
}
```

**Response:**
```json
{
  "success": true,
  "guess": {
    "word": "CRANE",
    "feedback": ["absent", "correct", "absent", "present", "absent"],
    "timestamp": "2024-01-01T12:01:00.000Z"
  },
  "gameState": {
    "gameId": "1703123456789abc123def",
    "guesses": [...],
    "gameState": "playing",
    "maxGuesses": 6,
    "targetWord": null,
    "startTime": "2024-01-01T12:00:00.000Z",
    "endTime": null
  }
}
```

### Get Game State
```http
GET /api/game/:gameId
```

### Validate Word
```http
POST /api/validate
Content-Type: application/json

{
  "word": "CRANE"
}
```

**Response:**
```json
{
  "success": true,
  "word": "CRANE",
  "isValid": true,
  "isTarget": true
}
```

### API Information
```http
GET /api/info
```

## 🎮 Feedback System

The API returns feedback for each letter in a guess:

- **`correct`**: Letter is in the correct position (green)
- **`present`**: Letter is in the word but wrong position (yellow)
- **`absent`**: Letter is not in the word (gray)

## 🛠️ Installation & Setup

1. **Clone and Navigate:**
   ```bash
   cd backend/wordle-api
   ```

2. **Install Dependencies:**
   ```bash
   npm install
   ```

3. **Start Development Server:**
   ```bash
   npm run dev
   ```

4. **Start Production Server:**
   ```bash
   npm start
   ```

5. **Access the API:**
   - API Base URL: `http://localhost:3001`
   - Demo Client: `http://localhost:3001`
   - API Info: `http://localhost:3001/api/info`

## 📦 Dependencies

- **express**: Web framework for Node.js
- **cors**: Cross-Origin Resource Sharing middleware
- **nodemon**: Development server with auto-restart

## 🎯 Game Rules

1. **Objective**: Guess the 5-letter word in 6 attempts
2. **Daily Words**: New word each day, same for all players
3. **Valid Words**: Only dictionary words accepted
4. **Feedback**: Color-coded hints after each guess
5. **Win Condition**: Guess the exact word
6. **Lose Condition**: Use all 6 guesses without success

## 🔧 Technical Details

### Word Lists
- **Target Words**: 315 carefully selected 5-letter words
- **Valid Guesses**: 2000+ acceptable words for validation
- **Daily Rotation**: Deterministic selection based on date

### Game State
- **In-Memory Storage**: Games stored in Map (use database for production)
- **Unique Game IDs**: Timestamp + random string
- **State Tracking**: Playing, won, lost states
- **Timing**: Start and end timestamps

### Error Handling
- **Validation Errors**: Invalid words, wrong length
- **Game Errors**: Game not found, already finished
- **Server Errors**: Comprehensive error responses

## 🚀 Production Deployment

For production deployment:

1. **Use a Database**: Replace in-memory storage with MongoDB/PostgreSQL
2. **Add Authentication**: Implement user accounts and sessions
3. **Rate Limiting**: Add request rate limiting
4. **Logging**: Implement proper logging system
5. **Environment Variables**: Use .env for configuration
6. **HTTPS**: Enable SSL/TLS encryption

## 📊 Example Usage

```javascript
// Create new game
const gameResponse = await fetch('/api/game/new', {
  method: 'POST'
});
const { gameId } = await gameResponse.json();

// Make a guess
const guessResponse = await fetch(`/api/game/${gameId}/guess`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ word: 'CRANE' })
});
const result = await guessResponse.json();
```

## 🎨 Demo Client Features

- **Interactive Interface**: Clean, responsive design
- **Real-time Feedback**: Visual letter coloring
- **Error Handling**: User-friendly error messages
- **Keyboard Support**: Enter key and auto-uppercase
- **Game Management**: New game and guess functionality

## 📝 License

MIT License - feel free to use this code for your own projects!

---

**Built with ❤️ for the Portfolio Six showcase**
