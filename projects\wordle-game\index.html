<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Wordle Game - Zayden Sharp</title>
  <meta name="description" content="Complete Wordle recreation with word validation and statistics">
  <meta name="keywords" content="wordle, word game, puzzle, javascript, portfolio">
  <meta name="author" content="Zayden Sharp">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://zaydenjs.github.io/PortFolio-2025/projects/wordle-game/">
  <meta property="og:title" content="Wordle Game - Word Puzzle Challenge">
  <meta property="og:description" content="Complete Wordle recreation with statistics and daily challenges">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary">
  <meta property="twitter:url" content="https://zaydenjs.github.io/PortFolio-2025/projects/wordle-game/">
  <meta property="twitter:title" content="Wordle Game - Word Puzzle Challenge">
  <meta property="twitter:description" content="Complete Wordle recreation with statistics and daily challenges">

  <link rel="icon" type="image/svg+xml" href="../../assets/favicon.svg">
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="container">
    <!-- Header -->
    <header class="header">
      <div class="header-content">
        <div class="header-left">
          <a href="../../index.html" class="back-button">← Back to Portfolio</a>
          <h1 class="title">WORDLE</h1>
        </div>
        <div class="header-buttons">
          <button class="icon-btn" id="helpBtn" aria-label="How to play">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z"/>
            </svg>
          </button>
          <button class="icon-btn" id="statsBtn" aria-label="Statistics">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M16,11V3H8v6H2v12h20V11H16z M10,5h4v14h-4V5z M4,11h4v8H4V11z M20,19h-4v-6h4V19z"/>
            </svg>
          </button>
          <button class="icon-btn" id="settingsBtn" aria-label="Settings">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.82,11.69,4.82,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
            </svg>
          </button>
        </div>
      </div>
    </header>

    <!-- Game Board -->
    <main class="game-container">
      <div class="board" id="gameBoard">
        <!-- Game grid will be generated by JavaScript -->
      </div>
      
      <!-- Game Messages -->
      <div class="message" id="gameMessage" aria-live="polite"></div>
      
      <!-- Virtual Keyboard -->
      <div class="keyboard" id="keyboard">
        <div class="keyboard-row">
          <button class="key" data-key="Q">Q</button>
          <button class="key" data-key="W">W</button>
          <button class="key" data-key="E">E</button>
          <button class="key" data-key="R">R</button>
          <button class="key" data-key="T">T</button>
          <button class="key" data-key="Y">Y</button>
          <button class="key" data-key="U">U</button>
          <button class="key" data-key="I">I</button>
          <button class="key" data-key="O">O</button>
          <button class="key" data-key="P">P</button>
        </div>
        <div class="keyboard-row">
          <button class="key" data-key="A">A</button>
          <button class="key" data-key="S">S</button>
          <button class="key" data-key="D">D</button>
          <button class="key" data-key="F">F</button>
          <button class="key" data-key="G">G</button>
          <button class="key" data-key="H">H</button>
          <button class="key" data-key="J">J</button>
          <button class="key" data-key="K">K</button>
          <button class="key" data-key="L">L</button>
        </div>
        <div class="keyboard-row">
          <button class="key key-large" data-key="Enter">ENTER</button>
          <button class="key" data-key="Z">Z</button>
          <button class="key" data-key="X">X</button>
          <button class="key" data-key="C">C</button>
          <button class="key" data-key="V">V</button>
          <button class="key" data-key="B">B</button>
          <button class="key" data-key="N">N</button>
          <button class="key" data-key="M">M</button>
          <button class="key key-large" data-key="Backspace">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M22 3H7c-.69 0-1.23.35-1.59.88L0 12l5.41 8.11c.36.53.9.89 1.59.89h15c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-3 12.59L17.59 17 14 13.41 10.41 17 9 15.59 12.59 12 9 8.41 10.41 7 14 10.59 17.59 7 19 8.41 15.41 12 19 15.59z"/>
            </svg>
          </button>
        </div>
      </div>
    </main>

    <!-- Modals -->
    <!-- Help Modal -->
    <div class="modal" id="helpModal">
      <div class="modal-content">
        <div class="modal-header">
          <h2>How To Play</h2>
          <button class="close-btn" data-modal="helpModal">&times;</button>
        </div>
        <div class="modal-body">
          <p>Guess the <strong>WORDLE</strong> in 6 tries.</p>
          <ul>
            <li>Each guess must be a valid 5-letter word.</li>
            <li>Hit the enter button to submit.</li>
            <li>After each guess, the color of the tiles will change to show how close your guess was to the word.</li>
          </ul>
          
          <div class="examples">
            <p><strong>Examples</strong></p>
            <div class="example-row">
              <div class="tile correct">W</div>
              <div class="tile">E</div>
              <div class="tile">A</div>
              <div class="tile">R</div>
              <div class="tile">Y</div>
            </div>
            <p>The letter <strong>W</strong> is in the word and in the correct spot.</p>
            
            <div class="example-row">
              <div class="tile">P</div>
              <div class="tile present">I</div>
              <div class="tile">L</div>
              <div class="tile">L</div>
              <div class="tile">S</div>
            </div>
            <p>The letter <strong>I</strong> is in the word but in the wrong spot.</p>
            
            <div class="example-row">
              <div class="tile">V</div>
              <div class="tile">A</div>
              <div class="tile">G</div>
              <div class="tile absent">U</div>
              <div class="tile">E</div>
            </div>
            <p>The letter <strong>U</strong> is not in the word in any spot.</p>
          </div>
          
          <p><strong>A new WORDLE will be available each day!</strong></p>
        </div>
      </div>
    </div>

    <!-- Statistics Modal -->
    <div class="modal" id="statsModal">
      <div class="modal-content">
        <div class="modal-header">
          <h2>Statistics</h2>
          <button class="close-btn" data-modal="statsModal">&times;</button>
        </div>
        <div class="modal-body">
          <div class="stats-container">
            <div class="stat">
              <div class="stat-number" id="gamesPlayed">0</div>
              <div class="stat-label">Played</div>
            </div>
            <div class="stat">
              <div class="stat-number" id="winPercentage">0</div>
              <div class="stat-label">Win %</div>
            </div>
            <div class="stat">
              <div class="stat-number" id="currentStreak">0</div>
              <div class="stat-label">Current Streak</div>
            </div>
            <div class="stat">
              <div class="stat-number" id="maxStreak">0</div>
              <div class="stat-label">Max Streak</div>
            </div>
          </div>
          
          <div class="guess-distribution">
            <h3>Guess Distribution</h3>
            <div class="distribution-container" id="distributionContainer">
              <!-- Distribution bars will be generated by JavaScript -->
            </div>
          </div>
          
          <div class="next-wordle">
            <h3>Next Wordle</h3>
            <div class="countdown" id="countdown">--:--:--</div>
          </div>
          
          <button class="share-btn" id="shareBtn">Share</button>
        </div>
      </div>
    </div>

    <!-- Settings Modal -->
    <div class="modal" id="settingsModal">
      <div class="modal-content">
        <div class="modal-header">
          <h2>Settings</h2>
          <button class="close-btn" data-modal="settingsModal">&times;</button>
        </div>
        <div class="modal-body">
          <div class="setting">
            <div class="setting-text">
              <div class="setting-title">Hard Mode</div>
              <div class="setting-description">Any revealed hints must be used in subsequent guesses</div>
            </div>
            <div class="setting-switch">
              <input type="checkbox" id="hardMode" class="switch-input">
              <label for="hardMode" class="switch-label"></label>
            </div>
          </div>
          
          <div class="setting">
            <div class="setting-text">
              <div class="setting-title">Dark Theme</div>
              <div class="setting-description">Change the appearance of the game</div>
            </div>
            <div class="setting-switch">
              <input type="checkbox" id="darkTheme" class="switch-input">
              <label for="darkTheme" class="switch-label"></label>
            </div>
          </div>
          
          <div class="setting">
            <div class="setting-text">
              <div class="setting-title">High Contrast Mode</div>
              <div class="setting-description">For improved color vision</div>
            </div>
            <div class="setting-switch">
              <input type="checkbox" id="highContrast" class="switch-input">
              <label for="highContrast" class="switch-label"></label>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Toast Notifications -->
    <div class="toast" id="toast"></div>
  </div>

  <script src="words.js"></script>
  <script src="main.js"></script>
</body>
</html>
