const SudokuSolver = require('./solver');

class SudokuGenerator {
  constructor() {
    this.solver = new SudokuSolver();
    this.difficultySettings = {
      easy: { minClues: 36, maxClues: 46, techniques: ['NAKED_SINGLE'] },
      medium: { minClues: 28, maxClues: 35, techniques: ['NAKED_SINGLE', 'HIDDEN_SINGLE'] },
      hard: { minClues: 22, maxClues: 27, techniques: ['NAKED_SINGLE', 'HIDDEN_SINGLE', 'INTERSECTION_REMOVAL'] },
      expert: { minClues: 17, maxClues: 21, techniques: ['NAKED_SINGLE', 'HIDDEN_SINGLE', 'INTERSECTION_REMOVAL', 'NAKED_PAIR'] },
      evil: { minClues: 17, maxClues: 20, techniques: ['NAKED_SINGLE', 'HIDDEN_SINGLE', 'INTERSECTION_REMOVAL', 'NAKED_PAIR', 'BACKTRACKING'] }
    };
  }

  // Generate a new Sudoku puzzle
  generate(difficulty = 'medium', seed = null) {
    if (seed) {
      this.setSeed(seed);
    }

    const settings = this.difficultySettings[difficulty];
    if (!settings) {
      throw new Error(`Invalid difficulty: ${difficulty}`);
    }

    // Generate a complete solution first
    const solution = this.generateCompleteSolution();
    
    // Create puzzle by removing numbers
    const puzzle = this.createPuzzle(solution, settings);
    
    // Verify the puzzle meets difficulty requirements
    const verification = this.verifyDifficulty(puzzle, difficulty);
    
    return {
      puzzle: puzzle,
      solution: solution,
      clueCount: this.countClues(puzzle),
      uniqueSolution: verification.uniqueSolution,
      seed: seed || Date.now(),
      requiredTechniques: verification.techniques
    };
  }

  // Generate a complete valid Sudoku solution
  generateCompleteSolution() {
    // Start with empty grid
    const grid = Array(9).fill().map(() => Array(9).fill(0));
    
    // Fill diagonal boxes first (they don't interfere with each other)
    this.fillDiagonalBoxes(grid);
    
    // Fill remaining cells using backtracking
    this.fillRemaining(grid, 0, 3);
    
    return grid;
  }

  // Fill the three diagonal 3x3 boxes
  fillDiagonalBoxes(grid) {
    // Fill boxes at positions (0,0), (3,3), (6,6)
    this.fillBox(grid, 0, 0);
    this.fillBox(grid, 3, 3);
    this.fillBox(grid, 6, 6);
  }

  // Fill a 3x3 box with random valid numbers
  fillBox(grid, startRow, startCol) {
    const numbers = this.shuffleArray([1, 2, 3, 4, 5, 6, 7, 8, 9]);
    let index = 0;
    
    for (let row = startRow; row < startRow + 3; row++) {
      for (let col = startCol; col < startCol + 3; col++) {
        grid[row][col] = numbers[index++];
      }
    }
  }

  // Fill remaining cells using backtracking
  fillRemaining(grid, row = 0, col = 0) {
    // Find next empty cell
    let currentRow = row;
    let currentCol = col;

    while (currentRow < 9) {
      while (currentCol < 9) {
        if (grid[currentRow][currentCol] === 0) {
          break;
        }
        currentCol++;
      }
      if (currentCol < 9) break;
      currentRow++;
      currentCol = 0;
    }

    // If no empty cell found, we're done
    if (currentRow >= 9) {
      return true;
    }

    // Try numbers 1-9 in random order
    const numbers = this.shuffleArray([1, 2, 3, 4, 5, 6, 7, 8, 9]);

    for (const num of numbers) {
      if (this.solver.isValidMove(grid, currentRow, currentCol, num)) {
        grid[currentRow][currentCol] = num;

        if (this.fillRemaining(grid, currentRow, currentCol + 1)) {
          return true;
        }

        grid[currentRow][currentCol] = 0; // Backtrack
      }
    }

    return false;
  }

  // Create puzzle by removing numbers from complete solution
  createPuzzle(solution, settings) {
    const puzzle = solution.map(row => [...row]);
    const { minClues, maxClues } = settings;
    
    // Get all cell positions
    const positions = [];
    for (let row = 0; row < 9; row++) {
      for (let col = 0; col < 9; col++) {
        positions.push([row, col]);
      }
    }
    
    // Shuffle positions for random removal
    this.shuffleArray(positions);
    
    // Remove numbers while maintaining unique solution
    let clueCount = 81;
    const targetClues = minClues + Math.floor(Math.random() * (maxClues - minClues + 1));
    
    for (const [row, col] of positions) {
      if (clueCount <= targetClues) break;
      
      const originalValue = puzzle[row][col];
      puzzle[row][col] = 0;
      
      // Check if puzzle still has unique solution
      if (this.hasUniqueSolution(puzzle)) {
        clueCount--;
      } else {
        // Restore the number if removing it creates multiple solutions
        puzzle[row][col] = originalValue;
      }
    }
    
    return puzzle;
  }

  // Check if puzzle has a unique solution
  hasUniqueSolution(puzzle) {
    const solutions = [];
    this.findAllSolutions(puzzle.map(row => [...row]), solutions, 2); // Stop after finding 2 solutions
    return solutions.length === 1;
  }

  // Find all solutions (up to maxSolutions)
  findAllSolutions(grid, solutions, maxSolutions) {
    if (solutions.length >= maxSolutions) return;
    
    const emptyCell = this.solver.findEmptyCell(grid);
    if (!emptyCell) {
      solutions.push(grid.map(row => [...row]));
      return;
    }
    
    const [row, col] = emptyCell;
    
    for (let num = 1; num <= 9; num++) {
      if (this.solver.isValidMove(grid, row, col, num)) {
        grid[row][col] = num;
        this.findAllSolutions(grid, solutions, maxSolutions);
        grid[row][col] = 0;
        
        if (solutions.length >= maxSolutions) return;
      }
    }
  }

  // Verify that puzzle meets difficulty requirements
  verifyDifficulty(puzzle, difficulty) {
    const analysis = this.solver.analyzePuzzle(puzzle);
    const settings = this.difficultySettings[difficulty];
    
    // Check if required techniques are used
    const hasRequiredTechniques = settings.techniques.some(technique => 
      analysis.techniques.includes(technique)
    );
    
    return {
      uniqueSolution: analysis.uniqueSolution,
      techniques: analysis.techniques,
      meetsRequirements: hasRequiredTechniques && 
                        analysis.clueCount >= settings.minClues && 
                        analysis.clueCount <= settings.maxClues
    };
  }

  // Utility methods
  shuffleArray(array) {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }

  countClues(puzzle) {
    let count = 0;
    for (let row = 0; row < 9; row++) {
      for (let col = 0; col < 9; col++) {
        if (puzzle[row][col] !== 0) count++;
      }
    }
    return count;
  }

  setSeed(seed) {
    // Simple seeded random number generator
    this.seed = seed;
    this.random = () => {
      this.seed = (this.seed * 9301 + 49297) % 233280;
      return this.seed / 233280;
    };
    
    // Override Math.random for deterministic generation
    Math.random = this.random;
  }

  // Generate puzzle with specific pattern (for advanced generation)
  generateWithPattern(pattern, difficulty = 'medium') {
    // This would implement pattern-based generation
    // For now, return standard generation
    return this.generate(difficulty);
  }

  // Generate minimal puzzle (fewest clues possible)
  generateMinimal() {
    const solution = this.generateCompleteSolution();
    const puzzle = solution.map(row => [...row]);
    
    // Try to remove as many clues as possible while maintaining unique solution
    const positions = [];
    for (let row = 0; row < 9; row++) {
      for (let col = 0; col < 9; col++) {
        positions.push([row, col]);
      }
    }
    
    this.shuffleArray(positions);
    
    for (const [row, col] of positions) {
      const originalValue = puzzle[row][col];
      puzzle[row][col] = 0;
      
      if (!this.hasUniqueSolution(puzzle)) {
        puzzle[row][col] = originalValue;
      }
    }
    
    return {
      puzzle: puzzle,
      solution: solution,
      clueCount: this.countClues(puzzle),
      uniqueSolution: true,
      minimal: true
    };
  }
}

module.exports = SudokuGenerator;
