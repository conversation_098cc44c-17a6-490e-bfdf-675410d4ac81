// Wordle word lists for the API
const WORDS = [
  'ABOUT', 'ABOVE', 'ABUSE', 'ACTOR', 'ACUTE', 'ADMIT', 'ADOPT', 'ADULT', 'AFTER', 'AGAIN',
  'AGENT', 'AGREE', 'AHEAD', 'ALARM', 'ALBUM', 'ALERT', 'ALIE<PERSON>', 'ALIGN', 'ALI<PERSON>', 'ALIVE',
  'ALLOW', 'ALONE', 'ALONG', 'ALTER', 'AMONG', 'ANGER', 'ANGLE', 'ANGRY', 'APART', 'APPLE',
  'APPLY', 'ARENA', 'ARGUE', 'ARISE', 'ARRAY', 'ASIDE', 'ASSET', 'AUDIO', 'AUDIT', 'AVOID',
  'AWAKE', 'AWARD', 'AWARE', 'BADLY', 'BAKER', 'BASES', 'BASIC', 'BEACH', '<PERSON><PERSON><PERSON>', 'BEGIN',
  'BEING', '<PERSON><PERSON><PERSON>', '<PERSON>EN<PERSON>', 'B<PERSON>LY', 'BIRTH', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>LA<PERSON>', 'BLAN<PERSON>', 'BLIN<PERSON>', 'B<PERSON><PERSON><PERSON>',
  'BLOOD', 'BOARD', 'BOOST', 'BOOTH', 'BOUND', 'BRAIN', 'BRAND', 'BRASS', 'BRAVE', 'BREAD',
  'BREAK', 'BREED', 'BRIEF', 'BRING', 'BROAD', 'BROKE', 'BROWN', 'BUILD', 'BUILT', 'BUYER',
  'CABLE', 'CALIF', 'CARRY', 'CATCH', 'CAUSE', 'CHAIN', 'CHAIR', 'CHAOS', 'CHARM', 'CHART',
  'CHASE', 'CHEAP', 'CHECK', 'CHEST', 'CHIEF', 'CHILD', 'CHINA', 'CHOSE', 'CIVIL', 'CLAIM',
  'CLASS', 'CLEAN', 'CLEAR', 'CLICK', 'CLIMB', 'CLOCK', 'CLOSE', 'CLOUD', 'COACH', 'COAST',
  'COULD', 'COUNT', 'COURT', 'COVER', 'CRAFT', 'CRASH', 'CRAZY', 'CREAM', 'CRIME', 'CROSS',
  'CROWD', 'CROWN', 'CRUDE', 'CURVE', 'CYCLE', 'DAILY', 'DANCE', 'DATED', 'DEALT', 'DEATH',
  'DEBUT', 'DELAY', 'DEPTH', 'DOING', 'DOUBT', 'DOZEN', 'DRAFT', 'DRAMA', 'DRANK', 'DRAWN',
  'DREAM', 'DRESS', 'DRILL', 'DRINK', 'DRIVE', 'DROVE', 'DYING', 'EAGER', 'EARLY', 'EARTH',
  'EIGHT', 'ELITE', 'EMPTY', 'ENEMY', 'ENJOY', 'ENTER', 'ENTRY', 'EQUAL', 'ERROR', 'EVENT',
  'EVERY', 'EXACT', 'EXIST', 'EXTRA', 'FAITH', 'FALSE', 'FAULT', 'FIBER', 'FIELD', 'FIFTH',
  'FIFTY', 'FIGHT', 'FINAL', 'FIRST', 'FIXED', 'FLASH', 'FLEET', 'FLOOR', 'FLUID', 'FOCUS',
  'FORCE', 'FORTH', 'FORTY', 'FORUM', 'FOUND', 'FRAME', 'FRANK', 'FRAUD', 'FRESH', 'FRONT',
  'FRUIT', 'FULLY', 'FUNNY', 'GIANT', 'GIVEN', 'GLASS', 'GLOBE', 'GOING', 'GRACE', 'GRADE',
  'GRAND', 'GRANT', 'GRASS', 'GRAVE', 'GREAT', 'GREEN', 'GROSS', 'GROUP', 'GROWN', 'GUARD',
  'GUESS', 'GUEST', 'GUIDE', 'HAPPY', 'HARRY', 'HEART', 'HEAVY', 'HENCE', 'HENRY', 'HORSE',
  'HOTEL', 'HOUSE', 'HUMAN', 'IDEAL', 'IMAGE', 'INDEX', 'INNER', 'INPUT', 'ISSUE', 'JAPAN',
  'JIMMY', 'JOINT', 'JONES', 'JUDGE', 'KNOWN', 'LABEL', 'LARGE', 'LASER', 'LATER', 'LAUGH',
  'LAYER', 'LEARN', 'LEASE', 'LEAST', 'LEAVE', 'LEGAL', 'LEVEL', 'LEWIS', 'LIGHT', 'LIMIT',
  'LINKS', 'LIVES', 'LOCAL', 'LOOSE', 'LOWER', 'LUCKY', 'LUNCH', 'LYING', 'MAGIC', 'MAJOR',
  'MAKER', 'MARCH', 'MARIA', 'MATCH', 'MAYBE', 'MAYOR', 'MEANT', 'MEDIA', 'METAL', 'MIGHT',
  'MINOR', 'MINUS', 'MIXED', 'MODEL', 'MONEY', 'MONTH', 'MORAL', 'MOTOR', 'MOUNT', 'MOUSE',
  'MOUTH', 'MOVED', 'MOVIE', 'MUSIC', 'NEEDS', 'NEVER', 'NEWLY', 'NIGHT', 'NOISE', 'NORTH',
  'NOTED', 'NOVEL', 'NURSE', 'OCCUR', 'OCEAN', 'OFFER', 'OFTEN', 'ORDER', 'OTHER', 'OUGHT',
  'PAINT', 'PANEL', 'PAPER', 'PARTY', 'PEACE', 'PETER', 'PHASE', 'PHONE', 'PHOTO', 'PIANO',
  'PIECE', 'PILOT', 'PITCH', 'PLACE', 'PLAIN', 'PLANE', 'PLANT', 'PLATE', 'POINT', 'POUND',
  'POWER', 'PRESS', 'PRICE', 'PRIDE', 'PRIME', 'PRINT', 'PRIOR', 'PRIZE', 'PROOF', 'PROUD',
  'PROVE', 'QUEEN', 'QUICK', 'QUIET', 'QUITE', 'RADIO', 'RAISE', 'RANGE', 'RAPID', 'RATIO',
  'REACH', 'READY', 'REALM', 'REBEL', 'REFER', 'RELAX', 'REPAY', 'REPLY', 'RIGHT', 'RIGID',
  'RIVAL', 'RIVER', 'ROBIN', 'ROGER', 'ROMAN', 'ROUGH', 'ROUND', 'ROUTE', 'ROYAL', 'RURAL',
  'SCALE', 'SCENE', 'SCOPE', 'SCORE', 'SENSE', 'SERVE', 'SETUP', 'SEVEN', 'SHALL', 'SHAPE',
  'SHARE', 'SHARP', 'SHEET', 'SHELF', 'SHELL', 'SHIFT', 'SHINE', 'SHIRT', 'SHOCK', 'SHOOT',
  'SHORT', 'SHOWN', 'SIDES', 'SIGHT', 'SILLY', 'SINCE', 'SIXTH', 'SIXTY', 'SIZED', 'SKILL',
  'SLEEP', 'SLIDE', 'SMALL', 'SMART', 'SMILE', 'SMITH', 'SMOKE', 'SOLID', 'SOLVE', 'SORRY',
  'SOUND', 'SOUTH', 'SPACE', 'SPARE', 'SPEAK', 'SPEED', 'SPEND', 'SPENT', 'SPLIT', 'SPOKE',
  'SPORT', 'STAFF', 'STAGE', 'STAKE', 'STAND', 'START', 'STATE', 'STEAM', 'STEEL', 'STEEP',
  'STEER', 'STICK', 'STILL', 'STOCK', 'STONE', 'STOOD', 'STORE', 'STORM', 'STORY', 'STRIP',
  'STUCK', 'STUDY', 'STUFF', 'STYLE', 'SUGAR', 'SUITE', 'SUPER', 'SWEET', 'TABLE', 'TAKEN',
  'TASTE', 'TAXES', 'TEACH', 'TEAMS', 'TEETH', 'TERRY', 'TEXAS', 'THANK', 'THEFT', 'THEIR',
  'THEME', 'THERE', 'THESE', 'THICK', 'THING', 'THINK', 'THIRD', 'THOSE', 'THREE', 'THREW',
  'THROW', 'THUMB', 'TIGHT', 'TIRED', 'TITLE', 'TODAY', 'TOPIC', 'TOTAL', 'TOUCH', 'TOUGH',
  'TOWER', 'TRACK', 'TRADE', 'TRAIN', 'TREAT', 'TREND', 'TRIAL', 'TRIBE', 'TRICK', 'TRIED',
  'TRIES', 'TRUCK', 'TRULY', 'TRUNK', 'TRUST', 'TRUTH', 'TWICE', 'TWIST', 'TYLER', 'UNCLE',
  'UNDER', 'UNDUE', 'UNION', 'UNITY', 'UNTIL', 'UPPER', 'UPSET', 'URBAN', 'USAGE', 'USUAL',
  'VALUE', 'VIDEO', 'VIRUS', 'VISIT', 'VITAL', 'VOCAL', 'VOICE', 'WASTE', 'WATCH', 'WATER',
  'WHEEL', 'WHERE', 'WHICH', 'WHILE', 'WHITE', 'WHOLE', 'WHOSE', 'WOMAN', 'WOMEN', 'WORLD',
  'WORRY', 'WORSE', 'WORST', 'WORTH', 'WOULD', 'WRITE', 'WRONG', 'WROTE', 'YIELD', 'YOUNG',
  'YOUTH'
];

const VALID_GUESSES = [
  // All target words are valid guesses
  ...WORDS,
  // Additional valid words for guessing
  'AAHED', 'AALII', 'AARGH', 'AARTI', 'ABACA', 'ABACI', 'ABACS', 'ABAFT', 'ABAKA', 'ABAMP',
  'ABAND', 'ABASE', 'ABASH', 'ABASK', 'ABATE', 'ABAYA', 'ABBAS', 'ABBED', 'ABBES', 'ABBEY',
  'ABBOT', 'ABCEE', 'ABEAM', 'ABEAR', 'ABELE', 'ABERS', 'ABETS', 'ABHOR', 'ABIDE', 'ABLED',
  'ABLER', 'ABLES', 'ABLET', 'ABLOW', 'ABMHO', 'ABODE', 'ABOHM', 'ABOIL', 'ABOMA', 'ABOON',
  'ABORD', 'ABORE', 'ABORT', 'ABOS', 'ABOUL', 'ABRAM', 'ABRAY', 'ABRED', 'ABRI', 'ABRIM',
  'ABRIN', 'ABRIS', 'ABSEY', 'ABSIT', 'ABUNA', 'ABUNE', 'ABURS', 'ABUSH', 'ABUTS', 'ABUZZ',
  'ABYES', 'ABYSM', 'ABYSS', 'ACAIS', 'ACARI', 'ACERS', 'ACETA', 'ACHAR', 'ACHED', 'ACHES',
  'ACHOO', 'ACIDS', 'ACIDY', 'ACING', 'ACINI', 'ACKEE', 'ACKER', 'ACMES', 'ACNED', 'ACNES',
  'ACOCK', 'ACOLD', 'ACORN', 'ACRED', 'ACRES', 'ACRID', 'ACROS', 'ACTED', 'ACTIN', 'ACTON',
  'ACTORS', 'ACUTE', 'ACYLS', 'ADAGE', 'ADAPT', 'ADDAX', 'ADDED', 'ADDER', 'ADDIO', 'ADDLE',
  'ADEEM', 'ADEPT', 'ADHAN', 'ADIEU', 'ADIOS', 'ADITS', 'ADMAN', 'ADMEN', 'ADMIN', 'ADMIX',
  'ADOBE', 'ADOBO', 'ADOPT', 'ADORE', 'ADORN', 'ADOWN', 'ADOZE', 'ADRAD', 'ADRED', 'ADSUM',
  'ADUKI', 'ADULT', 'ADUNC', 'ADUST', 'ADVEW', 'ADYTA', 'ADZED', 'ADZES', 'AECIA', 'AEDES',
  'AEGIS', 'AEONS', 'AEROS', 'AESIR', 'AFALD', 'AFARA', 'AFARS', 'AFEAR', 'AFFIX', 'AFIRE',
  'AFLAJ', 'AFOOT', 'AFORE', 'AFOUL', 'AFRIT', 'AFROS', 'AFTER', 'AGAIN', 'AGAMA', 'AGAPE',
  'AGARS', 'AGAST', 'AGATE', 'AGAVE', 'AGAZE', 'AGENE', 'AGENT', 'AGERS', 'AGGER', 'AGGIE',
  'AGGRI', 'AGGRO', 'AGGRY', 'AGHAS', 'AGILA', 'AGILE', 'AGING', 'AGIOS', 'AGISM', 'AGIST',
  'AGITA', 'AGLEE', 'AGLET', 'AGLEY', 'AGLOO', 'AGLOW', 'AGLUS', 'AGMAS', 'AGMIX', 'AGNEW',
  'AGONE', 'AGONS', 'AGONY', 'AGOOD', 'AGORA', 'AGREE', 'AGRIA', 'AGRIN', 'AGIOS', 'AGUED',
  'AGUES', 'AGUNA', 'AGUTI', 'AHEAD', 'AHEAP', 'AHENT', 'AHIGH', 'AHIND', 'AHING', 'AHINT',
  'AHOLD', 'AHULL', 'AHURU', 'AIDED', 'AIDER', 'AIDES', 'AIDOI', 'AIDOS', 'AIERY', 'AIGAS',
  'AIGHT', 'AILED', 'AIMED', 'AIMER', 'AINEE', 'AIOLI', 'AIRED', 'AIRER', 'AIRNS', 'AIRTH',
  'AIRTS', 'AISLE', 'AITCH', 'AITUS', 'AIVER', 'AIZLE', 'AJAPA', 'AJIVA', 'AJOGS', 'AJUGA',
  'AJWAN', 'AKEES', 'AKELA', 'AKENE', 'AKING', 'AKITA', 'AKKAS', 'ALAAP', 'ALACK', 'ALAMO',
  'ALAND', 'ALANE', 'ALANG', 'ALANS', 'ALANT', 'ALAPA', 'ALAPS', 'ALARM', 'ALARY', 'ALATE',
  'ALAYS', 'ALBAS', 'ALBEE', 'ALBUM', 'ALCID', 'ALCOS', 'ALDEA', 'ALDER', 'ALDOL', 'ALECK',
  'ALECS', 'ALEFS', 'ALEFT', 'ALEPH', 'ALERT', 'ALEWS', 'ALEYE', 'ALFAS', 'ALGAE', 'ALGAL',
  'ALGAS', 'ALGID', 'ALGIN', 'ALGOR', 'ALGUM', 'ALIAS', 'ALIBI', 'ALIEN', 'ALIFS', 'ALIGN',
  'ALIKE', 'ALINE', 'ALIST', 'ALIVE', 'ALIYA', 'ALKIE', 'ALKOS', 'ALKYD', 'ALKYL', 'ALLAY',
  'ALLEE', 'ALLEL', 'ALLEY', 'ALLIS', 'ALLOD', 'ALLOT', 'ALLOW', 'ALLOY', 'ALLYL', 'ALMAH',
  'ALMAS', 'ALMEH', 'ALMES', 'ALMUD', 'ALMUG', 'ALODS', 'ALOED', 'ALOES', 'ALOFT', 'ALOHA',
  'ALOIN', 'ALONE', 'ALONG', 'ALOOF', 'ALOUD', 'ALOWE', 'ALPHA', 'ALTAR', 'ALTER', 'ALTHO',
  'ALTOS', 'ALULA', 'ALUMS', 'ALURE', 'ALWAY', 'AMAHS', 'AMAIN', 'AMASS', 'AMATE', 'AMAZE',
  'AMBER', 'AMBIT', 'AMBLE', 'AMBOS', 'AMBRY', 'AMEBA', 'AMEER', 'AMEND', 'AMENS', 'AMENT',
  'AMIAS', 'AMICE', 'AMICI', 'AMIDE', 'AMIDO', 'AMIDS', 'AMIES', 'AMIGA', 'AMIGO', 'AMINE',
  'AMINO', 'AMINS', 'AMIRS', 'AMISS', 'AMITY', 'AMLAS', 'AMMAN', 'AMMON', 'AMMOS', 'AMNIA',
  'AMNIC', 'AMNIO', 'AMOKS', 'AMOLE', 'AMONG', 'AMORT', 'AMOUR', 'AMOVE', 'AMOWT', 'AMPED',
  'AMPLE', 'AMPLY', 'AMUCK', 'AMUSE', 'AMYLS', 'ANANA', 'ANATA', 'ANCHO', 'ANCLE', 'ANCON',
  'ANDRO', 'ANEAR', 'ANELE', 'ANENT', 'ANGAS', 'ANGEL', 'ANGER', 'ANGLE', 'ANGLO', 'ANGRY',
  'ANGST', 'ANIGH', 'ANILE', 'ANILS', 'ANIMA', 'ANIME', 'ANIMI', 'ANION', 'ANISE', 'ANKER',
  'ANKLE', 'ANLAS', 'ANNAL', 'ANNAS', 'ANNAT', 'ANNEX', 'ANNOY', 'ANNUL', 'ANOAS', 'ANODE',
  'ANOLE', 'ANOMY', 'ANSAE', 'ANTAE', 'ANTAR', 'ANTAS', 'ANTED', 'ANTES', 'ANTIC', 'ANTIS',
  'ANTRA', 'ANTRE', 'ANTSY', 'ANURA', 'ANYON', 'AORTA', 'APACE', 'APAGE', 'APAID', 'APART',
  'APAYD', 'APAYS', 'APEAK', 'APEEK', 'APERS', 'APERT', 'APERY', 'APGAR', 'APHID', 'APHIS',
  'APIAN', 'APING', 'APIOL', 'APISH', 'APISM', 'APNEA', 'APODE', 'APODS', 'APOOP', 'APORT',
  'APPAL', 'APPAY', 'APPEL', 'APPLE', 'APPLY', 'APPRO', 'APPUI', 'APPUY', 'APRES', 'APRON',
  'APSES', 'APSIS', 'APSOS', 'APTED', 'APTER', 'APTLY', 'AQUAE', 'AQUAS', 'ARABA', 'ARAKS',
  'ARAME', 'ARARS', 'ARBAS', 'ARBOR', 'ARCED', 'ARCHI', 'ARCOS', 'ARCUS', 'ARDEB', 'ARDRI',
  'AREAD', 'AREAE', 'AREAL', 'AREAR', 'AREAS', 'ARECA', 'AREDD', 'AREDE', 'AREFY', 'AREIC',
  'ARENA', 'ARENE', 'AREPA', 'ARERE', 'ARETE', 'ARGAL', 'ARGAN', 'ARGIL', 'ARGLE', 'ARGOL',
  'ARGON', 'ARGOT', 'ARGUE', 'ARGUS', 'ARHAT', 'ARIAS', 'ARIEL', 'ARIKI', 'ARILS', 'ARIOT',
  'ARISE', 'ARISH', 'ARKED', 'ARLES', 'ARMED', 'ARMER', 'ARMET', 'ARMIL', 'ARMOR', 'ARNAS',
  'ARNUT', 'AROBA', 'AROHA', 'AROID', 'AROMA', 'AROSE', 'ARPAS', 'ARPEN', 'ARRAH', 'ARRAS',
  'ARRAY', 'ARRED', 'ARRET', 'ARRIS', 'ARROW', 'ARROZ', 'ARSED', 'ARSES', 'ARSEY', 'ARSIS',
  'ARSON', 'ARTAL', 'ARTEL', 'ARTER', 'ARTIC', 'ARTIS', 'ARTSY', 'ARUHE', 'ARUMS', 'ARVAL',
  'ARVOS', 'ARYLS', 'ASANA', 'ASCI', 'ASCOT', 'ASCUS', 'ASDIC', 'ASHED', 'ASHEN', 'ASHES',
  'ASHET', 'ASIDE', 'ASKED', 'ASKER', 'ASKEW', 'ASKOI', 'ASKOS', 'ASPEN', 'ASPER', 'ASPIC',
  'ASPIS', 'ASPRO', 'ASSAI', 'ASSAM', 'ASSAY', 'ASSES', 'ASSET', 'ASSEZ', 'ASSOT', 'ASTER',
  'ASTIR', 'ASTUN', 'ASURA', 'ASWAY', 'ASWIM', 'ASYLA', 'ATAPS', 'ATAXY', 'ATIGI', 'ATILT',
  'ATIMY', 'ATLAS', 'ATMAN', 'ATMAS', 'ATMOS', 'ATOCS', 'ATOKE', 'ATOKS', 'ATOLL', 'ATOMS',
  'ATOMY', 'ATONE', 'ATONY', 'ATOPY', 'ATRIA', 'ATRIP', 'ATTAP', 'ATTAR', 'ATTIC', 'ATUAS',
  'AUDAD', 'AUDIO', 'AUDIT', 'AUGER', 'AUGHT', 'AUGUR', 'AULAS', 'AULIC', 'AULOI', 'AULOS',
  'AUMIL', 'AUNTS', 'AUNTY', 'AURAE', 'AURAL', 'AURAR', 'AURAS', 'AUREI', 'AURES', 'AURIC',
  'AURIS', 'AURUM', 'AUTOS', 'AUXIN', 'AVAIL', 'AVALE', 'AVANT', 'AVAST', 'AVAZE', 'AVELS',
  'AVENS', 'AVERS', 'AVERT', 'AVGAS', 'AVIAN', 'AVIAS', 'AVION', 'AVISE', 'AVISO', 'AVIZE',
  'AVOID', 'AVOWS', 'AVYZE', 'AWACS', 'AWAFT', 'AWAKE', 'AWARD', 'AWARE', 'AWARN', 'AWASH',
  'AWATO', 'AWAVE', 'AWAYS', 'AWDLS', 'AWEEL', 'AWETO', 'AWFUL', 'AWING', 'AWMRY', 'AWNED',
  'AWNER', 'AWOKE', 'AWOLS', 'AWORK', 'AXELS', 'AXIAL', 'AXILE', 'AXILS', 'AXING', 'AXIOM',
  'AXION', 'AXITE', 'AXLED', 'AXLES', 'AXMAN', 'AXMEN', 'AXOID', 'AXONE', 'AXONS', 'AYAHS',
  'AYAYA', 'AYELP', 'AYGRE', 'AYINS', 'AYONT', 'AYRES', 'AYRIE', 'AZANS', 'AZIDE', 'AZIDO',
  'AZINE', 'AZLON', 'AZOIC', 'AZOLE', 'AZONS', 'AZOTE', 'AZOTH', 'AZUKI', 'AZURE', 'AZURN',
  'AZURY', 'AZYGY', 'AZYME', 'AZYMS', 'BAAED', 'BAALS', 'BABAS', 'BABEL', 'BABES', 'BABKA',
  'BABOO', 'BABUL', 'BABUS', 'BACCA', 'BACCO', 'BACCY', 'BACHA', 'BACHS', 'BACKS', 'BACON',
  'BADDY', 'BADGE', 'BADLY', 'BAELS', 'BAFFS', 'BAFFY', 'BAFTS', 'BAGEL', 'BAGGY', 'BAGHS',
  'BAGIE', 'BAHTS', 'BAHUT', 'BAILS', 'BAIRN', 'BAISA', 'BAITH', 'BAITS', 'BAIZA', 'BAIZE',
  'BAJRA', 'BAJRI', 'BAJUS', 'BAKED', 'BAKEN', 'BAKER', 'BAKES', 'BAKRA', 'BALAS', 'BALDS',
  'BALDY', 'BALED', 'BALER', 'BALES', 'BALKS', 'BALKY', 'BALLS', 'BALLY', 'BALMS', 'BALMY',
  'BALOO', 'BALSA', 'BALTI', 'BALUN', 'BALUS', 'BAMBI', 'BANAK', 'BANAL', 'BANCO', 'BANCS',
  'BANDA', 'BANDH', 'BANDS', 'BANDY', 'BANED', 'BANES', 'BANGS', 'BANIA', 'BANJO', 'BANKS',
  'BANNS', 'BANTS', 'BANTU', 'BANTY', 'BANYA', 'BAPUS', 'BARBE', 'BARBS', 'BARBY', 'BARCA',
  'BARDE', 'BARDO', 'BARDS', 'BARDY', 'BARED', 'BARER', 'BARES', 'BARFS', 'BARGE', 'BARIC',
  'BARKS', 'BARKY', 'BARMS', 'BARMY', 'BARNS', 'BARNY', 'BARON', 'BARPS', 'BARRA', 'BARRE',
  'BARRO', 'BARRY', 'BARYE', 'BASAN', 'BASED', 'BASER', 'BASES', 'BASHO', 'BASIC', 'BASIL',
  'BASIN', 'BASIS', 'BASKS', 'BASON', 'BASSE', 'BASSI', 'BASSO', 'BASSY', 'BASTA', 'BASTE',
  'BASTI', 'BASTO', 'BASTS', 'BATCH', 'BATED', 'BATES', 'BATHE', 'BATHS', 'BATIK', 'BATON',
  'BATTS', 'BATTU', 'BAUDS', 'BAUKS', 'BAULK', 'BAURS', 'BAVIN', 'BAWDS', 'BAWDY', 'BAWKS',
  'BAWLS', 'BAWNS', 'BAWRS', 'BAWTY', 'BAYED', 'BAYOU', 'BAYTS', 'BAZAR', 'BAZOO', 'BEACH',
  'BEADS', 'BEADY', 'BEAKS', 'BEAKY', 'BEAMS', 'BEAMY', 'BEANO', 'BEANS', 'BEANY', 'BEARD',
  'BEARE', 'BEARS', 'BEAST', 'BEATH', 'BEATS', 'BEATY', 'BEAUS', 'BEAUT', 'BEAUX', 'BEBOP',
  'BECAP', 'BECKE', 'BECKS', 'BEDAD', 'BEDEL', 'BEDES', 'BEDEW', 'BEDIM', 'BEDYE', 'BEECH',
  'BEEDI', 'BEEFS', 'BEEFY', 'BEEPS', 'BEERS', 'BEERY', 'BEETS', 'BEFOG', 'BEGAN', 'BEGAT',
  'BEGET', 'BEGIN', 'BEGOT', 'BEGUM', 'BEGUN', 'BEIGE', 'BEIGY', 'BEING', 'BEINS', 'BEKAH',
  'BELAH', 'BELAR', 'BELAY', 'BELCH', 'BELDS', 'BELGA', 'BELIE', 'BELLE', 'BELLS', 'BELLY',
  'BELON', 'BELOW', 'BELTS', 'BEMAS', 'BEMAD', 'BEMAS', 'BEMIX', 'BEMUD', 'BENCH', 'BENDS',
  'BENDY', 'BENES', 'BENET', 'BENGS', 'BENIS', 'BENNE', 'BENNI', 'BENNY', 'BENTO', 'BENTS',
  'BENTY', 'BEPAT', 'BERAY', 'BERES', 'BERET', 'BERGS', 'BERKO', 'BERKS', 'BERME', 'BERMS',
  'BERRY', 'BERTH', 'BERYL', 'BESAT', 'BESAW', 'BESEE', 'BESES', 'BESET', 'BESIT', 'BESOM',
  'BESOT', 'BESTI', 'BESTS', 'BETAS', 'BETED', 'BETEL', 'BETES', 'BETHS', 'BETID', 'BETON',
  'BETTA', 'BETTY', 'BEVEL', 'BEVER', 'BEVOR', 'BEVUE', 'BEVVY', 'BEWET', 'BEWIG', 'BEZEL',
  'BEZIL', 'BEZZY', 'BHAJI', 'BHANG', 'BHATS', 'BHELS', 'BHOOT', 'BHUNA', 'BHUTS', 'BIACH',
  'BIALI', 'BIALY', 'BIBBS', 'BIBED', 'BIBER', 'BIBES', 'BIBLE', 'BICCY', 'BICES', 'BIDDY',
  'BIDED', 'BIDER', 'BIDES', 'BIDET', 'BIDIS', 'BIDON', 'BIELD', 'BIERS', 'BIFFO', 'BIFFS',
  'BIFFY', 'BIFID', 'BIGAE', 'BIGAS', 'BIGGY', 'BIGHT', 'BIGLY', 'BIGOS', 'BIGOT', 'BIJOU',
  'BIKED', 'BIKER', 'BIKES', 'BIKIE', 'BILBO', 'BILBY', 'BILED', 'BILES', 'BILGE', 'BILGY',
  'BILKS', 'BILLS', 'BILLY', 'BIMBO', 'BINAL', 'BINDI', 'BINDS', 'BINES', 'BINGE', 'BINGO',
  'BINGS', 'BINGY', 'BINIT', 'BINKS', 'BINTS', 'BIOGS', 'BIOME', 'BIONT', 'BIOTA', 'BIPED',
  'BIPOD', 'BIRCH', 'BIRDS', 'BIRKS', 'BIRLE', 'BIRLS', 'BIROS', 'BIRRS', 'BIRSE', 'BIRTH',
  'BISES', 'BISKS', 'BISOM', 'BISON', 'BITCH', 'BITER', 'BITES', 'BITOS', 'BITOU', 'BITSY',
  'BITTE', 'BITTS', 'BITTY', 'BIVIA', 'BIVVY', 'BIZES', 'BIZZY', 'BLABS', 'BLACK', 'BLADE',
  'BLADS', 'BLADY', 'BLAES', 'BLAFF', 'BLAGS', 'BLAHS', 'BLAIN', 'BLAIR', 'BLAKE', 'BLAME',
  'BLAMS', 'BLAND', 'BLANK', 'BLARE', 'BLART', 'BLASE', 'BLAST', 'BLATE', 'BLATS', 'BLATT',
  'BLAUD', 'BLAWN', 'BLAWS', 'BLAZE', 'BLEAK', 'BLEAR', 'BLEAT', 'BLEBS', 'BLECH', 'BLEED',
  'BLEEP', 'BLEES', 'BLEND', 'BLENT', 'BLERT', 'BLESS', 'BLEST', 'BLETS', 'BLEYS', 'BLIMP',
  'BLIND', 'BLING', 'BLINK', 'BLINS', 'BLINY', 'BLIPS', 'BLISS', 'BLITE', 'BLITS', 'BLITZ',
  'BLIVE', 'BLOAT', 'BLOBS', 'BLOCK', 'BLOCS', 'BLOGS', 'BLOKE', 'BLOND', 'BLOOD', 'BLOOK',
  'BLOOM', 'BLOOP', 'BLORE', 'BLOTS', 'BLOWN', 'BLOWS', 'BLOWY', 'BLUBS', 'BLUDE', 'BLUDS',
  'BLUDY', 'BLUED', 'BLUER', 'BLUES', 'BLUET', 'BLUEY', 'BLUFF', 'BLUID', 'BLUME', 'BLUNK',
  'BLUNT', 'BLURB', 'BLURS', 'BLURT', 'BLUSH', 'BLYPE', 'BOABS', 'BOAKS', 'BOARD', 'BOARS',
  'BOART', 'BOAST', 'BOATS', 'BOBAC', 'BOBAK', 'BOBAS', 'BOBBY', 'BOBOL', 'BOBOS', 'BOCCA',
  'BOCCE', 'BOCCI', 'BOCHE', 'BOCKS', 'BODED', 'BODES', 'BOFFO', 'BOFFS', 'BOGAN', 'BOGEY',
  'BOGGY', 'BOGIE', 'BOGLE', 'BOGUE', 'BOGUS', 'BOHEA', 'BOHOS', 'BOILS', 'BOING', 'BOINK',
  'BOITE', 'BOKED', 'BOKEH', 'BOKES', 'BOKOS', 'BOLAR', 'BOLAS', 'BOLDS', 'BOLES', 'BOLLS',
  'BOLOS', 'BOLTS', 'BOLUS', 'BOMAS', 'BOMBE', 'BOMBO', 'BOMBS', 'BONCE', 'BONDS', 'BONED',
  'BONER', 'BONES', 'BONGS', 'BONIE', 'BONKS', 'BONNE', 'BONNY', 'BONUS', 'BONZE', 'BOOAI',
  'BOOAY', 'BOOBS', 'BOOBY', 'BOOCH', 'BOODY', 'BOOED', 'BOOFY', 'BOOGY', 'BOOHS', 'BOOKS',
  'BOOKY', 'BOOLS', 'BOOMS', 'BOOMY', 'BOONG', 'BOONS', 'BOORD', 'BOORS', 'BOOSE', 'BOOST',
  'BOOTH', 'BOOTS', 'BOOTY', 'BOOZE', 'BOOZY', 'BORAL', 'BORAS', 'BORAX', 'BORDE', 'BORDS',
  'BORED', 'BOREE', 'BOREL', 'BORER', 'BORES', 'BORGO', 'BORIC', 'BORKS', 'BORMS', 'BORNA',
  'BORNE', 'BORON', 'BORTS', 'BORTY', 'BORTZ', 'BOSIE', 'BOSKS', 'BOSKY', 'BOSOM', 'BOSON',
  'BOSSY', 'BOSUN', 'BOTAS', 'BOTCH', 'BOTEL', 'BOTES', 'BOTHY', 'BOTOX', 'BOTTE', 'BOTTS',
  'BOTTY', 'BOTUL', 'BOUBA', 'BOUCH', 'BOUDU', 'BOUFF', 'BOUFS', 'BOUGH', 'BOUKS', 'BOULE',
  'BOULS', 'BOUND', 'BOUNS', 'BOURD', 'BOURG', 'BOURN', 'BOURS', 'BOUSE', 'BOUSY', 'BOUTS',
  'BOVID', 'BOWAT', 'BOWED', 'BOWEL', 'BOWER', 'BOWES', 'BOWET', 'BOWIE', 'BOWLS', 'BOWNE',
  'BOWRS', 'BOWSE', 'BOXED', 'BOXEN', 'BOXER', 'BOXES', 'BOYAR', 'BOYAU', 'BOYED', 'BOYFS',
  'BOYGS', 'BOYLA', 'BOYOS', 'BOYSY', 'BOZOS', 'BRAAI', 'BRABS', 'BRACE', 'BRACH', 'BRACK',
  'BRACS', 'BRACT', 'BRADS', 'BRAES', 'BRAGS', 'BRAID', 'BRAIL', 'BRAIN', 'BRAKE', 'BRAKS',
  'BRAKY', 'BRAME', 'BRAND', 'BRANE', 'BRANK', 'BRANS', 'BRANT', 'BRASH', 'BRASS', 'BRAST',
  'BRATS', 'BRAVA', 'BRAVE', 'BRAVO', 'BRAWS', 'BRAXY', 'BRAYS', 'BRAZE', 'BREAD', 'BREAK',
  'BREAM', 'BREAR', 'BREAS', 'BREBA', 'BRECK', 'BREDD', 'BREDE', 'BREDS', 'BREED', 'BREEM',
  'BREER', 'BREES', 'BREID', 'BREIS', 'BREME', 'BRENS', 'BRENT', 'BRERE', 'BRERS', 'BREVE',
  'BREWS', 'BREYS', 'BRIBE', 'BRICK', 'BRIDE', 'BRIEF', 'BRIER', 'BRIES', 'BRIGS', 'BRIKS',
  'BRILL', 'BRIMS', 'BRINE', 'BRING', 'BRINK', 'BRINS', 'BRINY', 'BRIOS', 'BRISK', 'BRISS',
  'BRITH', 'BRITS', 'BRITT', 'BRIZE', 'BROAD', 'BROAK', 'BROGH', 'BROGS', 'BROIL', 'BROKE',
  'BROME', 'BROMO', 'BRONC', 'BROND', 'BROOD', 'BROOK', 'BROOL', 'BROOM', 'BROOS', 'BROSE',
  'BROSY', 'BROTH', 'BROWN', 'BROWS', 'BROYS', 'BRRRR', 'BRUBS', 'BRUCE', 'BRUCH', 'BRUCK',
  'BRUED', 'BRUER', 'BRUES', 'BRUGH', 'BRUHS', 'BRUIN', 'BRUIT', 'BRULE', 'BRUME', 'BRUNG',
  'BRUNK', 'BRUNS', 'BRUNT', 'BRURS', 'BRUSH', 'BRUSK', 'BRUST', 'BRUTE', 'BRUTS', 'BRUX',
  'BUATS', 'BUAZE', 'BUBAL', 'BUBAS', 'BUBBA', 'BUBBE', 'BUBBY', 'BUBUS', 'BUCAN', 'BUCCA',
  'BUCKO', 'BUCKS', 'BUCKU', 'BUDAS', 'BUDDY', 'BUDGE', 'BUDIS', 'BUDS', 'BUFFE', 'BUFFI',
  'BUFFO', 'BUFFS', 'BUFFY', 'BUFOS', 'BUFTY', 'BUGGY', 'BUGLE', 'BUHLS', 'BUHRS', 'BUILD',
  'BUILT', 'BUINS', 'BUIST', 'BUKES', 'BULBS', 'BULGE', 'BULGY', 'BULKS', 'BULKY', 'BULLA',
  'BULLS', 'BULLY', 'BULSE', 'BUMBO', 'BUMFS', 'BUMPH', 'BUMPS', 'BUMPY', 'BUNAS', 'BUNCE',
  'BUNCH', 'BUNCO', 'BUNDE', 'BUNDH', 'BUNDS', 'BUNDT', 'BUNDU', 'BUNDY', 'BUNGS', 'BUNGY',
  'BUNIA', 'BUNJE', 'BUNJY', 'BUNKO', 'BUNKS', 'BUNNS', 'BUNNY', 'BUNTS', 'BUNTY', 'BUNYA',
  'BUOYS', 'BUPPY', 'BURAN', 'BURAS', 'BURBS', 'BURDS', 'BURET', 'BURFI', 'BURGH', 'BURGS',
  'BURIN', 'BURKA', 'BURKE', 'BURKS', 'BURLS', 'BURLY', 'BURNS', 'BURNT', 'BUROO', 'BURPS',
  'BURQA', 'BURRO', 'BURRS', 'BURRY', 'BURSA', 'BURSE', 'BURST', 'BUSBY', 'BUSED', 'BUSES',
  'BUSHY', 'BUSKS', 'BUSKY', 'BUSSU', 'BUSTS', 'BUSTY', 'BUTCH', 'BUTEO', 'BUTES', 'BUTLE',
  'BUTOH', 'BUTTE', 'BUTTS', 'BUTTY', 'BUTUT', 'BUTYL', 'BUZZY', 'BWANA', 'BWAZI', 'BYDED',
  'BYDES', 'BYKED', 'BYKES', 'BYLAW', 'BYNED', 'BYNES', 'BYRES', 'BYRLS', 'BYSSI', 'BYTES',
  'BYWAY', 'BYWORK'
];

function getTodaysWord() {
  const today = new Date();
  const start = new Date('2024-01-01');
  const daysSinceStart = Math.floor((today - start) / (1000 * 60 * 60 * 24));
  return WORDS[daysSinceStart % WORDS.length];
}

function isValidGuess(word) {
  return VALID_GUESSES.includes(word.toUpperCase());
}

function isTargetWord(word) {
  return WORDS.includes(word.toUpperCase());
}

module.exports = {
  WORDS,
  VALID_GUESSES,
  getTodaysWord,
  isValidGuess,
  isTargetWord
};
