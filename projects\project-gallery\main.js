/**
 * Interactive Project Gallery - Main JavaScript
 * Comprehensive gallery functionality with lightbox and filtering
 */

class ProjectGallery {
  constructor() {
    this.currentProject = null;
    this.currentImageIndex = 0;
    this.isLightboxOpen = false;
    this.touchStartX = 0;
    this.touchEndX = 0;
    this.themeToggle = document.querySelector('.theme-toggle');
    this.themeIcon = document.querySelector('.theme-icon');

    this.init();
  }

  init() {
    this.setupTheme();
    this.setupEventListeners();
    this.setupKeyboardNavigation();
    this.setupTouchNavigation();
    this.initializeFilters();
    this.preloadImages();
  }

  setupEventListeners() {
    // Theme toggle
    this.themeToggle?.addEventListener('click', () => {
      const currentTheme = document.documentElement.getAttribute('data-theme');
      const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
      this.setTheme(newTheme);
    });

    // Filter buttons
    document.querySelectorAll('.filter-btn').forEach(btn => {
      btn.addEventListener('click', (e) => this.handleFilter(e));
    });

    // View gallery buttons
    document.querySelectorAll('.view-gallery-btn').forEach(btn => {
      btn.addEventListener('click', (e) => this.openLightbox(e));
    });

    // Lightbox controls
    document.getElementById('closeLightbox').addEventListener('click', () => this.closeLightbox());
    document.getElementById('prevBtn').addEventListener('click', () => this.previousImage());
    document.getElementById('nextBtn').addEventListener('click', () => this.nextImage());

    // Lightbox footer controls
    document.getElementById('fullscreenBtn').addEventListener('click', () => this.toggleFullscreen());
    document.getElementById('downloadBtn').addEventListener('click', () => this.downloadImage());
    document.getElementById('shareBtn').addEventListener('click', () => this.shareImage());

    // Lightbox backdrop click
    document.getElementById('lightbox').addEventListener('click', (e) => {
      if (e.target.id === 'lightbox') {
        this.closeLightbox();
      }
    });

    // Image load events
    document.getElementById('mainImage').addEventListener('load', () => this.hideImageLoader());
    document.getElementById('mainImage').addEventListener('error', () => this.handleImageError());

    // Thumbnail clicks will be added dynamically
  }

  setupKeyboardNavigation() {
    document.addEventListener('keydown', (e) => {
      if (!this.isLightboxOpen) return;

      switch (e.key) {
        case 'Escape':
          this.closeLightbox();
          break;
        case 'ArrowLeft':
          e.preventDefault();
          this.previousImage();
          break;
        case 'ArrowRight':
          e.preventDefault();
          this.nextImage();
          break;
        case 'f':
        case 'F':
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
            this.toggleFullscreen();
          }
          break;
        case 'd':
        case 'D':
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
            this.downloadImage();
          }
          break;
      }
    });
  }

  setupTouchNavigation() {
    const imageContainer = document.querySelector('.image-container');
    
    imageContainer.addEventListener('touchstart', (e) => {
      this.touchStartX = e.changedTouches[0].screenX;
    });

    imageContainer.addEventListener('touchend', (e) => {
      this.touchEndX = e.changedTouches[0].screenX;
      this.handleSwipe();
    });
  }

  handleSwipe() {
    const swipeThreshold = 50;
    const diff = this.touchStartX - this.touchEndX;

    if (Math.abs(diff) > swipeThreshold) {
      if (diff > 0) {
        // Swipe left - next image
        this.nextImage();
      } else {
        // Swipe right - previous image
        this.previousImage();
      }
    }
  }

  initializeFilters() {
    // Set initial filter state
    this.filterProjects('all');
  }

  handleFilter(e) {
    const filterBtn = e.target;
    const filter = filterBtn.dataset.filter;

    // Update active filter button
    document.querySelectorAll('.filter-btn').forEach(btn => {
      btn.classList.remove('active');
    });
    filterBtn.classList.add('active');

    // Filter projects
    this.filterProjects(filter);
  }

  filterProjects(filter) {
    const projectItems = document.querySelectorAll('.project-item');
    
    projectItems.forEach(item => {
      const categories = item.dataset.category.split(' ');
      const shouldShow = filter === 'all' || categories.includes(filter);
      
      if (shouldShow) {
        item.classList.remove('filtered-out');
        item.style.display = 'block';
      } else {
        item.classList.add('filtered-out');
        setTimeout(() => {
          if (item.classList.contains('filtered-out')) {
            item.style.display = 'none';
          }
        }, 300);
      }
    });

    // Animate grid reorganization
    this.animateGridReorganization();
  }

  animateGridReorganization() {
    const grid = document.querySelector('.gallery-grid');
    grid.style.opacity = '0.7';
    
    setTimeout(() => {
      grid.style.opacity = '1';
    }, 150);
  }

  openLightbox(e) {
    const projectItem = e.target.closest('.project-item');
    const projectId = projectItem.dataset.project;
    const projectData = getProjectData(projectId);

    if (!projectData) {
      this.showToast('Project data not found');
      return;
    }

    this.currentProject = projectData;
    this.currentImageIndex = 0;
    this.isLightboxOpen = true;

    // Update lightbox content
    this.updateLightboxContent();
    
    // Show lightbox
    document.getElementById('lightbox').classList.add('show');
    document.body.style.overflow = 'hidden';

    // Load first image
    this.loadImage(0);
  }

  closeLightbox() {
    document.getElementById('lightbox').classList.remove('show');
    document.body.style.overflow = '';
    this.isLightboxOpen = false;
    this.currentProject = null;
    this.currentImageIndex = 0;
  }

  updateLightboxContent() {
    if (!this.currentProject) return;

    // Update title
    document.getElementById('lightboxTitle').textContent = this.currentProject.title;

    // Update project links
    document.getElementById('viewProjectBtn').href = this.currentProject.liveUrl;
    document.getElementById('viewCodeBtn').href = this.currentProject.codeUrl;

    // Update total images count
    document.getElementById('totalImages').textContent = this.currentProject.screenshots.length;

    // Generate thumbnails
    this.generateThumbnails();
  }

  generateThumbnails() {
    const thumbnailStrip = document.getElementById('thumbnailStrip');
    thumbnailStrip.innerHTML = '';

    this.currentProject.screenshots.forEach((screenshot, index) => {
      const thumbnail = document.createElement('img');
      thumbnail.className = 'thumbnail';
      thumbnail.src = screenshot.url;
      thumbnail.alt = screenshot.title;
      thumbnail.dataset.index = index;
      
      thumbnail.addEventListener('click', () => this.loadImage(index));
      thumbnail.addEventListener('error', () => {
        thumbnail.src = generatePlaceholderImage(this.currentProject.title, screenshot.title, 160, 100);
      });

      if (index === 0) {
        thumbnail.classList.add('active');
      }

      thumbnailStrip.appendChild(thumbnail);
    });
  }

  loadImage(index) {
    if (!this.currentProject || index < 0 || index >= this.currentProject.screenshots.length) {
      return;
    }

    this.currentImageIndex = index;
    const screenshot = this.currentProject.screenshots[index];

    // Show loader
    this.showImageLoader();

    // Update main image
    const mainImage = document.getElementById('mainImage');
    mainImage.src = screenshot.url;
    mainImage.alt = screenshot.title;

    // Handle image error
    mainImage.onerror = () => {
      mainImage.src = generatePlaceholderImage(this.currentProject.title, screenshot.title);
      this.hideImageLoader();
    };

    // Update image info
    document.getElementById('currentImageIndex').textContent = index + 1;
    document.getElementById('imageTitle').textContent = screenshot.title;
    document.getElementById('imageDescription').textContent = screenshot.description;

    // Update active thumbnail
    document.querySelectorAll('.thumbnail').forEach((thumb, i) => {
      thumb.classList.toggle('active', i === index);
    });

    // Update navigation buttons
    document.getElementById('prevBtn').style.display = index === 0 ? 'none' : 'flex';
    document.getElementById('nextBtn').style.display = index === this.currentProject.screenshots.length - 1 ? 'none' : 'flex';
  }

  previousImage() {
    if (this.currentImageIndex > 0) {
      this.loadImage(this.currentImageIndex - 1);
    }
  }

  nextImage() {
    if (this.currentImageIndex < this.currentProject.screenshots.length - 1) {
      this.loadImage(this.currentImageIndex + 1);
    }
  }

  showImageLoader() {
    document.getElementById('imageLoader').classList.add('show');
    document.getElementById('mainImage').style.opacity = '0.5';
  }

  hideImageLoader() {
    document.getElementById('imageLoader').classList.remove('show');
    document.getElementById('mainImage').style.opacity = '1';
  }

  handleImageError() {
    this.hideImageLoader();
    this.showToast('Failed to load image');
  }

  toggleFullscreen() {
    const lightbox = document.getElementById('lightbox');
    
    if (!document.fullscreenElement) {
      lightbox.requestFullscreen().catch(err => {
        this.showToast('Fullscreen not supported');
      });
    } else {
      document.exitFullscreen();
    }
  }

  downloadImage() {
    if (!this.currentProject) return;

    const screenshot = this.currentProject.screenshots[this.currentImageIndex];
    const link = document.createElement('a');
    link.href = screenshot.url;
    link.download = `${this.currentProject.title}-${screenshot.title}.jpg`;
    link.click();

    this.showToast('Download started');
  }

  shareImage() {
    if (!this.currentProject) return;

    const screenshot = this.currentProject.screenshots[this.currentImageIndex];
    const shareData = {
      title: `${this.currentProject.title} - ${screenshot.title}`,
      text: screenshot.description,
      url: window.location.href
    };

    if (navigator.share) {
      navigator.share(shareData).catch(() => {
        this.fallbackShare(shareData);
      });
    } else {
      this.fallbackShare(shareData);
    }
  }

  fallbackShare(shareData) {
    const shareText = `${shareData.title}\n\n${shareData.text}\n\n${shareData.url}`;
    
    if (navigator.clipboard) {
      navigator.clipboard.writeText(shareText).then(() => {
        this.showToast('Share link copied to clipboard');
      }).catch(() => {
        this.showToast('Unable to copy share link');
      });
    } else {
      this.showToast('Sharing not supported');
    }
  }

  preloadImages() {
    // Preload first image of each project for better performance
    Object.keys(GALLERY_DATA).forEach(projectId => {
      const project = GALLERY_DATA[projectId];
      if (project.screenshots.length > 0) {
        const img = new Image();
        img.src = project.screenshots[0].url;
      }
    });
  }

  showToast(message) {
    const toast = document.getElementById('toast');
    toast.textContent = message;
    toast.classList.add('show');

    setTimeout(() => {
      toast.classList.remove('show');
    }, 3000);
  }

  // Theme Management
  setupTheme() {
    // Get saved theme or default to system preference
    const savedTheme = localStorage.getItem('gallery-theme');
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    const initialTheme = savedTheme || (systemPrefersDark ? 'dark' : 'light');

    this.setTheme(initialTheme);

    // Listen for system theme changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
      if (!localStorage.getItem('gallery-theme')) {
        this.setTheme(e.matches ? 'dark' : 'light');
      }
    });
  }

  setTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('gallery-theme', theme);

    // Update theme toggle icon
    if (this.themeIcon) {
      this.themeIcon.textContent = theme === 'dark' ? '☀️' : '🌙';
      this.themeToggle.setAttribute('aria-label',
        `Switch to ${theme === 'dark' ? 'light' : 'dark'} theme`
      );
    }
  }

  showLoadingOverlay() {
    document.getElementById('loadingOverlay').classList.add('show');
  }

  hideLoadingOverlay() {
    document.getElementById('loadingOverlay').classList.remove('show');
  }
}

// Utility functions for enhanced functionality
class GalleryUtils {
  static createImagePlaceholder(width, height, text) {
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext('2d');
    
    // Gradient background
    const gradient = ctx.createLinearGradient(0, 0, width, height);
    gradient.addColorStop(0, '#667eea');
    gradient.addColorStop(1, '#764ba2');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);
    
    // Text
    ctx.fillStyle = 'white';
    ctx.font = 'bold 16px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(text, width / 2, height / 2);
    
    return canvas.toDataURL();
  }

  static debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  static throttle(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  static animateValue(obj, start, end, duration, callback) {
    let startTimestamp = null;
    const step = (timestamp) => {
      if (!startTimestamp) startTimestamp = timestamp;
      const progress = Math.min((timestamp - startTimestamp) / duration, 1);
      const value = Math.floor(progress * (end - start) + start);
      callback(value);
      if (progress < 1) {
        window.requestAnimationFrame(step);
      }
    };
    window.requestAnimationFrame(step);
  }

  static lazyLoadImages() {
    const images = document.querySelectorAll('img[loading="lazy"]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          img.src = img.dataset.src || img.src;
          img.classList.remove('lazy');
          observer.unobserve(img);
        }
      });
    });

    images.forEach(img => imageObserver.observe(img));
  }
}

// Performance monitoring
class PerformanceMonitor {
  constructor() {
    this.metrics = {
      loadTime: 0,
      imageLoadTimes: [],
      interactionTimes: []
    };
    this.startTime = performance.now();
  }

  recordLoadTime() {
    this.metrics.loadTime = performance.now() - this.startTime;
    console.log(`Gallery loaded in ${this.metrics.loadTime.toFixed(2)}ms`);
  }

  recordImageLoad(startTime) {
    const loadTime = performance.now() - startTime;
    this.metrics.imageLoadTimes.push(loadTime);
  }

  recordInteraction(type, startTime) {
    const interactionTime = performance.now() - startTime;
    this.metrics.interactionTimes.push({ type, time: interactionTime });
  }

  getAverageImageLoadTime() {
    if (this.metrics.imageLoadTimes.length === 0) return 0;
    const sum = this.metrics.imageLoadTimes.reduce((a, b) => a + b, 0);
    return sum / this.metrics.imageLoadTimes.length;
  }

  logMetrics() {
    console.log('Gallery Performance Metrics:', this.metrics);
    console.log(`Average image load time: ${this.getAverageImageLoadTime().toFixed(2)}ms`);
  }
}

// Gallery initialization is now handled in the HTML file
// This allows for proper screenshot generation before gallery initialization

// Utility function to initialize gallery with performance monitoring
function initializeGalleryWithMonitoring() {
  const performanceMonitor = new PerformanceMonitor();

  // Initialize gallery
  const gallery = new ProjectGallery();

  // Initialize utilities
  GalleryUtils.lazyLoadImages();

  // Record load time
  window.addEventListener('load', () => {
    performanceMonitor.recordLoadTime();
  });

  // Add performance monitoring to gallery
  gallery.performanceMonitor = performanceMonitor;

  // Log metrics after 5 seconds
  setTimeout(() => {
    performanceMonitor.logMetrics();
  }, 5000);

  return gallery;
}

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { ProjectGallery, GalleryUtils, PerformanceMonitor };
}
