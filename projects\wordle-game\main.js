/**
 * Wordle Game - Main Game Logic
 * Complete implementation with all features
 */

class WordleGame {
  constructor() {
    this.targetWord = '';
    this.currentRow = 0;
    this.currentCol = 0;
    this.gameState = 'playing'; // 'playing', 'won', 'lost'
    this.guesses = [];
    this.keyboardState = {};
    this.settings = {
      hardMode: false,
      darkTheme: false,
      highContrast: false
    };
    this.stats = {
      gamesPlayed: 0,
      gamesWon: 0,
      currentStreak: 0,
      maxStreak: 0,
      guessDistribution: [0, 0, 0, 0, 0, 0]
    };
    
    this.init();
  }

  init() {
    this.loadSettings();
    this.loadStats();
    this.setTargetWord();
    this.createBoard();
    this.setupEventListeners();
    this.updateCountdown();
    this.applyTheme();
    
    // Update countdown every second
    setInterval(() => this.updateCountdown(), 1000);
  }

  setTargetWord() {
    // Use today's word for consistency
    this.targetWord = window.WordleWords.getTodaysWord();
    console.log('Target word:', this.targetWord); // For development - remove in production
  }

  createBoard() {
    const board = document.getElementById('gameBoard');
    board.innerHTML = '';
    
    for (let row = 0; row < 6; row++) {
      const rowElement = document.createElement('div');
      rowElement.className = 'row';
      rowElement.setAttribute('data-row', row);
      
      for (let col = 0; col < 5; col++) {
        const tile = document.createElement('div');
        tile.className = 'tile';
        tile.setAttribute('data-row', row);
        tile.setAttribute('data-col', col);
        tile.setAttribute('tabindex', '-1');
        rowElement.appendChild(tile);
      }
      
      board.appendChild(rowElement);
    }
  }

  setupEventListeners() {
    // Physical keyboard
    document.addEventListener('keydown', (e) => this.handleKeyPress(e.key));
    
    // Virtual keyboard
    document.getElementById('keyboard').addEventListener('click', (e) => {
      if (e.target.classList.contains('key')) {
        const key = e.target.getAttribute('data-key');
        this.handleKeyPress(key);
      }
    });
    
    // Modal controls
    document.getElementById('helpBtn').addEventListener('click', () => this.showModal('helpModal'));
    document.getElementById('statsBtn').addEventListener('click', () => this.showModal('statsModal'));
    document.getElementById('settingsBtn').addEventListener('click', () => this.showModal('settingsModal'));
    
    // Close modal buttons
    document.querySelectorAll('.close-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const modalId = e.target.getAttribute('data-modal');
        this.hideModal(modalId);
      });
    });
    
    // Modal backdrop clicks
    document.querySelectorAll('.modal').forEach(modal => {
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          this.hideModal(modal.id);
        }
      });
    });
    
    // Settings toggles
    document.getElementById('hardMode').addEventListener('change', (e) => {
      this.settings.hardMode = e.target.checked;
      this.saveSettings();
    });
    
    document.getElementById('darkTheme').addEventListener('change', (e) => {
      this.settings.darkTheme = e.target.checked;
      this.saveSettings();
      this.applyTheme();
    });
    
    document.getElementById('highContrast').addEventListener('change', (e) => {
      this.settings.highContrast = e.target.checked;
      this.saveSettings();
      this.applyTheme();
    });
    
    // Share button
    document.getElementById('shareBtn').addEventListener('click', () => this.shareResults());
  }

  handleKeyPress(key) {
    if (this.gameState !== 'playing') return;
    
    key = key.toUpperCase();
    
    if (key === 'ENTER') {
      this.submitGuess();
    } else if (key === 'BACKSPACE') {
      this.deleteLetter();
    } else if (key.match(/^[A-Z]$/) && key.length === 1) {
      this.addLetter(key);
    }
  }

  addLetter(letter) {
    if (this.currentCol < 5) {
      const tile = this.getTile(this.currentRow, this.currentCol);
      tile.textContent = letter;
      tile.classList.add('filled');
      tile.classList.add('pop');
      
      // Remove animation class after animation completes
      setTimeout(() => tile.classList.remove('pop'), 100);
      
      this.currentCol++;
    }
  }

  deleteLetter() {
    if (this.currentCol > 0) {
      this.currentCol--;
      const tile = this.getTile(this.currentRow, this.currentCol);
      tile.textContent = '';
      tile.classList.remove('filled');
    }
  }

  async submitGuess() {
    if (this.currentCol !== 5) {
      this.showMessage('Not enough letters');
      this.shakeRow(this.currentRow);
      return;
    }
    
    const guess = this.getCurrentGuess();
    
    if (!window.WordleWords.isValidWord(guess)) {
      this.showMessage('Not in word list');
      this.shakeRow(this.currentRow);
      return;
    }
    
    // Hard mode validation
    if (this.settings.hardMode && this.guesses.length > 0) {
      const violation = this.checkHardModeViolation(guess);
      if (violation) {
        this.showMessage(violation);
        this.shakeRow(this.currentRow);
        return;
      }
    }
    
    this.guesses.push(guess);
    await this.revealRow(this.currentRow, guess);
    this.updateKeyboard(guess);
    
    if (guess === this.targetWord) {
      this.gameState = 'won';
      this.showMessage('Magnificent!');
      this.bounceRow(this.currentRow);
      this.updateStats(true);
      setTimeout(() => this.showModal('statsModal'), 2000);
    } else if (this.currentRow === 5) {
      this.gameState = 'lost';
      this.showMessage(this.targetWord);
      this.updateStats(false);
      setTimeout(() => this.showModal('statsModal'), 2000);
    } else {
      this.currentRow++;
      this.currentCol = 0;
    }
  }

  getCurrentGuess() {
    let guess = '';
    for (let col = 0; col < 5; col++) {
      const tile = this.getTile(this.currentRow, col);
      guess += tile.textContent;
    }
    return guess;
  }

  async revealRow(row, guess) {
    const result = this.checkGuess(guess);
    
    for (let col = 0; col < 5; col++) {
      const tile = this.getTile(row, col);
      
      // Add flip animation with delay
      setTimeout(() => {
        tile.classList.add('flip');
        
        // Apply color after half the flip animation
        setTimeout(() => {
          tile.classList.add(result[col]);
        }, 300);
      }, col * 100);
    }
    
    // Wait for all animations to complete
    return new Promise(resolve => {
      setTimeout(resolve, 600);
    });
  }

  checkGuess(guess) {
    const result = [];
    const targetLetters = this.targetWord.split('');
    const guessLetters = guess.split('');
    
    // First pass: mark correct letters
    for (let i = 0; i < 5; i++) {
      if (guessLetters[i] === targetLetters[i]) {
        result[i] = 'correct';
        targetLetters[i] = null; // Mark as used
        guessLetters[i] = null; // Mark as used
      }
    }
    
    // Second pass: mark present letters
    for (let i = 0; i < 5; i++) {
      if (guessLetters[i] !== null) {
        const targetIndex = targetLetters.indexOf(guessLetters[i]);
        if (targetIndex !== -1) {
          result[i] = 'present';
          targetLetters[targetIndex] = null; // Mark as used
        } else {
          result[i] = 'absent';
        }
      }
    }
    
    return result;
  }

  updateKeyboard(guess) {
    const result = this.checkGuess(guess);
    
    for (let i = 0; i < 5; i++) {
      const letter = guess[i];
      const status = result[i];
      
      // Only update if the new status is better than the current one
      const currentStatus = this.keyboardState[letter];
      if (!currentStatus || 
          (status === 'correct') ||
          (status === 'present' && currentStatus !== 'correct') ||
          (status === 'absent' && currentStatus !== 'correct' && currentStatus !== 'present')) {
        this.keyboardState[letter] = status;
        
        const keyElement = document.querySelector(`[data-key="${letter}"]`);
        if (keyElement) {
          keyElement.classList.remove('correct', 'present', 'absent');
          keyElement.classList.add(status);
        }
      }
    }
  }

  checkHardModeViolation(guess) {
    if (this.guesses.length === 0) return null;
    
    const lastGuess = this.guesses[this.guesses.length - 1];
    const lastResult = this.checkGuess(lastGuess);
    
    // Check for correct letters in wrong positions
    for (let i = 0; i < 5; i++) {
      if (lastResult[i] === 'correct' && guess[i] !== lastGuess[i]) {
        return `${i + 1}${this.getOrdinalSuffix(i + 1)} letter must be ${lastGuess[i]}`;
      }
    }
    
    // Check for present letters not being used
    for (let i = 0; i < 5; i++) {
      if (lastResult[i] === 'present' && !guess.includes(lastGuess[i])) {
        return `Guess must contain ${lastGuess[i]}`;
      }
    }
    
    return null;
  }

  getOrdinalSuffix(num) {
    const suffixes = ['st', 'nd', 'rd', 'th'];
    const v = num % 100;
    return suffixes[(v - 20) % 10] || suffixes[v] || suffixes[0];
  }

  getTile(row, col) {
    return document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
  }

  shakeRow(row) {
    const rowElement = document.querySelector(`[data-row="${row}"]`);
    rowElement.classList.add('shake');
    setTimeout(() => rowElement.classList.remove('shake'), 500);
  }

  bounceRow(row) {
    const tiles = document.querySelectorAll(`[data-row="${row}"] .tile`);
    tiles.forEach((tile, index) => {
      setTimeout(() => {
        tile.classList.add('bounce');
        setTimeout(() => tile.classList.remove('bounce'), 500);
      }, index * 100);
    });
  }

  showMessage(message) {
    const messageElement = document.getElementById('gameMessage');
    messageElement.textContent = message;
    
    // Clear message after 3 seconds
    setTimeout(() => {
      messageElement.textContent = '';
    }, 3000);
  }

  showToast(message) {
    const toast = document.getElementById('toast');
    toast.textContent = message;
    toast.classList.add('show');
    
    setTimeout(() => {
      toast.classList.remove('show');
    }, 2000);
  }

  showModal(modalId) {
    const modal = document.getElementById(modalId);
    modal.classList.add('show');
    
    if (modalId === 'statsModal') {
      this.updateStatsDisplay();
    } else if (modalId === 'settingsModal') {
      this.updateSettingsDisplay();
    }
  }

  hideModal(modalId) {
    const modal = document.getElementById(modalId);
    modal.classList.remove('show');
  }

  updateStatsDisplay() {
    document.getElementById('gamesPlayed').textContent = this.stats.gamesPlayed;
    document.getElementById('winPercentage').textContent = 
      this.stats.gamesPlayed > 0 ? Math.round((this.stats.gamesWon / this.stats.gamesPlayed) * 100) : 0;
    document.getElementById('currentStreak').textContent = this.stats.currentStreak;
    document.getElementById('maxStreak').textContent = this.stats.maxStreak;
    
    this.updateDistributionChart();
  }

  updateDistributionChart() {
    const container = document.getElementById('distributionContainer');
    container.innerHTML = '';
    
    const maxCount = Math.max(...this.stats.guessDistribution, 1);
    
    for (let i = 0; i < 6; i++) {
      const bar = document.createElement('div');
      bar.className = 'distribution-bar';
      
      const number = document.createElement('div');
      number.className = 'distribution-number';
      number.textContent = i + 1;
      
      const fill = document.createElement('div');
      fill.className = 'distribution-fill';
      fill.textContent = this.stats.guessDistribution[i];
      fill.style.width = `${Math.max((this.stats.guessDistribution[i] / maxCount) * 100, 7)}%`;
      
      // Highlight the current game's guess count
      if (this.gameState === 'won' && i === this.currentRow) {
        fill.classList.add('highlight');
      }
      
      bar.appendChild(number);
      bar.appendChild(fill);
      container.appendChild(bar);
    }
  }

  updateSettingsDisplay() {
    document.getElementById('hardMode').checked = this.settings.hardMode;
    document.getElementById('darkTheme').checked = this.settings.darkTheme;
    document.getElementById('highContrast').checked = this.settings.highContrast;
  }

  updateStats(won) {
    this.stats.gamesPlayed++;
    
    if (won) {
      this.stats.gamesWon++;
      this.stats.currentStreak++;
      this.stats.maxStreak = Math.max(this.stats.maxStreak, this.stats.currentStreak);
      this.stats.guessDistribution[this.currentRow]++;
    } else {
      this.stats.currentStreak = 0;
    }
    
    this.saveStats();
  }

  updateCountdown() {
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    
    const diff = tomorrow - now;
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);
    
    const countdown = document.getElementById('countdown');
    if (countdown) {
      countdown.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
  }

  shareResults() {
    if (this.gameState === 'playing') {
      this.showToast('Finish the game first!');
      return;
    }
    
    const guessCount = this.gameState === 'won' ? this.currentRow + 1 : 'X';
    let shareText = `Wordle ${guessCount}/6\n\n`;
    
    for (let row = 0; row <= (this.gameState === 'won' ? this.currentRow : 5); row++) {
      if (row < this.guesses.length) {
        const result = this.checkGuess(this.guesses[row]);
        for (let col = 0; col < 5; col++) {
          if (result[col] === 'correct') {
            shareText += '🟩';
          } else if (result[col] === 'present') {
            shareText += '🟨';
          } else {
            shareText += '⬛';
          }
        }
        shareText += '\n';
      }
    }
    
    if (navigator.share) {
      navigator.share({
        title: 'Wordle',
        text: shareText
      });
    } else {
      navigator.clipboard.writeText(shareText).then(() => {
        this.showToast('Results copied to clipboard!');
      }).catch(() => {
        this.showToast('Unable to copy results');
      });
    }
  }

  applyTheme() {
    document.body.setAttribute('data-theme', this.settings.darkTheme ? 'dark' : 'light');
    document.body.setAttribute('data-contrast', this.settings.highContrast ? 'high' : 'normal');
  }

  saveSettings() {
    localStorage.setItem('wordle-settings', JSON.stringify(this.settings));
  }

  loadSettings() {
    const saved = localStorage.getItem('wordle-settings');
    if (saved) {
      this.settings = { ...this.settings, ...JSON.parse(saved) };
    }
  }

  saveStats() {
    localStorage.setItem('wordle-stats', JSON.stringify(this.stats));
  }

  loadStats() {
    const saved = localStorage.getItem('wordle-stats');
    if (saved) {
      this.stats = { ...this.stats, ...JSON.parse(saved) };
    }
  }
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', () => {
  new WordleGame();
});
