// Simple test to check basic functionality
const SudokuSolver = require('./algorithms/solver');
const SudokuGenerator = require('./algorithms/generator');
const SudokuValidator = require('./algorithms/validator');

console.log('🧪 Testing Sudoku algorithms...\n');

// Test puzzle
const testPuzzle = [
  [5,3,0,0,7,0,0,0,0],
  [6,0,0,1,9,5,0,0,0],
  [0,9,8,0,0,0,0,6,0],
  [8,0,0,0,6,0,0,0,3],
  [4,0,0,8,0,3,0,0,1],
  [7,0,0,0,2,0,0,0,6],
  [0,6,0,0,0,0,2,8,0],
  [0,0,0,4,1,9,0,0,5],
  [0,0,0,0,8,0,0,7,9]
];

// Test Solver
console.log('🧩 Testing Solver...');
try {
  const solver = new SudokuSolver();
  const result = solver.solve(testPuzzle);
  console.log('✅ Solver test passed');
  console.log(`   Solved: ${result.solved}`);
  console.log(`   Techniques: ${result.techniques.join(', ')}`);
} catch (error) {
  console.log('❌ Solver test failed:', error.message);
}

// Test Generator
console.log('\n🎲 Testing Generator...');
try {
  const generator = new SudokuGenerator();
  const result = generator.generate('easy');
  console.log('✅ Generator test passed');
  console.log(`   Clues: ${result.clueCount}`);
  console.log(`   Unique solution: ${result.uniqueSolution}`);
} catch (error) {
  console.log('❌ Generator test failed:', error.message);
  console.log('   Stack:', error.stack);
}

// Test Validator
console.log('\n✅ Testing Validator...');
try {
  const validator = new SudokuValidator();
  const isValid = validator.isValidPuzzle(testPuzzle);
  const completeness = validator.getCompleteness(testPuzzle);
  console.log('✅ Validator test passed');
  console.log(`   Is valid: ${isValid}`);
  console.log(`   Completeness: ${completeness.percentage}%`);
} catch (error) {
  console.log('❌ Validator test failed:', error.message);
}

console.log('\n🎉 Algorithm tests completed!');
