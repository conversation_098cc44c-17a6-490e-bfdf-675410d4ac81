<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WorkRoom - Collaborative Workspace</title>
    <script src="/socket.io/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
            color: #f8fafc;
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            height: 100vh;
            overflow: hidden;
        }

        .app-container {
            display: flex;
            height: 100vh;
        }

        .sidebar {
            width: 300px;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(16px);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            flex-direction: column;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .header {
            background: rgba(0, 0, 0, 0.2);
            padding: 16px 24px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .room-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #f8fafc;
        }

        .room-info {
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: 0.875rem;
            color: #cbd5e1;
        }

        .member-count {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #10b981;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .workspace-tabs {
            display: flex;
            background: rgba(0, 0, 0, 0.2);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .tab {
            padding: 12px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s ease;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .tab:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .tab.active {
            border-bottom-color: #3b82f6;
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
        }

        .tab-content {
            flex: 1;
            display: none;
            flex-direction: column;
            overflow: hidden;
        }

        .tab-content.active {
            display: flex;
        }

        /* Login Screen */
        .login-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(16px);
            border-radius: 16px;
            padding: 40px;
            width: 100%;
            max-width: 500px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .login-header {
            text-align: center;
            margin-bottom: 32px;
        }

        .login-header h1 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 8px;
            color: #f8fafc;
        }

        .login-header p {
            color: #cbd5e1;
            font-size: 1rem;
        }

        .login-tabs {
            display: flex;
            margin-bottom: 24px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            padding: 4px;
        }

        .login-tab {
            flex: 1;
            padding: 8px 16px;
            text-align: center;
            cursor: pointer;
            border-radius: 6px;
            transition: all 0.2s ease;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .login-tab.active {
            background: #3b82f6;
            color: #ffffff;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            color: #f8fafc;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: #f8fafc;
            font-size: 1rem;
            transition: all 0.2s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-input::placeholder {
            color: #94a3b8;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.875rem;
            transition: all 0.2s ease-in-out;
            border: 1px solid transparent;
            cursor: pointer;
            letter-spacing: -0.025em;
            width: 100%;
        }

        .btn-primary {
            background: #3b82f6;
            color: #ffffff;
            border-color: #3b82f6;
        }

        .btn-primary:hover {
            background: #2563eb;
            border-color: #2563eb;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }

        .btn-primary:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .rooms-list {
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }

        .room-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .room-item:hover {
            background: rgba(255, 255, 255, 0.08);
            transform: translateY(-1px);
        }

        .room-name {
            font-weight: 600;
            color: #f8fafc;
            margin-bottom: 4px;
        }

        .room-meta {
            font-size: 0.8125rem;
            color: #94a3b8;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .room-lock {
            color: #fbbf24;
        }

        .error-message {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.2);
            color: #fca5a5;
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 16px;
            font-size: 0.875rem;
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            color: #cbd5e1;
        }

        .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-top: 2px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Members List */
        .members-section {
            padding: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .section-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #f8fafc;
            margin-bottom: 12px;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .member-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .member-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 12px;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.05);
        }

        .member-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            font-weight: 600;
            color: #ffffff;
        }

        .member-info {
            flex: 1;
        }

        .member-name {
            font-size: 0.875rem;
            font-weight: 500;
            color: #f8fafc;
        }

        .member-status {
            font-size: 0.75rem;
            color: #94a3b8;
        }

        .member-status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #10b981;
        }

        .member-status-dot.away {
            background: #fbbf24;
        }

        .member-status-dot.busy {
            background: #ef4444;
        }

        .member-status-dot.offline {
            background: #6b7280;
        }

        /* Chat Section */
        .chat-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .message {
            display: flex;
            gap: 12px;
            padding: 8px 0;
        }

        .message-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            font-weight: 600;
            color: #ffffff;
            flex-shrink: 0;
        }

        .message-content {
            flex: 1;
        }

        .message-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;
        }

        .message-author {
            font-size: 0.875rem;
            font-weight: 600;
            color: #f8fafc;
        }

        .message-time {
            font-size: 0.75rem;
            color: #94a3b8;
        }

        .message-text {
            color: #cbd5e1;
            font-size: 0.875rem;
            line-height: 1.5;
            word-wrap: break-word;
        }

        .chat-input-section {
            padding: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(0, 0, 0, 0.2);
        }

        .chat-input-container {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: #f8fafc;
            font-size: 0.875rem;
            resize: none;
            min-height: 44px;
            max-height: 120px;
        }

        .chat-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .send-btn {
            padding: 12px 16px;
            background: #3b82f6;
            color: #ffffff;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .send-btn:hover {
            background: #2563eb;
        }

        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .typing-indicator {
            padding: 8px 20px;
            font-size: 0.8125rem;
            color: #94a3b8;
            font-style: italic;
            min-height: 32px;
        }

        @media (max-width: 768px) {
            .app-container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: auto;
                max-height: 40vh;
            }
            
            .login-card {
                margin: 20px;
                padding: 24px;
            }
        }
    </style>
</head>
<body>
    <!-- Login Screen -->
    <div class="login-screen" id="loginScreen">
        <div class="login-card">
            <div class="login-header">
                <h1>🏢 WorkRoom</h1>
                <p>Join a collaborative workspace or create your own</p>
            </div>

            <div class="login-tabs">
                <div class="login-tab active" onclick="switchLoginTab('join')">Join Room</div>
                <div class="login-tab" onclick="switchLoginTab('create')">Create Room</div>
            </div>

            <div id="errorMessage" class="error-message" style="display: none;"></div>

            <!-- Join Room Form -->
            <div id="joinForm">
                <div class="form-group">
                    <label class="form-label">Your Name</label>
                    <input type="text" class="form-input" id="joinUsername" placeholder="Enter your name" maxlength="20">
                </div>

                <button class="btn btn-primary" onclick="loadRooms()" id="loadRoomsBtn">
                    <span class="loading" id="loadRoomsLoading" style="display: none;">
                        <div class="spinner"></div>
                        Loading...
                    </span>
                    <span id="loadRoomsText">🔍 Browse Available Rooms</span>
                </button>

                <div id="roomsList" class="rooms-list"></div>
            </div>

            <!-- Create Room Form -->
            <div id="createForm" style="display: none;">
                <div class="form-group">
                    <label class="form-label">Your Name</label>
                    <input type="text" class="form-input" id="createUsername" placeholder="Enter your name" maxlength="20">
                </div>

                <div class="form-group">
                    <label class="form-label">Room Name</label>
                    <input type="text" class="form-input" id="roomName" placeholder="Enter room name" maxlength="50">
                </div>

                <div class="form-group">
                    <label class="form-label">Password (Optional)</label>
                    <input type="password" class="form-input" id="roomPassword" placeholder="Leave empty for public room" maxlength="20">
                </div>

                <button class="btn btn-primary" onclick="createRoom()" id="createRoomBtn">
                    <span class="loading" id="createRoomLoading" style="display: none;">
                        <div class="spinner"></div>
                        Creating...
                    </span>
                    <span id="createRoomText">🚀 Create Workspace</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Main Application -->
    <div class="app-container" id="appContainer" style="display: none;">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="members-section">
                <div class="section-title">Team Members</div>
                <div class="member-list" id="membersList">
                    <!-- Members will be populated here -->
                </div>
            </div>

            <div class="members-section">
                <div class="section-title">Focus Timer</div>
                <div id="focusTimerSection">
                    <div id="timerDisplay" style="text-align: center; margin-bottom: 16px;">
                        <div style="font-size: 2rem; font-weight: 700; color: #3b82f6;" id="timerTime">25:00</div>
                        <div style="font-size: 0.875rem; color: #94a3b8;" id="timerStatus">Ready to focus</div>
                    </div>
                    <div style="display: flex; gap: 8px;">
                        <button class="btn btn-primary" onclick="startFocusTimer()" id="startTimerBtn" style="flex: 1; font-size: 0.8125rem; padding: 8px 12px;">
                            ▶️ Start
                        </button>
                        <button class="btn btn-secondary" onclick="stopFocusTimer()" id="stopTimerBtn" style="flex: 1; font-size: 0.8125rem; padding: 8px 12px; background: rgba(239, 68, 68, 0.2); border-color: rgba(239, 68, 68, 0.3);" disabled>
                            ⏹️ Stop
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <div class="header">
                <div class="room-title" id="roomTitle">Loading...</div>
                <div class="room-info">
                    <div class="member-count">
                        <span class="status-indicator"></span>
                        <span id="memberCount">0 members</span>
                    </div>
                    <button onclick="leaveRoom()" style="background: rgba(239, 68, 68, 0.2); border: 1px solid rgba(239, 68, 68, 0.3); color: #fca5a5; padding: 6px 12px; border-radius: 6px; font-size: 0.8125rem; cursor: pointer;">
                        🚪 Leave Room
                    </button>
                </div>
            </div>

            <!-- Workspace Tabs -->
            <div class="workspace-tabs">
                <div class="tab active" onclick="switchTab('chat')">💬 Chat</div>
                <div class="tab" onclick="switchTab('tasks')">✅ Tasks</div>
                <div class="tab" onclick="switchTab('whiteboard')">🎨 Whiteboard</div>
            </div>

            <!-- Chat Tab -->
            <div class="tab-content active" id="chatTab">
                <div class="chat-section">
                    <div class="chat-messages" id="chatMessages">
                        <!-- Messages will be populated here -->
                    </div>
                    <div class="typing-indicator" id="typingIndicator"></div>
                    <div class="chat-input-section">
                        <div class="chat-input-container">
                            <textarea class="chat-input" id="messageInput" placeholder="Type your message..." rows="1"></textarea>
                            <button class="send-btn" onclick="sendMessage()" id="sendBtn">Send</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tasks Tab -->
            <div class="tab-content" id="tasksTab">
                <div style="padding: 20px; height: 100%; overflow-y: auto;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h3 style="color: #f8fafc; font-size: 1.25rem; font-weight: 600;">Team Tasks</h3>
                        <button class="btn btn-primary" onclick="showCreateTaskForm()" style="padding: 8px 16px; font-size: 0.8125rem;">
                            ➕ Add Task
                        </button>
                    </div>

                    <div id="createTaskForm" style="display: none; background: rgba(255, 255, 255, 0.05); border-radius: 12px; padding: 20px; margin-bottom: 20px;">
                        <div class="form-group">
                            <label class="form-label">Task Title</label>
                            <input type="text" class="form-input" id="taskTitle" placeholder="Enter task title" maxlength="100">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Description</label>
                            <textarea class="form-input" id="taskDescription" placeholder="Enter task description" rows="3" maxlength="500"></textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Priority</label>
                            <select class="form-input" id="taskPriority">
                                <option value="low">Low</option>
                                <option value="medium" selected>Medium</option>
                                <option value="high">High</option>
                            </select>
                        </div>
                        <div style="display: flex; gap: 12px;">
                            <button class="btn btn-primary" onclick="createTask()" style="flex: 1;">Create Task</button>
                            <button class="btn btn-secondary" onclick="hideCreateTaskForm()" style="flex: 1; background: rgba(255, 255, 255, 0.08);">Cancel</button>
                        </div>
                    </div>

                    <div id="tasksList">
                        <!-- Tasks will be populated here -->
                    </div>
                </div>
            </div>

            <!-- Whiteboard Tab -->
            <div class="tab-content" id="whiteboardTab">
                <div style="padding: 20px; height: 100%; display: flex; flex-direction: column;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                        <h3 style="color: #f8fafc; font-size: 1.25rem; font-weight: 600;">Collaborative Whiteboard</h3>
                        <div style="display: flex; gap: 8px;">
                            <button onclick="clearWhiteboard()" style="background: rgba(239, 68, 68, 0.2); border: 1px solid rgba(239, 68, 68, 0.3); color: #fca5a5; padding: 6px 12px; border-radius: 6px; font-size: 0.8125rem; cursor: pointer;">
                                🗑️ Clear
                            </button>
                        </div>
                    </div>
                    <div style="flex: 1; background: rgba(255, 255, 255, 0.95); border-radius: 12px; position: relative; overflow: hidden;">
                        <canvas id="whiteboard" style="width: 100%; height: 100%; cursor: crosshair;"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let socket = null;
        let currentUser = null;
        let currentRoom = null;
        let isAuthenticated = false;
        let typingTimeout = null;
        let focusTimerInterval = null;

        // API Configuration
        const API_BASE = 'http://localhost:3006/api';

        // Initialize application
        window.addEventListener('load', () => {
            setupEventListeners();
            initializeSocket();
        });

        function setupEventListeners() {
            // Enter key handling for inputs
            document.getElementById('joinUsername').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') loadRooms();
            });

            document.getElementById('createUsername').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') document.getElementById('roomName').focus();
            });

            document.getElementById('roomName').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') document.getElementById('roomPassword').focus();
            });

            document.getElementById('roomPassword').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') createRoom();
            });

            // Message input handling
            const messageInput = document.getElementById('messageInput');
            messageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            messageInput.addEventListener('input', () => {
                handleTyping();
                autoResizeTextarea(messageInput);
            });

            // Task input handling
            document.getElementById('taskTitle').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') createTask();
            });
        }

        function autoResizeTextarea(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
        }

        function initializeSocket() {
            socket = io();

            socket.on('connect', () => {
                console.log('Connected to server');
            });

            socket.on('disconnect', () => {
                console.log('Disconnected from server');
                showError('Connection lost. Please refresh the page.');
            });

            socket.on('auth_error', (data) => {
                showError(data.message);
                leaveRoom();
            });

            socket.on('room_data', (data) => {
                currentRoom = data;
                updateRoomDisplay();
                loadMessages(data.messages);
                loadTasks(data.tasks);
                updateFocusTimer(data.focusTimer);
            });

            socket.on('members_list', (members) => {
                updateMembersList(members);
            });

            socket.on('user_joined', (data) => {
                addSystemMessage(`${data.user.username} joined the room`);
                // Refresh members list
                requestMembersList();
            });

            socket.on('user_left', (data) => {
                addSystemMessage(`${data.username} left the room`);
                // Refresh members list
                requestMembersList();
            });

            socket.on('new_message', (message) => {
                addMessage(message);
            });

            socket.on('user_typing', (data) => {
                showTypingIndicator(data.username);
            });

            socket.on('user_stopped_typing', (data) => {
                hideTypingIndicator(data.userId);
            });

            socket.on('task_created', (task) => {
                addTaskToList(task);
                addSystemMessage(`New task created: ${task.title}`);
            });

            socket.on('task_updated', (task) => {
                updateTaskInList(task);
            });

            socket.on('task_deleted', (data) => {
                removeTaskFromList(data.taskId);
            });

            socket.on('focus_timer_started', (data) => {
                addSystemMessage(`Focus timer started by ${data.startedBy} (${Math.floor(data.duration / 60)} minutes)`);
                startTimerDisplay(data.duration);
            });

            socket.on('focus_timer_tick', (data) => {
                updateTimerDisplay(data.remaining);
            });

            socket.on('focus_timer_completed', () => {
                addSystemMessage('🎉 Focus session completed! Great work team!');
                resetTimerDisplay();
            });

            socket.on('focus_timer_stopped', (data) => {
                addSystemMessage(`Focus timer stopped by ${data.stoppedBy}`);
                resetTimerDisplay();
            });

            socket.on('error', (data) => {
                showError(data.message);
            });
        }

        // Login and Room Management
        function switchLoginTab(tab) {
            const joinTab = document.querySelector('.login-tab:first-child');
            const createTab = document.querySelector('.login-tab:last-child');
            const joinForm = document.getElementById('joinForm');
            const createForm = document.getElementById('createForm');

            if (tab === 'join') {
                joinTab.classList.add('active');
                createTab.classList.remove('active');
                joinForm.style.display = 'block';
                createForm.style.display = 'none';
            } else {
                joinTab.classList.remove('active');
                createTab.classList.add('active');
                joinForm.style.display = 'none';
                createForm.style.display = 'block';
            }

            hideError();
        }

        async function loadRooms() {
            const username = document.getElementById('joinUsername').value.trim();
            if (!username) {
                showError('Please enter your name');
                return;
            }

            const btn = document.getElementById('loadRoomsBtn');
            const loading = document.getElementById('loadRoomsLoading');
            const text = document.getElementById('loadRoomsText');

            btn.disabled = true;
            loading.style.display = 'flex';
            text.style.display = 'none';

            try {
                const response = await fetch(`${API_BASE}/rooms`);
                const data = await response.json();

                if (data.success) {
                    displayRooms(data.data);
                } else {
                    showError('Failed to load rooms');
                }
            } catch (error) {
                showError('Failed to connect to server. Make sure it\'s running on port 3004.');
            } finally {
                btn.disabled = false;
                loading.style.display = 'none';
                text.style.display = 'block';
            }
        }

        function displayRooms(rooms) {
            const roomsList = document.getElementById('roomsList');

            if (rooms.length === 0) {
                roomsList.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #94a3b8;">
                        <div style="font-size: 3rem; margin-bottom: 16px;">🏠</div>
                        <div style="font-size: 1.125rem; font-weight: 500; margin-bottom: 8px;">No active rooms</div>
                        <div style="font-size: 0.875rem;">Create the first room to get started!</div>
                    </div>
                `;
                return;
            }

            roomsList.innerHTML = rooms.map(room => `
                <div class="room-item" onclick="joinRoom('${room.id}', '${room.name}', ${room.hasPassword})">
                    <div class="room-name">
                        ${room.hasPassword ? '<span class="room-lock">🔒</span> ' : ''}
                        ${room.name}
                    </div>
                    <div class="room-meta">
                        <span>${room.memberCount} member${room.memberCount !== 1 ? 's' : ''}</span>
                        <span>${new Date(room.createdAt).toLocaleDateString()}</span>
                    </div>
                </div>
            `).join('');
        }

        async function joinRoom(roomId, roomName, hasPassword) {
            const username = document.getElementById('joinUsername').value.trim();
            let password = '';

            if (hasPassword) {
                password = prompt(`Enter password for "${roomName}":`);
                if (password === null) return; // User cancelled
            }

            try {
                const response = await fetch(`${API_BASE}/rooms/${roomId}/join`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username,
                        password
                    })
                });

                const data = await response.json();

                if (data.success) {
                    currentUser = data.data.user;
                    currentRoom = data.data.room;

                    // Authenticate with WebSocket
                    socket.emit('authenticate', { token: data.data.token });

                    // Hide login screen and show app
                    document.getElementById('loginScreen').style.display = 'none';
                    document.getElementById('appContainer').style.display = 'flex';

                    isAuthenticated = true;
                } else {
                    showError(data.message);
                }
            } catch (error) {
                showError('Failed to join room. Please try again.');
            }
        }

        async function createRoom() {
            const username = document.getElementById('createUsername').value.trim();
            const roomName = document.getElementById('roomName').value.trim();
            const password = document.getElementById('roomPassword').value.trim();

            if (!username) {
                showError('Please enter your name');
                return;
            }

            if (!roomName) {
                showError('Please enter a room name');
                return;
            }

            const btn = document.getElementById('createRoomBtn');
            const loading = document.getElementById('createRoomLoading');
            const text = document.getElementById('createRoomText');

            btn.disabled = true;
            loading.style.display = 'flex';
            text.style.display = 'none';

            try {
                const response = await fetch(`${API_BASE}/rooms`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: roomName,
                        username,
                        password: password || undefined
                    })
                });

                const data = await response.json();

                if (data.success) {
                    currentUser = data.data.user;
                    currentRoom = data.data.room;

                    // Authenticate with WebSocket
                    socket.emit('authenticate', { token: data.data.token });

                    // Hide login screen and show app
                    document.getElementById('loginScreen').style.display = 'none';
                    document.getElementById('appContainer').style.display = 'flex';

                    isAuthenticated = true;
                } else {
                    showError(data.errors ? data.errors.map(e => e.msg).join(', ') : data.message);
                }
            } catch (error) {
                showError('Failed to create room. Please try again.');
            } finally {
                btn.disabled = false;
                loading.style.display = 'none';
                text.style.display = 'block';
            }
        }

        function leaveRoom() {
            if (socket) {
                socket.disconnect();
            }

            // Reset state
            currentUser = null;
            currentRoom = null;
            isAuthenticated = false;

            // Clear timers
            if (focusTimerInterval) {
                clearInterval(focusTimerInterval);
                focusTimerInterval = null;
            }

            // Show login screen
            document.getElementById('loginScreen').style.display = 'flex';
            document.getElementById('appContainer').style.display = 'none';

            // Clear forms
            document.getElementById('joinUsername').value = '';
            document.getElementById('createUsername').value = '';
            document.getElementById('roomName').value = '';
            document.getElementById('roomPassword').value = '';
            document.getElementById('roomsList').innerHTML = '';

            // Reconnect socket
            setTimeout(() => {
                initializeSocket();
            }, 1000);
        }

        // UI Helper Functions
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        function hideError() {
            document.getElementById('errorMessage').style.display = 'none';
        }

        function switchTab(tabName) {
            // Update tab buttons
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelector(`.tab[onclick="switchTab('${tabName}')"]`).classList.add('active');

            // Update tab content
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            document.getElementById(`${tabName}Tab`).classList.add('active');

            // Initialize whiteboard if switching to it
            if (tabName === 'whiteboard') {
                setTimeout(initializeWhiteboard, 100);
            }
        }

        function updateRoomDisplay() {
            if (!currentRoom) return;

            document.getElementById('roomTitle').textContent = currentRoom.name;
            document.getElementById('memberCount').textContent = `${currentRoom.members.length} member${currentRoom.members.length !== 1 ? 's' : ''}`;
        }

        function updateMembersList(members) {
            const membersList = document.getElementById('membersList');

            membersList.innerHTML = members.map(member => `
                <div class="member-item">
                    <div class="member-avatar" style="background-color: ${member.avatar.color};">
                        ${member.avatar.initials}
                    </div>
                    <div class="member-info">
                        <div class="member-name">${member.username}</div>
                        <div class="member-status">
                            <span class="member-status-dot ${member.status}"></span>
                            ${member.status}
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function requestMembersList() {
            // This would typically be handled by the server automatically
            // For now, we'll rely on the server sending updates
        }

        // Chat Functions
        function loadMessages(messages) {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.innerHTML = '';

            messages.forEach(message => {
                addMessage(message, false);
            });

            scrollToBottom();
        }

        function addMessage(message, scroll = true) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message';

            const time = new Date(message.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

            messageDiv.innerHTML = `
                <div class="message-avatar" style="background-color: ${message.avatar.color};">
                    ${message.avatar.initials}
                </div>
                <div class="message-content">
                    <div class="message-header">
                        <span class="message-author">${message.username}</span>
                        <span class="message-time">${time}</span>
                    </div>
                    <div class="message-text">${escapeHtml(message.content)}</div>
                </div>
            `;

            chatMessages.appendChild(messageDiv);

            if (scroll) {
                scrollToBottom();
            }
        }

        function addSystemMessage(content) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message';
            messageDiv.style.opacity = '0.7';

            const time = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

            messageDiv.innerHTML = `
                <div class="message-avatar" style="background-color: #6b7280;">
                    🤖
                </div>
                <div class="message-content">
                    <div class="message-header">
                        <span class="message-author">System</span>
                        <span class="message-time">${time}</span>
                    </div>
                    <div class="message-text" style="font-style: italic;">${content}</div>
                </div>
            `;

            chatMessages.appendChild(messageDiv);
            scrollToBottom();
        }

        function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const content = messageInput.value.trim();

            if (!content || !isAuthenticated) return;

            socket.emit('send_message', { content });
            messageInput.value = '';
            messageInput.style.height = 'auto';

            // Stop typing indicator
            socket.emit('typing_stop');
        }

        function handleTyping() {
            if (!isAuthenticated) return;

            socket.emit('typing_start');

            // Clear existing timeout
            if (typingTimeout) {
                clearTimeout(typingTimeout);
            }

            // Set new timeout to stop typing indicator
            typingTimeout = setTimeout(() => {
                socket.emit('typing_stop');
            }, 2000);
        }

        function showTypingIndicator(username) {
            const indicator = document.getElementById('typingIndicator');
            indicator.textContent = `${username} is typing...`;
        }

        function hideTypingIndicator(userId) {
            const indicator = document.getElementById('typingIndicator');
            indicator.textContent = '';
        }

        function scrollToBottom() {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Task Functions
        function loadTasks(tasks) {
            const tasksList = document.getElementById('tasksList');
            tasksList.innerHTML = '';

            if (tasks.length === 0) {
                tasksList.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #94a3b8;">
                        <div style="font-size: 3rem; margin-bottom: 16px;">✅</div>
                        <div style="font-size: 1.125rem; font-weight: 500; margin-bottom: 8px;">No tasks yet</div>
                        <div style="font-size: 0.875rem;">Create your first task to get started!</div>
                    </div>
                `;
                return;
            }

            tasks.forEach(task => {
                addTaskToList(task, false);
            });
        }

        function addTaskToList(task, prepend = true) {
            const tasksList = document.getElementById('tasksList');
            const taskDiv = document.createElement('div');
            taskDiv.className = 'task-item';
            taskDiv.id = `task-${task.id}`;

            const priorityColors = {
                low: '#10b981',
                medium: '#f59e0b',
                high: '#ef4444'
            };

            const createdTime = new Date(task.createdAt).toLocaleDateString();

            taskDiv.innerHTML = `
                <div style="background: rgba(255, 255, 255, 0.05); border-radius: 12px; padding: 16px; margin-bottom: 12px; border-left: 4px solid ${priorityColors[task.priority]};">
                    <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 8px;">
                        <div style="flex: 1;">
                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 4px;">
                                <input type="checkbox" ${task.completed ? 'checked' : ''} onchange="toggleTask('${task.id}')" style="margin-right: 8px;">
                                <span style="font-weight: 600; color: #f8fafc; ${task.completed ? 'text-decoration: line-through; opacity: 0.6;' : ''}">${escapeHtml(task.title)}</span>
                                <span style="background: ${priorityColors[task.priority]}; color: white; padding: 2px 6px; border-radius: 4px; font-size: 0.75rem; font-weight: 500; text-transform: uppercase;">${task.priority}</span>
                            </div>
                            ${task.description ? `<div style="color: #cbd5e1; font-size: 0.875rem; margin-bottom: 8px;">${escapeHtml(task.description)}</div>` : ''}
                            <div style="font-size: 0.75rem; color: #94a3b8;">
                                Created by ${task.createdByName} on ${createdTime}
                            </div>
                        </div>
                        <button onclick="deleteTask('${task.id}')" style="background: rgba(239, 68, 68, 0.2); border: 1px solid rgba(239, 68, 68, 0.3); color: #fca5a5; padding: 4px 8px; border-radius: 4px; font-size: 0.75rem; cursor: pointer;">
                            🗑️
                        </button>
                    </div>
                </div>
            `;

            if (prepend && tasksList.firstChild) {
                tasksList.insertBefore(taskDiv, tasksList.firstChild);
            } else {
                tasksList.appendChild(taskDiv);
            }
        }

        function updateTaskInList(task) {
            const existingTask = document.getElementById(`task-${task.id}`);
            if (existingTask) {
                existingTask.remove();
            }
            addTaskToList(task, false);
        }

        function removeTaskFromList(taskId) {
            const taskElement = document.getElementById(`task-${taskId}`);
            if (taskElement) {
                taskElement.remove();
            }
        }

        function showCreateTaskForm() {
            document.getElementById('createTaskForm').style.display = 'block';
            document.getElementById('taskTitle').focus();
        }

        function hideCreateTaskForm() {
            document.getElementById('createTaskForm').style.display = 'none';
            document.getElementById('taskTitle').value = '';
            document.getElementById('taskDescription').value = '';
            document.getElementById('taskPriority').value = 'medium';
        }

        function createTask() {
            const title = document.getElementById('taskTitle').value.trim();
            const description = document.getElementById('taskDescription').value.trim();
            const priority = document.getElementById('taskPriority').value;

            if (!title || !isAuthenticated) {
                showError('Please enter a task title');
                return;
            }

            socket.emit('create_task', {
                title,
                description,
                priority
            });

            hideCreateTaskForm();
        }

        function toggleTask(taskId) {
            if (!isAuthenticated) return;

            const checkbox = document.querySelector(`#task-${taskId} input[type="checkbox"]`);
            const completed = checkbox.checked;

            socket.emit('update_task', {
                taskId,
                updates: { completed }
            });
        }

        function deleteTask(taskId) {
            if (!isAuthenticated) return;

            if (confirm('Are you sure you want to delete this task?')) {
                socket.emit('delete_task', { taskId });
            }
        }

        // Focus Timer Functions
        function updateFocusTimer(timerData) {
            if (timerData.isActive) {
                startTimerDisplay(timerData.remaining);
            } else {
                resetTimerDisplay();
            }
        }

        function startFocusTimer() {
            if (!isAuthenticated) return;

            const duration = 25 * 60; // 25 minutes in seconds
            socket.emit('start_focus_timer', { duration });
        }

        function stopFocusTimer() {
            if (!isAuthenticated) return;

            socket.emit('stop_focus_timer');
        }

        function startTimerDisplay(duration) {
            const startBtn = document.getElementById('startTimerBtn');
            const stopBtn = document.getElementById('stopTimerBtn');

            startBtn.disabled = true;
            stopBtn.disabled = false;

            updateTimerDisplay(duration);

            // Clear any existing interval
            if (focusTimerInterval) {
                clearInterval(focusTimerInterval);
            }
        }

        function updateTimerDisplay(remaining) {
            const minutes = Math.floor(remaining / 60);
            const seconds = remaining % 60;
            const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

            document.getElementById('timerTime').textContent = timeString;
            document.getElementById('timerStatus').textContent = 'Focus session active';
        }

        function resetTimerDisplay() {
            const startBtn = document.getElementById('startTimerBtn');
            const stopBtn = document.getElementById('stopTimerBtn');

            startBtn.disabled = false;
            stopBtn.disabled = true;

            document.getElementById('timerTime').textContent = '25:00';
            document.getElementById('timerStatus').textContent = 'Ready to focus';

            if (focusTimerInterval) {
                clearInterval(focusTimerInterval);
                focusTimerInterval = null;
            }
        }

        // Whiteboard Functions
        let canvas = null;
        let ctx = null;
        let isDrawing = false;
        let lastX = 0;
        let lastY = 0;

        function initializeWhiteboard() {
            canvas = document.getElementById('whiteboard');
            if (!canvas) return;

            ctx = canvas.getContext('2d');

            // Set canvas size
            const container = canvas.parentElement;
            canvas.width = container.clientWidth;
            canvas.height = container.clientHeight;

            // Set drawing styles
            ctx.strokeStyle = '#1f2937';
            ctx.lineWidth = 2;
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';

            // Add event listeners
            canvas.addEventListener('mousedown', startDrawing);
            canvas.addEventListener('mousemove', draw);
            canvas.addEventListener('mouseup', stopDrawing);
            canvas.addEventListener('mouseout', stopDrawing);

            // Touch events for mobile
            canvas.addEventListener('touchstart', handleTouch);
            canvas.addEventListener('touchmove', handleTouch);
            canvas.addEventListener('touchend', stopDrawing);

            // Load existing whiteboard data
            if (currentRoom && currentRoom.whiteboard && currentRoom.whiteboard.elements) {
                drawElements(currentRoom.whiteboard.elements);
            }
        }

        function startDrawing(e) {
            if (!isAuthenticated) return;

            isDrawing = true;
            const rect = canvas.getBoundingClientRect();
            lastX = e.clientX - rect.left;
            lastY = e.clientY - rect.top;
        }

        function draw(e) {
            if (!isDrawing || !isAuthenticated) return;

            const rect = canvas.getBoundingClientRect();
            const currentX = e.clientX - rect.left;
            const currentY = e.clientY - rect.top;

            ctx.beginPath();
            ctx.moveTo(lastX, lastY);
            ctx.lineTo(currentX, currentY);
            ctx.stroke();

            // Emit drawing data to other users
            socket.emit('whiteboard_update', {
                elements: [{
                    type: 'line',
                    startX: lastX,
                    startY: lastY,
                    endX: currentX,
                    endY: currentY,
                    color: ctx.strokeStyle,
                    width: ctx.lineWidth
                }]
            });

            lastX = currentX;
            lastY = currentY;
        }

        function stopDrawing() {
            isDrawing = false;
        }

        function handleTouch(e) {
            e.preventDefault();
            const touch = e.touches[0];
            const mouseEvent = new MouseEvent(e.type === 'touchstart' ? 'mousedown' :
                                            e.type === 'touchmove' ? 'mousemove' : 'mouseup', {
                clientX: touch.clientX,
                clientY: touch.clientY
            });
            canvas.dispatchEvent(mouseEvent);
        }

        function drawElements(elements) {
            if (!ctx) return;

            elements.forEach(element => {
                if (element.type === 'line') {
                    ctx.strokeStyle = element.color;
                    ctx.lineWidth = element.width;
                    ctx.beginPath();
                    ctx.moveTo(element.startX, element.startY);
                    ctx.lineTo(element.endX, element.endY);
                    ctx.stroke();
                }
            });
        }

        function clearWhiteboard() {
            if (!isAuthenticated || !ctx) return;

            if (confirm('Are you sure you want to clear the whiteboard? This action cannot be undone.')) {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                socket.emit('whiteboard_update', {
                    elements: []
                });
            }
        }

        // Handle whiteboard updates from other users
        socket.on('whiteboard_updated', (data) => {
            if (data.elements && data.elements.length > 0) {
                drawElements(data.elements);
            } else {
                // Clear whiteboard
                if (ctx) {
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                }
            }
        });

        // Handle window resize for whiteboard
        window.addEventListener('resize', () => {
            if (canvas && ctx) {
                const container = canvas.parentElement;
                canvas.width = container.clientWidth;
                canvas.height = container.clientHeight;

                // Redraw existing elements
                if (currentRoom && currentRoom.whiteboard && currentRoom.whiteboard.elements) {
                    drawElements(currentRoom.whiteboard.elements);
                }
            }
        });
    </script>
</body>
</html>
