/**
 * Request validation middleware using Joi schemas
 */

import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';
import { ValidationError } from './errorHandler';

/**
 * Validation schemas
 */
const schemas = {
  startGame: Joi.object({
    playerName: Joi.string().min(1).max(50).required(),
    difficulty: Joi.string().valid('easy', 'medium', 'hard', 'expert', 'nightmare').optional(),
    gameMode: Joi.string().valid('daily', 'practice', 'challenge', 'multiplayer', 'tournament', 'custom').optional(),
    category: Joi.string().valid('common', 'animals', 'food', 'science', 'technology', 'sports', 'music', 'movies', 'books', 'geography', 'history', 'custom').optional(),
    customWord: Joi.string().length(5).pattern(/^[A-Za-z]+$/).optional()
  }),

  makeGuess: Joi.object({
    gameId: Joi.string().uuid().required(),
    word: Joi.string().length(5).pattern(/^[A-Za-z]+$/).required(),
    timestamp: Joi.string().isoDate().required()
  }),

  getGame: Joi.object({
    gameId: Joi.string().uuid().required()
  }),

  createRoom: Joi.object({
    name: Joi.string().min(1).max(100).required(),
    maxPlayers: Joi.number().integer().min(2).max(10).default(4),
    gameMode: Joi.string().valid('multiplayer', 'tournament').default('multiplayer'),
    difficulty: Joi.string().valid('easy', 'medium', 'hard', 'expert', 'nightmare').default('medium'),
    category: Joi.string().valid('common', 'animals', 'food', 'science', 'technology', 'sports', 'music', 'movies', 'books', 'geography', 'history', 'custom').default('common'),
    isPrivate: Joi.boolean().default(false),
    password: Joi.string().min(4).max(20).when('isPrivate', { is: true, then: Joi.required(), otherwise: Joi.optional() }),
    settings: Joi.object({
      timeLimit: Joi.number().integer().min(30).max(600).optional(),
      hintsEnabled: Joi.boolean().default(true),
      spectatingAllowed: Joi.boolean().default(true),
      chatEnabled: Joi.boolean().default(true),
      autoStart: Joi.boolean().default(false),
      roundsToPlay: Joi.number().integer().min(1).max(10).default(1)
    }).optional()
  }),

  joinRoom: Joi.object({
    roomId: Joi.string().uuid().required(),
    playerName: Joi.string().min(1).max(50).required(),
    password: Joi.string().min(4).max(20).optional()
  }),

  getLeaderboard: Joi.object({
    category: Joi.string().valid('common', 'animals', 'food', 'science', 'technology', 'sports', 'music', 'movies', 'books', 'geography', 'history', 'custom').optional(),
    difficulty: Joi.string().valid('easy', 'medium', 'hard', 'expert', 'nightmare').optional(),
    timeframe: Joi.string().valid('daily', 'weekly', 'monthly', 'all-time').default('all-time'),
    limit: Joi.number().integer().min(1).max(100).default(50),
    offset: Joi.number().integer().min(0).default(0)
  }),

  getPlayerStats: Joi.object({
    playerId: Joi.string().uuid().optional(),
    playerName: Joi.string().min(1).max(50).optional(),
    timeframe: Joi.string().valid('daily', 'weekly', 'monthly', 'all-time').default('all-time'),
    category: Joi.string().valid('common', 'animals', 'food', 'science', 'technology', 'sports', 'music', 'movies', 'books', 'geography', 'history', 'custom').optional(),
    difficulty: Joi.string().valid('easy', 'medium', 'hard', 'expert', 'nightmare').optional()
  }).or('playerId', 'playerName'),

  getAnalytics: Joi.object({
    startDate: Joi.string().isoDate().optional(),
    endDate: Joi.string().isoDate().optional(),
    granularity: Joi.string().valid('hour', 'day', 'week', 'month').default('day'),
    category: Joi.string().valid('common', 'animals', 'food', 'science', 'technology', 'sports', 'music', 'movies', 'books', 'geography', 'history', 'custom').optional(),
    difficulty: Joi.string().valid('easy', 'medium', 'hard', 'expert', 'nightmare').optional()
  }),

  getWords: Joi.object({
    category: Joi.string().valid('common', 'animals', 'food', 'science', 'technology', 'sports', 'music', 'movies', 'books', 'geography', 'history', 'custom').optional(),
    difficulty: Joi.string().valid('easy', 'medium', 'hard', 'expert', 'nightmare').optional(),
    limit: Joi.number().integer().min(1).max(100).default(20),
    offset: Joi.number().integer().min(0).default(0),
    search: Joi.string().min(1).max(50).optional()
  }),

  addCustomWord: Joi.object({
    word: Joi.string().length(5).pattern(/^[A-Za-z]+$/).required(),
    category: Joi.string().valid('common', 'animals', 'food', 'science', 'technology', 'sports', 'music', 'movies', 'books', 'geography', 'history', 'custom').default('custom'),
    difficulty: Joi.string().valid('easy', 'medium', 'hard', 'expert', 'nightmare').default('medium'),
    definition: Joi.string().max(500).optional(),
    tags: Joi.array().items(Joi.string().max(50)).max(10).optional()
  })
};

/**
 * Create validation middleware for a specific schema
 */
function createValidationMiddleware(schema: Joi.ObjectSchema, source: 'body' | 'query' | 'params' = 'body') {
  return (req: Request, res: Response, next: NextFunction): void => {
    const data = source === 'body' ? req.body : source === 'query' ? req.query : req.params;
    
    const { error, value } = schema.validate(data, {
      abortEarly: false,
      stripUnknown: true,
      convert: true
    });

    if (error) {
      const details = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value,
        code: detail.type
      }));

      throw new ValidationError('Validation failed', JSON.stringify(details));
    }

    // Replace the original data with validated and sanitized data
    if (source === 'body') {
      req.body = value;
    } else if (source === 'query') {
      req.query = value;
    } else {
      req.params = value;
    }

    next();
  };
}

/**
 * Validation middleware exports
 */
export const validationMiddleware = {
  startGame: createValidationMiddleware(schemas.startGame, 'body'),
  makeGuess: createValidationMiddleware(schemas.makeGuess, 'body'),
  getGame: createValidationMiddleware(schemas.getGame, 'params'),
  createRoom: createValidationMiddleware(schemas.createRoom, 'body'),
  joinRoom: createValidationMiddleware(schemas.joinRoom, 'body'),
  getLeaderboard: createValidationMiddleware(schemas.getLeaderboard, 'query'),
  getPlayerStats: createValidationMiddleware(schemas.getPlayerStats, 'query'),
  getAnalytics: createValidationMiddleware(schemas.getAnalytics, 'query'),
  getWords: createValidationMiddleware(schemas.getWords, 'query'),
  addCustomWord: createValidationMiddleware(schemas.addCustomWord, 'body')
};

/**
 * Generic validation function for custom use cases
 */
export function validateData(data: any, schema: Joi.ObjectSchema): any {
  const { error, value } = schema.validate(data, {
    abortEarly: false,
    stripUnknown: true,
    convert: true
  });

  if (error) {
    const details = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message,
      value: detail.context?.value,
      code: detail.type
    }));

    throw new ValidationError('Validation failed', JSON.stringify(details));
  }

  return value;
}
