/* ===== CSS CUSTOM PROPERTIES ===== */
:root {
  /* Colors - Creative Theme */
  --color-primary: #8b5cf6;
  --color-primary-hover: #7c3aed;
  --color-secondary: #64748b;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-danger: #ef4444;
  --color-info: #3b82f6;
  --color-creative: #ec4899;
  
  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-card: #ffffff;
  --bg-overlay: rgba(0, 0, 0, 0.5);
  --bg-canvas: #ffffff;
  --bg-toolbar: #f8fafc;
  
  /* Text Colors */
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-tertiary: #64748b;
  --text-inverse: #ffffff;
  
  /* Border Colors */
  --border-primary: #e2e8f0;
  --border-secondary: #cbd5e1;
  --border-focus: #8b5cf6;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  
  /* Typography */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  
  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-12: 3rem;
  --space-16: 4rem;
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  
  /* Transitions */
  --transition: 0.2s ease-in-out;
  
  /* Z-Index Layers */
  --z-toolbar: 100;
  --z-panel: 200;
  --z-modal: 1000;
  --z-loading: 2000;
}

/* Dark Theme */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-card: #1e293b;
    --bg-canvas: #1e293b;
    --bg-toolbar: #334155;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-tertiary: #94a3b8;
    --border-primary: #334155;
    --border-secondary: #475569;
  }
}

[data-theme="dark"] {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-card: #1e293b;
  --bg-canvas: #1e293b;
  --bg-toolbar: #334155;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-tertiary: #94a3b8;
  --border-primary: #334155;
  --border-secondary: #475569;
}

/* ===== RESET & BASE STYLES ===== */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  transition: background-color var(--transition), color var(--transition);
  overflow-x: hidden;
}

/* ===== HEADER ===== */
.header {
  position: sticky;
  top: 0;
  z-index: var(--z-toolbar);
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-primary);
  backdrop-filter: blur(8px);
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--space-4);
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  text-decoration: none;
  color: var(--text-primary);
  font-weight: 700;
  font-size: 1.25rem;
}

.logo-icon {
  font-size: 1.5rem;
}

.nav-controls {
  display: flex;
  gap: var(--space-3);
  align-items: center;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-lg);
  background-color: var(--bg-secondary);
  font-size: 0.875rem;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--color-danger);
  animation: pulse 2s infinite;
}

.status-indicator.online {
  background-color: var(--color-success);
}

.status-indicator.connecting {
  background-color: var(--color-warning);
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.theme-toggle,
.back-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  background-color: var(--bg-card);
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition);
  text-decoration: none;
}

.theme-toggle:hover,
.back-link:hover {
  background-color: var(--bg-secondary);
  transform: translateY(-1px);
}

/* ===== BUTTONS ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-3) var(--space-6);
  border: 1px solid transparent;
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition);
  text-decoration: none;
  white-space: nowrap;
  font-family: inherit;
}

.btn-primary {
  background-color: var(--color-primary);
  color: var(--text-inverse);
  border-color: var(--color-primary);
}

.btn-primary:hover {
  background-color: var(--color-primary-hover);
}

.btn-secondary {
  background-color: transparent;
  color: var(--text-primary);
  border-color: var(--border-primary);
}

.btn-secondary:hover {
  background-color: var(--bg-secondary);
}

.btn-small {
  padding: var(--space-2) var(--space-4);
  font-size: 0.75rem;
}

/* ===== ROOM SECTION ===== */
.room-section {
  min-height: calc(100vh - 80px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-8);
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
}

.room-container {
  max-width: 500px;
  width: 100%;
}

.room-card {
  background-color: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  box-shadow: var(--shadow-xl);
  text-align: center;
}

.room-card h1 {
  font-size: 2rem;
  font-weight: 800;
  margin-bottom: var(--space-4);
  background: linear-gradient(135deg, var(--color-primary), var(--color-creative));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.room-card p {
  color: var(--text-secondary);
  margin-bottom: var(--space-8);
}

.room-actions {
  margin-bottom: var(--space-8);
}

.room-input-group {
  display: flex;
  gap: var(--space-3);
  margin-bottom: var(--space-6);
}

.room-input-group input {
  flex: 1;
  padding: var(--space-3);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-family: inherit;
}

.room-input-group input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.divider {
  display: flex;
  align-items: center;
  margin: var(--space-6) 0;
  color: var(--text-tertiary);
  font-size: 0.875rem;
}

.divider::before,
.divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background-color: var(--border-primary);
}

.divider span {
  padding: 0 var(--space-4);
}

.room-info {
  text-align: left;
  background-color: var(--bg-secondary);
  padding: var(--space-6);
  border-radius: var(--radius-lg);
}

.room-info h3 {
  margin-bottom: var(--space-4);
  color: var(--text-primary);
}

.room-info ul {
  list-style: none;
}

.room-info li {
  padding: var(--space-2) 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* ===== WHITEBOARD SECTION ===== */
.whiteboard-section {
  height: calc(100vh - 80px);
  display: flex;
  flex-direction: column;
  background-color: var(--bg-secondary);
}

/* ===== TOOLBAR ===== */
.toolbar {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-3) var(--space-4);
  background-color: var(--bg-toolbar);
  border-bottom: 1px solid var(--border-primary);
  overflow-x: auto;
  z-index: var(--z-toolbar);
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: 0 var(--space-3);
  border-right: 1px solid var(--border-primary);
}

.toolbar-group:last-child {
  border-right: none;
}

.tool-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background-color: var(--bg-card);
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition);
  font-size: 1rem;
}

.tool-btn:hover {
  background-color: var(--bg-secondary);
  transform: translateY(-1px);
}

.tool-btn.active {
  background-color: var(--color-primary);
  color: var(--text-inverse);
  border-color: var(--color-primary);
}

.color-picker {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.color-picker input[type="color"] {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
}

.color-presets {
  display: flex;
  gap: var(--space-1);
}

.color-preset {
  width: 24px;
  height: 24px;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: transform var(--transition);
}

.color-preset:hover {
  transform: scale(1.1);
}

.brush-size {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: 0.875rem;
}

.brush-size input[type="range"] {
  width: 80px;
}

.room-info-toolbar {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
  font-size: 0.75rem;
  color: var(--text-secondary);
}

/* ===== CANVAS CONTAINER ===== */
.canvas-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  background-color: var(--bg-canvas);
}

#whiteboard {
  display: block;
  cursor: crosshair;
  background-color: var(--bg-canvas);
  border: 1px solid var(--border-primary);
}

.cursors-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}

.user-cursor {
  position: absolute;
  width: 20px;
  height: 20px;
  pointer-events: none;
  transition: all 0.1s ease;
}

.cursor-pointer {
  width: 0;
  height: 0;
  border-left: 10px solid;
  border-right: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-top: 5px solid transparent;
}

.cursor-label {
  position: absolute;
  top: 20px;
  left: 0;
  background-color: var(--color-primary);
  color: var(--text-inverse);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  white-space: nowrap;
}

.canvas-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.zoom-controls {
  position: absolute;
  bottom: var(--space-4);
  right: var(--space-4);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  background-color: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-2);
  pointer-events: auto;
  box-shadow: var(--shadow-md);
}

.zoom-btn {
  width: 32px;
  height: 32px;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background-color: var(--bg-card);
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition);
  font-size: 0.875rem;
  font-weight: 600;
}

.zoom-btn:hover {
  background-color: var(--bg-secondary);
}

.zoom-level {
  font-size: 0.75rem;
  color: var(--text-secondary);
  min-width: 40px;
  text-align: center;
}

@media (max-width: 768px) {
  .room-input-group {
    flex-direction: column;
  }
  
  .toolbar {
    flex-wrap: wrap;
    gap: var(--space-2);
  }
  
  .toolbar-group {
    border-right: none;
    border-bottom: 1px solid var(--border-primary);
    padding-bottom: var(--space-2);
  }
  
  .toolbar-group:last-child {
    border-bottom: none;
  }
  
  .zoom-controls {
    bottom: var(--space-2);
    right: var(--space-2);
  }
}

/* ===== SIDE PANELS ===== */
.users-panel,
.chat-panel {
  position: fixed;
  top: 80px;
  width: 300px;
  height: calc(100vh - 80px);
  background-color: var(--bg-card);
  border: 1px solid var(--border-primary);
  z-index: var(--z-panel);
  display: flex;
  flex-direction: column;
  transition: transform var(--transition);
}

.users-panel {
  right: 0;
  transform: translateX(100%);
}

.users-panel.open {
  transform: translateX(0);
}

.chat-panel {
  right: 320px;
  transform: translateX(100%);
}

.chat-panel.open {
  transform: translateX(0);
}

.users-header,
.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-4);
  border-bottom: 1px solid var(--border-primary);
  background-color: var(--bg-secondary);
}

.users-header h3,
.chat-header h3 {
  font-size: 1rem;
  color: var(--text-primary);
}

.panel-toggle {
  position: absolute;
  left: -40px;
  top: var(--space-4);
  width: 40px;
  height: 40px;
  border: 1px solid var(--border-primary);
  border-right: none;
  border-radius: var(--radius-md) 0 0 var(--radius-md);
  background-color: var(--bg-card);
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition);
  font-size: 1.2rem;
}

.panel-toggle:hover {
  background-color: var(--bg-secondary);
}

/* Users List */
.users-list {
  flex: 1;
  padding: var(--space-4);
  overflow-y: auto;
}

.user-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  border-radius: var(--radius-md);
  margin-bottom: var(--space-2);
  transition: background-color var(--transition);
}

.user-item:hover {
  background-color: var(--bg-secondary);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-inverse);
}

.user-info {
  flex: 1;
}

.user-name {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.user-status {
  font-size: 0.75rem;
  color: var(--text-tertiary);
}

/* Chat */
.chat-messages {
  flex: 1;
  padding: var(--space-4);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.chat-message {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.message-header {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.message-author {
  font-weight: 500;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.message-time {
  font-size: 0.75rem;
  color: var(--text-tertiary);
}

.message-content {
  background-color: var(--bg-secondary);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  color: var(--text-primary);
  word-wrap: break-word;
}

.message-content.own {
  background-color: var(--color-primary);
  color: var(--text-inverse);
  align-self: flex-end;
}

.chat-input {
  display: flex;
  gap: var(--space-2);
  padding: var(--space-4);
  border-top: 1px solid var(--border-primary);
}

.chat-input input {
  flex: 1;
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-family: inherit;
}

.chat-input input:focus {
  outline: none;
  border-color: var(--border-focus);
}

/* ===== MODALS ===== */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--bg-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  padding: var(--space-4);
}

.modal-content {
  background-color: var(--bg-card);
  border-radius: var(--radius-xl);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-xl);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-primary);
}

.modal-header h3 {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-secondary);
  padding: var(--space-2);
  border-radius: var(--radius-md);
  transition: all var(--transition);
}

.modal-close:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.modal-body {
  padding: var(--space-6);
}

.modal-body textarea {
  width: 100%;
  padding: var(--space-3);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-family: inherit;
  resize: vertical;
  min-height: 100px;
}

.modal-body textarea:focus {
  outline: none;
  border-color: var(--border-focus);
}

.text-options {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  margin-top: var(--space-4);
}

.text-options label {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: 0.875rem;
  color: var(--text-primary);
}

.text-options input[type="range"] {
  flex: 1;
  max-width: 150px;
}

.modal-actions {
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
  padding: var(--space-6);
  border-top: 1px solid var(--border-primary);
}

/* Connection Modal */
.connection-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.connection-step {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  border-radius: var(--radius-md);
  background-color: var(--bg-secondary);
}

.step-icon {
  font-size: 1.5rem;
}

.step-text {
  flex: 1;
  color: var(--text-primary);
}

.step-status {
  font-size: 1.2rem;
}

/* ===== LOADING OVERLAY ===== */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--bg-overlay);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: var(--z-loading);
  color: var(--text-inverse);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-overlay p {
  font-size: 1.125rem;
  font-weight: 500;
}

/* ===== FOOTER ===== */
.footer {
  padding: var(--space-8) 0;
  background-color: var(--bg-tertiary);
  text-align: center;
  border-top: 1px solid var(--border-primary);
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.footer p {
  color: var(--text-secondary);
  margin-bottom: var(--space-2);
}

.footer a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition);
}

.footer a:hover {
  color: var(--color-primary-hover);
  text-decoration: underline;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .users-panel,
  .chat-panel {
    width: 100%;
    right: 0;
  }

  .chat-panel {
    right: 0;
  }

  .panel-toggle {
    position: fixed;
    right: var(--space-4);
    left: auto;
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    z-index: calc(var(--z-panel) + 1);
  }

  .modal-content {
    margin: var(--space-4);
    width: calc(100% - 2rem);
  }

  .text-options {
    flex-direction: column;
  }

  .text-options label {
    flex-direction: column;
    align-items: flex-start;
  }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

.btn:focus,
.tool-btn:focus,
.panel-toggle:focus,
.modal-close:focus,
input:focus,
textarea:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* ===== UTILITY CLASSES ===== */
.hidden { display: none !important; }
.visible { display: block !important; }

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
