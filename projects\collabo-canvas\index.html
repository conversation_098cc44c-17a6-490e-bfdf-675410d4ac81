<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="CollaboCanvas - Real-time collaborative whiteboard with WebRTC and drawing tools">
  <meta name="keywords" content="whiteboard, collaboration, WebRTC, drawing, real-time, canvas">
  <title>CollaboCanvas - Collaborative Whiteboard</title>
  <link rel="icon" type="image/svg+xml" href="../../assets/favicon.svg">
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <header class="header">
    <nav class="nav">
      <div class="nav-container">
        <a href="../../" class="logo">
          <span class="logo-icon">🎨</span>
          <span class="logo-text">CollaboCanvas</span>
        </a>
        <div class="nav-controls">
          <div class="connection-status" id="connectionStatus">
            <span class="status-indicator offline"></span>
            <span class="status-text">Offline</span>
          </div>
          <button class="theme-toggle" aria-label="Toggle theme">
            <span class="theme-icon">🌙</span>
          </button>
          <a href="../../" class="back-link" aria-label="Back to portfolio">
            <span class="back-icon">←</span>
          </a>
        </div>
      </div>
    </nav>
  </header>

  <main class="main">
    <!-- Room Management -->
    <section class="room-section" id="roomSection">
      <div class="room-container">
        <div class="room-card">
          <h1>Join Collaborative Whiteboard</h1>
          <p>Create a new room or join an existing one to start collaborating</p>
          
          <div class="room-actions">
            <div class="room-input-group">
              <input type="text" id="roomIdInput" placeholder="Enter room ID" maxlength="20">
              <button class="btn btn-primary" id="joinRoomBtn">Join Room</button>
            </div>
            
            <div class="divider">
              <span>or</span>
            </div>
            
            <button class="btn btn-secondary" id="createRoomBtn">Create New Room</button>
          </div>
          
          <div class="room-info">
            <h3>Features:</h3>
            <ul>
              <li>🎨 Real-time collaborative drawing</li>
              <li>🔧 Multiple drawing tools and colors</li>
              <li>👥 See other users' cursors in real-time</li>
              <li>💾 Auto-save and export capabilities</li>
              <li>📱 Works on desktop and mobile devices</li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <!-- Whiteboard Interface -->
    <section class="whiteboard-section" id="whiteboardSection" style="display: none;">
      <!-- Toolbar -->
      <div class="toolbar">
        <div class="toolbar-group">
          <button class="tool-btn active" data-tool="pen" title="Pen Tool">
            <span class="tool-icon">✏️</span>
          </button>
          <button class="tool-btn" data-tool="eraser" title="Eraser">
            <span class="tool-icon">🧹</span>
          </button>
          <button class="tool-btn" data-tool="line" title="Line Tool">
            <span class="tool-icon">📏</span>
          </button>
          <button class="tool-btn" data-tool="rectangle" title="Rectangle">
            <span class="tool-icon">⬜</span>
          </button>
          <button class="tool-btn" data-tool="circle" title="Circle">
            <span class="tool-icon">⭕</span>
          </button>
          <button class="tool-btn" data-tool="text" title="Text Tool">
            <span class="tool-icon">📝</span>
          </button>
        </div>

        <div class="toolbar-group">
          <div class="color-picker">
            <input type="color" id="colorPicker" value="#000000" title="Choose Color">
            <div class="color-presets">
              <button class="color-preset" data-color="#000000" style="background: #000000"></button>
              <button class="color-preset" data-color="#ff0000" style="background: #ff0000"></button>
              <button class="color-preset" data-color="#00ff00" style="background: #00ff00"></button>
              <button class="color-preset" data-color="#0000ff" style="background: #0000ff"></button>
              <button class="color-preset" data-color="#ffff00" style="background: #ffff00"></button>
              <button class="color-preset" data-color="#ff00ff" style="background: #ff00ff"></button>
              <button class="color-preset" data-color="#00ffff" style="background: #00ffff"></button>
              <button class="color-preset" data-color="#ffffff" style="background: #ffffff; border: 1px solid #ccc"></button>
            </div>
          </div>
        </div>

        <div class="toolbar-group">
          <div class="brush-size">
            <label for="brushSize">Size:</label>
            <input type="range" id="brushSize" min="1" max="50" value="5">
            <span id="brushSizeValue">5</span>
          </div>
        </div>

        <div class="toolbar-group">
          <button class="tool-btn" id="undoBtn" title="Undo">
            <span class="tool-icon">↶</span>
          </button>
          <button class="tool-btn" id="redoBtn" title="Redo">
            <span class="tool-icon">↷</span>
          </button>
          <button class="tool-btn" id="clearBtn" title="Clear Canvas">
            <span class="tool-icon">🗑️</span>
          </button>
        </div>

        <div class="toolbar-group">
          <button class="tool-btn" id="saveBtn" title="Save Canvas">
            <span class="tool-icon">💾</span>
          </button>
          <button class="tool-btn" id="exportBtn" title="Export as Image">
            <span class="tool-icon">📤</span>
          </button>
        </div>

        <div class="toolbar-group">
          <div class="room-info-toolbar">
            <span class="room-id-display">Room: <span id="currentRoomId">-</span></span>
            <span class="users-count">Users: <span id="usersCount">1</span></span>
          </div>
          <button class="tool-btn" id="leaveRoomBtn" title="Leave Room">
            <span class="tool-icon">🚪</span>
          </button>
        </div>
      </div>

      <!-- Canvas Container -->
      <div class="canvas-container">
        <canvas id="whiteboard" width="1200" height="800"></canvas>
        
        <!-- User Cursors -->
        <div class="cursors-layer" id="cursorsLayer"></div>
        
        <!-- Canvas Overlay for UI -->
        <div class="canvas-overlay">
          <div class="zoom-controls">
            <button class="zoom-btn" id="zoomInBtn">+</button>
            <span class="zoom-level" id="zoomLevel">100%</span>
            <button class="zoom-btn" id="zoomOutBtn">-</button>
            <button class="zoom-btn" id="resetZoomBtn">Reset</button>
          </div>
        </div>
      </div>
    </section>

    <!-- Users Panel -->
    <aside class="users-panel" id="usersPanel">
      <div class="users-header">
        <h3>Connected Users</h3>
        <button class="panel-toggle" id="usersPanelToggle">👥</button>
      </div>
      <div class="users-list" id="usersList">
        <!-- Users will be dynamically added here -->
      </div>
    </aside>

    <!-- Chat Panel -->
    <aside class="chat-panel" id="chatPanel">
      <div class="chat-header">
        <h3>Chat</h3>
        <button class="panel-toggle" id="chatPanelToggle">💬</button>
      </div>
      <div class="chat-messages" id="chatMessages">
        <!-- Messages will be dynamically added here -->
      </div>
      <div class="chat-input">
        <input type="text" id="chatInput" placeholder="Type a message..." maxlength="500">
        <button class="btn btn-primary" id="sendMessageBtn">Send</button>
      </div>
    </aside>
  </main>

  <!-- Modals -->
  <div class="modal" id="textModal" style="display: none;">
    <div class="modal-content">
      <div class="modal-header">
        <h3>Add Text</h3>
        <button class="modal-close" id="closeTextModal">&times;</button>
      </div>
      <div class="modal-body">
        <textarea id="textInput" placeholder="Enter your text..." rows="4"></textarea>
        <div class="text-options">
          <label>
            Font Size:
            <input type="range" id="fontSize" min="12" max="72" value="24">
            <span id="fontSizeValue">24px</span>
          </label>
          <label>
            <input type="checkbox" id="boldText"> Bold
          </label>
          <label>
            <input type="checkbox" id="italicText"> Italic
          </label>
        </div>
      </div>
      <div class="modal-actions">
        <button class="btn btn-secondary" id="cancelText">Cancel</button>
        <button class="btn btn-primary" id="addText">Add Text</button>
      </div>
    </div>
  </div>

  <!-- Connection Status Modal -->
  <div class="modal" id="connectionModal" style="display: none;">
    <div class="modal-content">
      <div class="modal-header">
        <h3>Connection Status</h3>
      </div>
      <div class="modal-body">
        <div class="connection-info">
          <div class="connection-step">
            <span class="step-icon">🔗</span>
            <span class="step-text">Connecting to signaling server...</span>
            <span class="step-status" id="signalingStatus">⏳</span>
          </div>
          <div class="connection-step">
            <span class="step-icon">🤝</span>
            <span class="step-text">Establishing peer connections...</span>
            <span class="step-status" id="peerStatus">⏳</span>
          </div>
          <div class="connection-step">
            <span class="step-icon">✅</span>
            <span class="step-text">Ready for collaboration!</span>
            <span class="step-status" id="readyStatus">⏳</span>
          </div>
        </div>
      </div>
      <div class="modal-actions">
        <button class="btn btn-primary" id="closeConnectionModal">Continue</button>
      </div>
    </div>
  </div>

  <!-- Loading Overlay -->
  <div class="loading-overlay" id="loadingOverlay" style="display: none;">
    <div class="loading-spinner"></div>
    <p id="loadingText">Connecting...</p>
  </div>

  <footer class="footer">
    <div class="footer-container">
      <p>&copy; 2025 CollaboCanvas. Part of Portfolio Eight.</p>
      <p>
        <a href="../../">← Back to Portfolio</a> |
        <a href="https://github.com/yourusername/portfolio-eight/tree/main/projects/collabo-canvas">View Source</a>
      </p>
    </div>
  </footer>

  <!-- JavaScript Modules -->
  <script src="js/webrtc.js" type="module"></script>
  <script src="js/canvas.js" type="module"></script>
  <script src="js/tools.js" type="module"></script>
  <script src="js/chat.js" type="module"></script>
  <script src="js/users.js" type="module"></script>
  <script src="main.js" type="module"></script>
</body>
</html>
