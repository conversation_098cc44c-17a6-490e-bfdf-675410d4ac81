/**
 * FinTrack Transactions Module
 * Handles transaction management and UI interactions
 */

class TransactionManager {
  constructor() {
    this.currentFilters = {
      category: 'all',
      period: 'all',
      account: 'all'
    };
    this.currentPage = 0;
    this.pageSize = 20;
    this.transactions = [];
    
    this.categories = {
      income: [
        { value: 'salary', label: 'Salary' },
        { value: 'freelance', label: 'Freelance' },
        { value: 'investment', label: 'Investment' },
        { value: 'business', label: 'Business' },
        { value: 'other-income', label: 'Other Income' }
      ],
      expense: [
        { value: 'food', label: 'Food & Dining' },
        { value: 'transport', label: 'Transportation' },
        { value: 'shopping', label: 'Shopping' },
        { value: 'bills', label: 'Bills & Utilities' },
        { value: 'entertainment', label: 'Entertainment' },
        { value: 'health', label: 'Health & Fitness' },
        { value: 'education', label: 'Education' },
        { value: 'travel', label: 'Travel' },
        { value: 'other', label: 'Other' }
      ],
      transfer: [
        { value: 'transfer', label: 'Account Transfer' }
      ]
    };
    
    this.categoryIcons = {
      'salary': '💼',
      'freelance': '💻',
      'investment': '📈',
      'business': '🏢',
      'other-income': '💰',
      'food': '🍽️',
      'transport': '🚗',
      'shopping': '🛍️',
      'bills': '📄',
      'entertainment': '🎬',
      'health': '🏥',
      'education': '📚',
      'travel': '✈️',
      'transfer': '🔄',
      'other': '📦'
    };
  }

  async init() {
    this.setupEventListeners();
    await this.loadTransactions();
    this.renderTransactions();
  }

  setupEventListeners() {
    // Filter controls
    document.getElementById('filterCategory')?.addEventListener('change', (e) => {
      this.currentFilters.category = e.target.value;
      this.applyFilters();
    });

    document.getElementById('filterPeriod')?.addEventListener('change', (e) => {
      this.currentFilters.period = e.target.value;
      this.applyFilters();
    });

    // Load more button
    document.getElementById('loadMoreBtn')?.addEventListener('click', () => {
      this.loadMoreTransactions();
    });

    // Transaction type change
    document.getElementById('transactionType')?.addEventListener('change', (e) => {
      this.updateCategoryOptions(e.target.value);
    });

    // Transaction form
    document.getElementById('transactionForm')?.addEventListener('submit', (e) => {
      this.handleTransactionSubmit(e);
    });

    // Cancel transaction
    document.getElementById('cancelTransaction')?.addEventListener('click', () => {
      this.hideTransactionModal();
    });

    // Modal close
    document.getElementById('closeModal')?.addEventListener('click', () => {
      this.hideTransactionModal();
    });
  }

  async loadTransactions() {
    try {
      this.transactions = await window.finTrackDB.getTransactions(this.getFilterParams());
      console.log('Transactions loaded:', this.transactions.length);
    } catch (error) {
      console.error('Failed to load transactions:', error);
      this.showNotification('Failed to load transactions', 'error');
    }
  }

  getFilterParams() {
    const params = {};
    
    if (this.currentFilters.category !== 'all') {
      params.category = this.currentFilters.category;
    }
    
    if (this.currentFilters.period !== 'all') {
      const now = new Date();
      switch (this.currentFilters.period) {
        case 'today':
          params.dateFrom = new Date(now.getFullYear(), now.getMonth(), now.getDate()).toISOString();
          break;
        case 'week':
          params.dateFrom = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();
          break;
        case 'month':
          params.dateFrom = new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
          break;
        case 'year':
          params.dateFrom = new Date(now.getFullYear(), 0, 1).toISOString();
          break;
      }
    }
    
    return params;
  }

  async applyFilters() {
    this.currentPage = 0;
    await this.loadTransactions();
    this.renderTransactions();
  }

  renderTransactions() {
    const container = document.getElementById('transactionsList');
    if (!container) return;

    const startIndex = 0;
    const endIndex = (this.currentPage + 1) * this.pageSize;
    const visibleTransactions = this.transactions.slice(startIndex, endIndex);

    if (visibleTransactions.length === 0) {
      container.innerHTML = `
        <div class="empty-state">
          <div class="empty-icon">📊</div>
          <h3>No transactions found</h3>
          <p>Start by adding your first transaction using the quick actions above.</p>
        </div>
      `;
      return;
    }

    container.innerHTML = visibleTransactions.map(transaction => 
      this.createTransactionHTML(transaction)
    ).join('');

    // Update load more button
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    if (loadMoreBtn) {
      loadMoreBtn.style.display = endIndex < this.transactions.length ? 'block' : 'none';
    }

    // Add click listeners to transaction items
    container.querySelectorAll('.transaction-item').forEach(item => {
      item.addEventListener('click', () => {
        const transactionId = parseInt(item.dataset.transactionId);
        this.showTransactionDetails(transactionId);
      });
    });
  }

  createTransactionHTML(transaction) {
    const icon = this.categoryIcons[transaction.category] || '📦';
    const date = new Date(transaction.date).toLocaleDateString();
    const amount = parseFloat(transaction.amount);
    const formattedAmount = transaction.type === 'expense' ? `-$${amount.toFixed(2)}` : `+$${amount.toFixed(2)}`;

    return `
      <div class="transaction-item" data-transaction-id="${transaction.id}">
        <div class="transaction-icon ${transaction.type}">
          ${icon}
        </div>
        <div class="transaction-details">
          <div class="transaction-description">${transaction.description}</div>
          <div class="transaction-meta">
            <span class="transaction-category">${this.formatCategoryName(transaction.category)}</span>
            <span class="transaction-date">${date}</span>
            <span class="transaction-account">${transaction.account}</span>
          </div>
        </div>
        <div class="transaction-amount ${transaction.type}">
          ${formattedAmount}
        </div>
      </div>
    `;
  }

  loadMoreTransactions() {
    this.currentPage++;
    this.renderTransactions();
  }

  showTransactionModal(type = null, transactionData = null) {
    const modal = document.getElementById('transactionModal');
    const form = document.getElementById('transactionForm');
    const title = document.getElementById('modalTitle');
    
    if (!modal || !form) return;

    // Reset form
    form.reset();
    
    // Set today's date as default
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('transactionDate').value = today;

    if (transactionData) {
      // Edit mode
      title.textContent = 'Edit Transaction';
      this.populateTransactionForm(transactionData);
    } else {
      // Add mode
      title.textContent = 'Add Transaction';
      if (type) {
        document.getElementById('transactionType').value = type;
        this.updateCategoryOptions(type);
      }
    }

    modal.style.display = 'flex';
    document.getElementById('transactionType').focus();
  }

  hideTransactionModal() {
    const modal = document.getElementById('transactionModal');
    if (modal) {
      modal.style.display = 'none';
    }
  }

  populateTransactionForm(transaction) {
    document.getElementById('transactionType').value = transaction.type;
    document.getElementById('transactionAmount').value = transaction.amount;
    document.getElementById('transactionDescription').value = transaction.description;
    document.getElementById('transactionDate').value = transaction.date;
    document.getElementById('transactionAccount').value = transaction.account;
    document.getElementById('transactionNotes').value = transaction.notes || '';
    
    this.updateCategoryOptions(transaction.type);
    document.getElementById('transactionCategory').value = transaction.category;
  }

  updateCategoryOptions(type) {
    const categorySelect = document.getElementById('transactionCategory');
    if (!categorySelect || !type) return;

    const categories = this.categories[type] || [];
    
    categorySelect.innerHTML = '<option value="">Select category</option>' +
      categories.map(cat => `<option value="${cat.value}">${cat.label}</option>`).join('');
  }

  async handleTransactionSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const transactionData = {
      type: document.getElementById('transactionType').value,
      amount: parseFloat(document.getElementById('transactionAmount').value),
      category: document.getElementById('transactionCategory').value,
      description: document.getElementById('transactionDescription').value,
      date: document.getElementById('transactionDate').value,
      account: document.getElementById('transactionAccount').value,
      notes: document.getElementById('transactionNotes').value
    };

    try {
      await window.finTrackDB.addTransaction(transactionData);
      
      this.hideTransactionModal();
      await this.loadTransactions();
      this.renderTransactions();
      
      // Update dashboard
      if (window.finTrackApp && window.finTrackApp.updateDashboard) {
        await window.finTrackApp.updateDashboard();
      }
      
      // Update charts (debounced)
      if (window.finTrackApp && window.finTrackApp.debouncedUpdateCharts) {
        window.finTrackApp.debouncedUpdateCharts();
      }
      
      this.showNotification('Transaction added successfully!', 'success');
      
      // Register background sync if supported
      if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
        navigator.serviceWorker.ready.then(registration => {
          return registration.sync.register('sync-transactions');
        });
      }
      
    } catch (error) {
      console.error('Failed to add transaction:', error);
      this.showNotification('Failed to add transaction', 'error');
    }
  }

  async showTransactionDetails(transactionId) {
    const transaction = this.transactions.find(t => t.id === transactionId);
    if (!transaction) return;

    // For now, just show edit modal
    this.showTransactionModal(null, transaction);
  }

  async deleteTransaction(transactionId) {
    if (!confirm('Are you sure you want to delete this transaction?')) {
      return;
    }

    try {
      await window.finTrackDB.deleteTransaction(transactionId);
      
      await this.loadTransactions();
      this.renderTransactions();
      
      // Update dashboard
      if (window.finTrackApp && window.finTrackApp.updateDashboard) {
        await window.finTrackApp.updateDashboard();
      }
      
      this.showNotification('Transaction deleted successfully!', 'success');
      
    } catch (error) {
      console.error('Failed to delete transaction:', error);
      this.showNotification('Failed to delete transaction', 'error');
    }
  }

  // Utility methods
  formatCategoryName(category) {
    return category.charAt(0).toUpperCase() + category.slice(1).replace('-', ' ');
  }

  showNotification(message, type = 'info') {
    // Simple notification system
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 24px;
      border-radius: 8px;
      color: white;
      font-weight: 500;
      z-index: 4000;
      animation: slideIn 0.3s ease-out;
      background-color: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
      notification.style.animation = 'slideOut 0.3s ease-in';
      setTimeout(() => notification.remove(), 300);
    }, 3000);
  }

  // Quick action methods
  showAddIncomeModal() {
    this.showTransactionModal('income');
  }

  showAddExpenseModal() {
    this.showTransactionModal('expense');
  }

  showAddTransferModal() {
    this.showTransactionModal('transfer');
  }
}

// Create and export singleton instance
const transactionManager = new TransactionManager();

export default transactionManager;
