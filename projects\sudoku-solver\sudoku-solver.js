/**
 * Sudoku Solver & Generator
 * Advanced algorithms for solving and generating Sudoku puzzles
 */

class SudokuSolver {
  constructor() {
    this.grid = Array(9).fill().map(() => Array(9).fill(0));
    this.solution = Array(9).fill().map(() => Array(9).fill(0));
    this.originalGrid = Array(9).fill().map(() => Array(9).fill(0));
  }

  // Check if a number is valid in a specific position
  isValid(grid, row, col, num) {
    // Check row
    for (let x = 0; x < 9; x++) {
      if (grid[row][x] === num) return false;
    }

    // Check column
    for (let x = 0; x < 9; x++) {
      if (grid[x][col] === num) return false;
    }

    // Check 3x3 box
    const startRow = row - (row % 3);
    const startCol = col - (col % 3);
    for (let i = 0; i < 3; i++) {
      for (let j = 0; j < 3; j++) {
        if (grid[i + startRow][j + startCol] === num) return false;
      }
    }

    return true;
  }

  // Get all possible numbers for a cell
  getPossibleNumbers(grid, row, col) {
    if (grid[row][col] !== 0) return [];
    
    const possible = [];
    for (let num = 1; num <= 9; num++) {
      if (this.isValid(grid, row, col, num)) {
        possible.push(num);
      }
    }
    return possible;
  }

  // Solve using backtracking algorithm
  solve(grid = this.grid) {
    const emptyCell = this.findEmptyCell(grid);
    if (!emptyCell) return true; // Puzzle solved

    const [row, col] = emptyCell;
    
    for (let num = 1; num <= 9; num++) {
      if (this.isValid(grid, row, col, num)) {
        grid[row][col] = num;
        
        if (this.solve(grid)) return true;
        
        grid[row][col] = 0; // Backtrack
      }
    }
    
    return false;
  }

  // Find the next empty cell (0)
  findEmptyCell(grid) {
    for (let row = 0; row < 9; row++) {
      for (let col = 0; col < 9; col++) {
        if (grid[row][col] === 0) {
          return [row, col];
        }
      }
    }
    return null;
  }

  // Find empty cell with minimum possibilities (MRV heuristic)
  findBestEmptyCell(grid) {
    let bestCell = null;
    let minPossibilities = 10;

    for (let row = 0; row < 9; row++) {
      for (let col = 0; col < 9; col++) {
        if (grid[row][col] === 0) {
          const possibilities = this.getPossibleNumbers(grid, row, col);
          if (possibilities.length < minPossibilities) {
            minPossibilities = possibilities.length;
            bestCell = [row, col];
            if (minPossibilities === 1) break; // Can't get better than 1
          }
        }
      }
      if (minPossibilities === 1) break;
    }

    return bestCell;
  }

  // Advanced solve with MRV heuristic
  solveAdvanced(grid = this.grid) {
    const emptyCell = this.findBestEmptyCell(grid);
    if (!emptyCell) return true; // Puzzle solved

    const [row, col] = emptyCell;
    const possibilities = this.getPossibleNumbers(grid, row, col);
    
    for (const num of possibilities) {
      grid[row][col] = num;
      
      if (this.solveAdvanced(grid)) return true;
      
      grid[row][col] = 0; // Backtrack
    }
    
    return false;
  }

  // Generate a complete valid Sudoku grid
  generateComplete() {
    this.grid = Array(9).fill().map(() => Array(9).fill(0));

    // Fill diagonal boxes first (they don't affect each other)
    this.fillDiagonalBoxes();

    // Fill remaining cells
    this.fillRemaining(0, 3);

    return this.copyGrid(this.grid);
  }

  // Fill the three diagonal 3x3 boxes
  fillDiagonalBoxes() {
    for (let i = 0; i < 9; i += 3) {
      this.fillBox(i, i);
    }
  }

  // Fill a 3x3 box
  fillBox(row, col) {
    const numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9];
    this.shuffleArray(numbers);

    let index = 0;
    for (let i = 0; i < 3; i++) {
      for (let j = 0; j < 3; j++) {
        this.grid[row + i][col + j] = numbers[index++];
      }
    }
  }

  // Fill remaining cells recursively
  fillRemaining(i, j) {
    if (j >= 9 && i < 8) {
      i++;
      j = 0;
    }
    if (i >= 9 && j >= 9) return true;

    if (i < 3) {
      if (j < 3) j = 3;
    } else if (i < 6) {
      if (j === Math.floor(i / 3) * 3) j += 3;
    } else {
      if (j === 6) {
        i++;
        j = 0;
        if (i >= 9) return true;
      }
    }

    for (let num = 1; num <= 9; num++) {
      if (this.isValid(this.grid, i, j, num)) {
        this.grid[i][j] = num;
        if (this.fillRemaining(i, j + 1)) return true;
        this.grid[i][j] = 0;
      }
    }
    return false;
  }

  // Generate puzzle by removing numbers from complete grid
  generatePuzzle(difficulty = 'medium') {
    const completeGrid = this.generateComplete();
    const puzzle = this.copyGrid(completeGrid);
    
    const difficultySettings = {
      easy: { minClues: 36, maxClues: 40 },
      medium: { minClues: 30, maxClues: 35 },
      hard: { minClues: 25, maxClues: 29 },
      expert: { minClues: 20, maxClues: 24 }
    };
    
    const { minClues, maxClues } = difficultySettings[difficulty];
    const targetClues = minClues + Math.floor(Math.random() * (maxClues - minClues + 1));
    const cellsToRemove = 81 - targetClues;
    
    // Create list of all cell positions
    const positions = [];
    for (let row = 0; row < 9; row++) {
      for (let col = 0; col < 9; col++) {
        positions.push([row, col]);
      }
    }
    
    this.shuffleArray(positions);
    
    let removed = 0;
    for (const [row, col] of positions) {
      if (removed >= cellsToRemove) break;
      
      const backup = puzzle[row][col];
      puzzle[row][col] = 0;
      
      // Check if puzzle still has unique solution
      if (this.hasUniqueSolution(puzzle)) {
        removed++;
      } else {
        puzzle[row][col] = backup; // Restore if multiple solutions
      }
    }
    
    this.grid = this.copyGrid(puzzle);
    this.solution = this.copyGrid(completeGrid);
    this.originalGrid = this.copyGrid(puzzle);
    
    return {
      puzzle: this.copyGrid(puzzle),
      solution: this.copyGrid(completeGrid)
    };
  }

  // Check if puzzle has unique solution
  hasUniqueSolution(grid) {
    const testGrid = this.copyGrid(grid);
    const solutions = [];
    this.findAllSolutions(testGrid, solutions, 2); // Stop after finding 2 solutions
    return solutions.length === 1;
  }

  // Find all solutions (up to maxSolutions)
  findAllSolutions(grid, solutions, maxSolutions) {
    if (solutions.length >= maxSolutions) return;
    
    const emptyCell = this.findEmptyCell(grid);
    if (!emptyCell) {
      solutions.push(this.copyGrid(grid));
      return;
    }

    const [row, col] = emptyCell;
    
    for (let num = 1; num <= 9; num++) {
      if (this.isValid(grid, row, col, num)) {
        grid[row][col] = num;
        this.findAllSolutions(grid, solutions, maxSolutions);
        grid[row][col] = 0;
        
        if (solutions.length >= maxSolutions) return;
      }
    }
  }

  // Validate entire grid
  isGridValid(grid = this.grid) {
    for (let row = 0; row < 9; row++) {
      for (let col = 0; col < 9; col++) {
        if (grid[row][col] !== 0) {
          const num = grid[row][col];
          grid[row][col] = 0; // Temporarily remove to test
          if (!this.isValid(grid, row, col, num)) {
            grid[row][col] = num; // Restore
            return false;
          }
          grid[row][col] = num; // Restore
        }
      }
    }
    return true;
  }

  // Check if grid is complete
  isComplete(grid = this.grid) {
    for (let row = 0; row < 9; row++) {
      for (let col = 0; col < 9; col++) {
        if (grid[row][col] === 0) return false;
      }
    }
    return this.isGridValid(grid);
  }

  // Get hint for next move
  getHint(grid = this.grid) {
    // Find cell with only one possibility
    for (let row = 0; row < 9; row++) {
      for (let col = 0; col < 9; col++) {
        if (grid[row][col] === 0) {
          const possibilities = this.getPossibleNumbers(grid, row, col);
          if (possibilities.length === 1) {
            return {
              row,
              col,
              number: possibilities[0],
              technique: 'Naked Single'
            };
          }
        }
      }
    }

    // Find hidden singles
    const hint = this.findHiddenSingle(grid);
    if (hint) return hint;

    // If no obvious moves, return any valid move
    for (let row = 0; row < 9; row++) {
      for (let col = 0; col < 9; col++) {
        if (grid[row][col] === 0) {
          const possibilities = this.getPossibleNumbers(grid, row, col);
          if (possibilities.length > 0) {
            return {
              row,
              col,
              number: possibilities[0],
              technique: 'Trial and Error'
            };
          }
        }
      }
    }

    return null;
  }

  // Find hidden singles
  findHiddenSingle(grid) {
    // Check rows
    for (let row = 0; row < 9; row++) {
      for (let num = 1; num <= 9; num++) {
        const possibleCols = [];
        for (let col = 0; col < 9; col++) {
          if (grid[row][col] === 0 && this.isValid(grid, row, col, num)) {
            possibleCols.push(col);
          }
        }
        if (possibleCols.length === 1) {
          return {
            row,
            col: possibleCols[0],
            number: num,
            technique: 'Hidden Single (Row)'
          };
        }
      }
    }

    // Check columns
    for (let col = 0; col < 9; col++) {
      for (let num = 1; num <= 9; num++) {
        const possibleRows = [];
        for (let row = 0; row < 9; row++) {
          if (grid[row][col] === 0 && this.isValid(grid, row, col, num)) {
            possibleRows.push(row);
          }
        }
        if (possibleRows.length === 1) {
          return {
            row: possibleRows[0],
            col,
            number: num,
            technique: 'Hidden Single (Column)'
          };
        }
      }
    }

    // Check boxes
    for (let boxRow = 0; boxRow < 3; boxRow++) {
      for (let boxCol = 0; boxCol < 3; boxCol++) {
        for (let num = 1; num <= 9; num++) {
          const possibleCells = [];
          for (let row = boxRow * 3; row < boxRow * 3 + 3; row++) {
            for (let col = boxCol * 3; col < boxCol * 3 + 3; col++) {
              if (grid[row][col] === 0 && this.isValid(grid, row, col, num)) {
                possibleCells.push([row, col]);
              }
            }
          }
          if (possibleCells.length === 1) {
            return {
              row: possibleCells[0][0],
              col: possibleCells[0][1],
              number: num,
              technique: 'Hidden Single (Box)'
            };
          }
        }
      }
    }

    return null;
  }

  // Utility functions
  copyGrid(grid) {
    return grid.map(row => [...row]);
  }

  shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]];
    }
  }

  // Load grid from array
  loadGrid(gridArray) {
    this.grid = this.copyGrid(gridArray);
    this.originalGrid = this.copyGrid(gridArray);
  }

  // Get current grid
  getGrid() {
    return this.copyGrid(this.grid);
  }

  // Get solution
  getSolution() {
    return this.copyGrid(this.solution);
  }

  // Check if cell is original (given)
  isOriginalCell(row, col) {
    return this.originalGrid[row][col] !== 0;
  }

  // Set cell value
  setCell(row, col, value) {
    if (!this.isOriginalCell(row, col)) {
      this.grid[row][col] = value;
      return true;
    }
    return false;
  }

  // Get cell value
  getCell(row, col) {
    return this.grid[row][col];
  }

  // Clear cell
  clearCell(row, col) {
    if (!this.isOriginalCell(row, col)) {
      this.grid[row][col] = 0;
      return true;
    }
    return false;
  }

  // Get difficulty rating
  getDifficultyRating(grid) {
    const clues = grid.flat().filter(cell => cell !== 0).length;
    
    if (clues >= 36) return 'easy';
    if (clues >= 30) return 'medium';
    if (clues >= 25) return 'hard';
    return 'expert';
  }
}

// Export for use in main.js
window.SudokuSolver = SudokuSolver;
