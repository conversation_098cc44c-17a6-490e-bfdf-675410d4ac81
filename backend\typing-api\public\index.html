<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Typing Analytics - Interactive Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
            color: #f8fafc;
            min-height: 100vh;
            padding: 20px;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(16px);
            border-radius: 16px;
            padding: 40px;
            box-shadow: 
                0 4px 6px -1px rgba(0, 0, 0, 0.1),
                0 2px 4px -1px rgba(0, 0, 0, 0.06),
                0 0 0 1px rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 12px;
            color: #f8fafc;
            letter-spacing: -0.025em;
        }

        .header p {
            font-size: 1.125rem;
            color: #cbd5e1;
            font-weight: 400;
            line-height: 1.7;
        }

        .test-setup {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .setup-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .control-group label {
            font-size: 0.875rem;
            font-weight: 600;
            color: #f8fafc;
            letter-spacing: -0.025em;
        }

        .control-group select, .control-group input {
            padding: 12px 16px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.1);
            color: #f8fafc;
            font-size: 0.875rem;
            transition: all 0.2s ease-in-out;
        }

        .control-group select:focus, .control-group input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .control-group select option {
            background: #334155;
            color: #f8fafc;
        }

        .typing-area {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            display: none;
        }

        .typing-area.active {
            display: block;
        }

        .text-display {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 20px;
            font-family: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', Consolas, 'Courier New', monospace;
            font-size: 1.125rem;
            line-height: 1.8;
            border: 2px solid rgba(255, 255, 255, 0.1);
            min-height: 120px;
            max-height: 200px;
            overflow-y: auto;
            word-wrap: break-word;
            white-space: pre-wrap;
        }

        .text-char {
            position: relative;
            padding: 1px 2px;
            border-radius: 2px;
            transition: all 0.1s ease-in-out;
        }

        .text-char.correct {
            background-color: rgba(34, 197, 94, 0.4);
            color: #22c55e;
            font-weight: 500;
        }

        .text-char.incorrect {
            background-color: rgba(239, 68, 68, 0.4);
            color: #ef4444;
            font-weight: 500;
            text-decoration: underline;
        }

        .text-char.current {
            background-color: rgba(59, 130, 246, 0.5);
            color: #3b82f6;
            font-weight: 600;
            animation: blink 1s infinite;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        .typing-input {
            width: 100%;
            padding: 16px;
            border-radius: 8px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.1);
            color: #f8fafc;
            font-family: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', Consolas, 'Courier New', monospace;
            font-size: 1rem;
            resize: none;
            outline: none;
            transition: all 0.2s ease-in-out;
            min-height: 100px;
            max-height: 150px;
            overflow-y: auto;
            word-wrap: break-word;
            white-space: pre-wrap;
        }

        .typing-input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .typing-input:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .stats-display {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 8px;
            padding: 16px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #3b82f6;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 0.875rem;
            color: #cbd5e1;
            font-weight: 500;
        }

        .results-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 30px;
            margin-top: 30px;
            display: none;
        }

        .results-section.active {
            display: block;
        }

        .results-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .results-header h2 {
            font-size: 1.875rem;
            font-weight: 600;
            color: #f8fafc;
            margin-bottom: 8px;
        }

        .final-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .final-stat-card {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 12px;
            padding: 24px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .final-stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .final-stat-value.wpm {
            color: #3b82f6;
        }

        .final-stat-value.accuracy {
            color: #10b981;
        }

        .final-stat-value.time {
            color: #f59e0b;
        }

        .final-stat-value.errors {
            color: #ef4444;
        }

        .final-stat-label {
            font-size: 1rem;
            color: #cbd5e1;
            font-weight: 500;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.875rem;
            transition: all 0.2s ease-in-out;
            margin: 8px;
            border: 1px solid transparent;
            cursor: pointer;
            letter-spacing: -0.025em;
        }

        .btn-primary {
            background: #3b82f6;
            color: #ffffff;
            border-color: #3b82f6;
        }

        .btn-primary:hover:not(:disabled) {
            background: #2563eb;
            border-color: #2563eb;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.08);
            color: #f8fafc;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-secondary:hover:not(:disabled) {
            background: rgba(255, 255, 255, 0.12);
            border-color: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        .error-analysis {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }

        .error-analysis h3 {
            color: #ef4444;
            font-size: 1.125rem;
            margin-bottom: 12px;
        }

        .error-list {
            list-style: none;
            padding: 0;
        }

        .error-item {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 4px;
            padding: 8px 12px;
            margin-bottom: 8px;
            font-family: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', Consolas, 'Courier New', monospace;
            font-size: 0.875rem;
        }

        .leaderboard-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 30px;
            margin-top: 30px;
        }

        .leaderboard-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .leaderboard-table th,
        .leaderboard-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .leaderboard-table th {
            background: rgba(255, 255, 255, 0.08);
            font-weight: 600;
            color: #f8fafc;
        }

        .leaderboard-table td {
            color: #cbd5e1;
        }

        .rank-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .rank-1 { background: #fbbf24; color: #92400e; }
        .rank-2 { background: #9ca3af; color: #374151; }
        .rank-3 { background: #cd7c2f; color: #92400e; }
        .rank-other { background: #3b82f6; color: #ffffff; }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }

            .setup-controls {
                grid-template-columns: 1fr;
            }

            .stats-display,
            .final-stats {
                grid-template-columns: repeat(2, 1fr);
            }

            .header h1 {
                font-size: 2rem;
            }

            .text-display {
                font-size: 1rem;
                padding: 16px;
                max-height: 150px;
            }

            .typing-input {
                font-size: 0.9rem;
                min-height: 80px;
                max-height: 120px;
            }

            .leaderboard-table {
                font-size: 0.875rem;
            }

            .leaderboard-table th,
            .leaderboard-table td {
                padding: 8px 4px;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 15px;
                margin: 5px;
            }

            .text-display {
                font-size: 0.9rem;
                padding: 12px;
                line-height: 1.6;
            }

            .typing-input {
                font-size: 0.85rem;
                padding: 12px;
            }

            .stats-display,
            .final-stats {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .btn {
                padding: 10px 16px;
                font-size: 0.8rem;
                margin: 4px;
            }

            .header h1 {
                font-size: 1.75rem;
            }

            .header p {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚡ Typing Analytics Test</h1>
            <p>Professional typing speed and accuracy analysis</p>
        </div>

        <div class="test-setup" id="testSetup">
            <h2 style="color: #f8fafc; margin-bottom: 20px; font-size: 1.5rem;">Test Configuration</h2>
            
            <div class="setup-controls">
                <div class="control-group">
                    <label for="difficultySelect">Difficulty Level</label>
                    <select id="difficultySelect">
                        <option value="easy">Easy - Simple sentences</option>
                        <option value="medium" selected>Medium - Technical content</option>
                        <option value="hard">Hard - Complex programming</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label for="categorySelect">Content Category</label>
                    <select id="categorySelect">
                        <option value="">All Categories</option>
                        <option value="pangram">Pangrams</option>
                        <option value="programming">Programming</option>
                        <option value="technical">Technical</option>
                        <option value="nature">Nature</option>
                        <option value="technology">Technology</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label for="usernameInput">Your Name</label>
                    <input type="text" id="usernameInput" placeholder="Enter your name" maxlength="20">
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 20px;">
                <button class="btn btn-primary" onclick="startTest()">🚀 Start Typing Test</button>
                <button class="btn btn-secondary" onclick="loadLeaderboard()">🏆 View Leaderboard</button>
            </div>
        </div>

        <div class="typing-area" id="typingArea">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h2 style="color: #f8fafc; font-size: 1.5rem;">Typing Test in Progress</h2>
                <button class="btn btn-secondary" onclick="cancelTest()">Cancel Test</button>
            </div>

            <div class="text-display" id="textDisplay">
                Loading text sample...
            </div>

            <textarea
                class="typing-input"
                id="typingInput"
                placeholder="Start typing here..."
                rows="4"
                disabled
            ></textarea>

            <div class="stats-display">
                <div class="stat-card">
                    <div class="stat-value" id="wpmStat">0</div>
                    <div class="stat-label">WPM</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="accuracyStat">100%</div>
                    <div class="stat-label">Accuracy</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="timeStat">0s</div>
                    <div class="stat-label">Time</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="errorsStat">0</div>
                    <div class="stat-label">Errors</div>
                </div>
            </div>
        </div>

        <div class="results-section" id="resultsSection">
            <div class="results-header">
                <h2>🎉 Test Complete!</h2>
                <p style="color: #cbd5e1;">Your typing performance analysis</p>
            </div>

            <div class="final-stats">
                <div class="final-stat-card">
                    <div class="final-stat-value wpm" id="finalWPM">0</div>
                    <div class="final-stat-label">Words Per Minute</div>
                </div>
                <div class="final-stat-card">
                    <div class="final-stat-value accuracy" id="finalAccuracy">0%</div>
                    <div class="final-stat-label">Accuracy</div>
                </div>
                <div class="final-stat-card">
                    <div class="final-stat-value time" id="finalTime">0s</div>
                    <div class="final-stat-label">Total Time</div>
                </div>
                <div class="final-stat-card">
                    <div class="final-stat-value errors" id="finalErrors">0</div>
                    <div class="final-stat-label">Total Errors</div>
                </div>
            </div>

            <div class="error-analysis" id="errorAnalysis" style="display: none;">
                <h3>📊 Error Analysis</h3>
                <ul class="error-list" id="errorList"></ul>
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <button class="btn btn-primary" onclick="startNewTest()">🔄 Take Another Test</button>
                <button class="btn btn-secondary" onclick="viewPersonalStats()">📈 View My Stats</button>
                <button class="btn btn-secondary" onclick="loadLeaderboard()">🏆 Leaderboard</button>
            </div>
        </div>

        <div class="leaderboard-section" id="leaderboardSection" style="display: none;">
            <h2 style="color: #f8fafc; margin-bottom: 20px; font-size: 1.5rem;">🏆 Leaderboard</h2>

            <div style="margin-bottom: 20px;">
                <select id="leaderboardDifficulty" onchange="loadLeaderboard()">
                    <option value="">All Difficulties</option>
                    <option value="easy">Easy</option>
                    <option value="medium">Medium</option>
                    <option value="hard">Hard</option>
                </select>
            </div>

            <table class="leaderboard-table">
                <thead>
                    <tr>
                        <th>Rank</th>
                        <th>Name</th>
                        <th>WPM</th>
                        <th>Accuracy</th>
                        <th>Difficulty</th>
                        <th>Date</th>
                    </tr>
                </thead>
                <tbody id="leaderboardBody">
                    <tr>
                        <td colspan="6" style="text-align: center; color: #94a3b8;">Loading leaderboard...</td>
                    </tr>
                </tbody>
            </table>

            <div style="text-align: center; margin-top: 20px;">
                <button class="btn btn-secondary" onclick="hideLeaderboard()">Close Leaderboard</button>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3003/api';

        let currentTest = {
            textSample: null,
            startTime: null,
            endTime: null,
            errors: [],
            isActive: false,
            currentPosition: 0
        };

        let statsInterval = null;

        // Initialize the application
        window.addEventListener('load', () => {
            console.log('Typing Analytics Test loaded');
        });

        async function startTest() {
            const difficulty = document.getElementById('difficultySelect').value;
            const category = document.getElementById('categorySelect').value;
            const username = document.getElementById('usernameInput').value.trim();

            if (!username) {
                alert('Please enter your name to start the test');
                return;
            }

            try {
                // Get text samples
                let url = `${API_BASE}/texts?difficulty=${difficulty}`;
                if (category) {
                    url += `&category=${category}`;
                }

                const response = await fetch(url);
                const data = await response.json();

                if (!data.success || data.data.length === 0) {
                    throw new Error('No text samples available');
                }

                // Select random text
                const randomIndex = Math.floor(Math.random() * data.data.length);
                currentTest.textSample = data.data[randomIndex];

                // Setup test UI
                setupTestUI();

            } catch (error) {
                console.error('Error starting test:', error);
                alert('Failed to start test. Make sure the server is running on port 3003.');
            }
        }

        function setupTestUI() {
            // Hide setup, show typing area
            document.getElementById('testSetup').style.display = 'none';
            document.getElementById('typingArea').classList.add('active');
            document.getElementById('resultsSection').classList.remove('active');
            document.getElementById('leaderboardSection').style.display = 'none';

            // Display text with highlighting
            displayTextWithHighlighting();

            // Reset test state
            currentTest.startTime = null;
            currentTest.endTime = null;
            currentTest.errors = [];
            currentTest.isActive = true;
            currentTest.currentPosition = 0;

            // Enable input and focus
            const input = document.getElementById('typingInput');
            input.disabled = false;
            input.value = '';
            input.focus();

            // Add event listeners
            input.addEventListener('input', handleTyping);
            input.addEventListener('paste', (e) => e.preventDefault());

            // Reset stats
            updateStats();
        }

        function displayTextWithHighlighting() {
            const textDisplay = document.getElementById('textDisplay');
            const text = currentTest.textSample.text;

            let html = '';
            for (let i = 0; i < text.length; i++) {
                const char = text[i];
                const className = i === 0 ? 'text-char current' : 'text-char';
                html += `<span class="${className}" data-index="${i}">${char === ' ' ? '&nbsp;' : char}</span>`;
            }

            textDisplay.innerHTML = html;
        }

        function handleTyping(event) {
            const input = event.target;
            const typedText = input.value;
            const originalText = currentTest.textSample.text;

            // Start timer on first keystroke
            if (!currentTest.startTime) {
                currentTest.startTime = Date.now();
                startStatsUpdater();
            }

            // Update highlighting
            updateTextHighlighting(typedText, originalText);

            // Check if test is complete
            if (typedText.length >= originalText.length) {
                completeTest();
            }
        }

        function updateTextHighlighting(typedText, originalText) {
            const textChars = document.querySelectorAll('.text-char');

            // Reset all classes
            textChars.forEach(char => {
                char.className = 'text-char';
            });

            // Apply highlighting based on typed text
            for (let i = 0; i < Math.max(typedText.length, originalText.length); i++) {
                const char = textChars[i];
                if (!char) continue;

                if (i < typedText.length) {
                    if (typedText[i] === originalText[i]) {
                        char.classList.add('correct');
                    } else {
                        char.classList.add('incorrect');
                        // Track error
                        const existingError = currentTest.errors.find(e => e.position === i);
                        if (!existingError) {
                            currentTest.errors.push({
                                position: i,
                                expected: originalText[i],
                                typed: typedText[i]
                            });
                        }
                    }
                } else if (i === typedText.length) {
                    char.classList.add('current');
                }
            }

            currentTest.currentPosition = typedText.length;
        }

        function startStatsUpdater() {
            statsInterval = setInterval(updateStats, 100);
        }

        function updateStats() {
            if (!currentTest.startTime) return;

            const now = Date.now();
            const timeElapsed = (now - currentTest.startTime) / 1000;
            const input = document.getElementById('typingInput');
            const typedText = input.value;
            const originalText = currentTest.textSample.text;

            // Calculate stats
            const correctChars = calculateCorrectCharacters(typedText, originalText);
            const wpm = calculateWPM(correctChars, timeElapsed);
            const accuracy = typedText.length > 0 ? Math.round((correctChars / typedText.length) * 100) : 100;

            // Update display
            document.getElementById('wpmStat').textContent = Math.round(wpm);
            document.getElementById('accuracyStat').textContent = accuracy + '%';
            document.getElementById('timeStat').textContent = Math.round(timeElapsed) + 's';
            document.getElementById('errorsStat').textContent = currentTest.errors.length;
        }

        function calculateCorrectCharacters(typedText, originalText) {
            let correct = 0;
            for (let i = 0; i < typedText.length; i++) {
                if (typedText[i] === originalText[i]) {
                    correct++;
                }
            }
            return correct;
        }

        function calculateWPM(correctChars, timeInSeconds) {
            if (timeInSeconds === 0) return 0;
            const words = correctChars / 5; // Standard: 5 characters = 1 word
            const minutes = timeInSeconds / 60;
            return words / minutes;
        }

        async function completeTest() {
            currentTest.endTime = Date.now();
            currentTest.isActive = false;

            // Stop stats updater
            if (statsInterval) {
                clearInterval(statsInterval);
                statsInterval = null;
            }

            // Disable input
            const input = document.getElementById('typingInput');
            input.disabled = true;

            // Calculate final stats
            const timeInSeconds = (currentTest.endTime - currentTest.startTime) / 1000;
            const typedText = input.value;
            const originalText = currentTest.textSample.text;
            const correctChars = calculateCorrectCharacters(typedText, originalText);
            const wpm = Math.round(calculateWPM(correctChars, timeInSeconds));
            const accuracy = Math.round((correctChars / typedText.length) * 100);

            // Submit results to API
            try {
                const username = document.getElementById('usernameInput').value.trim();
                const submitData = {
                    username,
                    textId: currentTest.textSample.id,
                    typedText,
                    timeInSeconds,
                    correctCharacters: correctChars,
                    totalCharacters: typedText.length,
                    errors: currentTest.errors
                };

                const response = await fetch(`${API_BASE}/submit`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(submitData)
                });

                const result = await response.json();
                if (result.success) {
                    console.log('Test results submitted successfully');
                }
            } catch (error) {
                console.error('Error submitting results:', error);
            }

            // Show results
            showResults(wpm, accuracy, timeInSeconds, currentTest.errors.length);
        }

        function showResults(wpm, accuracy, time, errorCount) {
            // Hide typing area, show results
            document.getElementById('typingArea').classList.remove('active');
            document.getElementById('resultsSection').classList.add('active');

            // Update final stats
            document.getElementById('finalWPM').textContent = wpm;
            document.getElementById('finalAccuracy').textContent = accuracy + '%';
            document.getElementById('finalTime').textContent = Math.round(time) + 's';
            document.getElementById('finalErrors').textContent = errorCount;

            // Show error analysis if there are errors
            if (currentTest.errors.length > 0) {
                showErrorAnalysis();
            } else {
                document.getElementById('errorAnalysis').style.display = 'none';
            }
        }

        function showErrorAnalysis() {
            const errorAnalysis = document.getElementById('errorAnalysis');
            const errorList = document.getElementById('errorList');

            errorList.innerHTML = '';
            currentTest.errors.forEach(error => {
                const li = document.createElement('li');
                li.className = 'error-item';
                li.innerHTML = `Position ${error.position + 1}: Expected '<strong>${error.expected}</strong>' but typed '<strong>${error.typed}</strong>'`;
                errorList.appendChild(li);
            });

            errorAnalysis.style.display = 'block';
        }

        function cancelTest() {
            if (confirm('Are you sure you want to cancel the current test?')) {
                resetToSetup();
            }
        }

        function startNewTest() {
            resetToSetup();
        }

        function resetToSetup() {
            // Stop any running intervals
            if (statsInterval) {
                clearInterval(statsInterval);
                statsInterval = null;
            }

            // Reset test state
            currentTest = {
                textSample: null,
                startTime: null,
                endTime: null,
                errors: [],
                isActive: false,
                currentPosition: 0
            };

            // Show setup, hide other sections
            document.getElementById('testSetup').style.display = 'block';
            document.getElementById('typingArea').classList.remove('active');
            document.getElementById('resultsSection').classList.remove('active');
            document.getElementById('leaderboardSection').style.display = 'none';

            // Reset input
            const input = document.getElementById('typingInput');
            input.removeEventListener('input', handleTyping);
            input.value = '';
            input.disabled = true;
        }

        async function loadLeaderboard() {
            const leaderboardSection = document.getElementById('leaderboardSection');
            const leaderboardBody = document.getElementById('leaderboardBody');
            const difficulty = document.getElementById('leaderboardDifficulty').value;

            leaderboardSection.style.display = 'block';
            leaderboardBody.innerHTML = '<tr><td colspan="6" style="text-align: center; color: #94a3b8;">Loading leaderboard...</td></tr>';

            try {
                let url = `${API_BASE}/leaderboard?limit=20`;
                if (difficulty) {
                    url += `&difficulty=${difficulty}`;
                }

                const response = await fetch(url);
                const data = await response.json();

                if (data.success && data.data.length > 0) {
                    leaderboardBody.innerHTML = '';
                    data.data.forEach((entry, index) => {
                        const row = document.createElement('tr');
                        const rankClass = index === 0 ? 'rank-1' : index === 1 ? 'rank-2' : index === 2 ? 'rank-3' : 'rank-other';
                        const date = new Date(entry.timestamp).toLocaleDateString();

                        row.innerHTML = `
                            <td><span class="rank-badge ${rankClass}">${index + 1}</span></td>
                            <td>${entry.username}</td>
                            <td>${entry.wpm}</td>
                            <td>${entry.accuracy}%</td>
                            <td style="text-transform: capitalize;">${entry.difficulty}</td>
                            <td>${date}</td>
                        `;
                        leaderboardBody.appendChild(row);
                    });
                } else {
                    leaderboardBody.innerHTML = '<tr><td colspan="6" style="text-align: center; color: #94a3b8;">No entries found</td></tr>';
                }
            } catch (error) {
                console.error('Error loading leaderboard:', error);
                leaderboardBody.innerHTML = '<tr><td colspan="6" style="text-align: center; color: #ef4444;">Error loading leaderboard</td></tr>';
            }
        }

        function hideLeaderboard() {
            document.getElementById('leaderboardSection').style.display = 'none';
        }

        async function viewPersonalStats() {
            const username = document.getElementById('usernameInput').value.trim();
            if (!username) {
                alert('Please enter your name first');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/user/${encodeURIComponent(username)}/stats`);
                const data = await response.json();

                if (data.success) {
                    const stats = data.data;
                    alert(`Your Statistics:\n\nTotal Tests: ${stats.totalTests}\nAverage WPM: ${stats.averageWPM}\nAverage Accuracy: ${stats.averageAccuracy}%\nBest WPM: ${stats.bestWPM}\nBest Accuracy: ${stats.bestAccuracy}%`);
                } else {
                    alert('No statistics found for this username');
                }
            } catch (error) {
                console.error('Error loading personal stats:', error);
                alert('Error loading personal statistics');
            }
        }
    </script>
</body>
</html>
