// Simple test script for Sudoku API
const http = require('http');

// Test data
const testPuzzle = [
  [5,3,0,0,7,0,0,0,0],
  [6,0,0,1,9,5,0,0,0],
  [0,9,8,0,0,0,0,6,0],
  [8,0,0,0,6,0,0,0,3],
  [4,0,0,8,0,3,0,0,1],
  [7,0,0,0,2,0,0,0,6],
  [0,6,0,0,0,0,2,8,0],
  [0,0,0,4,1,9,0,0,5],
  [0,0,0,0,8,0,0,7,9]
];

// Helper function to make HTTP requests
function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3002,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(body);
          resolve({ status: res.statusCode, data: jsonData });
        } catch (error) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

// Test functions
async function testAPIInfo() {
  console.log('🔍 Testing API Info...');
  try {
    const result = await makeRequest('/api/info');
    if (result.status === 200 && result.data.name) {
      console.log('✅ API Info test passed');
      console.log(`   API Name: ${result.data.name}`);
      console.log(`   Version: ${result.data.version}`);
      return true;
    } else {
      console.log('❌ API Info test failed');
      return false;
    }
  } catch (error) {
    console.log('❌ API Info test failed:', error.message);
    return false;
  }
}

async function testGenerate() {
  console.log('🎲 Testing Puzzle Generation...');
  try {
    const result = await makeRequest('/api/generate', 'POST', { difficulty: 'easy' });
    if (result.status === 200 && result.data.success && result.data.puzzle) {
      console.log('✅ Generate test passed');
      console.log(`   Difficulty: ${result.data.difficulty}`);
      console.log(`   Clues: ${result.data.metadata.clues}`);
      return true;
    } else {
      console.log('❌ Generate test failed');
      console.log('   Response:', result.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Generate test failed:', error.message);
    return false;
  }
}

async function testSolve() {
  console.log('🧩 Testing Puzzle Solving...');
  try {
    const result = await makeRequest('/api/solve', 'POST', { puzzle: testPuzzle });
    if (result.status === 200 && result.data.success && result.data.solution) {
      console.log('✅ Solve test passed');
      console.log(`   Solving time: ${result.data.solvingTime}ms`);
      console.log(`   Techniques: ${result.data.techniques.join(', ')}`);
      return true;
    } else {
      console.log('❌ Solve test failed');
      console.log('   Response:', result.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Solve test failed:', error.message);
    return false;
  }
}

async function testValidate() {
  console.log('✅ Testing Puzzle Validation...');
  try {
    const result = await makeRequest('/api/validate', 'POST', { puzzle: testPuzzle });
    if (result.status === 200 && result.data.success !== undefined) {
      console.log('✅ Validate test passed');
      console.log(`   Is valid: ${result.data.isValid}`);
      console.log(`   Completeness: ${result.data.completeness.percentage}%`);
      return true;
    } else {
      console.log('❌ Validate test failed');
      console.log('   Response:', result.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Validate test failed:', error.message);
    return false;
  }
}

async function testHint() {
  console.log('💡 Testing Hint System...');
  try {
    const result = await makeRequest('/api/hint', 'POST', { puzzle: testPuzzle });
    if (result.status === 200) {
      if (result.data.success && result.data.hint) {
        console.log('✅ Hint test passed');
        console.log(`   Hint: Row ${result.data.hint.row + 1}, Col ${result.data.hint.col + 1} = ${result.data.hint.value}`);
        console.log(`   Technique: ${result.data.hint.technique}`);
      } else {
        console.log('✅ Hint test passed (no hints available)');
      }
      return true;
    } else {
      console.log('❌ Hint test failed');
      console.log('   Response:', result.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Hint test failed:', error.message);
    return false;
  }
}

async function testAnalyze() {
  console.log('📊 Testing Puzzle Analysis...');
  try {
    const result = await makeRequest('/api/analyze', 'POST', { puzzle: testPuzzle });
    if (result.status === 200 && result.data.success && result.data.analysis) {
      console.log('✅ Analyze test passed');
      console.log(`   Difficulty: ${result.data.analysis.difficulty}`);
      console.log(`   Clue count: ${result.data.analysis.clueCount}`);
      console.log(`   Required techniques: ${result.data.analysis.requiredTechniques.join(', ')}`);
      return true;
    } else {
      console.log('❌ Analyze test failed');
      console.log('   Response:', result.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Analyze test failed:', error.message);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting Sudoku API Tests...\n');
  
  const tests = [
    testAPIInfo,
    testGenerate,
    testSolve,
    testValidate,
    testHint,
    testAnalyze
  ];
  
  let passed = 0;
  let total = tests.length;
  
  for (const test of tests) {
    const result = await test();
    if (result) passed++;
    console.log('');
  }
  
  console.log('📋 Test Results:');
  console.log(`   Passed: ${passed}/${total}`);
  console.log(`   Success Rate: ${Math.round((passed / total) * 100)}%`);
  
  if (passed === total) {
    console.log('🎉 All tests passed! API is working perfectly.');
  } else {
    console.log('⚠️  Some tests failed. Check the server logs for details.');
  }
}

// Check if server is running first
async function checkServer() {
  try {
    await makeRequest('/api/info');
    return true;
  } catch (error) {
    console.log('❌ Server is not running. Please start the server with: npm start');
    return false;
  }
}

// Main execution
async function main() {
  const serverRunning = await checkServer();
  if (serverRunning) {
    await runAllTests();
  }
}

main();
