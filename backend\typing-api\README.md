# 🚀 Typing Analytics API

A comprehensive, professional-grade typing analytics API built with Node.js and Express. Features advanced performance tracking, leaderboards, and detailed analytics for typing tests.

## 🏗️ Architecture Overview

### Core Components
- **Analytics Engine**: Real-time WPM and accuracy calculations
- **Leaderboard System**: Dynamic ranking with difficulty-based categorization  
- **Text Management**: Curated samples with difficulty levels
- **User Statistics**: Personal performance tracking and history
- **Global Analytics**: Aggregated statistics and trend analysis

### Technical Stack
- **Runtime**: Node.js 16+
- **Framework**: Express.js
- **Security**: Helmet, CORS, Rate Limiting
- **Performance**: Compression middleware
- **Architecture**: RESTful API with JSON responses

## 📊 Key Features

### Performance Metrics
- **WPM Calculation**: Words per minute based on 5-character standard
- **Accuracy Analysis**: Character-level precision tracking
- **Error Detection**: Detailed mistake analysis and categorization
- **Time Tracking**: Precise timing with millisecond accuracy

### Text Management
- **Difficulty Levels**: Easy, Medium, Hard categorization
- **Content Categories**: Programming, Technical, Nature, etc.
- **Sample Variety**: Diverse texts for comprehensive testing
- **Metadata**: Character counts, complexity metrics

### Analytics & Insights
- **Global Statistics**: Platform-wide performance metrics
- **User Profiles**: Individual progress and achievements
- **Leaderboards**: Competitive rankings by performance
- **Trend Analysis**: Performance patterns over time

## 🚀 API Endpoints

### Text Management
```
GET /api/texts
GET /api/texts/:id
```

### Test Submission
```
POST /api/submit
```

### Analytics & Statistics
```
GET /api/leaderboard
GET /api/analytics
GET /api/user/:username/stats
```

### System Information
```
GET /api/info
```

## 🛠️ Installation & Setup

### Prerequisites
- Node.js 16.0.0 or higher
- npm or yarn package manager

### Quick Start
```bash
# Clone the repository
git clone <repository-url>
cd typing-api

# Install dependencies
npm install

# Start the server
npm start

# Development mode with auto-reload
npm run dev
```

### Server Configuration
- **Port**: 3003 (configurable via PORT environment variable)
- **CORS**: Enabled for cross-origin requests
- **Rate Limiting**: 100 requests per 15 minutes per IP
- **Security**: Helmet middleware for security headers

## 📝 Usage Examples

### Get Available Texts
```javascript
fetch('/api/texts?difficulty=medium&category=programming')
  .then(response => response.json())
  .then(data => console.log(data));
```

### Submit Test Result
```javascript
const testResult = {
  username: 'john_doe',
  textId: 2,
  typedText: 'JavaScript is a versatile...',
  timeInSeconds: 45.2,
  correctCharacters: 95,
  totalCharacters: 100,
  errors: [
    { position: 15, expected: 'a', typed: 'e' }
  ]
};

fetch('/api/submit', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(testResult)
});
```

### Get Leaderboard
```javascript
fetch('/api/leaderboard?limit=10&difficulty=hard')
  .then(response => response.json())
  .then(data => console.log(data));
```

## 🧪 Testing

### Run Tests
```bash
npm test
```

### Manual Testing
1. Start the server: `npm start`
2. Open browser to `http://localhost:3003`
3. Use the interactive demo interface
4. Test endpoints with curl or Postman

### Example curl Commands
```bash
# Get API info
curl http://localhost:3003/api/info

# Get text samples
curl http://localhost:3003/api/texts

# Submit test result
curl -X POST http://localhost:3003/api/submit \
  -H "Content-Type: application/json" \
  -d '{"username":"test","textId":1,"typedText":"test","timeInSeconds":30,"correctCharacters":20,"totalCharacters":25}'
```

## 📈 Performance Considerations

### Scalability
- In-memory storage for demo purposes
- Ready for database integration (MongoDB, PostgreSQL)
- Stateless design for horizontal scaling
- Efficient algorithms for real-time calculations

### Security
- Input validation and sanitization
- Rate limiting to prevent abuse
- CORS configuration for controlled access
- Helmet middleware for security headers

### Monitoring
- Error handling and logging
- Performance metrics tracking
- Health check endpoints
- Request/response timing

## 🔧 Configuration

### Environment Variables
```bash
PORT=3003                    # Server port
NODE_ENV=production         # Environment mode
RATE_LIMIT_WINDOW=900000    # Rate limit window (15 minutes)
RATE_LIMIT_MAX=100          # Max requests per window
```

### Customization
- Text samples can be modified in `server.js`
- Difficulty algorithms can be adjusted
- Analytics calculations can be customized
- Leaderboard rules can be modified

## 📚 API Documentation

### Response Format
All API responses follow a consistent format:
```json
{
  "success": true,
  "data": { ... },
  "meta": { ... },
  "message": "Optional message"
}
```

### Error Handling
```json
{
  "success": false,
  "error": "Error description",
  "code": "ERROR_CODE"
}
```

### Status Codes
- `200`: Success
- `400`: Bad Request (validation errors)
- `404`: Not Found
- `429`: Too Many Requests (rate limited)
- `500`: Internal Server Error

## 🚀 Deployment

### Production Considerations
- Use process manager (PM2, Forever)
- Configure reverse proxy (Nginx)
- Set up SSL/TLS certificates
- Implement proper logging
- Configure monitoring and alerts

### Docker Support
```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3003
CMD ["node", "server.js"]
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 👨‍💻 Author

**Zayden Sharp**
- Portfolio: [Your Portfolio URL]
- GitHub: [Your GitHub URL]
- LinkedIn: [Your LinkedIn URL]

---

*Built with ❤️ using Node.js and Express*
