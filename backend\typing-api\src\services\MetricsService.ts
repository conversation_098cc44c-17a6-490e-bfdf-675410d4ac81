/**
 * Metrics service for Prometheus monitoring
 */

import { register } from 'prom-client';
import { logger } from '@/utils/logger';

export class MetricsService {
  private isHealthy: boolean = false;

  /**
   * Initialize metrics service
   */
  initialize(): void {
    try {
      // Metrics are already registered in middleware/metrics.ts
      this.isHealthy = true;
      logger.info('Metrics service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize metrics service:', error);
      this.isHealthy = false;
    }
  }

  /**
   * Get all metrics in Prometheus format
   */
  async getMetrics(): Promise<string> {
    return register.metrics();
  }

  /**
   * Check if metrics service is healthy
   */
  isHealthy(): boolean {
    return this.isHealthy;
  }
}
